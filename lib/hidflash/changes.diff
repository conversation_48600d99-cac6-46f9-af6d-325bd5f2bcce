diff --git a/cli/Makefile b/cli/Makefile
index dee725e..d038615 100644
--- a/cli/Makefile
+++ b/cli/Makefile
@@ -3,6 +3,7 @@ CFLAGS=-c -Wall
 LDFLAGS=
 SOURCES=main.c
 INCLUDE_DIRS=-I .
+CHECK=1
 
 ifeq ($(OS),Windows_NT)
 	SOURCES+=hid-win.c
@@ -15,19 +16,21 @@ else
 		SOURCES+=rs232.c
 		LIBS=-framework IOKit -framework CoreFoundation
 	else
+		CHECK:=$(shell pkg-config --exists libusb-1.0 && echo 1)
 		SOURCES+=hid-libusb.c
 		SOURCES+=rs232.c
 		LIBS=`pkg-config libusb-1.0 --libs` -lrt -lpthread
 		INCLUDE_DIRS+=`pkg-config libusb-1.0 --cflags`
 		CFLAGS+=-std=gnu99
 		LDFLAGS+=-no-pie
-		endif
+	endif
 endif
 
 OBJECTS=$(SOURCES:.c=.o)
 
 EXECUTABLE = hid-flash
 
+ifeq ($(CHECK), 1)
 all: $(SOURCES) $(EXECUTABLE)
 
 $(EXECUTABLE): $(OBJECTS)
@@ -35,6 +38,11 @@ $(EXECUTABLE): $(OBJECTS)
 
 .c.o:
 	$(CC) $(CFLAGS) $(INCLUDE_DIRS) $< -o $@
+else
+all:
+	@echo "    hid-flash requires libusb-1.0, please install with:"
+	@echo "    sudo apt-get install libusb-1.0"
+endif
 
 clean:
 	rm -f $(OBJECTS) $(EXECUTABLE) $(EXECUTABLE).exe
diff --git a/cli/main.c b/cli/main.c
index a6a8f7f..84abc33 100644
--- a/cli/main.c
+++ b/cli/main.c
@@ -19,6 +19,12 @@
 *	by Vassilis Serasidis <<EMAIL>>
 *	This HID bootloader work with bluepill + STM32duino + Arduino IDE <http://www.stm32duino.com/>
 *
+* Modified 4/24/2020
+* by Eric Callahan <<EMAIL>>
+* This version of hid-flash has been modified to work with Klipper.
+* The serial port argument is now optional.  If entered and found this program
+* will attempt to force Klipper jump to the bootloader by connecting at
+* 1200 baud and enabling DTR.
 */
 
 #include <stdlib.h>
@@ -37,6 +43,8 @@
 #define PID           0xBEBA
 #define FIRMWARE_VER  0x0300
 
+#define MAX_PAGE_SIZE 2048
+
 int serial_init(char *argument, uint8_t __timer);
 
 
@@ -73,45 +81,48 @@ int main(int argc, char *argv[]) {
   int i;
   setbuf(stdout, NULL);
   uint8_t _timer = 0;
-  
+
   printf("\n+-----------------------------------------------------------------------+\n");
   printf  ("|         HID-Flash v2.2.1 - STM32 HID Bootloader Flash Tool            |\n");
   printf  ("|     (c)      2018 - Bruno Freitas       http://www.brunofreitas.com   |\n");
   printf  ("|     (c) 2018-2019 - Vassilis Serasidis  https://www.serasidis.gr      |\n");
   printf  ("|   Customized for STM32duino ecosystem   https://www.stm32duino.com    |\n");
   printf  ("+-----------------------------------------------------------------------+\n\n");
-  
-  if(argc < 3) {
-    printf("Usage: hid-flash <bin_firmware_file> <comport> <delay (optional)>\n");
+
+  // TODO:  This really needs an option parser
+  if(argc < 2) {
+    printf("Usage: hid-flash <bin_firmware_file> <comport (optional)> <delay (optional)>\n");
     return 1;
   }else if(argc == 4){
     _timer = atol(argv[3]);
   }
-  
+
   firmware_file = fopen(argv[1], "rb");
   if(!firmware_file) {
     printf("> Error opening firmware file: %s\n", argv[1]);
     return error;
   }
-  
-  if(serial_init(argv[2], _timer) == 0){ //Setting up Serial port
-    RS232_CloseComport();
-  }else{
-    printf("> Unable to open the [%s]\n",argv[2]);
+
+  if (argc > 2) {
+    if(serial_init(argv[2], _timer) == 0){ //Setting up Serial port
+      RS232_CloseComport();
+    }else{
+      printf("> Unable to open the [%s]\n",argv[2]);
+    }
   }
-  
+
   hid_init();
-  
+
   printf("> Searching for [%04X:%04X] device...\n",VID,PID);
-  
+
   struct hid_device_info *devs, *cur_dev;
   uint8_t valid_hid_devices = 0;
-  
+
   for(i=0;i<10;i++){ //Try up to 10 times to open the HID device.
     devs = hid_enumerate(VID, PID);
     cur_dev = devs;
     while (cur_dev) { //Search for valid HID Bootloader USB devices
-      if((cur_dev->vendor_id == VID)&&(cur_dev->product_id = PID)){
+      if((cur_dev->vendor_id == VID)&&(cur_dev->product_id == PID)){
         valid_hid_devices++;
         if(cur_dev->release_number < FIRMWARE_VER){ //The STM32 board has firmware lower than 3.00
           printf("\nError - Please update the firmware to the latest version (v3.00+)");
@@ -129,18 +140,18 @@ int main(int argc, char *argv[]) {
     printf("\nError - [%04X:%04X] device is not found :(",VID,PID);
     error = 1;
     goto exit;
-  } 
-  
+  }
+
   handle = hid_open(VID, PID, NULL);
-  
+
   if (i == 10 && handle != NULL) {
     printf("\n> Unable to open the [%04X:%04X] device.\n",VID,PID);
     error = 1;
     goto exit;
   }
- 
+
   printf("\n> [%04X:%04X] device is found !\n",VID,PID);
-  
+
   // Send RESET PAGES command to put HID bootloader in initial stage...
   memset(hid_tx_buf, 0, sizeof(hid_tx_buf)); //Fill the hid_tx_buf with zeros.
   memcpy(&hid_tx_buf[1], CMD_RESET_PAGES, sizeof(CMD_RESET_PAGES));
@@ -161,7 +172,7 @@ int main(int argc, char *argv[]) {
   memset(page_data, 0, sizeof(page_data));
   read_bytes = fread(page_data, 1, sizeof(page_data), firmware_file);
 
-  while(read_bytes > 0) {
+  while(1) {
 
     for(int i = 0; i < SECTOR_SIZE; i += HID_TX_SIZE - 1) {
       memcpy(&hid_tx_buf[1], page_data + i, HID_TX_SIZE - 1);
@@ -169,7 +180,7 @@ int main(int argc, char *argv[]) {
       if((i % 1024) == 0){
         printf(".");
       }
-      
+
       // Flash is unavailable when writing to it, so USB interrupt may fail here
       if(!usb_write(handle, hid_tx_buf, HID_TX_SIZE)) {
         printf("> Error while flashing firmware data.\n");
@@ -179,20 +190,31 @@ int main(int argc, char *argv[]) {
       n_bytes += (HID_TX_SIZE - 1);
       usleep(500);
     }
-    
+
     printf(" %d Bytes\n", n_bytes);
 
     do{
       hid_read(handle, hid_rx_buf, 9);
       usleep(500);
-    }while(hid_rx_buf[7] != 0x02);
-    
+    // Exit the loop if we recieve 0x02 or 0x03
+    }while((hid_rx_buf[7] & 0xFE) != 0x02);
+
     memset(page_data, 0, sizeof(page_data));
     read_bytes = fread(page_data, 1, sizeof(page_data), firmware_file);
+
+    // For stm32f1 high density devices (2K page size) will receive a
+    // 0x03 command acknowledgement above.  In that case, we must
+    // make sure that we send a full 2K so the last page is written.
+    // Note that this issue does not affect STM32F4 devices with larger
+    // page sizes.
+    if (read_bytes == 0) {
+      if (hid_rx_buf[7] != 0x03 || (n_bytes % MAX_PAGE_SIZE) == 0)
+        break;
+    }
   }
 
   printf("\n> Done!\n");
-  
+
   // Send CMD_REBOOT_MCU command to reboot the microcontroller...
   memset(hid_tx_buf, 0, sizeof(hid_tx_buf));
   memcpy(&hid_tx_buf[1], CMD_REBOOT_MCU, sizeof(CMD_REBOOT_MCU));
@@ -203,7 +225,7 @@ int main(int argc, char *argv[]) {
   if(!usb_write(handle, hid_tx_buf, HID_TX_SIZE)) {
     printf("> Error while sending <reboot mcu> command.\n");
   }
-  
+
 exit:
   if(handle) {
     hid_close(handle);
@@ -214,22 +236,24 @@ exit:
   if(firmware_file) {
     fclose(firmware_file);
   }
-  
-  printf("> Searching for [%s] ...\n",argv[2]);
 
-  for(int i=0;i<5;i++){
-    if(RS232_OpenComport(argv[2]) == 0){
-      printf("> [%s] is found !\n",argv[2] );
-      break;
+  if (argc > 2) {
+    printf("> Searching for [%s] ...\n",argv[2]);
+
+    for(int i=0;i<5;i++){
+      if(RS232_OpenComport(argv[2]) == 0){
+        printf("> [%s] is found !\n",argv[2] );
+        break;
+      }
+      sleep(1);
+    }
+
+    if(i==5){
+      printf("> Comport is not found\n");
     }
-    sleep(1);
-  }
-  
-  if(i==5){
-    printf("> Comport is not found\n");
   }
   printf("> Finish\n");
-  
+
   return error;
 }
 
@@ -240,20 +264,14 @@ int serial_init(char *argument, uint8_t __timer) {
     return(1);
   }
   printf("> Toggling DTR...\n");
-  
+
   RS232_disableRTS();
-  RS232_enableDTR();
-  usleep(200000L);
   RS232_disableDTR();
   usleep(200000L);
   RS232_enableDTR();
   usleep(200000L);
-  RS232_disableDTR();
-  usleep(200000L);
-  RS232_send_magic();
-  usleep(200000L);
   RS232_CloseComport();
-  
+
   //printf("A %i\n",__timer);
   if (__timer > 0) {
     usleep(__timer);
diff --git a/cli/rs232.c b/cli/rs232.c
index 067ef1a..ce7de08 100644
--- a/cli/rs232.c
+++ b/cli/rs232.c
@@ -80,12 +80,12 @@
     new_termios.c_cc[VEOL2]    = 0;
 
 
-    if (cfsetispeed(&new_termios, B9600) != 0) {
-        fprintf(stderr, "cfsetispeed(&new_termios, B9600) failed: %s\n", strerror(errno));
+    if (cfsetispeed(&new_termios, B1200) != 0) {
+        fprintf(stderr, "cfsetispeed(&new_termios, B1200) failed: %s\n", strerror(errno));
         return 1;
     }
-    if (cfsetospeed(&new_termios, B9600) != 0) {
-        fprintf(stderr, "cfsetospeed(&new_termios, B9600) failed: %s\n", strerror(errno));
+    if (cfsetospeed(&new_termios, B1200) != 0) {
+        fprintf(stderr, "cfsetospeed(&new_termios, B1200) failed: %s\n", strerror(errno));
         return 1;
     }
 
