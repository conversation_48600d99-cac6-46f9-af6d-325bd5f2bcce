/*
***************************************************************************
*
* Author: <PERSON><PERSON><PERSON>
*
* Copyright (C) 2005, 2006, 2007, 2008, 2009, 2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017 <PERSON><PERSON><PERSON> van <PERSON>
*
* Email: <EMAIL>
*
***************************************************************************
*
* This program is free software: you can redistribute it and/or modify
* it under the terms of the GNU General Public License as published by
* the Free Software Foundation, either version 3 of the License, or
* (at your option) any later version.
*
* This program is distributed in the hope that it will be useful,
* but WITHOUT ANY WARRANTY; without even the implied warranty of
* MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
* GNU General Public License for more details.
*
* You should have received a copy of the GNU General Public License
* along with this program.  If not, see <http://www.gnu.org/licenses/>.
*
***************************************************************************
*/

/**
 * This rs232 source code file is a customized version for HID Bootloader project
 * (c) 10 May 2018 by Vassilis Serasidis http://www.serasidis.gr <<EMAIL>>
 *
 */

/* For more info and how to use this library, visit: http://www.teuniz.net/RS-232/ */


#ifndef rs232_INCLUDED
#define rs232_INCLUDED

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <string.h>



#if defined(__linux__) || defined(__FreeBSD__) || defined(__APPLE__)

#include <termios.h>
#include <sys/ioctl.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <limits.h>
#include <sys/file.h>
#include <errno.h>

#else

#include <windows.h>

#endif

int  RS232_OpenComport(char *);
int  RS232_SendByte(unsigned char);
//int  RS232_ReadByte();
void RS232_CloseComport();
void RS232_enableDTR();
void RS232_disableDTR();
void RS232_enableRTS();
void RS232_disableRTS();
void RS232_send_magic();

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif


