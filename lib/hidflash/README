The source for the hid-flash program in this folder is a deriviative of the
"cli" modified by <PERSON><PERSON><PERSON> to work with the STM32 Arduino core,
available here:
https://github.com/Serasidis/STM32_HID_Bootloader/tree/master/cli

The original source for hid-flash was written by <PERSON>, available
here:
https://github.com/bootsector/stm32-hid-bootloader/tree/master/cli

This derivative contains the following changes:
1) The "port" argument is now optional.  If not supplied hid-flash will not
   attempt to open a serial port and enter the bootloader, it will assume
   that the device has already entered the bootloader and look for a USB
   device with the appropriate Vendor and Product IDs. This change allows
   <PERSON><PERSON><PERSON>'s "flash_usb.py" script to enter the bootloader and wait for the
   mcu to reconnect.
2) When the serial port is specified, hid-flash will use <PERSON><PERSON><PERSON>'s procedure
   for entering the bootloader (Open the port at 1200 baud and toggle DTR).
3) The hid-flash program now accepts a command from the bootloader that allows
   a STM32F103 device to identify itself as a "high-density" device during
   the flashing process.  This fixes a bug where the final page would not
   be written if the last portion of the binary was less than or equal to
   1024 bytes. A forked version of the bootloader is required to send the
   "high-density" command, however the tool will still work correctly with
   the original bootloader (sans the bug that affects high density devices).
4) A typo was fixed where an "if" statement was using an assignment operator
   to test for equality.
5) The Makefile was changed to check for the libusb-1.0 dependency. If it
   does not exist the user will be warned and the build aborted, however
   no error will be generated.  This allows Klipper's "make FLASH"
   functionality to work when flashing via DFU without installing the
   libusb-1.0 dependency for hid-flash.

- Eric Callahan <<EMAIL>>
