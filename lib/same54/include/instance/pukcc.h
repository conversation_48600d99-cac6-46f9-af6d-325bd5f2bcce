/**
 * \file
 *
 * \brief Instance description for PUKCC
 *
 * Copyright (c) 2019 Microchip Technology Inc.
 *
 * \asf_license_start
 *
 * \page License
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the Licence at
 * 
 * http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * \asf_license_stop
 *
 */

#ifndef _SAME54_PUKCC_INSTANCE_
#define _SAME54_PUKCC_INSTANCE_

/* ========== Instance parameters for PUKCC peripheral ========== */
#define PUKCC_CLK_AHB_ID            20      
#define PUKCC_RAM_ADDR_SIZE         12      
#define PUKCC_ROM_ADDR_SIZE         16      

#endif /* _SAME54_PUKCC_INSTANCE_ */
