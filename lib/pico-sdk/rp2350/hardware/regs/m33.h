// THIS HEADER FILE IS AUTOMATICALLY GENERATED -- DO NOT EDIT

/**
 * Copyright (c) 2024 Raspberry Pi Ltd.
 *
 * SPDX-License-Identifier: BSD-3-Clause
 */
// =============================================================================
// Register block : M33
// Version        : 1
// Bus type       : apb
// Description    : TEAL registers accessible through the debug interface
// =============================================================================
#ifndef _HARDWARE_REGS_M33_H
#define _HARDWARE_REGS_M33_H
// =============================================================================
// Register    : M33_ITM_STIM0
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM0_OFFSET _u(0x00000000)
#define M33_ITM_STIM0_BITS   _u(0xffffffff)
#define M33_ITM_STIM0_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM0_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM0_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM0_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM0_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM0_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM0_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM1
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM1_OFFSET _u(0x00000004)
#define M33_ITM_STIM1_BITS   _u(0xffffffff)
#define M33_ITM_STIM1_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM1_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM1_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM1_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM1_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM1_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM1_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM2
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM2_OFFSET _u(0x00000008)
#define M33_ITM_STIM2_BITS   _u(0xffffffff)
#define M33_ITM_STIM2_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM2_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM2_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM2_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM2_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM2_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM2_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM3
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM3_OFFSET _u(0x0000000c)
#define M33_ITM_STIM3_BITS   _u(0xffffffff)
#define M33_ITM_STIM3_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM3_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM3_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM3_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM3_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM3_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM3_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM4
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM4_OFFSET _u(0x00000010)
#define M33_ITM_STIM4_BITS   _u(0xffffffff)
#define M33_ITM_STIM4_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM4_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM4_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM4_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM4_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM4_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM4_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM5
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM5_OFFSET _u(0x00000014)
#define M33_ITM_STIM5_BITS   _u(0xffffffff)
#define M33_ITM_STIM5_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM5_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM5_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM5_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM5_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM5_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM5_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM6
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM6_OFFSET _u(0x00000018)
#define M33_ITM_STIM6_BITS   _u(0xffffffff)
#define M33_ITM_STIM6_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM6_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM6_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM6_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM6_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM6_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM6_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM7
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM7_OFFSET _u(0x0000001c)
#define M33_ITM_STIM7_BITS   _u(0xffffffff)
#define M33_ITM_STIM7_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM7_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM7_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM7_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM7_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM7_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM7_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM8
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM8_OFFSET _u(0x00000020)
#define M33_ITM_STIM8_BITS   _u(0xffffffff)
#define M33_ITM_STIM8_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM8_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM8_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM8_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM8_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM8_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM8_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM9
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM9_OFFSET _u(0x00000024)
#define M33_ITM_STIM9_BITS   _u(0xffffffff)
#define M33_ITM_STIM9_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM9_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM9_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM9_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM9_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM9_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM9_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM10
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM10_OFFSET _u(0x00000028)
#define M33_ITM_STIM10_BITS   _u(0xffffffff)
#define M33_ITM_STIM10_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM10_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM10_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM10_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM10_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM10_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM10_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM11
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM11_OFFSET _u(0x0000002c)
#define M33_ITM_STIM11_BITS   _u(0xffffffff)
#define M33_ITM_STIM11_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM11_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM11_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM11_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM11_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM11_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM11_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM12
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM12_OFFSET _u(0x00000030)
#define M33_ITM_STIM12_BITS   _u(0xffffffff)
#define M33_ITM_STIM12_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM12_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM12_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM12_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM12_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM12_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM12_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM13
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM13_OFFSET _u(0x00000034)
#define M33_ITM_STIM13_BITS   _u(0xffffffff)
#define M33_ITM_STIM13_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM13_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM13_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM13_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM13_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM13_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM13_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM14
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM14_OFFSET _u(0x00000038)
#define M33_ITM_STIM14_BITS   _u(0xffffffff)
#define M33_ITM_STIM14_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM14_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM14_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM14_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM14_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM14_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM14_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM15
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM15_OFFSET _u(0x0000003c)
#define M33_ITM_STIM15_BITS   _u(0xffffffff)
#define M33_ITM_STIM15_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM15_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM15_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM15_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM15_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM15_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM15_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM16
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM16_OFFSET _u(0x00000040)
#define M33_ITM_STIM16_BITS   _u(0xffffffff)
#define M33_ITM_STIM16_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM16_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM16_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM16_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM16_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM16_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM16_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM17
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM17_OFFSET _u(0x00000044)
#define M33_ITM_STIM17_BITS   _u(0xffffffff)
#define M33_ITM_STIM17_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM17_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM17_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM17_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM17_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM17_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM17_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM18
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM18_OFFSET _u(0x00000048)
#define M33_ITM_STIM18_BITS   _u(0xffffffff)
#define M33_ITM_STIM18_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM18_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM18_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM18_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM18_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM18_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM18_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM19
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM19_OFFSET _u(0x0000004c)
#define M33_ITM_STIM19_BITS   _u(0xffffffff)
#define M33_ITM_STIM19_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM19_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM19_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM19_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM19_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM19_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM19_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM20
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM20_OFFSET _u(0x00000050)
#define M33_ITM_STIM20_BITS   _u(0xffffffff)
#define M33_ITM_STIM20_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM20_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM20_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM20_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM20_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM20_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM20_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM21
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM21_OFFSET _u(0x00000054)
#define M33_ITM_STIM21_BITS   _u(0xffffffff)
#define M33_ITM_STIM21_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM21_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM21_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM21_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM21_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM21_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM21_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM22
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM22_OFFSET _u(0x00000058)
#define M33_ITM_STIM22_BITS   _u(0xffffffff)
#define M33_ITM_STIM22_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM22_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM22_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM22_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM22_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM22_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM22_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM23
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM23_OFFSET _u(0x0000005c)
#define M33_ITM_STIM23_BITS   _u(0xffffffff)
#define M33_ITM_STIM23_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM23_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM23_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM23_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM23_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM23_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM23_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM24
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM24_OFFSET _u(0x00000060)
#define M33_ITM_STIM24_BITS   _u(0xffffffff)
#define M33_ITM_STIM24_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM24_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM24_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM24_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM24_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM24_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM24_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM25
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM25_OFFSET _u(0x00000064)
#define M33_ITM_STIM25_BITS   _u(0xffffffff)
#define M33_ITM_STIM25_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM25_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM25_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM25_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM25_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM25_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM25_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM26
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM26_OFFSET _u(0x00000068)
#define M33_ITM_STIM26_BITS   _u(0xffffffff)
#define M33_ITM_STIM26_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM26_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM26_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM26_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM26_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM26_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM26_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM27
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM27_OFFSET _u(0x0000006c)
#define M33_ITM_STIM27_BITS   _u(0xffffffff)
#define M33_ITM_STIM27_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM27_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM27_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM27_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM27_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM27_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM27_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM28
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM28_OFFSET _u(0x00000070)
#define M33_ITM_STIM28_BITS   _u(0xffffffff)
#define M33_ITM_STIM28_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM28_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM28_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM28_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM28_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM28_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM28_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM29
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM29_OFFSET _u(0x00000074)
#define M33_ITM_STIM29_BITS   _u(0xffffffff)
#define M33_ITM_STIM29_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM29_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM29_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM29_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM29_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM29_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM29_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM30
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM30_OFFSET _u(0x00000078)
#define M33_ITM_STIM30_BITS   _u(0xffffffff)
#define M33_ITM_STIM30_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM30_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM30_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM30_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM30_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM30_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM30_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_STIM31
// Description : Provides the interface for generating Instrumentation packets
#define M33_ITM_STIM31_OFFSET _u(0x0000007c)
#define M33_ITM_STIM31_BITS   _u(0xffffffff)
#define M33_ITM_STIM31_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_STIM31_STIMULUS
// Description : Data to write to the Stimulus Port FIFO, for forwarding as an
//               Instrumentation packet. The size of write access determines the
//               type of Instrumentation packet generated.
#define M33_ITM_STIM31_STIMULUS_RESET  _u(0x00000000)
#define M33_ITM_STIM31_STIMULUS_BITS   _u(0xffffffff)
#define M33_ITM_STIM31_STIMULUS_MSB    _u(31)
#define M33_ITM_STIM31_STIMULUS_LSB    _u(0)
#define M33_ITM_STIM31_STIMULUS_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_TER0
// Description : Provide an individual enable bit for each ITM_STIM register
#define M33_ITM_TER0_OFFSET _u(0x00000e00)
#define M33_ITM_TER0_BITS   _u(0xffffffff)
#define M33_ITM_TER0_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_TER0_STIMENA
// Description : For STIMENA[m] in ITM_TER*n, controls whether ITM_STIM(32*n +
//               m) is enabled
#define M33_ITM_TER0_STIMENA_RESET  _u(0x00000000)
#define M33_ITM_TER0_STIMENA_BITS   _u(0xffffffff)
#define M33_ITM_TER0_STIMENA_MSB    _u(31)
#define M33_ITM_TER0_STIMENA_LSB    _u(0)
#define M33_ITM_TER0_STIMENA_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_TPR
// Description : Controls which stimulus ports can be accessed by unprivileged
//               code
#define M33_ITM_TPR_OFFSET _u(0x00000e40)
#define M33_ITM_TPR_BITS   _u(0x0000000f)
#define M33_ITM_TPR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_TPR_PRIVMASK
// Description : Bit mask to enable tracing on ITM stimulus ports
#define M33_ITM_TPR_PRIVMASK_RESET  _u(0x0)
#define M33_ITM_TPR_PRIVMASK_BITS   _u(0x0000000f)
#define M33_ITM_TPR_PRIVMASK_MSB    _u(3)
#define M33_ITM_TPR_PRIVMASK_LSB    _u(0)
#define M33_ITM_TPR_PRIVMASK_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_TCR
// Description : Configures and controls transfers through the ITM interface
#define M33_ITM_TCR_OFFSET _u(0x00000e80)
#define M33_ITM_TCR_BITS   _u(0x00ff0f3f)
#define M33_ITM_TCR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_TCR_BUSY
// Description : Indicates whether the ITM is currently processing events
#define M33_ITM_TCR_BUSY_RESET  _u(0x0)
#define M33_ITM_TCR_BUSY_BITS   _u(0x00800000)
#define M33_ITM_TCR_BUSY_MSB    _u(23)
#define M33_ITM_TCR_BUSY_LSB    _u(23)
#define M33_ITM_TCR_BUSY_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_TCR_TRACEBUSID
// Description : Identifier for multi-source trace stream formatting. If multi-
//               source trace is in use, the debugger must write a unique non-
//               zero trace ID value to this field
#define M33_ITM_TCR_TRACEBUSID_RESET  _u(0x00)
#define M33_ITM_TCR_TRACEBUSID_BITS   _u(0x007f0000)
#define M33_ITM_TCR_TRACEBUSID_MSB    _u(22)
#define M33_ITM_TCR_TRACEBUSID_LSB    _u(16)
#define M33_ITM_TCR_TRACEBUSID_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_TCR_GTSFREQ
// Description : Defines how often the ITM generates a global timestamp, based
//               on the global timestamp clock frequency, or disables generation
//               of global timestamps
#define M33_ITM_TCR_GTSFREQ_RESET  _u(0x0)
#define M33_ITM_TCR_GTSFREQ_BITS   _u(0x00000c00)
#define M33_ITM_TCR_GTSFREQ_MSB    _u(11)
#define M33_ITM_TCR_GTSFREQ_LSB    _u(10)
#define M33_ITM_TCR_GTSFREQ_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_TCR_TSPRESCALE
// Description : Local timestamp prescaler, used with the trace packet reference
//               clock
#define M33_ITM_TCR_TSPRESCALE_RESET  _u(0x0)
#define M33_ITM_TCR_TSPRESCALE_BITS   _u(0x00000300)
#define M33_ITM_TCR_TSPRESCALE_MSB    _u(9)
#define M33_ITM_TCR_TSPRESCALE_LSB    _u(8)
#define M33_ITM_TCR_TSPRESCALE_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_TCR_STALLENA
// Description : Stall the PE to guarantee delivery of Data Trace packets.
#define M33_ITM_TCR_STALLENA_RESET  _u(0x0)
#define M33_ITM_TCR_STALLENA_BITS   _u(0x00000020)
#define M33_ITM_TCR_STALLENA_MSB    _u(5)
#define M33_ITM_TCR_STALLENA_LSB    _u(5)
#define M33_ITM_TCR_STALLENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_TCR_SWOENA
// Description : Enables asynchronous clocking of the timestamp counter
#define M33_ITM_TCR_SWOENA_RESET  _u(0x0)
#define M33_ITM_TCR_SWOENA_BITS   _u(0x00000010)
#define M33_ITM_TCR_SWOENA_MSB    _u(4)
#define M33_ITM_TCR_SWOENA_LSB    _u(4)
#define M33_ITM_TCR_SWOENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_TCR_TXENA
// Description : Enables forwarding of hardware event packet from the DWT unit
//               to the ITM for output to the TPIU
#define M33_ITM_TCR_TXENA_RESET  _u(0x0)
#define M33_ITM_TCR_TXENA_BITS   _u(0x00000008)
#define M33_ITM_TCR_TXENA_MSB    _u(3)
#define M33_ITM_TCR_TXENA_LSB    _u(3)
#define M33_ITM_TCR_TXENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_TCR_SYNCENA
// Description : Enables Synchronization packet transmission for a synchronous
//               TPIU
#define M33_ITM_TCR_SYNCENA_RESET  _u(0x0)
#define M33_ITM_TCR_SYNCENA_BITS   _u(0x00000004)
#define M33_ITM_TCR_SYNCENA_MSB    _u(2)
#define M33_ITM_TCR_SYNCENA_LSB    _u(2)
#define M33_ITM_TCR_SYNCENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_TCR_TSENA
// Description : Enables Local timestamp generation
#define M33_ITM_TCR_TSENA_RESET  _u(0x0)
#define M33_ITM_TCR_TSENA_BITS   _u(0x00000002)
#define M33_ITM_TCR_TSENA_MSB    _u(1)
#define M33_ITM_TCR_TSENA_LSB    _u(1)
#define M33_ITM_TCR_TSENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_TCR_ITMENA
// Description : Enables the ITM
#define M33_ITM_TCR_ITMENA_RESET  _u(0x0)
#define M33_ITM_TCR_ITMENA_BITS   _u(0x00000001)
#define M33_ITM_TCR_ITMENA_MSB    _u(0)
#define M33_ITM_TCR_ITMENA_LSB    _u(0)
#define M33_ITM_TCR_ITMENA_ACCESS "RW"
// =============================================================================
// Register    : M33_INT_ATREADY
// Description : Integration Mode: Read ATB Ready
#define M33_INT_ATREADY_OFFSET _u(0x00000ef0)
#define M33_INT_ATREADY_BITS   _u(0x00000003)
#define M33_INT_ATREADY_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_INT_ATREADY_AFVALID
// Description : A read of this bit returns the value of AFVALID
#define M33_INT_ATREADY_AFVALID_RESET  _u(0x0)
#define M33_INT_ATREADY_AFVALID_BITS   _u(0x00000002)
#define M33_INT_ATREADY_AFVALID_MSB    _u(1)
#define M33_INT_ATREADY_AFVALID_LSB    _u(1)
#define M33_INT_ATREADY_AFVALID_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_INT_ATREADY_ATREADY
// Description : A read of this bit returns the value of ATREADY
#define M33_INT_ATREADY_ATREADY_RESET  _u(0x0)
#define M33_INT_ATREADY_ATREADY_BITS   _u(0x00000001)
#define M33_INT_ATREADY_ATREADY_MSB    _u(0)
#define M33_INT_ATREADY_ATREADY_LSB    _u(0)
#define M33_INT_ATREADY_ATREADY_ACCESS "RO"
// =============================================================================
// Register    : M33_INT_ATVALID
// Description : Integration Mode: Write ATB Valid
#define M33_INT_ATVALID_OFFSET _u(0x00000ef8)
#define M33_INT_ATVALID_BITS   _u(0x00000003)
#define M33_INT_ATVALID_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_INT_ATVALID_AFREADY
// Description : A write to this bit gives the value of AFREADY
#define M33_INT_ATVALID_AFREADY_RESET  _u(0x0)
#define M33_INT_ATVALID_AFREADY_BITS   _u(0x00000002)
#define M33_INT_ATVALID_AFREADY_MSB    _u(1)
#define M33_INT_ATVALID_AFREADY_LSB    _u(1)
#define M33_INT_ATVALID_AFREADY_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_INT_ATVALID_ATREADY
// Description : A write to this bit gives the value of ATVALID
#define M33_INT_ATVALID_ATREADY_RESET  _u(0x0)
#define M33_INT_ATVALID_ATREADY_BITS   _u(0x00000001)
#define M33_INT_ATVALID_ATREADY_MSB    _u(0)
#define M33_INT_ATVALID_ATREADY_LSB    _u(0)
#define M33_INT_ATVALID_ATREADY_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_ITCTRL
// Description : Integration Mode Control Register
#define M33_ITM_ITCTRL_OFFSET _u(0x00000f00)
#define M33_ITM_ITCTRL_BITS   _u(0x00000001)
#define M33_ITM_ITCTRL_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_ITCTRL_IME
// Description : Integration mode enable bit - The possible values are:  0 - The
//               trace unit is not in integration mode. 1 - The trace unit is in
//               integration mode. This mode enables: A debug agent to perform
//               topology detection. SoC test software to perform integration
//               testing.
#define M33_ITM_ITCTRL_IME_RESET  _u(0x0)
#define M33_ITM_ITCTRL_IME_BITS   _u(0x00000001)
#define M33_ITM_ITCTRL_IME_MSB    _u(0)
#define M33_ITM_ITCTRL_IME_LSB    _u(0)
#define M33_ITM_ITCTRL_IME_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_DEVARCH
// Description : Provides CoreSight discovery information for the ITM
#define M33_ITM_DEVARCH_OFFSET _u(0x00000fbc)
#define M33_ITM_DEVARCH_BITS   _u(0xffffffff)
#define M33_ITM_DEVARCH_RESET  _u(0x47701a01)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_DEVARCH_ARCHITECT
// Description : Defines the architect of the component. Bits [31:28] are the
//               JEP106 continuation code (JEP106 bank ID, minus 1) and bits
//               [27:21] are the JEP106 ID code.
#define M33_ITM_DEVARCH_ARCHITECT_RESET  _u(0x23b)
#define M33_ITM_DEVARCH_ARCHITECT_BITS   _u(0xffe00000)
#define M33_ITM_DEVARCH_ARCHITECT_MSB    _u(31)
#define M33_ITM_DEVARCH_ARCHITECT_LSB    _u(21)
#define M33_ITM_DEVARCH_ARCHITECT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_DEVARCH_PRESENT
// Description : Defines that the DEVARCH register is present
#define M33_ITM_DEVARCH_PRESENT_RESET  _u(0x1)
#define M33_ITM_DEVARCH_PRESENT_BITS   _u(0x00100000)
#define M33_ITM_DEVARCH_PRESENT_MSB    _u(20)
#define M33_ITM_DEVARCH_PRESENT_LSB    _u(20)
#define M33_ITM_DEVARCH_PRESENT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_DEVARCH_REVISION
// Description : Defines the architecture revision of the component
#define M33_ITM_DEVARCH_REVISION_RESET  _u(0x0)
#define M33_ITM_DEVARCH_REVISION_BITS   _u(0x000f0000)
#define M33_ITM_DEVARCH_REVISION_MSB    _u(19)
#define M33_ITM_DEVARCH_REVISION_LSB    _u(16)
#define M33_ITM_DEVARCH_REVISION_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_DEVARCH_ARCHVER
// Description : Defines the architecture version of the component
#define M33_ITM_DEVARCH_ARCHVER_RESET  _u(0x1)
#define M33_ITM_DEVARCH_ARCHVER_BITS   _u(0x0000f000)
#define M33_ITM_DEVARCH_ARCHVER_MSB    _u(15)
#define M33_ITM_DEVARCH_ARCHVER_LSB    _u(12)
#define M33_ITM_DEVARCH_ARCHVER_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_DEVARCH_ARCHPART
// Description : Defines the architecture of the component
#define M33_ITM_DEVARCH_ARCHPART_RESET  _u(0xa01)
#define M33_ITM_DEVARCH_ARCHPART_BITS   _u(0x00000fff)
#define M33_ITM_DEVARCH_ARCHPART_MSB    _u(11)
#define M33_ITM_DEVARCH_ARCHPART_LSB    _u(0)
#define M33_ITM_DEVARCH_ARCHPART_ACCESS "RO"
// =============================================================================
// Register    : M33_ITM_DEVTYPE
// Description : Provides CoreSight discovery information for the ITM
#define M33_ITM_DEVTYPE_OFFSET _u(0x00000fcc)
#define M33_ITM_DEVTYPE_BITS   _u(0x000000ff)
#define M33_ITM_DEVTYPE_RESET  _u(0x00000043)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_DEVTYPE_SUB
// Description : Component sub-type
#define M33_ITM_DEVTYPE_SUB_RESET  _u(0x4)
#define M33_ITM_DEVTYPE_SUB_BITS   _u(0x000000f0)
#define M33_ITM_DEVTYPE_SUB_MSB    _u(7)
#define M33_ITM_DEVTYPE_SUB_LSB    _u(4)
#define M33_ITM_DEVTYPE_SUB_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_DEVTYPE_MAJOR
// Description : Component major type
#define M33_ITM_DEVTYPE_MAJOR_RESET  _u(0x3)
#define M33_ITM_DEVTYPE_MAJOR_BITS   _u(0x0000000f)
#define M33_ITM_DEVTYPE_MAJOR_MSB    _u(3)
#define M33_ITM_DEVTYPE_MAJOR_LSB    _u(0)
#define M33_ITM_DEVTYPE_MAJOR_ACCESS "RO"
// =============================================================================
// Register    : M33_ITM_PIDR4
// Description : Provides CoreSight discovery information for the ITM
#define M33_ITM_PIDR4_OFFSET _u(0x00000fd0)
#define M33_ITM_PIDR4_BITS   _u(0x000000ff)
#define M33_ITM_PIDR4_RESET  _u(0x00000004)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_PIDR4_SIZE
// Description : See CoreSight Architecture Specification
#define M33_ITM_PIDR4_SIZE_RESET  _u(0x0)
#define M33_ITM_PIDR4_SIZE_BITS   _u(0x000000f0)
#define M33_ITM_PIDR4_SIZE_MSB    _u(7)
#define M33_ITM_PIDR4_SIZE_LSB    _u(4)
#define M33_ITM_PIDR4_SIZE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_PIDR4_DES_2
// Description : See CoreSight Architecture Specification
#define M33_ITM_PIDR4_DES_2_RESET  _u(0x4)
#define M33_ITM_PIDR4_DES_2_BITS   _u(0x0000000f)
#define M33_ITM_PIDR4_DES_2_MSB    _u(3)
#define M33_ITM_PIDR4_DES_2_LSB    _u(0)
#define M33_ITM_PIDR4_DES_2_ACCESS "RO"
// =============================================================================
// Register    : M33_ITM_PIDR5
// Description : Provides CoreSight discovery information for the ITM
#define M33_ITM_PIDR5_OFFSET _u(0x00000fd4)
#define M33_ITM_PIDR5_BITS   _u(0x00000000)
#define M33_ITM_PIDR5_RESET  _u(0x00000000)
#define M33_ITM_PIDR5_MSB    _u(31)
#define M33_ITM_PIDR5_LSB    _u(0)
#define M33_ITM_PIDR5_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_PIDR6
// Description : Provides CoreSight discovery information for the ITM
#define M33_ITM_PIDR6_OFFSET _u(0x00000fd8)
#define M33_ITM_PIDR6_BITS   _u(0x00000000)
#define M33_ITM_PIDR6_RESET  _u(0x00000000)
#define M33_ITM_PIDR6_MSB    _u(31)
#define M33_ITM_PIDR6_LSB    _u(0)
#define M33_ITM_PIDR6_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_PIDR7
// Description : Provides CoreSight discovery information for the ITM
#define M33_ITM_PIDR7_OFFSET _u(0x00000fdc)
#define M33_ITM_PIDR7_BITS   _u(0x00000000)
#define M33_ITM_PIDR7_RESET  _u(0x00000000)
#define M33_ITM_PIDR7_MSB    _u(31)
#define M33_ITM_PIDR7_LSB    _u(0)
#define M33_ITM_PIDR7_ACCESS "RW"
// =============================================================================
// Register    : M33_ITM_PIDR0
// Description : Provides CoreSight discovery information for the ITM
#define M33_ITM_PIDR0_OFFSET _u(0x00000fe0)
#define M33_ITM_PIDR0_BITS   _u(0x000000ff)
#define M33_ITM_PIDR0_RESET  _u(0x00000021)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_PIDR0_PART_0
// Description : See CoreSight Architecture Specification
#define M33_ITM_PIDR0_PART_0_RESET  _u(0x21)
#define M33_ITM_PIDR0_PART_0_BITS   _u(0x000000ff)
#define M33_ITM_PIDR0_PART_0_MSB    _u(7)
#define M33_ITM_PIDR0_PART_0_LSB    _u(0)
#define M33_ITM_PIDR0_PART_0_ACCESS "RO"
// =============================================================================
// Register    : M33_ITM_PIDR1
// Description : Provides CoreSight discovery information for the ITM
#define M33_ITM_PIDR1_OFFSET _u(0x00000fe4)
#define M33_ITM_PIDR1_BITS   _u(0x000000ff)
#define M33_ITM_PIDR1_RESET  _u(0x000000bd)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_PIDR1_DES_0
// Description : See CoreSight Architecture Specification
#define M33_ITM_PIDR1_DES_0_RESET  _u(0xb)
#define M33_ITM_PIDR1_DES_0_BITS   _u(0x000000f0)
#define M33_ITM_PIDR1_DES_0_MSB    _u(7)
#define M33_ITM_PIDR1_DES_0_LSB    _u(4)
#define M33_ITM_PIDR1_DES_0_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_PIDR1_PART_1
// Description : See CoreSight Architecture Specification
#define M33_ITM_PIDR1_PART_1_RESET  _u(0xd)
#define M33_ITM_PIDR1_PART_1_BITS   _u(0x0000000f)
#define M33_ITM_PIDR1_PART_1_MSB    _u(3)
#define M33_ITM_PIDR1_PART_1_LSB    _u(0)
#define M33_ITM_PIDR1_PART_1_ACCESS "RO"
// =============================================================================
// Register    : M33_ITM_PIDR2
// Description : Provides CoreSight discovery information for the ITM
#define M33_ITM_PIDR2_OFFSET _u(0x00000fe8)
#define M33_ITM_PIDR2_BITS   _u(0x000000ff)
#define M33_ITM_PIDR2_RESET  _u(0x0000000b)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_PIDR2_REVISION
// Description : See CoreSight Architecture Specification
#define M33_ITM_PIDR2_REVISION_RESET  _u(0x0)
#define M33_ITM_PIDR2_REVISION_BITS   _u(0x000000f0)
#define M33_ITM_PIDR2_REVISION_MSB    _u(7)
#define M33_ITM_PIDR2_REVISION_LSB    _u(4)
#define M33_ITM_PIDR2_REVISION_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_PIDR2_JEDEC
// Description : See CoreSight Architecture Specification
#define M33_ITM_PIDR2_JEDEC_RESET  _u(0x1)
#define M33_ITM_PIDR2_JEDEC_BITS   _u(0x00000008)
#define M33_ITM_PIDR2_JEDEC_MSB    _u(3)
#define M33_ITM_PIDR2_JEDEC_LSB    _u(3)
#define M33_ITM_PIDR2_JEDEC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_PIDR2_DES_1
// Description : See CoreSight Architecture Specification
#define M33_ITM_PIDR2_DES_1_RESET  _u(0x3)
#define M33_ITM_PIDR2_DES_1_BITS   _u(0x00000007)
#define M33_ITM_PIDR2_DES_1_MSB    _u(2)
#define M33_ITM_PIDR2_DES_1_LSB    _u(0)
#define M33_ITM_PIDR2_DES_1_ACCESS "RO"
// =============================================================================
// Register    : M33_ITM_PIDR3
// Description : Provides CoreSight discovery information for the ITM
#define M33_ITM_PIDR3_OFFSET _u(0x00000fec)
#define M33_ITM_PIDR3_BITS   _u(0x000000ff)
#define M33_ITM_PIDR3_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_PIDR3_REVAND
// Description : See CoreSight Architecture Specification
#define M33_ITM_PIDR3_REVAND_RESET  _u(0x0)
#define M33_ITM_PIDR3_REVAND_BITS   _u(0x000000f0)
#define M33_ITM_PIDR3_REVAND_MSB    _u(7)
#define M33_ITM_PIDR3_REVAND_LSB    _u(4)
#define M33_ITM_PIDR3_REVAND_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_PIDR3_CMOD
// Description : See CoreSight Architecture Specification
#define M33_ITM_PIDR3_CMOD_RESET  _u(0x0)
#define M33_ITM_PIDR3_CMOD_BITS   _u(0x0000000f)
#define M33_ITM_PIDR3_CMOD_MSB    _u(3)
#define M33_ITM_PIDR3_CMOD_LSB    _u(0)
#define M33_ITM_PIDR3_CMOD_ACCESS "RO"
// =============================================================================
// Register    : M33_ITM_CIDR0
// Description : Provides CoreSight discovery information for the ITM
#define M33_ITM_CIDR0_OFFSET _u(0x00000ff0)
#define M33_ITM_CIDR0_BITS   _u(0x000000ff)
#define M33_ITM_CIDR0_RESET  _u(0x0000000d)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_CIDR0_PRMBL_0
// Description : See CoreSight Architecture Specification
#define M33_ITM_CIDR0_PRMBL_0_RESET  _u(0x0d)
#define M33_ITM_CIDR0_PRMBL_0_BITS   _u(0x000000ff)
#define M33_ITM_CIDR0_PRMBL_0_MSB    _u(7)
#define M33_ITM_CIDR0_PRMBL_0_LSB    _u(0)
#define M33_ITM_CIDR0_PRMBL_0_ACCESS "RO"
// =============================================================================
// Register    : M33_ITM_CIDR1
// Description : Provides CoreSight discovery information for the ITM
#define M33_ITM_CIDR1_OFFSET _u(0x00000ff4)
#define M33_ITM_CIDR1_BITS   _u(0x000000ff)
#define M33_ITM_CIDR1_RESET  _u(0x00000090)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_CIDR1_CLASS
// Description : See CoreSight Architecture Specification
#define M33_ITM_CIDR1_CLASS_RESET  _u(0x9)
#define M33_ITM_CIDR1_CLASS_BITS   _u(0x000000f0)
#define M33_ITM_CIDR1_CLASS_MSB    _u(7)
#define M33_ITM_CIDR1_CLASS_LSB    _u(4)
#define M33_ITM_CIDR1_CLASS_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ITM_CIDR1_PRMBL_1
// Description : See CoreSight Architecture Specification
#define M33_ITM_CIDR1_PRMBL_1_RESET  _u(0x0)
#define M33_ITM_CIDR1_PRMBL_1_BITS   _u(0x0000000f)
#define M33_ITM_CIDR1_PRMBL_1_MSB    _u(3)
#define M33_ITM_CIDR1_PRMBL_1_LSB    _u(0)
#define M33_ITM_CIDR1_PRMBL_1_ACCESS "RO"
// =============================================================================
// Register    : M33_ITM_CIDR2
// Description : Provides CoreSight discovery information for the ITM
#define M33_ITM_CIDR2_OFFSET _u(0x00000ff8)
#define M33_ITM_CIDR2_BITS   _u(0x000000ff)
#define M33_ITM_CIDR2_RESET  _u(0x00000005)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_CIDR2_PRMBL_2
// Description : See CoreSight Architecture Specification
#define M33_ITM_CIDR2_PRMBL_2_RESET  _u(0x05)
#define M33_ITM_CIDR2_PRMBL_2_BITS   _u(0x000000ff)
#define M33_ITM_CIDR2_PRMBL_2_MSB    _u(7)
#define M33_ITM_CIDR2_PRMBL_2_LSB    _u(0)
#define M33_ITM_CIDR2_PRMBL_2_ACCESS "RO"
// =============================================================================
// Register    : M33_ITM_CIDR3
// Description : Provides CoreSight discovery information for the ITM
#define M33_ITM_CIDR3_OFFSET _u(0x00000ffc)
#define M33_ITM_CIDR3_BITS   _u(0x000000ff)
#define M33_ITM_CIDR3_RESET  _u(0x000000b1)
// -----------------------------------------------------------------------------
// Field       : M33_ITM_CIDR3_PRMBL_3
// Description : See CoreSight Architecture Specification
#define M33_ITM_CIDR3_PRMBL_3_RESET  _u(0xb1)
#define M33_ITM_CIDR3_PRMBL_3_BITS   _u(0x000000ff)
#define M33_ITM_CIDR3_PRMBL_3_MSB    _u(7)
#define M33_ITM_CIDR3_PRMBL_3_LSB    _u(0)
#define M33_ITM_CIDR3_PRMBL_3_ACCESS "RO"
// =============================================================================
// Register    : M33_DWT_CTRL
// Description : Provides configuration and status information for the DWT unit,
//               and used to control features of the unit
#define M33_DWT_CTRL_OFFSET _u(0x00001000)
#define M33_DWT_CTRL_BITS   _u(0xffff1fff)
#define M33_DWT_CTRL_RESET  _u(0x73741824)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_NUMCOMP
// Description : Number of DWT comparators implemented
#define M33_DWT_CTRL_NUMCOMP_RESET  _u(0x7)
#define M33_DWT_CTRL_NUMCOMP_BITS   _u(0xf0000000)
#define M33_DWT_CTRL_NUMCOMP_MSB    _u(31)
#define M33_DWT_CTRL_NUMCOMP_LSB    _u(28)
#define M33_DWT_CTRL_NUMCOMP_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_NOTRCPKT
// Description : Indicates whether the implementation does not support trace
#define M33_DWT_CTRL_NOTRCPKT_RESET  _u(0x0)
#define M33_DWT_CTRL_NOTRCPKT_BITS   _u(0x08000000)
#define M33_DWT_CTRL_NOTRCPKT_MSB    _u(27)
#define M33_DWT_CTRL_NOTRCPKT_LSB    _u(27)
#define M33_DWT_CTRL_NOTRCPKT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_NOEXTTRIG
// Description : Reserved, RAZ
#define M33_DWT_CTRL_NOEXTTRIG_RESET  _u(0x0)
#define M33_DWT_CTRL_NOEXTTRIG_BITS   _u(0x04000000)
#define M33_DWT_CTRL_NOEXTTRIG_MSB    _u(26)
#define M33_DWT_CTRL_NOEXTTRIG_LSB    _u(26)
#define M33_DWT_CTRL_NOEXTTRIG_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_NOCYCCNT
// Description : Indicates whether the implementation does not include a cycle
//               counter
#define M33_DWT_CTRL_NOCYCCNT_RESET  _u(0x1)
#define M33_DWT_CTRL_NOCYCCNT_BITS   _u(0x02000000)
#define M33_DWT_CTRL_NOCYCCNT_MSB    _u(25)
#define M33_DWT_CTRL_NOCYCCNT_LSB    _u(25)
#define M33_DWT_CTRL_NOCYCCNT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_NOPRFCNT
// Description : Indicates whether the implementation does not include the
//               profiling counters
#define M33_DWT_CTRL_NOPRFCNT_RESET  _u(0x1)
#define M33_DWT_CTRL_NOPRFCNT_BITS   _u(0x01000000)
#define M33_DWT_CTRL_NOPRFCNT_MSB    _u(24)
#define M33_DWT_CTRL_NOPRFCNT_LSB    _u(24)
#define M33_DWT_CTRL_NOPRFCNT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_CYCDISS
// Description : Controls whether the cycle counter is disabled in Secure state
#define M33_DWT_CTRL_CYCDISS_RESET  _u(0x0)
#define M33_DWT_CTRL_CYCDISS_BITS   _u(0x00800000)
#define M33_DWT_CTRL_CYCDISS_MSB    _u(23)
#define M33_DWT_CTRL_CYCDISS_LSB    _u(23)
#define M33_DWT_CTRL_CYCDISS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_CYCEVTENA
// Description : Enables Event Counter packet generation on POSTCNT underflow
#define M33_DWT_CTRL_CYCEVTENA_RESET  _u(0x1)
#define M33_DWT_CTRL_CYCEVTENA_BITS   _u(0x00400000)
#define M33_DWT_CTRL_CYCEVTENA_MSB    _u(22)
#define M33_DWT_CTRL_CYCEVTENA_LSB    _u(22)
#define M33_DWT_CTRL_CYCEVTENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_FOLDEVTENA
// Description : Enables DWT_FOLDCNT counter
#define M33_DWT_CTRL_FOLDEVTENA_RESET  _u(0x1)
#define M33_DWT_CTRL_FOLDEVTENA_BITS   _u(0x00200000)
#define M33_DWT_CTRL_FOLDEVTENA_MSB    _u(21)
#define M33_DWT_CTRL_FOLDEVTENA_LSB    _u(21)
#define M33_DWT_CTRL_FOLDEVTENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_LSUEVTENA
// Description : Enables DWT_LSUCNT counter
#define M33_DWT_CTRL_LSUEVTENA_RESET  _u(0x1)
#define M33_DWT_CTRL_LSUEVTENA_BITS   _u(0x00100000)
#define M33_DWT_CTRL_LSUEVTENA_MSB    _u(20)
#define M33_DWT_CTRL_LSUEVTENA_LSB    _u(20)
#define M33_DWT_CTRL_LSUEVTENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_SLEEPEVTENA
// Description : Enable DWT_SLEEPCNT counter
#define M33_DWT_CTRL_SLEEPEVTENA_RESET  _u(0x0)
#define M33_DWT_CTRL_SLEEPEVTENA_BITS   _u(0x00080000)
#define M33_DWT_CTRL_SLEEPEVTENA_MSB    _u(19)
#define M33_DWT_CTRL_SLEEPEVTENA_LSB    _u(19)
#define M33_DWT_CTRL_SLEEPEVTENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_EXCEVTENA
// Description : Enables DWT_EXCCNT counter
#define M33_DWT_CTRL_EXCEVTENA_RESET  _u(0x1)
#define M33_DWT_CTRL_EXCEVTENA_BITS   _u(0x00040000)
#define M33_DWT_CTRL_EXCEVTENA_MSB    _u(18)
#define M33_DWT_CTRL_EXCEVTENA_LSB    _u(18)
#define M33_DWT_CTRL_EXCEVTENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_CPIEVTENA
// Description : Enables DWT_CPICNT counter
#define M33_DWT_CTRL_CPIEVTENA_RESET  _u(0x0)
#define M33_DWT_CTRL_CPIEVTENA_BITS   _u(0x00020000)
#define M33_DWT_CTRL_CPIEVTENA_MSB    _u(17)
#define M33_DWT_CTRL_CPIEVTENA_LSB    _u(17)
#define M33_DWT_CTRL_CPIEVTENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_EXTTRCENA
// Description : Enables generation of Exception Trace packets
#define M33_DWT_CTRL_EXTTRCENA_RESET  _u(0x0)
#define M33_DWT_CTRL_EXTTRCENA_BITS   _u(0x00010000)
#define M33_DWT_CTRL_EXTTRCENA_MSB    _u(16)
#define M33_DWT_CTRL_EXTTRCENA_LSB    _u(16)
#define M33_DWT_CTRL_EXTTRCENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_PCSAMPLENA
// Description : Enables use of POSTCNT counter as a timer for Periodic PC
//               Sample packet generation
#define M33_DWT_CTRL_PCSAMPLENA_RESET  _u(0x1)
#define M33_DWT_CTRL_PCSAMPLENA_BITS   _u(0x00001000)
#define M33_DWT_CTRL_PCSAMPLENA_MSB    _u(12)
#define M33_DWT_CTRL_PCSAMPLENA_LSB    _u(12)
#define M33_DWT_CTRL_PCSAMPLENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_SYNCTAP
// Description : Selects the position of the synchronization packet counter tap
//               on the CYCCNT counter. This determines the Synchronization
//               packet rate
#define M33_DWT_CTRL_SYNCTAP_RESET  _u(0x2)
#define M33_DWT_CTRL_SYNCTAP_BITS   _u(0x00000c00)
#define M33_DWT_CTRL_SYNCTAP_MSB    _u(11)
#define M33_DWT_CTRL_SYNCTAP_LSB    _u(10)
#define M33_DWT_CTRL_SYNCTAP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_CYCTAP
// Description : Selects the position of the POSTCNT tap on the CYCCNT counter
#define M33_DWT_CTRL_CYCTAP_RESET  _u(0x0)
#define M33_DWT_CTRL_CYCTAP_BITS   _u(0x00000200)
#define M33_DWT_CTRL_CYCTAP_MSB    _u(9)
#define M33_DWT_CTRL_CYCTAP_LSB    _u(9)
#define M33_DWT_CTRL_CYCTAP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_POSTINIT
// Description : Initial value for the POSTCNT counter
#define M33_DWT_CTRL_POSTINIT_RESET  _u(0x1)
#define M33_DWT_CTRL_POSTINIT_BITS   _u(0x000001e0)
#define M33_DWT_CTRL_POSTINIT_MSB    _u(8)
#define M33_DWT_CTRL_POSTINIT_LSB    _u(5)
#define M33_DWT_CTRL_POSTINIT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_POSTPRESET
// Description : Reload value for the POSTCNT counter
#define M33_DWT_CTRL_POSTPRESET_RESET  _u(0x2)
#define M33_DWT_CTRL_POSTPRESET_BITS   _u(0x0000001e)
#define M33_DWT_CTRL_POSTPRESET_MSB    _u(4)
#define M33_DWT_CTRL_POSTPRESET_LSB    _u(1)
#define M33_DWT_CTRL_POSTPRESET_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CTRL_CYCCNTENA
// Description : Enables CYCCNT
#define M33_DWT_CTRL_CYCCNTENA_RESET  _u(0x0)
#define M33_DWT_CTRL_CYCCNTENA_BITS   _u(0x00000001)
#define M33_DWT_CTRL_CYCCNTENA_MSB    _u(0)
#define M33_DWT_CTRL_CYCCNTENA_LSB    _u(0)
#define M33_DWT_CTRL_CYCCNTENA_ACCESS "RW"
// =============================================================================
// Register    : M33_DWT_CYCCNT
// Description : Shows or sets the value of the processor cycle counter, CYCCNT
#define M33_DWT_CYCCNT_OFFSET _u(0x00001004)
#define M33_DWT_CYCCNT_BITS   _u(0xffffffff)
#define M33_DWT_CYCCNT_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CYCCNT_CYCCNT
// Description : Increments one on each processor clock cycle when
//               DWT_CTRL.CYCCNTENA == 1 and DEMCR.TRCENA == 1. On overflow,
//               CYCCNT wraps to zero
#define M33_DWT_CYCCNT_CYCCNT_RESET  _u(0x00000000)
#define M33_DWT_CYCCNT_CYCCNT_BITS   _u(0xffffffff)
#define M33_DWT_CYCCNT_CYCCNT_MSB    _u(31)
#define M33_DWT_CYCCNT_CYCCNT_LSB    _u(0)
#define M33_DWT_CYCCNT_CYCCNT_ACCESS "RW"
// =============================================================================
// Register    : M33_DWT_EXCCNT
// Description : Counts the total cycles spent in exception processing
#define M33_DWT_EXCCNT_OFFSET _u(0x0000100c)
#define M33_DWT_EXCCNT_BITS   _u(0x000000ff)
#define M33_DWT_EXCCNT_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_EXCCNT_EXCCNT
// Description : Counts one on each cycle when all of the following are true: -
//               DWT_CTRL.EXCEVTENA == 1 and DEMCR.TRCENA == 1. - No instruction
//               is executed, see DWT_CPICNT. - An exception-entry or exception-
//               exit related operation is in progress. - Either
//               SecureNoninvasiveDebugAllowed() == TRUE, or NS-Req for the
//               operation is set to Non-secure and NoninvasiveDebugAllowed() ==
//               TRUE.
#define M33_DWT_EXCCNT_EXCCNT_RESET  _u(0x00)
#define M33_DWT_EXCCNT_EXCCNT_BITS   _u(0x000000ff)
#define M33_DWT_EXCCNT_EXCCNT_MSB    _u(7)
#define M33_DWT_EXCCNT_EXCCNT_LSB    _u(0)
#define M33_DWT_EXCCNT_EXCCNT_ACCESS "RW"
// =============================================================================
// Register    : M33_DWT_LSUCNT
// Description : Increments on the additional cycles required to execute all
//               load or store instructions
#define M33_DWT_LSUCNT_OFFSET _u(0x00001014)
#define M33_DWT_LSUCNT_BITS   _u(0x000000ff)
#define M33_DWT_LSUCNT_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_LSUCNT_LSUCNT
// Description : Counts one on each cycle when all of the following are true: -
//               DWT_CTRL.LSUEVTENA == 1 and DEMCR.TRCENA == 1. - No instruction
//               is executed, see DWT_CPICNT. - No exception-entry or exception-
//               exit operation is in progress, see DWT_EXCCNT. - A load-store
//               operation is in progress. - Either
//               SecureNoninvasiveDebugAllowed() == TRUE, or NS-Req for the
//               operation is set to Non-secure and NoninvasiveDebugAllowed() ==
//               TRUE.
#define M33_DWT_LSUCNT_LSUCNT_RESET  _u(0x00)
#define M33_DWT_LSUCNT_LSUCNT_BITS   _u(0x000000ff)
#define M33_DWT_LSUCNT_LSUCNT_MSB    _u(7)
#define M33_DWT_LSUCNT_LSUCNT_LSB    _u(0)
#define M33_DWT_LSUCNT_LSUCNT_ACCESS "RW"
// =============================================================================
// Register    : M33_DWT_FOLDCNT
// Description : Increments on the additional cycles required to execute all
//               load or store instructions
#define M33_DWT_FOLDCNT_OFFSET _u(0x00001018)
#define M33_DWT_FOLDCNT_BITS   _u(0x000000ff)
#define M33_DWT_FOLDCNT_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FOLDCNT_FOLDCNT
// Description : Counts on each cycle when all of the following are true: -
//               DWT_CTRL.FOLDEVTENA == 1 and DEMCR.TRCENA == 1. - At least two
//               instructions are executed, see DWT_CPICNT. - Either
//               SecureNoninvasiveDebugAllowed() == TRUE, or the PE is in Non-
//               secure state and NoninvasiveDebugAllowed() == TRUE. The counter
//               is incremented by the number of instructions executed, minus
//               one
#define M33_DWT_FOLDCNT_FOLDCNT_RESET  _u(0x00)
#define M33_DWT_FOLDCNT_FOLDCNT_BITS   _u(0x000000ff)
#define M33_DWT_FOLDCNT_FOLDCNT_MSB    _u(7)
#define M33_DWT_FOLDCNT_FOLDCNT_LSB    _u(0)
#define M33_DWT_FOLDCNT_FOLDCNT_ACCESS "RW"
// =============================================================================
// Register    : M33_DWT_COMP0
// Description : Provides a reference value for use by watchpoint comparator 0
#define M33_DWT_COMP0_OFFSET _u(0x00001020)
#define M33_DWT_COMP0_BITS   _u(0xffffffff)
#define M33_DWT_COMP0_RESET  _u(0x00000000)
#define M33_DWT_COMP0_MSB    _u(31)
#define M33_DWT_COMP0_LSB    _u(0)
#define M33_DWT_COMP0_ACCESS "RW"
// =============================================================================
// Register    : M33_DWT_FUNCTION0
// Description : Controls the operation of watchpoint comparator 0
#define M33_DWT_FUNCTION0_OFFSET _u(0x00001028)
#define M33_DWT_FUNCTION0_BITS   _u(0xf9000c3f)
#define M33_DWT_FUNCTION0_RESET  _u(0x58000000)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION0_ID
// Description : Identifies the capabilities for MATCH for comparator *n
#define M33_DWT_FUNCTION0_ID_RESET  _u(0x0b)
#define M33_DWT_FUNCTION0_ID_BITS   _u(0xf8000000)
#define M33_DWT_FUNCTION0_ID_MSB    _u(31)
#define M33_DWT_FUNCTION0_ID_LSB    _u(27)
#define M33_DWT_FUNCTION0_ID_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION0_MATCHED
// Description : Set to 1 when the comparator matches
#define M33_DWT_FUNCTION0_MATCHED_RESET  _u(0x0)
#define M33_DWT_FUNCTION0_MATCHED_BITS   _u(0x01000000)
#define M33_DWT_FUNCTION0_MATCHED_MSB    _u(24)
#define M33_DWT_FUNCTION0_MATCHED_LSB    _u(24)
#define M33_DWT_FUNCTION0_MATCHED_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION0_DATAVSIZE
// Description : Defines the size of the object being watched for by Data Value
//               and Data Address comparators
#define M33_DWT_FUNCTION0_DATAVSIZE_RESET  _u(0x0)
#define M33_DWT_FUNCTION0_DATAVSIZE_BITS   _u(0x00000c00)
#define M33_DWT_FUNCTION0_DATAVSIZE_MSB    _u(11)
#define M33_DWT_FUNCTION0_DATAVSIZE_LSB    _u(10)
#define M33_DWT_FUNCTION0_DATAVSIZE_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION0_ACTION
// Description : Defines the action on a match. This field is ignored and the
//               comparator generates no actions if it is disabled by MATCH
#define M33_DWT_FUNCTION0_ACTION_RESET  _u(0x0)
#define M33_DWT_FUNCTION0_ACTION_BITS   _u(0x00000030)
#define M33_DWT_FUNCTION0_ACTION_MSB    _u(5)
#define M33_DWT_FUNCTION0_ACTION_LSB    _u(4)
#define M33_DWT_FUNCTION0_ACTION_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION0_MATCH
// Description : Controls the type of match generated by this comparator
#define M33_DWT_FUNCTION0_MATCH_RESET  _u(0x0)
#define M33_DWT_FUNCTION0_MATCH_BITS   _u(0x0000000f)
#define M33_DWT_FUNCTION0_MATCH_MSB    _u(3)
#define M33_DWT_FUNCTION0_MATCH_LSB    _u(0)
#define M33_DWT_FUNCTION0_MATCH_ACCESS "RW"
// =============================================================================
// Register    : M33_DWT_COMP1
// Description : Provides a reference value for use by watchpoint comparator 1
#define M33_DWT_COMP1_OFFSET _u(0x00001030)
#define M33_DWT_COMP1_BITS   _u(0xffffffff)
#define M33_DWT_COMP1_RESET  _u(0x00000000)
#define M33_DWT_COMP1_MSB    _u(31)
#define M33_DWT_COMP1_LSB    _u(0)
#define M33_DWT_COMP1_ACCESS "RW"
// =============================================================================
// Register    : M33_DWT_FUNCTION1
// Description : Controls the operation of watchpoint comparator 1
#define M33_DWT_FUNCTION1_OFFSET _u(0x00001038)
#define M33_DWT_FUNCTION1_BITS   _u(0xf9000c3f)
#define M33_DWT_FUNCTION1_RESET  _u(0x89000828)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION1_ID
// Description : Identifies the capabilities for MATCH for comparator *n
#define M33_DWT_FUNCTION1_ID_RESET  _u(0x11)
#define M33_DWT_FUNCTION1_ID_BITS   _u(0xf8000000)
#define M33_DWT_FUNCTION1_ID_MSB    _u(31)
#define M33_DWT_FUNCTION1_ID_LSB    _u(27)
#define M33_DWT_FUNCTION1_ID_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION1_MATCHED
// Description : Set to 1 when the comparator matches
#define M33_DWT_FUNCTION1_MATCHED_RESET  _u(0x1)
#define M33_DWT_FUNCTION1_MATCHED_BITS   _u(0x01000000)
#define M33_DWT_FUNCTION1_MATCHED_MSB    _u(24)
#define M33_DWT_FUNCTION1_MATCHED_LSB    _u(24)
#define M33_DWT_FUNCTION1_MATCHED_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION1_DATAVSIZE
// Description : Defines the size of the object being watched for by Data Value
//               and Data Address comparators
#define M33_DWT_FUNCTION1_DATAVSIZE_RESET  _u(0x2)
#define M33_DWT_FUNCTION1_DATAVSIZE_BITS   _u(0x00000c00)
#define M33_DWT_FUNCTION1_DATAVSIZE_MSB    _u(11)
#define M33_DWT_FUNCTION1_DATAVSIZE_LSB    _u(10)
#define M33_DWT_FUNCTION1_DATAVSIZE_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION1_ACTION
// Description : Defines the action on a match. This field is ignored and the
//               comparator generates no actions if it is disabled by MATCH
#define M33_DWT_FUNCTION1_ACTION_RESET  _u(0x2)
#define M33_DWT_FUNCTION1_ACTION_BITS   _u(0x00000030)
#define M33_DWT_FUNCTION1_ACTION_MSB    _u(5)
#define M33_DWT_FUNCTION1_ACTION_LSB    _u(4)
#define M33_DWT_FUNCTION1_ACTION_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION1_MATCH
// Description : Controls the type of match generated by this comparator
#define M33_DWT_FUNCTION1_MATCH_RESET  _u(0x8)
#define M33_DWT_FUNCTION1_MATCH_BITS   _u(0x0000000f)
#define M33_DWT_FUNCTION1_MATCH_MSB    _u(3)
#define M33_DWT_FUNCTION1_MATCH_LSB    _u(0)
#define M33_DWT_FUNCTION1_MATCH_ACCESS "RW"
// =============================================================================
// Register    : M33_DWT_COMP2
// Description : Provides a reference value for use by watchpoint comparator 2
#define M33_DWT_COMP2_OFFSET _u(0x00001040)
#define M33_DWT_COMP2_BITS   _u(0xffffffff)
#define M33_DWT_COMP2_RESET  _u(0x00000000)
#define M33_DWT_COMP2_MSB    _u(31)
#define M33_DWT_COMP2_LSB    _u(0)
#define M33_DWT_COMP2_ACCESS "RW"
// =============================================================================
// Register    : M33_DWT_FUNCTION2
// Description : Controls the operation of watchpoint comparator 2
#define M33_DWT_FUNCTION2_OFFSET _u(0x00001048)
#define M33_DWT_FUNCTION2_BITS   _u(0xf9000c3f)
#define M33_DWT_FUNCTION2_RESET  _u(0x50000000)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION2_ID
// Description : Identifies the capabilities for MATCH for comparator *n
#define M33_DWT_FUNCTION2_ID_RESET  _u(0x0a)
#define M33_DWT_FUNCTION2_ID_BITS   _u(0xf8000000)
#define M33_DWT_FUNCTION2_ID_MSB    _u(31)
#define M33_DWT_FUNCTION2_ID_LSB    _u(27)
#define M33_DWT_FUNCTION2_ID_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION2_MATCHED
// Description : Set to 1 when the comparator matches
#define M33_DWT_FUNCTION2_MATCHED_RESET  _u(0x0)
#define M33_DWT_FUNCTION2_MATCHED_BITS   _u(0x01000000)
#define M33_DWT_FUNCTION2_MATCHED_MSB    _u(24)
#define M33_DWT_FUNCTION2_MATCHED_LSB    _u(24)
#define M33_DWT_FUNCTION2_MATCHED_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION2_DATAVSIZE
// Description : Defines the size of the object being watched for by Data Value
//               and Data Address comparators
#define M33_DWT_FUNCTION2_DATAVSIZE_RESET  _u(0x0)
#define M33_DWT_FUNCTION2_DATAVSIZE_BITS   _u(0x00000c00)
#define M33_DWT_FUNCTION2_DATAVSIZE_MSB    _u(11)
#define M33_DWT_FUNCTION2_DATAVSIZE_LSB    _u(10)
#define M33_DWT_FUNCTION2_DATAVSIZE_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION2_ACTION
// Description : Defines the action on a match. This field is ignored and the
//               comparator generates no actions if it is disabled by MATCH
#define M33_DWT_FUNCTION2_ACTION_RESET  _u(0x0)
#define M33_DWT_FUNCTION2_ACTION_BITS   _u(0x00000030)
#define M33_DWT_FUNCTION2_ACTION_MSB    _u(5)
#define M33_DWT_FUNCTION2_ACTION_LSB    _u(4)
#define M33_DWT_FUNCTION2_ACTION_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION2_MATCH
// Description : Controls the type of match generated by this comparator
#define M33_DWT_FUNCTION2_MATCH_RESET  _u(0x0)
#define M33_DWT_FUNCTION2_MATCH_BITS   _u(0x0000000f)
#define M33_DWT_FUNCTION2_MATCH_MSB    _u(3)
#define M33_DWT_FUNCTION2_MATCH_LSB    _u(0)
#define M33_DWT_FUNCTION2_MATCH_ACCESS "RW"
// =============================================================================
// Register    : M33_DWT_COMP3
// Description : Provides a reference value for use by watchpoint comparator 3
#define M33_DWT_COMP3_OFFSET _u(0x00001050)
#define M33_DWT_COMP3_BITS   _u(0xffffffff)
#define M33_DWT_COMP3_RESET  _u(0x00000000)
#define M33_DWT_COMP3_MSB    _u(31)
#define M33_DWT_COMP3_LSB    _u(0)
#define M33_DWT_COMP3_ACCESS "RW"
// =============================================================================
// Register    : M33_DWT_FUNCTION3
// Description : Controls the operation of watchpoint comparator 3
#define M33_DWT_FUNCTION3_OFFSET _u(0x00001058)
#define M33_DWT_FUNCTION3_BITS   _u(0xf9000c3f)
#define M33_DWT_FUNCTION3_RESET  _u(0x20000800)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION3_ID
// Description : Identifies the capabilities for MATCH for comparator *n
#define M33_DWT_FUNCTION3_ID_RESET  _u(0x04)
#define M33_DWT_FUNCTION3_ID_BITS   _u(0xf8000000)
#define M33_DWT_FUNCTION3_ID_MSB    _u(31)
#define M33_DWT_FUNCTION3_ID_LSB    _u(27)
#define M33_DWT_FUNCTION3_ID_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION3_MATCHED
// Description : Set to 1 when the comparator matches
#define M33_DWT_FUNCTION3_MATCHED_RESET  _u(0x0)
#define M33_DWT_FUNCTION3_MATCHED_BITS   _u(0x01000000)
#define M33_DWT_FUNCTION3_MATCHED_MSB    _u(24)
#define M33_DWT_FUNCTION3_MATCHED_LSB    _u(24)
#define M33_DWT_FUNCTION3_MATCHED_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION3_DATAVSIZE
// Description : Defines the size of the object being watched for by Data Value
//               and Data Address comparators
#define M33_DWT_FUNCTION3_DATAVSIZE_RESET  _u(0x2)
#define M33_DWT_FUNCTION3_DATAVSIZE_BITS   _u(0x00000c00)
#define M33_DWT_FUNCTION3_DATAVSIZE_MSB    _u(11)
#define M33_DWT_FUNCTION3_DATAVSIZE_LSB    _u(10)
#define M33_DWT_FUNCTION3_DATAVSIZE_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION3_ACTION
// Description : Defines the action on a match. This field is ignored and the
//               comparator generates no actions if it is disabled by MATCH
#define M33_DWT_FUNCTION3_ACTION_RESET  _u(0x0)
#define M33_DWT_FUNCTION3_ACTION_BITS   _u(0x00000030)
#define M33_DWT_FUNCTION3_ACTION_MSB    _u(5)
#define M33_DWT_FUNCTION3_ACTION_LSB    _u(4)
#define M33_DWT_FUNCTION3_ACTION_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_FUNCTION3_MATCH
// Description : Controls the type of match generated by this comparator
#define M33_DWT_FUNCTION3_MATCH_RESET  _u(0x0)
#define M33_DWT_FUNCTION3_MATCH_BITS   _u(0x0000000f)
#define M33_DWT_FUNCTION3_MATCH_MSB    _u(3)
#define M33_DWT_FUNCTION3_MATCH_LSB    _u(0)
#define M33_DWT_FUNCTION3_MATCH_ACCESS "RW"
// =============================================================================
// Register    : M33_DWT_DEVARCH
// Description : Provides CoreSight discovery information for the DWT
#define M33_DWT_DEVARCH_OFFSET _u(0x00001fbc)
#define M33_DWT_DEVARCH_BITS   _u(0xffffffff)
#define M33_DWT_DEVARCH_RESET  _u(0x47701a02)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_DEVARCH_ARCHITECT
// Description : Defines the architect of the component. Bits [31:28] are the
//               JEP106 continuation code (JEP106 bank ID, minus 1) and bits
//               [27:21] are the JEP106 ID code.
#define M33_DWT_DEVARCH_ARCHITECT_RESET  _u(0x23b)
#define M33_DWT_DEVARCH_ARCHITECT_BITS   _u(0xffe00000)
#define M33_DWT_DEVARCH_ARCHITECT_MSB    _u(31)
#define M33_DWT_DEVARCH_ARCHITECT_LSB    _u(21)
#define M33_DWT_DEVARCH_ARCHITECT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_DEVARCH_PRESENT
// Description : Defines that the DEVARCH register is present
#define M33_DWT_DEVARCH_PRESENT_RESET  _u(0x1)
#define M33_DWT_DEVARCH_PRESENT_BITS   _u(0x00100000)
#define M33_DWT_DEVARCH_PRESENT_MSB    _u(20)
#define M33_DWT_DEVARCH_PRESENT_LSB    _u(20)
#define M33_DWT_DEVARCH_PRESENT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_DEVARCH_REVISION
// Description : Defines the architecture revision of the component
#define M33_DWT_DEVARCH_REVISION_RESET  _u(0x0)
#define M33_DWT_DEVARCH_REVISION_BITS   _u(0x000f0000)
#define M33_DWT_DEVARCH_REVISION_MSB    _u(19)
#define M33_DWT_DEVARCH_REVISION_LSB    _u(16)
#define M33_DWT_DEVARCH_REVISION_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_DEVARCH_ARCHVER
// Description : Defines the architecture version of the component
#define M33_DWT_DEVARCH_ARCHVER_RESET  _u(0x1)
#define M33_DWT_DEVARCH_ARCHVER_BITS   _u(0x0000f000)
#define M33_DWT_DEVARCH_ARCHVER_MSB    _u(15)
#define M33_DWT_DEVARCH_ARCHVER_LSB    _u(12)
#define M33_DWT_DEVARCH_ARCHVER_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_DEVARCH_ARCHPART
// Description : Defines the architecture of the component
#define M33_DWT_DEVARCH_ARCHPART_RESET  _u(0xa02)
#define M33_DWT_DEVARCH_ARCHPART_BITS   _u(0x00000fff)
#define M33_DWT_DEVARCH_ARCHPART_MSB    _u(11)
#define M33_DWT_DEVARCH_ARCHPART_LSB    _u(0)
#define M33_DWT_DEVARCH_ARCHPART_ACCESS "RO"
// =============================================================================
// Register    : M33_DWT_DEVTYPE
// Description : Provides CoreSight discovery information for the DWT
#define M33_DWT_DEVTYPE_OFFSET _u(0x00001fcc)
#define M33_DWT_DEVTYPE_BITS   _u(0x000000ff)
#define M33_DWT_DEVTYPE_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_DEVTYPE_SUB
// Description : Component sub-type
#define M33_DWT_DEVTYPE_SUB_RESET  _u(0x0)
#define M33_DWT_DEVTYPE_SUB_BITS   _u(0x000000f0)
#define M33_DWT_DEVTYPE_SUB_MSB    _u(7)
#define M33_DWT_DEVTYPE_SUB_LSB    _u(4)
#define M33_DWT_DEVTYPE_SUB_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_DEVTYPE_MAJOR
// Description : Component major type
#define M33_DWT_DEVTYPE_MAJOR_RESET  _u(0x0)
#define M33_DWT_DEVTYPE_MAJOR_BITS   _u(0x0000000f)
#define M33_DWT_DEVTYPE_MAJOR_MSB    _u(3)
#define M33_DWT_DEVTYPE_MAJOR_LSB    _u(0)
#define M33_DWT_DEVTYPE_MAJOR_ACCESS "RO"
// =============================================================================
// Register    : M33_DWT_PIDR4
// Description : Provides CoreSight discovery information for the DWT
#define M33_DWT_PIDR4_OFFSET _u(0x00001fd0)
#define M33_DWT_PIDR4_BITS   _u(0x000000ff)
#define M33_DWT_PIDR4_RESET  _u(0x00000004)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_PIDR4_SIZE
// Description : See CoreSight Architecture Specification
#define M33_DWT_PIDR4_SIZE_RESET  _u(0x0)
#define M33_DWT_PIDR4_SIZE_BITS   _u(0x000000f0)
#define M33_DWT_PIDR4_SIZE_MSB    _u(7)
#define M33_DWT_PIDR4_SIZE_LSB    _u(4)
#define M33_DWT_PIDR4_SIZE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_PIDR4_DES_2
// Description : See CoreSight Architecture Specification
#define M33_DWT_PIDR4_DES_2_RESET  _u(0x4)
#define M33_DWT_PIDR4_DES_2_BITS   _u(0x0000000f)
#define M33_DWT_PIDR4_DES_2_MSB    _u(3)
#define M33_DWT_PIDR4_DES_2_LSB    _u(0)
#define M33_DWT_PIDR4_DES_2_ACCESS "RO"
// =============================================================================
// Register    : M33_DWT_PIDR5
// Description : Provides CoreSight discovery information for the DWT
#define M33_DWT_PIDR5_OFFSET _u(0x00001fd4)
#define M33_DWT_PIDR5_BITS   _u(0x00000000)
#define M33_DWT_PIDR5_RESET  _u(0x00000000)
#define M33_DWT_PIDR5_MSB    _u(31)
#define M33_DWT_PIDR5_LSB    _u(0)
#define M33_DWT_PIDR5_ACCESS "RW"
// =============================================================================
// Register    : M33_DWT_PIDR6
// Description : Provides CoreSight discovery information for the DWT
#define M33_DWT_PIDR6_OFFSET _u(0x00001fd8)
#define M33_DWT_PIDR6_BITS   _u(0x00000000)
#define M33_DWT_PIDR6_RESET  _u(0x00000000)
#define M33_DWT_PIDR6_MSB    _u(31)
#define M33_DWT_PIDR6_LSB    _u(0)
#define M33_DWT_PIDR6_ACCESS "RW"
// =============================================================================
// Register    : M33_DWT_PIDR7
// Description : Provides CoreSight discovery information for the DWT
#define M33_DWT_PIDR7_OFFSET _u(0x00001fdc)
#define M33_DWT_PIDR7_BITS   _u(0x00000000)
#define M33_DWT_PIDR7_RESET  _u(0x00000000)
#define M33_DWT_PIDR7_MSB    _u(31)
#define M33_DWT_PIDR7_LSB    _u(0)
#define M33_DWT_PIDR7_ACCESS "RW"
// =============================================================================
// Register    : M33_DWT_PIDR0
// Description : Provides CoreSight discovery information for the DWT
#define M33_DWT_PIDR0_OFFSET _u(0x00001fe0)
#define M33_DWT_PIDR0_BITS   _u(0x000000ff)
#define M33_DWT_PIDR0_RESET  _u(0x00000021)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_PIDR0_PART_0
// Description : See CoreSight Architecture Specification
#define M33_DWT_PIDR0_PART_0_RESET  _u(0x21)
#define M33_DWT_PIDR0_PART_0_BITS   _u(0x000000ff)
#define M33_DWT_PIDR0_PART_0_MSB    _u(7)
#define M33_DWT_PIDR0_PART_0_LSB    _u(0)
#define M33_DWT_PIDR0_PART_0_ACCESS "RO"
// =============================================================================
// Register    : M33_DWT_PIDR1
// Description : Provides CoreSight discovery information for the DWT
#define M33_DWT_PIDR1_OFFSET _u(0x00001fe4)
#define M33_DWT_PIDR1_BITS   _u(0x000000ff)
#define M33_DWT_PIDR1_RESET  _u(0x000000bd)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_PIDR1_DES_0
// Description : See CoreSight Architecture Specification
#define M33_DWT_PIDR1_DES_0_RESET  _u(0xb)
#define M33_DWT_PIDR1_DES_0_BITS   _u(0x000000f0)
#define M33_DWT_PIDR1_DES_0_MSB    _u(7)
#define M33_DWT_PIDR1_DES_0_LSB    _u(4)
#define M33_DWT_PIDR1_DES_0_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_PIDR1_PART_1
// Description : See CoreSight Architecture Specification
#define M33_DWT_PIDR1_PART_1_RESET  _u(0xd)
#define M33_DWT_PIDR1_PART_1_BITS   _u(0x0000000f)
#define M33_DWT_PIDR1_PART_1_MSB    _u(3)
#define M33_DWT_PIDR1_PART_1_LSB    _u(0)
#define M33_DWT_PIDR1_PART_1_ACCESS "RO"
// =============================================================================
// Register    : M33_DWT_PIDR2
// Description : Provides CoreSight discovery information for the DWT
#define M33_DWT_PIDR2_OFFSET _u(0x00001fe8)
#define M33_DWT_PIDR2_BITS   _u(0x000000ff)
#define M33_DWT_PIDR2_RESET  _u(0x0000000b)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_PIDR2_REVISION
// Description : See CoreSight Architecture Specification
#define M33_DWT_PIDR2_REVISION_RESET  _u(0x0)
#define M33_DWT_PIDR2_REVISION_BITS   _u(0x000000f0)
#define M33_DWT_PIDR2_REVISION_MSB    _u(7)
#define M33_DWT_PIDR2_REVISION_LSB    _u(4)
#define M33_DWT_PIDR2_REVISION_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_PIDR2_JEDEC
// Description : See CoreSight Architecture Specification
#define M33_DWT_PIDR2_JEDEC_RESET  _u(0x1)
#define M33_DWT_PIDR2_JEDEC_BITS   _u(0x00000008)
#define M33_DWT_PIDR2_JEDEC_MSB    _u(3)
#define M33_DWT_PIDR2_JEDEC_LSB    _u(3)
#define M33_DWT_PIDR2_JEDEC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_PIDR2_DES_1
// Description : See CoreSight Architecture Specification
#define M33_DWT_PIDR2_DES_1_RESET  _u(0x3)
#define M33_DWT_PIDR2_DES_1_BITS   _u(0x00000007)
#define M33_DWT_PIDR2_DES_1_MSB    _u(2)
#define M33_DWT_PIDR2_DES_1_LSB    _u(0)
#define M33_DWT_PIDR2_DES_1_ACCESS "RO"
// =============================================================================
// Register    : M33_DWT_PIDR3
// Description : Provides CoreSight discovery information for the DWT
#define M33_DWT_PIDR3_OFFSET _u(0x00001fec)
#define M33_DWT_PIDR3_BITS   _u(0x000000ff)
#define M33_DWT_PIDR3_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_PIDR3_REVAND
// Description : See CoreSight Architecture Specification
#define M33_DWT_PIDR3_REVAND_RESET  _u(0x0)
#define M33_DWT_PIDR3_REVAND_BITS   _u(0x000000f0)
#define M33_DWT_PIDR3_REVAND_MSB    _u(7)
#define M33_DWT_PIDR3_REVAND_LSB    _u(4)
#define M33_DWT_PIDR3_REVAND_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_PIDR3_CMOD
// Description : See CoreSight Architecture Specification
#define M33_DWT_PIDR3_CMOD_RESET  _u(0x0)
#define M33_DWT_PIDR3_CMOD_BITS   _u(0x0000000f)
#define M33_DWT_PIDR3_CMOD_MSB    _u(3)
#define M33_DWT_PIDR3_CMOD_LSB    _u(0)
#define M33_DWT_PIDR3_CMOD_ACCESS "RO"
// =============================================================================
// Register    : M33_DWT_CIDR0
// Description : Provides CoreSight discovery information for the DWT
#define M33_DWT_CIDR0_OFFSET _u(0x00001ff0)
#define M33_DWT_CIDR0_BITS   _u(0x000000ff)
#define M33_DWT_CIDR0_RESET  _u(0x0000000d)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CIDR0_PRMBL_0
// Description : See CoreSight Architecture Specification
#define M33_DWT_CIDR0_PRMBL_0_RESET  _u(0x0d)
#define M33_DWT_CIDR0_PRMBL_0_BITS   _u(0x000000ff)
#define M33_DWT_CIDR0_PRMBL_0_MSB    _u(7)
#define M33_DWT_CIDR0_PRMBL_0_LSB    _u(0)
#define M33_DWT_CIDR0_PRMBL_0_ACCESS "RO"
// =============================================================================
// Register    : M33_DWT_CIDR1
// Description : Provides CoreSight discovery information for the DWT
#define M33_DWT_CIDR1_OFFSET _u(0x00001ff4)
#define M33_DWT_CIDR1_BITS   _u(0x000000ff)
#define M33_DWT_CIDR1_RESET  _u(0x00000090)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CIDR1_CLASS
// Description : See CoreSight Architecture Specification
#define M33_DWT_CIDR1_CLASS_RESET  _u(0x9)
#define M33_DWT_CIDR1_CLASS_BITS   _u(0x000000f0)
#define M33_DWT_CIDR1_CLASS_MSB    _u(7)
#define M33_DWT_CIDR1_CLASS_LSB    _u(4)
#define M33_DWT_CIDR1_CLASS_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CIDR1_PRMBL_1
// Description : See CoreSight Architecture Specification
#define M33_DWT_CIDR1_PRMBL_1_RESET  _u(0x0)
#define M33_DWT_CIDR1_PRMBL_1_BITS   _u(0x0000000f)
#define M33_DWT_CIDR1_PRMBL_1_MSB    _u(3)
#define M33_DWT_CIDR1_PRMBL_1_LSB    _u(0)
#define M33_DWT_CIDR1_PRMBL_1_ACCESS "RO"
// =============================================================================
// Register    : M33_DWT_CIDR2
// Description : Provides CoreSight discovery information for the DWT
#define M33_DWT_CIDR2_OFFSET _u(0x00001ff8)
#define M33_DWT_CIDR2_BITS   _u(0x000000ff)
#define M33_DWT_CIDR2_RESET  _u(0x00000005)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CIDR2_PRMBL_2
// Description : See CoreSight Architecture Specification
#define M33_DWT_CIDR2_PRMBL_2_RESET  _u(0x05)
#define M33_DWT_CIDR2_PRMBL_2_BITS   _u(0x000000ff)
#define M33_DWT_CIDR2_PRMBL_2_MSB    _u(7)
#define M33_DWT_CIDR2_PRMBL_2_LSB    _u(0)
#define M33_DWT_CIDR2_PRMBL_2_ACCESS "RO"
// =============================================================================
// Register    : M33_DWT_CIDR3
// Description : Provides CoreSight discovery information for the DWT
#define M33_DWT_CIDR3_OFFSET _u(0x00001ffc)
#define M33_DWT_CIDR3_BITS   _u(0x000000ff)
#define M33_DWT_CIDR3_RESET  _u(0x000000b1)
// -----------------------------------------------------------------------------
// Field       : M33_DWT_CIDR3_PRMBL_3
// Description : See CoreSight Architecture Specification
#define M33_DWT_CIDR3_PRMBL_3_RESET  _u(0xb1)
#define M33_DWT_CIDR3_PRMBL_3_BITS   _u(0x000000ff)
#define M33_DWT_CIDR3_PRMBL_3_MSB    _u(7)
#define M33_DWT_CIDR3_PRMBL_3_LSB    _u(0)
#define M33_DWT_CIDR3_PRMBL_3_ACCESS "RO"
// =============================================================================
// Register    : M33_FP_CTRL
// Description : Provides FPB implementation information, and the global enable
//               for the FPB unit
#define M33_FP_CTRL_OFFSET _u(0x00002000)
#define M33_FP_CTRL_BITS   _u(0xf0007ff3)
#define M33_FP_CTRL_RESET  _u(0x60005580)
// -----------------------------------------------------------------------------
// Field       : M33_FP_CTRL_REV
// Description : Flash Patch and Breakpoint Unit architecture revision
#define M33_FP_CTRL_REV_RESET  _u(0x6)
#define M33_FP_CTRL_REV_BITS   _u(0xf0000000)
#define M33_FP_CTRL_REV_MSB    _u(31)
#define M33_FP_CTRL_REV_LSB    _u(28)
#define M33_FP_CTRL_REV_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_FP_CTRL_NUM_CODE_14_12_
// Description : Indicates the number of implemented instruction address
//               comparators. Zero indicates no Instruction Address comparators
//               are implemented. The Instruction Address comparators are
//               numbered from 0 to NUM_CODE - 1
#define M33_FP_CTRL_NUM_CODE_14_12__RESET  _u(0x5)
#define M33_FP_CTRL_NUM_CODE_14_12__BITS   _u(0x00007000)
#define M33_FP_CTRL_NUM_CODE_14_12__MSB    _u(14)
#define M33_FP_CTRL_NUM_CODE_14_12__LSB    _u(12)
#define M33_FP_CTRL_NUM_CODE_14_12__ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_FP_CTRL_NUM_LIT
// Description : Indicates the number of implemented literal address
//               comparators. The Literal Address comparators are numbered from
//               NUM_CODE to NUM_CODE + NUM_LIT - 1
#define M33_FP_CTRL_NUM_LIT_RESET  _u(0x5)
#define M33_FP_CTRL_NUM_LIT_BITS   _u(0x00000f00)
#define M33_FP_CTRL_NUM_LIT_MSB    _u(11)
#define M33_FP_CTRL_NUM_LIT_LSB    _u(8)
#define M33_FP_CTRL_NUM_LIT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_FP_CTRL_NUM_CODE_7_4_
// Description : Indicates the number of implemented instruction address
//               comparators. Zero indicates no Instruction Address comparators
//               are implemented. The Instruction Address comparators are
//               numbered from 0 to NUM_CODE - 1
#define M33_FP_CTRL_NUM_CODE_7_4__RESET  _u(0x8)
#define M33_FP_CTRL_NUM_CODE_7_4__BITS   _u(0x000000f0)
#define M33_FP_CTRL_NUM_CODE_7_4__MSB    _u(7)
#define M33_FP_CTRL_NUM_CODE_7_4__LSB    _u(4)
#define M33_FP_CTRL_NUM_CODE_7_4__ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_FP_CTRL_KEY
// Description : Writes to the FP_CTRL are ignored unless KEY is concurrently
//               written to one
#define M33_FP_CTRL_KEY_RESET  _u(0x0)
#define M33_FP_CTRL_KEY_BITS   _u(0x00000002)
#define M33_FP_CTRL_KEY_MSB    _u(1)
#define M33_FP_CTRL_KEY_LSB    _u(1)
#define M33_FP_CTRL_KEY_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FP_CTRL_ENABLE
// Description : Enables the FPB
#define M33_FP_CTRL_ENABLE_RESET  _u(0x0)
#define M33_FP_CTRL_ENABLE_BITS   _u(0x00000001)
#define M33_FP_CTRL_ENABLE_MSB    _u(0)
#define M33_FP_CTRL_ENABLE_LSB    _u(0)
#define M33_FP_CTRL_ENABLE_ACCESS "RW"
// =============================================================================
// Register    : M33_FP_REMAP
// Description : Indicates whether the implementation supports Flash Patch remap
//               and, if it does, holds the target address for remap
#define M33_FP_REMAP_OFFSET _u(0x00002004)
#define M33_FP_REMAP_BITS   _u(0x3fffffe0)
#define M33_FP_REMAP_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_FP_REMAP_RMPSPT
// Description : Indicates whether the FPB unit supports the Flash Patch remap
//               function
#define M33_FP_REMAP_RMPSPT_RESET  _u(0x0)
#define M33_FP_REMAP_RMPSPT_BITS   _u(0x20000000)
#define M33_FP_REMAP_RMPSPT_MSB    _u(29)
#define M33_FP_REMAP_RMPSPT_LSB    _u(29)
#define M33_FP_REMAP_RMPSPT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_FP_REMAP_REMAP
// Description : Holds the bits[28:5] of the Flash Patch remap address
#define M33_FP_REMAP_REMAP_RESET  _u(0x000000)
#define M33_FP_REMAP_REMAP_BITS   _u(0x1fffffe0)
#define M33_FP_REMAP_REMAP_MSB    _u(28)
#define M33_FP_REMAP_REMAP_LSB    _u(5)
#define M33_FP_REMAP_REMAP_ACCESS "RO"
// =============================================================================
// Register    : M33_FP_COMP0
// Description : Holds an address for comparison. The effect of the match
//               depends on the configuration of the FPB and whether the
//               comparator is an instruction address comparator or a literal
//               address comparator
#define M33_FP_COMP0_OFFSET _u(0x00002008)
#define M33_FP_COMP0_BITS   _u(0x00000001)
#define M33_FP_COMP0_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_FP_COMP0_BE
// Description : Selects between flashpatch and breakpoint functionality
#define M33_FP_COMP0_BE_RESET  _u(0x0)
#define M33_FP_COMP0_BE_BITS   _u(0x00000001)
#define M33_FP_COMP0_BE_MSB    _u(0)
#define M33_FP_COMP0_BE_LSB    _u(0)
#define M33_FP_COMP0_BE_ACCESS "RW"
// =============================================================================
// Register    : M33_FP_COMP1
// Description : Holds an address for comparison. The effect of the match
//               depends on the configuration of the FPB and whether the
//               comparator is an instruction address comparator or a literal
//               address comparator
#define M33_FP_COMP1_OFFSET _u(0x0000200c)
#define M33_FP_COMP1_BITS   _u(0x00000001)
#define M33_FP_COMP1_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_FP_COMP1_BE
// Description : Selects between flashpatch and breakpoint functionality
#define M33_FP_COMP1_BE_RESET  _u(0x0)
#define M33_FP_COMP1_BE_BITS   _u(0x00000001)
#define M33_FP_COMP1_BE_MSB    _u(0)
#define M33_FP_COMP1_BE_LSB    _u(0)
#define M33_FP_COMP1_BE_ACCESS "RW"
// =============================================================================
// Register    : M33_FP_COMP2
// Description : Holds an address for comparison. The effect of the match
//               depends on the configuration of the FPB and whether the
//               comparator is an instruction address comparator or a literal
//               address comparator
#define M33_FP_COMP2_OFFSET _u(0x00002010)
#define M33_FP_COMP2_BITS   _u(0x00000001)
#define M33_FP_COMP2_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_FP_COMP2_BE
// Description : Selects between flashpatch and breakpoint functionality
#define M33_FP_COMP2_BE_RESET  _u(0x0)
#define M33_FP_COMP2_BE_BITS   _u(0x00000001)
#define M33_FP_COMP2_BE_MSB    _u(0)
#define M33_FP_COMP2_BE_LSB    _u(0)
#define M33_FP_COMP2_BE_ACCESS "RW"
// =============================================================================
// Register    : M33_FP_COMP3
// Description : Holds an address for comparison. The effect of the match
//               depends on the configuration of the FPB and whether the
//               comparator is an instruction address comparator or a literal
//               address comparator
#define M33_FP_COMP3_OFFSET _u(0x00002014)
#define M33_FP_COMP3_BITS   _u(0x00000001)
#define M33_FP_COMP3_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_FP_COMP3_BE
// Description : Selects between flashpatch and breakpoint functionality
#define M33_FP_COMP3_BE_RESET  _u(0x0)
#define M33_FP_COMP3_BE_BITS   _u(0x00000001)
#define M33_FP_COMP3_BE_MSB    _u(0)
#define M33_FP_COMP3_BE_LSB    _u(0)
#define M33_FP_COMP3_BE_ACCESS "RW"
// =============================================================================
// Register    : M33_FP_COMP4
// Description : Holds an address for comparison. The effect of the match
//               depends on the configuration of the FPB and whether the
//               comparator is an instruction address comparator or a literal
//               address comparator
#define M33_FP_COMP4_OFFSET _u(0x00002018)
#define M33_FP_COMP4_BITS   _u(0x00000001)
#define M33_FP_COMP4_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_FP_COMP4_BE
// Description : Selects between flashpatch and breakpoint functionality
#define M33_FP_COMP4_BE_RESET  _u(0x0)
#define M33_FP_COMP4_BE_BITS   _u(0x00000001)
#define M33_FP_COMP4_BE_MSB    _u(0)
#define M33_FP_COMP4_BE_LSB    _u(0)
#define M33_FP_COMP4_BE_ACCESS "RW"
// =============================================================================
// Register    : M33_FP_COMP5
// Description : Holds an address for comparison. The effect of the match
//               depends on the configuration of the FPB and whether the
//               comparator is an instruction address comparator or a literal
//               address comparator
#define M33_FP_COMP5_OFFSET _u(0x0000201c)
#define M33_FP_COMP5_BITS   _u(0x00000001)
#define M33_FP_COMP5_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_FP_COMP5_BE
// Description : Selects between flashpatch and breakpoint functionality
#define M33_FP_COMP5_BE_RESET  _u(0x0)
#define M33_FP_COMP5_BE_BITS   _u(0x00000001)
#define M33_FP_COMP5_BE_MSB    _u(0)
#define M33_FP_COMP5_BE_LSB    _u(0)
#define M33_FP_COMP5_BE_ACCESS "RW"
// =============================================================================
// Register    : M33_FP_COMP6
// Description : Holds an address for comparison. The effect of the match
//               depends on the configuration of the FPB and whether the
//               comparator is an instruction address comparator or a literal
//               address comparator
#define M33_FP_COMP6_OFFSET _u(0x00002020)
#define M33_FP_COMP6_BITS   _u(0x00000001)
#define M33_FP_COMP6_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_FP_COMP6_BE
// Description : Selects between flashpatch and breakpoint functionality
#define M33_FP_COMP6_BE_RESET  _u(0x0)
#define M33_FP_COMP6_BE_BITS   _u(0x00000001)
#define M33_FP_COMP6_BE_MSB    _u(0)
#define M33_FP_COMP6_BE_LSB    _u(0)
#define M33_FP_COMP6_BE_ACCESS "RW"
// =============================================================================
// Register    : M33_FP_COMP7
// Description : Holds an address for comparison. The effect of the match
//               depends on the configuration of the FPB and whether the
//               comparator is an instruction address comparator or a literal
//               address comparator
#define M33_FP_COMP7_OFFSET _u(0x00002024)
#define M33_FP_COMP7_BITS   _u(0x00000001)
#define M33_FP_COMP7_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_FP_COMP7_BE
// Description : Selects between flashpatch and breakpoint functionality
#define M33_FP_COMP7_BE_RESET  _u(0x0)
#define M33_FP_COMP7_BE_BITS   _u(0x00000001)
#define M33_FP_COMP7_BE_MSB    _u(0)
#define M33_FP_COMP7_BE_LSB    _u(0)
#define M33_FP_COMP7_BE_ACCESS "RW"
// =============================================================================
// Register    : M33_FP_DEVARCH
// Description : Provides CoreSight discovery information for the FPB
#define M33_FP_DEVARCH_OFFSET _u(0x00002fbc)
#define M33_FP_DEVARCH_BITS   _u(0xffffffff)
#define M33_FP_DEVARCH_RESET  _u(0x47701a03)
// -----------------------------------------------------------------------------
// Field       : M33_FP_DEVARCH_ARCHITECT
// Description : Defines the architect of the component. Bits [31:28] are the
//               JEP106 continuation code (JEP106 bank ID, minus 1) and bits
//               [27:21] are the JEP106 ID code.
#define M33_FP_DEVARCH_ARCHITECT_RESET  _u(0x23b)
#define M33_FP_DEVARCH_ARCHITECT_BITS   _u(0xffe00000)
#define M33_FP_DEVARCH_ARCHITECT_MSB    _u(31)
#define M33_FP_DEVARCH_ARCHITECT_LSB    _u(21)
#define M33_FP_DEVARCH_ARCHITECT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_FP_DEVARCH_PRESENT
// Description : Defines that the DEVARCH register is present
#define M33_FP_DEVARCH_PRESENT_RESET  _u(0x1)
#define M33_FP_DEVARCH_PRESENT_BITS   _u(0x00100000)
#define M33_FP_DEVARCH_PRESENT_MSB    _u(20)
#define M33_FP_DEVARCH_PRESENT_LSB    _u(20)
#define M33_FP_DEVARCH_PRESENT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_FP_DEVARCH_REVISION
// Description : Defines the architecture revision of the component
#define M33_FP_DEVARCH_REVISION_RESET  _u(0x0)
#define M33_FP_DEVARCH_REVISION_BITS   _u(0x000f0000)
#define M33_FP_DEVARCH_REVISION_MSB    _u(19)
#define M33_FP_DEVARCH_REVISION_LSB    _u(16)
#define M33_FP_DEVARCH_REVISION_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_FP_DEVARCH_ARCHVER
// Description : Defines the architecture version of the component
#define M33_FP_DEVARCH_ARCHVER_RESET  _u(0x1)
#define M33_FP_DEVARCH_ARCHVER_BITS   _u(0x0000f000)
#define M33_FP_DEVARCH_ARCHVER_MSB    _u(15)
#define M33_FP_DEVARCH_ARCHVER_LSB    _u(12)
#define M33_FP_DEVARCH_ARCHVER_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_FP_DEVARCH_ARCHPART
// Description : Defines the architecture of the component
#define M33_FP_DEVARCH_ARCHPART_RESET  _u(0xa03)
#define M33_FP_DEVARCH_ARCHPART_BITS   _u(0x00000fff)
#define M33_FP_DEVARCH_ARCHPART_MSB    _u(11)
#define M33_FP_DEVARCH_ARCHPART_LSB    _u(0)
#define M33_FP_DEVARCH_ARCHPART_ACCESS "RO"
// =============================================================================
// Register    : M33_FP_DEVTYPE
// Description : Provides CoreSight discovery information for the FPB
#define M33_FP_DEVTYPE_OFFSET _u(0x00002fcc)
#define M33_FP_DEVTYPE_BITS   _u(0x000000ff)
#define M33_FP_DEVTYPE_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_FP_DEVTYPE_SUB
// Description : Component sub-type
#define M33_FP_DEVTYPE_SUB_RESET  _u(0x0)
#define M33_FP_DEVTYPE_SUB_BITS   _u(0x000000f0)
#define M33_FP_DEVTYPE_SUB_MSB    _u(7)
#define M33_FP_DEVTYPE_SUB_LSB    _u(4)
#define M33_FP_DEVTYPE_SUB_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_FP_DEVTYPE_MAJOR
// Description : Component major type
#define M33_FP_DEVTYPE_MAJOR_RESET  _u(0x0)
#define M33_FP_DEVTYPE_MAJOR_BITS   _u(0x0000000f)
#define M33_FP_DEVTYPE_MAJOR_MSB    _u(3)
#define M33_FP_DEVTYPE_MAJOR_LSB    _u(0)
#define M33_FP_DEVTYPE_MAJOR_ACCESS "RO"
// =============================================================================
// Register    : M33_FP_PIDR4
// Description : Provides CoreSight discovery information for the FP
#define M33_FP_PIDR4_OFFSET _u(0x00002fd0)
#define M33_FP_PIDR4_BITS   _u(0x000000ff)
#define M33_FP_PIDR4_RESET  _u(0x00000004)
// -----------------------------------------------------------------------------
// Field       : M33_FP_PIDR4_SIZE
// Description : See CoreSight Architecture Specification
#define M33_FP_PIDR4_SIZE_RESET  _u(0x0)
#define M33_FP_PIDR4_SIZE_BITS   _u(0x000000f0)
#define M33_FP_PIDR4_SIZE_MSB    _u(7)
#define M33_FP_PIDR4_SIZE_LSB    _u(4)
#define M33_FP_PIDR4_SIZE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_FP_PIDR4_DES_2
// Description : See CoreSight Architecture Specification
#define M33_FP_PIDR4_DES_2_RESET  _u(0x4)
#define M33_FP_PIDR4_DES_2_BITS   _u(0x0000000f)
#define M33_FP_PIDR4_DES_2_MSB    _u(3)
#define M33_FP_PIDR4_DES_2_LSB    _u(0)
#define M33_FP_PIDR4_DES_2_ACCESS "RO"
// =============================================================================
// Register    : M33_FP_PIDR5
// Description : Provides CoreSight discovery information for the FP
#define M33_FP_PIDR5_OFFSET _u(0x00002fd4)
#define M33_FP_PIDR5_BITS   _u(0x00000000)
#define M33_FP_PIDR5_RESET  _u(0x00000000)
#define M33_FP_PIDR5_MSB    _u(31)
#define M33_FP_PIDR5_LSB    _u(0)
#define M33_FP_PIDR5_ACCESS "RW"
// =============================================================================
// Register    : M33_FP_PIDR6
// Description : Provides CoreSight discovery information for the FP
#define M33_FP_PIDR6_OFFSET _u(0x00002fd8)
#define M33_FP_PIDR6_BITS   _u(0x00000000)
#define M33_FP_PIDR6_RESET  _u(0x00000000)
#define M33_FP_PIDR6_MSB    _u(31)
#define M33_FP_PIDR6_LSB    _u(0)
#define M33_FP_PIDR6_ACCESS "RW"
// =============================================================================
// Register    : M33_FP_PIDR7
// Description : Provides CoreSight discovery information for the FP
#define M33_FP_PIDR7_OFFSET _u(0x00002fdc)
#define M33_FP_PIDR7_BITS   _u(0x00000000)
#define M33_FP_PIDR7_RESET  _u(0x00000000)
#define M33_FP_PIDR7_MSB    _u(31)
#define M33_FP_PIDR7_LSB    _u(0)
#define M33_FP_PIDR7_ACCESS "RW"
// =============================================================================
// Register    : M33_FP_PIDR0
// Description : Provides CoreSight discovery information for the FP
#define M33_FP_PIDR0_OFFSET _u(0x00002fe0)
#define M33_FP_PIDR0_BITS   _u(0x000000ff)
#define M33_FP_PIDR0_RESET  _u(0x00000021)
// -----------------------------------------------------------------------------
// Field       : M33_FP_PIDR0_PART_0
// Description : See CoreSight Architecture Specification
#define M33_FP_PIDR0_PART_0_RESET  _u(0x21)
#define M33_FP_PIDR0_PART_0_BITS   _u(0x000000ff)
#define M33_FP_PIDR0_PART_0_MSB    _u(7)
#define M33_FP_PIDR0_PART_0_LSB    _u(0)
#define M33_FP_PIDR0_PART_0_ACCESS "RO"
// =============================================================================
// Register    : M33_FP_PIDR1
// Description : Provides CoreSight discovery information for the FP
#define M33_FP_PIDR1_OFFSET _u(0x00002fe4)
#define M33_FP_PIDR1_BITS   _u(0x000000ff)
#define M33_FP_PIDR1_RESET  _u(0x000000bd)
// -----------------------------------------------------------------------------
// Field       : M33_FP_PIDR1_DES_0
// Description : See CoreSight Architecture Specification
#define M33_FP_PIDR1_DES_0_RESET  _u(0xb)
#define M33_FP_PIDR1_DES_0_BITS   _u(0x000000f0)
#define M33_FP_PIDR1_DES_0_MSB    _u(7)
#define M33_FP_PIDR1_DES_0_LSB    _u(4)
#define M33_FP_PIDR1_DES_0_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_FP_PIDR1_PART_1
// Description : See CoreSight Architecture Specification
#define M33_FP_PIDR1_PART_1_RESET  _u(0xd)
#define M33_FP_PIDR1_PART_1_BITS   _u(0x0000000f)
#define M33_FP_PIDR1_PART_1_MSB    _u(3)
#define M33_FP_PIDR1_PART_1_LSB    _u(0)
#define M33_FP_PIDR1_PART_1_ACCESS "RO"
// =============================================================================
// Register    : M33_FP_PIDR2
// Description : Provides CoreSight discovery information for the FP
#define M33_FP_PIDR2_OFFSET _u(0x00002fe8)
#define M33_FP_PIDR2_BITS   _u(0x000000ff)
#define M33_FP_PIDR2_RESET  _u(0x0000000b)
// -----------------------------------------------------------------------------
// Field       : M33_FP_PIDR2_REVISION
// Description : See CoreSight Architecture Specification
#define M33_FP_PIDR2_REVISION_RESET  _u(0x0)
#define M33_FP_PIDR2_REVISION_BITS   _u(0x000000f0)
#define M33_FP_PIDR2_REVISION_MSB    _u(7)
#define M33_FP_PIDR2_REVISION_LSB    _u(4)
#define M33_FP_PIDR2_REVISION_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_FP_PIDR2_JEDEC
// Description : See CoreSight Architecture Specification
#define M33_FP_PIDR2_JEDEC_RESET  _u(0x1)
#define M33_FP_PIDR2_JEDEC_BITS   _u(0x00000008)
#define M33_FP_PIDR2_JEDEC_MSB    _u(3)
#define M33_FP_PIDR2_JEDEC_LSB    _u(3)
#define M33_FP_PIDR2_JEDEC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_FP_PIDR2_DES_1
// Description : See CoreSight Architecture Specification
#define M33_FP_PIDR2_DES_1_RESET  _u(0x3)
#define M33_FP_PIDR2_DES_1_BITS   _u(0x00000007)
#define M33_FP_PIDR2_DES_1_MSB    _u(2)
#define M33_FP_PIDR2_DES_1_LSB    _u(0)
#define M33_FP_PIDR2_DES_1_ACCESS "RO"
// =============================================================================
// Register    : M33_FP_PIDR3
// Description : Provides CoreSight discovery information for the FP
#define M33_FP_PIDR3_OFFSET _u(0x00002fec)
#define M33_FP_PIDR3_BITS   _u(0x000000ff)
#define M33_FP_PIDR3_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_FP_PIDR3_REVAND
// Description : See CoreSight Architecture Specification
#define M33_FP_PIDR3_REVAND_RESET  _u(0x0)
#define M33_FP_PIDR3_REVAND_BITS   _u(0x000000f0)
#define M33_FP_PIDR3_REVAND_MSB    _u(7)
#define M33_FP_PIDR3_REVAND_LSB    _u(4)
#define M33_FP_PIDR3_REVAND_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_FP_PIDR3_CMOD
// Description : See CoreSight Architecture Specification
#define M33_FP_PIDR3_CMOD_RESET  _u(0x0)
#define M33_FP_PIDR3_CMOD_BITS   _u(0x0000000f)
#define M33_FP_PIDR3_CMOD_MSB    _u(3)
#define M33_FP_PIDR3_CMOD_LSB    _u(0)
#define M33_FP_PIDR3_CMOD_ACCESS "RO"
// =============================================================================
// Register    : M33_FP_CIDR0
// Description : Provides CoreSight discovery information for the FP
#define M33_FP_CIDR0_OFFSET _u(0x00002ff0)
#define M33_FP_CIDR0_BITS   _u(0x000000ff)
#define M33_FP_CIDR0_RESET  _u(0x0000000d)
// -----------------------------------------------------------------------------
// Field       : M33_FP_CIDR0_PRMBL_0
// Description : See CoreSight Architecture Specification
#define M33_FP_CIDR0_PRMBL_0_RESET  _u(0x0d)
#define M33_FP_CIDR0_PRMBL_0_BITS   _u(0x000000ff)
#define M33_FP_CIDR0_PRMBL_0_MSB    _u(7)
#define M33_FP_CIDR0_PRMBL_0_LSB    _u(0)
#define M33_FP_CIDR0_PRMBL_0_ACCESS "RO"
// =============================================================================
// Register    : M33_FP_CIDR1
// Description : Provides CoreSight discovery information for the FP
#define M33_FP_CIDR1_OFFSET _u(0x00002ff4)
#define M33_FP_CIDR1_BITS   _u(0x000000ff)
#define M33_FP_CIDR1_RESET  _u(0x00000090)
// -----------------------------------------------------------------------------
// Field       : M33_FP_CIDR1_CLASS
// Description : See CoreSight Architecture Specification
#define M33_FP_CIDR1_CLASS_RESET  _u(0x9)
#define M33_FP_CIDR1_CLASS_BITS   _u(0x000000f0)
#define M33_FP_CIDR1_CLASS_MSB    _u(7)
#define M33_FP_CIDR1_CLASS_LSB    _u(4)
#define M33_FP_CIDR1_CLASS_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_FP_CIDR1_PRMBL_1
// Description : See CoreSight Architecture Specification
#define M33_FP_CIDR1_PRMBL_1_RESET  _u(0x0)
#define M33_FP_CIDR1_PRMBL_1_BITS   _u(0x0000000f)
#define M33_FP_CIDR1_PRMBL_1_MSB    _u(3)
#define M33_FP_CIDR1_PRMBL_1_LSB    _u(0)
#define M33_FP_CIDR1_PRMBL_1_ACCESS "RO"
// =============================================================================
// Register    : M33_FP_CIDR2
// Description : Provides CoreSight discovery information for the FP
#define M33_FP_CIDR2_OFFSET _u(0x00002ff8)
#define M33_FP_CIDR2_BITS   _u(0x000000ff)
#define M33_FP_CIDR2_RESET  _u(0x00000005)
// -----------------------------------------------------------------------------
// Field       : M33_FP_CIDR2_PRMBL_2
// Description : See CoreSight Architecture Specification
#define M33_FP_CIDR2_PRMBL_2_RESET  _u(0x05)
#define M33_FP_CIDR2_PRMBL_2_BITS   _u(0x000000ff)
#define M33_FP_CIDR2_PRMBL_2_MSB    _u(7)
#define M33_FP_CIDR2_PRMBL_2_LSB    _u(0)
#define M33_FP_CIDR2_PRMBL_2_ACCESS "RO"
// =============================================================================
// Register    : M33_FP_CIDR3
// Description : Provides CoreSight discovery information for the FP
#define M33_FP_CIDR3_OFFSET _u(0x00002ffc)
#define M33_FP_CIDR3_BITS   _u(0x000000ff)
#define M33_FP_CIDR3_RESET  _u(0x000000b1)
// -----------------------------------------------------------------------------
// Field       : M33_FP_CIDR3_PRMBL_3
// Description : See CoreSight Architecture Specification
#define M33_FP_CIDR3_PRMBL_3_RESET  _u(0xb1)
#define M33_FP_CIDR3_PRMBL_3_BITS   _u(0x000000ff)
#define M33_FP_CIDR3_PRMBL_3_MSB    _u(7)
#define M33_FP_CIDR3_PRMBL_3_LSB    _u(0)
#define M33_FP_CIDR3_PRMBL_3_ACCESS "RO"
// =============================================================================
// Register    : M33_ICTR
// Description : Provides information about the interrupt controller
#define M33_ICTR_OFFSET _u(0x0000e004)
#define M33_ICTR_BITS   _u(0x0000000f)
#define M33_ICTR_RESET  _u(0x00000001)
// -----------------------------------------------------------------------------
// Field       : M33_ICTR_INTLINESNUM
// Description : Indicates the number of the highest implemented register in
//               each of the NVIC control register sets, or in the case of
//               NVIC_IPR*n, 4×INTLINESNUM
#define M33_ICTR_INTLINESNUM_RESET  _u(0x1)
#define M33_ICTR_INTLINESNUM_BITS   _u(0x0000000f)
#define M33_ICTR_INTLINESNUM_MSB    _u(3)
#define M33_ICTR_INTLINESNUM_LSB    _u(0)
#define M33_ICTR_INTLINESNUM_ACCESS "RO"
// =============================================================================
// Register    : M33_ACTLR
// Description : Provides IMPLEMENTATION DEFINED configuration and control
//               options
#define M33_ACTLR_OFFSET _u(0x0000e008)
#define M33_ACTLR_BITS   _u(0x20001605)
#define M33_ACTLR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ACTLR_EXTEXCLALL
// Description : External Exclusives Allowed with no MPU
#define M33_ACTLR_EXTEXCLALL_RESET  _u(0x0)
#define M33_ACTLR_EXTEXCLALL_BITS   _u(0x20000000)
#define M33_ACTLR_EXTEXCLALL_MSB    _u(29)
#define M33_ACTLR_EXTEXCLALL_LSB    _u(29)
#define M33_ACTLR_EXTEXCLALL_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_ACTLR_DISITMATBFLUSH
// Description : Disable ATB Flush
#define M33_ACTLR_DISITMATBFLUSH_RESET  _u(0x0)
#define M33_ACTLR_DISITMATBFLUSH_BITS   _u(0x00001000)
#define M33_ACTLR_DISITMATBFLUSH_MSB    _u(12)
#define M33_ACTLR_DISITMATBFLUSH_LSB    _u(12)
#define M33_ACTLR_DISITMATBFLUSH_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_ACTLR_FPEXCODIS
// Description : Disable FPU exception outputs
#define M33_ACTLR_FPEXCODIS_RESET  _u(0x0)
#define M33_ACTLR_FPEXCODIS_BITS   _u(0x00000400)
#define M33_ACTLR_FPEXCODIS_MSB    _u(10)
#define M33_ACTLR_FPEXCODIS_LSB    _u(10)
#define M33_ACTLR_FPEXCODIS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_ACTLR_DISOOFP
// Description : Disable out-of-order FP instruction completion
#define M33_ACTLR_DISOOFP_RESET  _u(0x0)
#define M33_ACTLR_DISOOFP_BITS   _u(0x00000200)
#define M33_ACTLR_DISOOFP_MSB    _u(9)
#define M33_ACTLR_DISOOFP_LSB    _u(9)
#define M33_ACTLR_DISOOFP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_ACTLR_DISFOLD
// Description : Disable dual-issue.
#define M33_ACTLR_DISFOLD_RESET  _u(0x0)
#define M33_ACTLR_DISFOLD_BITS   _u(0x00000004)
#define M33_ACTLR_DISFOLD_MSB    _u(2)
#define M33_ACTLR_DISFOLD_LSB    _u(2)
#define M33_ACTLR_DISFOLD_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_ACTLR_DISMCYCINT
// Description : Disable dual-issue.
#define M33_ACTLR_DISMCYCINT_RESET  _u(0x0)
#define M33_ACTLR_DISMCYCINT_BITS   _u(0x00000001)
#define M33_ACTLR_DISMCYCINT_MSB    _u(0)
#define M33_ACTLR_DISMCYCINT_LSB    _u(0)
#define M33_ACTLR_DISMCYCINT_ACCESS "RW"
// =============================================================================
// Register    : M33_SYST_CSR
// Description : Use the SysTick Control and Status Register to enable the
//               SysTick features.
#define M33_SYST_CSR_OFFSET _u(0x0000e010)
#define M33_SYST_CSR_BITS   _u(0x00010007)
#define M33_SYST_CSR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_SYST_CSR_COUNTFLAG
// Description : Returns 1 if timer counted to 0 since last time this was read.
//               Clears on read by application or debugger.
#define M33_SYST_CSR_COUNTFLAG_RESET  _u(0x0)
#define M33_SYST_CSR_COUNTFLAG_BITS   _u(0x00010000)
#define M33_SYST_CSR_COUNTFLAG_MSB    _u(16)
#define M33_SYST_CSR_COUNTFLAG_LSB    _u(16)
#define M33_SYST_CSR_COUNTFLAG_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_SYST_CSR_CLKSOURCE
// Description : SysTick clock source. Always reads as one if SYST_CALIB reports
//               NOREF.
//               Selects the SysTick timer clock source:
//               0 = External reference clock.
//               1 = Processor clock.
#define M33_SYST_CSR_CLKSOURCE_RESET  _u(0x0)
#define M33_SYST_CSR_CLKSOURCE_BITS   _u(0x00000004)
#define M33_SYST_CSR_CLKSOURCE_MSB    _u(2)
#define M33_SYST_CSR_CLKSOURCE_LSB    _u(2)
#define M33_SYST_CSR_CLKSOURCE_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SYST_CSR_TICKINT
// Description : Enables SysTick exception request:
//               0 = Counting down to zero does not assert the SysTick exception
//               request.
//               1 = Counting down to zero to asserts the SysTick exception
//               request.
#define M33_SYST_CSR_TICKINT_RESET  _u(0x0)
#define M33_SYST_CSR_TICKINT_BITS   _u(0x00000002)
#define M33_SYST_CSR_TICKINT_MSB    _u(1)
#define M33_SYST_CSR_TICKINT_LSB    _u(1)
#define M33_SYST_CSR_TICKINT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SYST_CSR_ENABLE
// Description : Enable SysTick counter:
//               0 = Counter disabled.
//               1 = Counter enabled.
#define M33_SYST_CSR_ENABLE_RESET  _u(0x0)
#define M33_SYST_CSR_ENABLE_BITS   _u(0x00000001)
#define M33_SYST_CSR_ENABLE_MSB    _u(0)
#define M33_SYST_CSR_ENABLE_LSB    _u(0)
#define M33_SYST_CSR_ENABLE_ACCESS "RW"
// =============================================================================
// Register    : M33_SYST_RVR
// Description : Use the SysTick Reload Value Register to specify the start
//               value to load into the current value register when the counter
//               reaches 0. It can be any value between 0 and 0x00FFFFFF. A
//               start value of 0 is possible, but has no effect because the
//               SysTick interrupt and COUNTFLAG are activated when counting
//               from 1 to 0. The reset value of this register is UNKNOWN.
//               To generate a multi-shot timer with a period of N processor
//               clock cycles, use a RELOAD value of N-1. For example, if the
//               SysTick interrupt is required every 100 clock pulses, set
//               RELOAD to 99.
#define M33_SYST_RVR_OFFSET _u(0x0000e014)
#define M33_SYST_RVR_BITS   _u(0x00ffffff)
#define M33_SYST_RVR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_SYST_RVR_RELOAD
// Description : Value to load into the SysTick Current Value Register when the
//               counter reaches 0.
#define M33_SYST_RVR_RELOAD_RESET  _u(0x000000)
#define M33_SYST_RVR_RELOAD_BITS   _u(0x00ffffff)
#define M33_SYST_RVR_RELOAD_MSB    _u(23)
#define M33_SYST_RVR_RELOAD_LSB    _u(0)
#define M33_SYST_RVR_RELOAD_ACCESS "RW"
// =============================================================================
// Register    : M33_SYST_CVR
// Description : Use the SysTick Current Value Register to find the current
//               value in the register. The reset value of this register is
//               UNKNOWN.
#define M33_SYST_CVR_OFFSET _u(0x0000e018)
#define M33_SYST_CVR_BITS   _u(0x00ffffff)
#define M33_SYST_CVR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_SYST_CVR_CURRENT
// Description : Reads return the current value of the SysTick counter. This
//               register is write-clear. Writing to it with any value clears
//               the register to 0. Clearing this register also clears the
//               COUNTFLAG bit of the SysTick Control and Status Register.
#define M33_SYST_CVR_CURRENT_RESET  _u(0x000000)
#define M33_SYST_CVR_CURRENT_BITS   _u(0x00ffffff)
#define M33_SYST_CVR_CURRENT_MSB    _u(23)
#define M33_SYST_CVR_CURRENT_LSB    _u(0)
#define M33_SYST_CVR_CURRENT_ACCESS "RW"
// =============================================================================
// Register    : M33_SYST_CALIB
// Description : Use the SysTick Calibration Value Register to enable software
//               to scale to any required speed using divide and multiply.
#define M33_SYST_CALIB_OFFSET _u(0x0000e01c)
#define M33_SYST_CALIB_BITS   _u(0xc0ffffff)
#define M33_SYST_CALIB_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_SYST_CALIB_NOREF
// Description : If reads as 1, the Reference clock is not provided - the
//               CLKSOURCE bit of the SysTick Control and Status register will
//               be forced to 1 and cannot be cleared to 0.
#define M33_SYST_CALIB_NOREF_RESET  _u(0x0)
#define M33_SYST_CALIB_NOREF_BITS   _u(0x80000000)
#define M33_SYST_CALIB_NOREF_MSB    _u(31)
#define M33_SYST_CALIB_NOREF_LSB    _u(31)
#define M33_SYST_CALIB_NOREF_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_SYST_CALIB_SKEW
// Description : If reads as 1, the calibration value for 10ms is inexact (due
//               to clock frequency).
#define M33_SYST_CALIB_SKEW_RESET  _u(0x0)
#define M33_SYST_CALIB_SKEW_BITS   _u(0x40000000)
#define M33_SYST_CALIB_SKEW_MSB    _u(30)
#define M33_SYST_CALIB_SKEW_LSB    _u(30)
#define M33_SYST_CALIB_SKEW_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_SYST_CALIB_TENMS
// Description : An optional Reload value to be used for 10ms (100Hz) timing,
//               subject to system clock skew errors. If the value reads as 0,
//               the calibration value is not known.
#define M33_SYST_CALIB_TENMS_RESET  _u(0x000000)
#define M33_SYST_CALIB_TENMS_BITS   _u(0x00ffffff)
#define M33_SYST_CALIB_TENMS_MSB    _u(23)
#define M33_SYST_CALIB_TENMS_LSB    _u(0)
#define M33_SYST_CALIB_TENMS_ACCESS "RO"
// =============================================================================
// Register    : M33_NVIC_ISER0
// Description : Enables or reads the enabled state of each group of 32
//               interrupts
#define M33_NVIC_ISER0_OFFSET _u(0x0000e100)
#define M33_NVIC_ISER0_BITS   _u(0xffffffff)
#define M33_NVIC_ISER0_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_ISER0_SETENA
// Description : For SETENA[m] in NVIC_ISER*n, indicates whether interrupt 32*n
//               + m is enabled
#define M33_NVIC_ISER0_SETENA_RESET  _u(0x00000000)
#define M33_NVIC_ISER0_SETENA_BITS   _u(0xffffffff)
#define M33_NVIC_ISER0_SETENA_MSB    _u(31)
#define M33_NVIC_ISER0_SETENA_LSB    _u(0)
#define M33_NVIC_ISER0_SETENA_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_ISER1
// Description : Enables or reads the enabled state of each group of 32
//               interrupts
#define M33_NVIC_ISER1_OFFSET _u(0x0000e104)
#define M33_NVIC_ISER1_BITS   _u(0xffffffff)
#define M33_NVIC_ISER1_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_ISER1_SETENA
// Description : For SETENA[m] in NVIC_ISER*n, indicates whether interrupt 32*n
//               + m is enabled
#define M33_NVIC_ISER1_SETENA_RESET  _u(0x00000000)
#define M33_NVIC_ISER1_SETENA_BITS   _u(0xffffffff)
#define M33_NVIC_ISER1_SETENA_MSB    _u(31)
#define M33_NVIC_ISER1_SETENA_LSB    _u(0)
#define M33_NVIC_ISER1_SETENA_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_ICER0
// Description : Clears or reads the enabled state of each group of 32
//               interrupts
#define M33_NVIC_ICER0_OFFSET _u(0x0000e180)
#define M33_NVIC_ICER0_BITS   _u(0xffffffff)
#define M33_NVIC_ICER0_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_ICER0_CLRENA
// Description : For CLRENA[m] in NVIC_ICER*n, indicates whether interrupt 32*n
//               + m is enabled
#define M33_NVIC_ICER0_CLRENA_RESET  _u(0x00000000)
#define M33_NVIC_ICER0_CLRENA_BITS   _u(0xffffffff)
#define M33_NVIC_ICER0_CLRENA_MSB    _u(31)
#define M33_NVIC_ICER0_CLRENA_LSB    _u(0)
#define M33_NVIC_ICER0_CLRENA_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_ICER1
// Description : Clears or reads the enabled state of each group of 32
//               interrupts
#define M33_NVIC_ICER1_OFFSET _u(0x0000e184)
#define M33_NVIC_ICER1_BITS   _u(0xffffffff)
#define M33_NVIC_ICER1_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_ICER1_CLRENA
// Description : For CLRENA[m] in NVIC_ICER*n, indicates whether interrupt 32*n
//               + m is enabled
#define M33_NVIC_ICER1_CLRENA_RESET  _u(0x00000000)
#define M33_NVIC_ICER1_CLRENA_BITS   _u(0xffffffff)
#define M33_NVIC_ICER1_CLRENA_MSB    _u(31)
#define M33_NVIC_ICER1_CLRENA_LSB    _u(0)
#define M33_NVIC_ICER1_CLRENA_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_ISPR0
// Description : Enables or reads the pending state of each group of 32
//               interrupts
#define M33_NVIC_ISPR0_OFFSET _u(0x0000e200)
#define M33_NVIC_ISPR0_BITS   _u(0xffffffff)
#define M33_NVIC_ISPR0_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_ISPR0_SETPEND
// Description : For SETPEND[m] in NVIC_ISPR*n, indicates whether interrupt 32*n
//               + m is pending
#define M33_NVIC_ISPR0_SETPEND_RESET  _u(0x00000000)
#define M33_NVIC_ISPR0_SETPEND_BITS   _u(0xffffffff)
#define M33_NVIC_ISPR0_SETPEND_MSB    _u(31)
#define M33_NVIC_ISPR0_SETPEND_LSB    _u(0)
#define M33_NVIC_ISPR0_SETPEND_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_ISPR1
// Description : Enables or reads the pending state of each group of 32
//               interrupts
#define M33_NVIC_ISPR1_OFFSET _u(0x0000e204)
#define M33_NVIC_ISPR1_BITS   _u(0xffffffff)
#define M33_NVIC_ISPR1_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_ISPR1_SETPEND
// Description : For SETPEND[m] in NVIC_ISPR*n, indicates whether interrupt 32*n
//               + m is pending
#define M33_NVIC_ISPR1_SETPEND_RESET  _u(0x00000000)
#define M33_NVIC_ISPR1_SETPEND_BITS   _u(0xffffffff)
#define M33_NVIC_ISPR1_SETPEND_MSB    _u(31)
#define M33_NVIC_ISPR1_SETPEND_LSB    _u(0)
#define M33_NVIC_ISPR1_SETPEND_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_ICPR0
// Description : Clears or reads the pending state of each group of 32
//               interrupts
#define M33_NVIC_ICPR0_OFFSET _u(0x0000e280)
#define M33_NVIC_ICPR0_BITS   _u(0xffffffff)
#define M33_NVIC_ICPR0_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_ICPR0_CLRPEND
// Description : For CLRPEND[m] in NVIC_ICPR*n, indicates whether interrupt 32*n
//               + m is pending
#define M33_NVIC_ICPR0_CLRPEND_RESET  _u(0x00000000)
#define M33_NVIC_ICPR0_CLRPEND_BITS   _u(0xffffffff)
#define M33_NVIC_ICPR0_CLRPEND_MSB    _u(31)
#define M33_NVIC_ICPR0_CLRPEND_LSB    _u(0)
#define M33_NVIC_ICPR0_CLRPEND_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_ICPR1
// Description : Clears or reads the pending state of each group of 32
//               interrupts
#define M33_NVIC_ICPR1_OFFSET _u(0x0000e284)
#define M33_NVIC_ICPR1_BITS   _u(0xffffffff)
#define M33_NVIC_ICPR1_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_ICPR1_CLRPEND
// Description : For CLRPEND[m] in NVIC_ICPR*n, indicates whether interrupt 32*n
//               + m is pending
#define M33_NVIC_ICPR1_CLRPEND_RESET  _u(0x00000000)
#define M33_NVIC_ICPR1_CLRPEND_BITS   _u(0xffffffff)
#define M33_NVIC_ICPR1_CLRPEND_MSB    _u(31)
#define M33_NVIC_ICPR1_CLRPEND_LSB    _u(0)
#define M33_NVIC_ICPR1_CLRPEND_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IABR0
// Description : For each group of 32 interrupts, shows the active state of each
//               interrupt
#define M33_NVIC_IABR0_OFFSET _u(0x0000e300)
#define M33_NVIC_IABR0_BITS   _u(0xffffffff)
#define M33_NVIC_IABR0_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IABR0_ACTIVE
// Description : For ACTIVE[m] in NVIC_IABR*n, indicates the active state for
//               interrupt 32*n+m
#define M33_NVIC_IABR0_ACTIVE_RESET  _u(0x00000000)
#define M33_NVIC_IABR0_ACTIVE_BITS   _u(0xffffffff)
#define M33_NVIC_IABR0_ACTIVE_MSB    _u(31)
#define M33_NVIC_IABR0_ACTIVE_LSB    _u(0)
#define M33_NVIC_IABR0_ACTIVE_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IABR1
// Description : For each group of 32 interrupts, shows the active state of each
//               interrupt
#define M33_NVIC_IABR1_OFFSET _u(0x0000e304)
#define M33_NVIC_IABR1_BITS   _u(0xffffffff)
#define M33_NVIC_IABR1_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IABR1_ACTIVE
// Description : For ACTIVE[m] in NVIC_IABR*n, indicates the active state for
//               interrupt 32*n+m
#define M33_NVIC_IABR1_ACTIVE_RESET  _u(0x00000000)
#define M33_NVIC_IABR1_ACTIVE_BITS   _u(0xffffffff)
#define M33_NVIC_IABR1_ACTIVE_MSB    _u(31)
#define M33_NVIC_IABR1_ACTIVE_LSB    _u(0)
#define M33_NVIC_IABR1_ACTIVE_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_ITNS0
// Description : For each group of 32 interrupts, determines whether each
//               interrupt targets Non-secure or Secure state
#define M33_NVIC_ITNS0_OFFSET _u(0x0000e380)
#define M33_NVIC_ITNS0_BITS   _u(0xffffffff)
#define M33_NVIC_ITNS0_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_ITNS0_ITNS
// Description : For ITNS[m] in NVIC_ITNS*n, `IAAMO the target Security state
//               for interrupt 32*n+m
#define M33_NVIC_ITNS0_ITNS_RESET  _u(0x00000000)
#define M33_NVIC_ITNS0_ITNS_BITS   _u(0xffffffff)
#define M33_NVIC_ITNS0_ITNS_MSB    _u(31)
#define M33_NVIC_ITNS0_ITNS_LSB    _u(0)
#define M33_NVIC_ITNS0_ITNS_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_ITNS1
// Description : For each group of 32 interrupts, determines whether each
//               interrupt targets Non-secure or Secure state
#define M33_NVIC_ITNS1_OFFSET _u(0x0000e384)
#define M33_NVIC_ITNS1_BITS   _u(0xffffffff)
#define M33_NVIC_ITNS1_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_ITNS1_ITNS
// Description : For ITNS[m] in NVIC_ITNS*n, `IAAMO the target Security state
//               for interrupt 32*n+m
#define M33_NVIC_ITNS1_ITNS_RESET  _u(0x00000000)
#define M33_NVIC_ITNS1_ITNS_BITS   _u(0xffffffff)
#define M33_NVIC_ITNS1_ITNS_MSB    _u(31)
#define M33_NVIC_ITNS1_ITNS_LSB    _u(0)
#define M33_NVIC_ITNS1_ITNS_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IPR0
// Description : Sets or reads interrupt priorities
#define M33_NVIC_IPR0_OFFSET _u(0x0000e400)
#define M33_NVIC_IPR0_BITS   _u(0xf0f0f0f0)
#define M33_NVIC_IPR0_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR0_PRI_N3
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+3,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR0_PRI_N3_RESET  _u(0x0)
#define M33_NVIC_IPR0_PRI_N3_BITS   _u(0xf0000000)
#define M33_NVIC_IPR0_PRI_N3_MSB    _u(31)
#define M33_NVIC_IPR0_PRI_N3_LSB    _u(28)
#define M33_NVIC_IPR0_PRI_N3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR0_PRI_N2
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+2,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR0_PRI_N2_RESET  _u(0x0)
#define M33_NVIC_IPR0_PRI_N2_BITS   _u(0x00f00000)
#define M33_NVIC_IPR0_PRI_N2_MSB    _u(23)
#define M33_NVIC_IPR0_PRI_N2_LSB    _u(20)
#define M33_NVIC_IPR0_PRI_N2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR0_PRI_N1
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+1,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR0_PRI_N1_RESET  _u(0x0)
#define M33_NVIC_IPR0_PRI_N1_BITS   _u(0x0000f000)
#define M33_NVIC_IPR0_PRI_N1_MSB    _u(15)
#define M33_NVIC_IPR0_PRI_N1_LSB    _u(12)
#define M33_NVIC_IPR0_PRI_N1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR0_PRI_N0
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+0,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR0_PRI_N0_RESET  _u(0x0)
#define M33_NVIC_IPR0_PRI_N0_BITS   _u(0x000000f0)
#define M33_NVIC_IPR0_PRI_N0_MSB    _u(7)
#define M33_NVIC_IPR0_PRI_N0_LSB    _u(4)
#define M33_NVIC_IPR0_PRI_N0_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IPR1
// Description : Sets or reads interrupt priorities
#define M33_NVIC_IPR1_OFFSET _u(0x0000e404)
#define M33_NVIC_IPR1_BITS   _u(0xf0f0f0f0)
#define M33_NVIC_IPR1_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR1_PRI_N3
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+3,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR1_PRI_N3_RESET  _u(0x0)
#define M33_NVIC_IPR1_PRI_N3_BITS   _u(0xf0000000)
#define M33_NVIC_IPR1_PRI_N3_MSB    _u(31)
#define M33_NVIC_IPR1_PRI_N3_LSB    _u(28)
#define M33_NVIC_IPR1_PRI_N3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR1_PRI_N2
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+2,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR1_PRI_N2_RESET  _u(0x0)
#define M33_NVIC_IPR1_PRI_N2_BITS   _u(0x00f00000)
#define M33_NVIC_IPR1_PRI_N2_MSB    _u(23)
#define M33_NVIC_IPR1_PRI_N2_LSB    _u(20)
#define M33_NVIC_IPR1_PRI_N2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR1_PRI_N1
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+1,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR1_PRI_N1_RESET  _u(0x0)
#define M33_NVIC_IPR1_PRI_N1_BITS   _u(0x0000f000)
#define M33_NVIC_IPR1_PRI_N1_MSB    _u(15)
#define M33_NVIC_IPR1_PRI_N1_LSB    _u(12)
#define M33_NVIC_IPR1_PRI_N1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR1_PRI_N0
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+0,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR1_PRI_N0_RESET  _u(0x0)
#define M33_NVIC_IPR1_PRI_N0_BITS   _u(0x000000f0)
#define M33_NVIC_IPR1_PRI_N0_MSB    _u(7)
#define M33_NVIC_IPR1_PRI_N0_LSB    _u(4)
#define M33_NVIC_IPR1_PRI_N0_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IPR2
// Description : Sets or reads interrupt priorities
#define M33_NVIC_IPR2_OFFSET _u(0x0000e408)
#define M33_NVIC_IPR2_BITS   _u(0xf0f0f0f0)
#define M33_NVIC_IPR2_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR2_PRI_N3
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+3,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR2_PRI_N3_RESET  _u(0x0)
#define M33_NVIC_IPR2_PRI_N3_BITS   _u(0xf0000000)
#define M33_NVIC_IPR2_PRI_N3_MSB    _u(31)
#define M33_NVIC_IPR2_PRI_N3_LSB    _u(28)
#define M33_NVIC_IPR2_PRI_N3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR2_PRI_N2
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+2,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR2_PRI_N2_RESET  _u(0x0)
#define M33_NVIC_IPR2_PRI_N2_BITS   _u(0x00f00000)
#define M33_NVIC_IPR2_PRI_N2_MSB    _u(23)
#define M33_NVIC_IPR2_PRI_N2_LSB    _u(20)
#define M33_NVIC_IPR2_PRI_N2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR2_PRI_N1
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+1,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR2_PRI_N1_RESET  _u(0x0)
#define M33_NVIC_IPR2_PRI_N1_BITS   _u(0x0000f000)
#define M33_NVIC_IPR2_PRI_N1_MSB    _u(15)
#define M33_NVIC_IPR2_PRI_N1_LSB    _u(12)
#define M33_NVIC_IPR2_PRI_N1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR2_PRI_N0
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+0,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR2_PRI_N0_RESET  _u(0x0)
#define M33_NVIC_IPR2_PRI_N0_BITS   _u(0x000000f0)
#define M33_NVIC_IPR2_PRI_N0_MSB    _u(7)
#define M33_NVIC_IPR2_PRI_N0_LSB    _u(4)
#define M33_NVIC_IPR2_PRI_N0_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IPR3
// Description : Sets or reads interrupt priorities
#define M33_NVIC_IPR3_OFFSET _u(0x0000e40c)
#define M33_NVIC_IPR3_BITS   _u(0xf0f0f0f0)
#define M33_NVIC_IPR3_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR3_PRI_N3
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+3,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR3_PRI_N3_RESET  _u(0x0)
#define M33_NVIC_IPR3_PRI_N3_BITS   _u(0xf0000000)
#define M33_NVIC_IPR3_PRI_N3_MSB    _u(31)
#define M33_NVIC_IPR3_PRI_N3_LSB    _u(28)
#define M33_NVIC_IPR3_PRI_N3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR3_PRI_N2
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+2,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR3_PRI_N2_RESET  _u(0x0)
#define M33_NVIC_IPR3_PRI_N2_BITS   _u(0x00f00000)
#define M33_NVIC_IPR3_PRI_N2_MSB    _u(23)
#define M33_NVIC_IPR3_PRI_N2_LSB    _u(20)
#define M33_NVIC_IPR3_PRI_N2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR3_PRI_N1
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+1,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR3_PRI_N1_RESET  _u(0x0)
#define M33_NVIC_IPR3_PRI_N1_BITS   _u(0x0000f000)
#define M33_NVIC_IPR3_PRI_N1_MSB    _u(15)
#define M33_NVIC_IPR3_PRI_N1_LSB    _u(12)
#define M33_NVIC_IPR3_PRI_N1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR3_PRI_N0
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+0,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR3_PRI_N0_RESET  _u(0x0)
#define M33_NVIC_IPR3_PRI_N0_BITS   _u(0x000000f0)
#define M33_NVIC_IPR3_PRI_N0_MSB    _u(7)
#define M33_NVIC_IPR3_PRI_N0_LSB    _u(4)
#define M33_NVIC_IPR3_PRI_N0_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IPR4
// Description : Sets or reads interrupt priorities
#define M33_NVIC_IPR4_OFFSET _u(0x0000e410)
#define M33_NVIC_IPR4_BITS   _u(0xf0f0f0f0)
#define M33_NVIC_IPR4_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR4_PRI_N3
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+3,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR4_PRI_N3_RESET  _u(0x0)
#define M33_NVIC_IPR4_PRI_N3_BITS   _u(0xf0000000)
#define M33_NVIC_IPR4_PRI_N3_MSB    _u(31)
#define M33_NVIC_IPR4_PRI_N3_LSB    _u(28)
#define M33_NVIC_IPR4_PRI_N3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR4_PRI_N2
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+2,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR4_PRI_N2_RESET  _u(0x0)
#define M33_NVIC_IPR4_PRI_N2_BITS   _u(0x00f00000)
#define M33_NVIC_IPR4_PRI_N2_MSB    _u(23)
#define M33_NVIC_IPR4_PRI_N2_LSB    _u(20)
#define M33_NVIC_IPR4_PRI_N2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR4_PRI_N1
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+1,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR4_PRI_N1_RESET  _u(0x0)
#define M33_NVIC_IPR4_PRI_N1_BITS   _u(0x0000f000)
#define M33_NVIC_IPR4_PRI_N1_MSB    _u(15)
#define M33_NVIC_IPR4_PRI_N1_LSB    _u(12)
#define M33_NVIC_IPR4_PRI_N1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR4_PRI_N0
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+0,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR4_PRI_N0_RESET  _u(0x0)
#define M33_NVIC_IPR4_PRI_N0_BITS   _u(0x000000f0)
#define M33_NVIC_IPR4_PRI_N0_MSB    _u(7)
#define M33_NVIC_IPR4_PRI_N0_LSB    _u(4)
#define M33_NVIC_IPR4_PRI_N0_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IPR5
// Description : Sets or reads interrupt priorities
#define M33_NVIC_IPR5_OFFSET _u(0x0000e414)
#define M33_NVIC_IPR5_BITS   _u(0xf0f0f0f0)
#define M33_NVIC_IPR5_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR5_PRI_N3
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+3,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR5_PRI_N3_RESET  _u(0x0)
#define M33_NVIC_IPR5_PRI_N3_BITS   _u(0xf0000000)
#define M33_NVIC_IPR5_PRI_N3_MSB    _u(31)
#define M33_NVIC_IPR5_PRI_N3_LSB    _u(28)
#define M33_NVIC_IPR5_PRI_N3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR5_PRI_N2
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+2,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR5_PRI_N2_RESET  _u(0x0)
#define M33_NVIC_IPR5_PRI_N2_BITS   _u(0x00f00000)
#define M33_NVIC_IPR5_PRI_N2_MSB    _u(23)
#define M33_NVIC_IPR5_PRI_N2_LSB    _u(20)
#define M33_NVIC_IPR5_PRI_N2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR5_PRI_N1
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+1,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR5_PRI_N1_RESET  _u(0x0)
#define M33_NVIC_IPR5_PRI_N1_BITS   _u(0x0000f000)
#define M33_NVIC_IPR5_PRI_N1_MSB    _u(15)
#define M33_NVIC_IPR5_PRI_N1_LSB    _u(12)
#define M33_NVIC_IPR5_PRI_N1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR5_PRI_N0
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+0,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR5_PRI_N0_RESET  _u(0x0)
#define M33_NVIC_IPR5_PRI_N0_BITS   _u(0x000000f0)
#define M33_NVIC_IPR5_PRI_N0_MSB    _u(7)
#define M33_NVIC_IPR5_PRI_N0_LSB    _u(4)
#define M33_NVIC_IPR5_PRI_N0_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IPR6
// Description : Sets or reads interrupt priorities
#define M33_NVIC_IPR6_OFFSET _u(0x0000e418)
#define M33_NVIC_IPR6_BITS   _u(0xf0f0f0f0)
#define M33_NVIC_IPR6_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR6_PRI_N3
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+3,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR6_PRI_N3_RESET  _u(0x0)
#define M33_NVIC_IPR6_PRI_N3_BITS   _u(0xf0000000)
#define M33_NVIC_IPR6_PRI_N3_MSB    _u(31)
#define M33_NVIC_IPR6_PRI_N3_LSB    _u(28)
#define M33_NVIC_IPR6_PRI_N3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR6_PRI_N2
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+2,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR6_PRI_N2_RESET  _u(0x0)
#define M33_NVIC_IPR6_PRI_N2_BITS   _u(0x00f00000)
#define M33_NVIC_IPR6_PRI_N2_MSB    _u(23)
#define M33_NVIC_IPR6_PRI_N2_LSB    _u(20)
#define M33_NVIC_IPR6_PRI_N2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR6_PRI_N1
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+1,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR6_PRI_N1_RESET  _u(0x0)
#define M33_NVIC_IPR6_PRI_N1_BITS   _u(0x0000f000)
#define M33_NVIC_IPR6_PRI_N1_MSB    _u(15)
#define M33_NVIC_IPR6_PRI_N1_LSB    _u(12)
#define M33_NVIC_IPR6_PRI_N1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR6_PRI_N0
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+0,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR6_PRI_N0_RESET  _u(0x0)
#define M33_NVIC_IPR6_PRI_N0_BITS   _u(0x000000f0)
#define M33_NVIC_IPR6_PRI_N0_MSB    _u(7)
#define M33_NVIC_IPR6_PRI_N0_LSB    _u(4)
#define M33_NVIC_IPR6_PRI_N0_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IPR7
// Description : Sets or reads interrupt priorities
#define M33_NVIC_IPR7_OFFSET _u(0x0000e41c)
#define M33_NVIC_IPR7_BITS   _u(0xf0f0f0f0)
#define M33_NVIC_IPR7_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR7_PRI_N3
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+3,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR7_PRI_N3_RESET  _u(0x0)
#define M33_NVIC_IPR7_PRI_N3_BITS   _u(0xf0000000)
#define M33_NVIC_IPR7_PRI_N3_MSB    _u(31)
#define M33_NVIC_IPR7_PRI_N3_LSB    _u(28)
#define M33_NVIC_IPR7_PRI_N3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR7_PRI_N2
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+2,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR7_PRI_N2_RESET  _u(0x0)
#define M33_NVIC_IPR7_PRI_N2_BITS   _u(0x00f00000)
#define M33_NVIC_IPR7_PRI_N2_MSB    _u(23)
#define M33_NVIC_IPR7_PRI_N2_LSB    _u(20)
#define M33_NVIC_IPR7_PRI_N2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR7_PRI_N1
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+1,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR7_PRI_N1_RESET  _u(0x0)
#define M33_NVIC_IPR7_PRI_N1_BITS   _u(0x0000f000)
#define M33_NVIC_IPR7_PRI_N1_MSB    _u(15)
#define M33_NVIC_IPR7_PRI_N1_LSB    _u(12)
#define M33_NVIC_IPR7_PRI_N1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR7_PRI_N0
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+0,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR7_PRI_N0_RESET  _u(0x0)
#define M33_NVIC_IPR7_PRI_N0_BITS   _u(0x000000f0)
#define M33_NVIC_IPR7_PRI_N0_MSB    _u(7)
#define M33_NVIC_IPR7_PRI_N0_LSB    _u(4)
#define M33_NVIC_IPR7_PRI_N0_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IPR8
// Description : Sets or reads interrupt priorities
#define M33_NVIC_IPR8_OFFSET _u(0x0000e420)
#define M33_NVIC_IPR8_BITS   _u(0xf0f0f0f0)
#define M33_NVIC_IPR8_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR8_PRI_N3
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+3,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR8_PRI_N3_RESET  _u(0x0)
#define M33_NVIC_IPR8_PRI_N3_BITS   _u(0xf0000000)
#define M33_NVIC_IPR8_PRI_N3_MSB    _u(31)
#define M33_NVIC_IPR8_PRI_N3_LSB    _u(28)
#define M33_NVIC_IPR8_PRI_N3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR8_PRI_N2
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+2,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR8_PRI_N2_RESET  _u(0x0)
#define M33_NVIC_IPR8_PRI_N2_BITS   _u(0x00f00000)
#define M33_NVIC_IPR8_PRI_N2_MSB    _u(23)
#define M33_NVIC_IPR8_PRI_N2_LSB    _u(20)
#define M33_NVIC_IPR8_PRI_N2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR8_PRI_N1
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+1,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR8_PRI_N1_RESET  _u(0x0)
#define M33_NVIC_IPR8_PRI_N1_BITS   _u(0x0000f000)
#define M33_NVIC_IPR8_PRI_N1_MSB    _u(15)
#define M33_NVIC_IPR8_PRI_N1_LSB    _u(12)
#define M33_NVIC_IPR8_PRI_N1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR8_PRI_N0
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+0,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR8_PRI_N0_RESET  _u(0x0)
#define M33_NVIC_IPR8_PRI_N0_BITS   _u(0x000000f0)
#define M33_NVIC_IPR8_PRI_N0_MSB    _u(7)
#define M33_NVIC_IPR8_PRI_N0_LSB    _u(4)
#define M33_NVIC_IPR8_PRI_N0_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IPR9
// Description : Sets or reads interrupt priorities
#define M33_NVIC_IPR9_OFFSET _u(0x0000e424)
#define M33_NVIC_IPR9_BITS   _u(0xf0f0f0f0)
#define M33_NVIC_IPR9_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR9_PRI_N3
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+3,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR9_PRI_N3_RESET  _u(0x0)
#define M33_NVIC_IPR9_PRI_N3_BITS   _u(0xf0000000)
#define M33_NVIC_IPR9_PRI_N3_MSB    _u(31)
#define M33_NVIC_IPR9_PRI_N3_LSB    _u(28)
#define M33_NVIC_IPR9_PRI_N3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR9_PRI_N2
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+2,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR9_PRI_N2_RESET  _u(0x0)
#define M33_NVIC_IPR9_PRI_N2_BITS   _u(0x00f00000)
#define M33_NVIC_IPR9_PRI_N2_MSB    _u(23)
#define M33_NVIC_IPR9_PRI_N2_LSB    _u(20)
#define M33_NVIC_IPR9_PRI_N2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR9_PRI_N1
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+1,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR9_PRI_N1_RESET  _u(0x0)
#define M33_NVIC_IPR9_PRI_N1_BITS   _u(0x0000f000)
#define M33_NVIC_IPR9_PRI_N1_MSB    _u(15)
#define M33_NVIC_IPR9_PRI_N1_LSB    _u(12)
#define M33_NVIC_IPR9_PRI_N1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR9_PRI_N0
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+0,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR9_PRI_N0_RESET  _u(0x0)
#define M33_NVIC_IPR9_PRI_N0_BITS   _u(0x000000f0)
#define M33_NVIC_IPR9_PRI_N0_MSB    _u(7)
#define M33_NVIC_IPR9_PRI_N0_LSB    _u(4)
#define M33_NVIC_IPR9_PRI_N0_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IPR10
// Description : Sets or reads interrupt priorities
#define M33_NVIC_IPR10_OFFSET _u(0x0000e428)
#define M33_NVIC_IPR10_BITS   _u(0xf0f0f0f0)
#define M33_NVIC_IPR10_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR10_PRI_N3
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+3,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR10_PRI_N3_RESET  _u(0x0)
#define M33_NVIC_IPR10_PRI_N3_BITS   _u(0xf0000000)
#define M33_NVIC_IPR10_PRI_N3_MSB    _u(31)
#define M33_NVIC_IPR10_PRI_N3_LSB    _u(28)
#define M33_NVIC_IPR10_PRI_N3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR10_PRI_N2
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+2,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR10_PRI_N2_RESET  _u(0x0)
#define M33_NVIC_IPR10_PRI_N2_BITS   _u(0x00f00000)
#define M33_NVIC_IPR10_PRI_N2_MSB    _u(23)
#define M33_NVIC_IPR10_PRI_N2_LSB    _u(20)
#define M33_NVIC_IPR10_PRI_N2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR10_PRI_N1
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+1,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR10_PRI_N1_RESET  _u(0x0)
#define M33_NVIC_IPR10_PRI_N1_BITS   _u(0x0000f000)
#define M33_NVIC_IPR10_PRI_N1_MSB    _u(15)
#define M33_NVIC_IPR10_PRI_N1_LSB    _u(12)
#define M33_NVIC_IPR10_PRI_N1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR10_PRI_N0
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+0,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR10_PRI_N0_RESET  _u(0x0)
#define M33_NVIC_IPR10_PRI_N0_BITS   _u(0x000000f0)
#define M33_NVIC_IPR10_PRI_N0_MSB    _u(7)
#define M33_NVIC_IPR10_PRI_N0_LSB    _u(4)
#define M33_NVIC_IPR10_PRI_N0_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IPR11
// Description : Sets or reads interrupt priorities
#define M33_NVIC_IPR11_OFFSET _u(0x0000e42c)
#define M33_NVIC_IPR11_BITS   _u(0xf0f0f0f0)
#define M33_NVIC_IPR11_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR11_PRI_N3
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+3,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR11_PRI_N3_RESET  _u(0x0)
#define M33_NVIC_IPR11_PRI_N3_BITS   _u(0xf0000000)
#define M33_NVIC_IPR11_PRI_N3_MSB    _u(31)
#define M33_NVIC_IPR11_PRI_N3_LSB    _u(28)
#define M33_NVIC_IPR11_PRI_N3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR11_PRI_N2
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+2,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR11_PRI_N2_RESET  _u(0x0)
#define M33_NVIC_IPR11_PRI_N2_BITS   _u(0x00f00000)
#define M33_NVIC_IPR11_PRI_N2_MSB    _u(23)
#define M33_NVIC_IPR11_PRI_N2_LSB    _u(20)
#define M33_NVIC_IPR11_PRI_N2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR11_PRI_N1
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+1,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR11_PRI_N1_RESET  _u(0x0)
#define M33_NVIC_IPR11_PRI_N1_BITS   _u(0x0000f000)
#define M33_NVIC_IPR11_PRI_N1_MSB    _u(15)
#define M33_NVIC_IPR11_PRI_N1_LSB    _u(12)
#define M33_NVIC_IPR11_PRI_N1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR11_PRI_N0
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+0,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR11_PRI_N0_RESET  _u(0x0)
#define M33_NVIC_IPR11_PRI_N0_BITS   _u(0x000000f0)
#define M33_NVIC_IPR11_PRI_N0_MSB    _u(7)
#define M33_NVIC_IPR11_PRI_N0_LSB    _u(4)
#define M33_NVIC_IPR11_PRI_N0_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IPR12
// Description : Sets or reads interrupt priorities
#define M33_NVIC_IPR12_OFFSET _u(0x0000e430)
#define M33_NVIC_IPR12_BITS   _u(0xf0f0f0f0)
#define M33_NVIC_IPR12_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR12_PRI_N3
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+3,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR12_PRI_N3_RESET  _u(0x0)
#define M33_NVIC_IPR12_PRI_N3_BITS   _u(0xf0000000)
#define M33_NVIC_IPR12_PRI_N3_MSB    _u(31)
#define M33_NVIC_IPR12_PRI_N3_LSB    _u(28)
#define M33_NVIC_IPR12_PRI_N3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR12_PRI_N2
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+2,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR12_PRI_N2_RESET  _u(0x0)
#define M33_NVIC_IPR12_PRI_N2_BITS   _u(0x00f00000)
#define M33_NVIC_IPR12_PRI_N2_MSB    _u(23)
#define M33_NVIC_IPR12_PRI_N2_LSB    _u(20)
#define M33_NVIC_IPR12_PRI_N2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR12_PRI_N1
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+1,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR12_PRI_N1_RESET  _u(0x0)
#define M33_NVIC_IPR12_PRI_N1_BITS   _u(0x0000f000)
#define M33_NVIC_IPR12_PRI_N1_MSB    _u(15)
#define M33_NVIC_IPR12_PRI_N1_LSB    _u(12)
#define M33_NVIC_IPR12_PRI_N1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR12_PRI_N0
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+0,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR12_PRI_N0_RESET  _u(0x0)
#define M33_NVIC_IPR12_PRI_N0_BITS   _u(0x000000f0)
#define M33_NVIC_IPR12_PRI_N0_MSB    _u(7)
#define M33_NVIC_IPR12_PRI_N0_LSB    _u(4)
#define M33_NVIC_IPR12_PRI_N0_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IPR13
// Description : Sets or reads interrupt priorities
#define M33_NVIC_IPR13_OFFSET _u(0x0000e434)
#define M33_NVIC_IPR13_BITS   _u(0xf0f0f0f0)
#define M33_NVIC_IPR13_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR13_PRI_N3
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+3,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR13_PRI_N3_RESET  _u(0x0)
#define M33_NVIC_IPR13_PRI_N3_BITS   _u(0xf0000000)
#define M33_NVIC_IPR13_PRI_N3_MSB    _u(31)
#define M33_NVIC_IPR13_PRI_N3_LSB    _u(28)
#define M33_NVIC_IPR13_PRI_N3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR13_PRI_N2
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+2,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR13_PRI_N2_RESET  _u(0x0)
#define M33_NVIC_IPR13_PRI_N2_BITS   _u(0x00f00000)
#define M33_NVIC_IPR13_PRI_N2_MSB    _u(23)
#define M33_NVIC_IPR13_PRI_N2_LSB    _u(20)
#define M33_NVIC_IPR13_PRI_N2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR13_PRI_N1
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+1,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR13_PRI_N1_RESET  _u(0x0)
#define M33_NVIC_IPR13_PRI_N1_BITS   _u(0x0000f000)
#define M33_NVIC_IPR13_PRI_N1_MSB    _u(15)
#define M33_NVIC_IPR13_PRI_N1_LSB    _u(12)
#define M33_NVIC_IPR13_PRI_N1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR13_PRI_N0
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+0,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR13_PRI_N0_RESET  _u(0x0)
#define M33_NVIC_IPR13_PRI_N0_BITS   _u(0x000000f0)
#define M33_NVIC_IPR13_PRI_N0_MSB    _u(7)
#define M33_NVIC_IPR13_PRI_N0_LSB    _u(4)
#define M33_NVIC_IPR13_PRI_N0_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IPR14
// Description : Sets or reads interrupt priorities
#define M33_NVIC_IPR14_OFFSET _u(0x0000e438)
#define M33_NVIC_IPR14_BITS   _u(0xf0f0f0f0)
#define M33_NVIC_IPR14_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR14_PRI_N3
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+3,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR14_PRI_N3_RESET  _u(0x0)
#define M33_NVIC_IPR14_PRI_N3_BITS   _u(0xf0000000)
#define M33_NVIC_IPR14_PRI_N3_MSB    _u(31)
#define M33_NVIC_IPR14_PRI_N3_LSB    _u(28)
#define M33_NVIC_IPR14_PRI_N3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR14_PRI_N2
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+2,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR14_PRI_N2_RESET  _u(0x0)
#define M33_NVIC_IPR14_PRI_N2_BITS   _u(0x00f00000)
#define M33_NVIC_IPR14_PRI_N2_MSB    _u(23)
#define M33_NVIC_IPR14_PRI_N2_LSB    _u(20)
#define M33_NVIC_IPR14_PRI_N2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR14_PRI_N1
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+1,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR14_PRI_N1_RESET  _u(0x0)
#define M33_NVIC_IPR14_PRI_N1_BITS   _u(0x0000f000)
#define M33_NVIC_IPR14_PRI_N1_MSB    _u(15)
#define M33_NVIC_IPR14_PRI_N1_LSB    _u(12)
#define M33_NVIC_IPR14_PRI_N1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR14_PRI_N0
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+0,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR14_PRI_N0_RESET  _u(0x0)
#define M33_NVIC_IPR14_PRI_N0_BITS   _u(0x000000f0)
#define M33_NVIC_IPR14_PRI_N0_MSB    _u(7)
#define M33_NVIC_IPR14_PRI_N0_LSB    _u(4)
#define M33_NVIC_IPR14_PRI_N0_ACCESS "RW"
// =============================================================================
// Register    : M33_NVIC_IPR15
// Description : Sets or reads interrupt priorities
#define M33_NVIC_IPR15_OFFSET _u(0x0000e43c)
#define M33_NVIC_IPR15_BITS   _u(0xf0f0f0f0)
#define M33_NVIC_IPR15_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR15_PRI_N3
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+3,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR15_PRI_N3_RESET  _u(0x0)
#define M33_NVIC_IPR15_PRI_N3_BITS   _u(0xf0000000)
#define M33_NVIC_IPR15_PRI_N3_MSB    _u(31)
#define M33_NVIC_IPR15_PRI_N3_LSB    _u(28)
#define M33_NVIC_IPR15_PRI_N3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR15_PRI_N2
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+2,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR15_PRI_N2_RESET  _u(0x0)
#define M33_NVIC_IPR15_PRI_N2_BITS   _u(0x00f00000)
#define M33_NVIC_IPR15_PRI_N2_MSB    _u(23)
#define M33_NVIC_IPR15_PRI_N2_LSB    _u(20)
#define M33_NVIC_IPR15_PRI_N2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR15_PRI_N1
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+1,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR15_PRI_N1_RESET  _u(0x0)
#define M33_NVIC_IPR15_PRI_N1_BITS   _u(0x0000f000)
#define M33_NVIC_IPR15_PRI_N1_MSB    _u(15)
#define M33_NVIC_IPR15_PRI_N1_LSB    _u(12)
#define M33_NVIC_IPR15_PRI_N1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NVIC_IPR15_PRI_N0
// Description : For register NVIC_IPRn, the priority of interrupt number 4*n+0,
//               or RES0 if the PE does not implement this interrupt
#define M33_NVIC_IPR15_PRI_N0_RESET  _u(0x0)
#define M33_NVIC_IPR15_PRI_N0_BITS   _u(0x000000f0)
#define M33_NVIC_IPR15_PRI_N0_MSB    _u(7)
#define M33_NVIC_IPR15_PRI_N0_LSB    _u(4)
#define M33_NVIC_IPR15_PRI_N0_ACCESS "RW"
// =============================================================================
// Register    : M33_CPUID
// Description : Provides identification information for the PE, including an
//               implementer code for the device and a device ID number
#define M33_CPUID_OFFSET _u(0x0000ed00)
#define M33_CPUID_BITS   _u(0xffffffff)
#define M33_CPUID_RESET  _u(0x411fd210)
// -----------------------------------------------------------------------------
// Field       : M33_CPUID_IMPLEMENTER
// Description : This field must hold an implementer code that has been assigned
//               by ARM
#define M33_CPUID_IMPLEMENTER_RESET  _u(0x41)
#define M33_CPUID_IMPLEMENTER_BITS   _u(0xff000000)
#define M33_CPUID_IMPLEMENTER_MSB    _u(31)
#define M33_CPUID_IMPLEMENTER_LSB    _u(24)
#define M33_CPUID_IMPLEMENTER_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_CPUID_VARIANT
// Description : IMPLEMENTATION DEFINED variant number. Typically, this field is
//               used to distinguish between different product variants, or
//               major revisions of a product
#define M33_CPUID_VARIANT_RESET  _u(0x1)
#define M33_CPUID_VARIANT_BITS   _u(0x00f00000)
#define M33_CPUID_VARIANT_MSB    _u(23)
#define M33_CPUID_VARIANT_LSB    _u(20)
#define M33_CPUID_VARIANT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_CPUID_ARCHITECTURE
// Description : Defines the Architecture implemented by the PE
#define M33_CPUID_ARCHITECTURE_RESET  _u(0xf)
#define M33_CPUID_ARCHITECTURE_BITS   _u(0x000f0000)
#define M33_CPUID_ARCHITECTURE_MSB    _u(19)
#define M33_CPUID_ARCHITECTURE_LSB    _u(16)
#define M33_CPUID_ARCHITECTURE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_CPUID_PARTNO
// Description : IMPLEMENTATION DEFINED primary part number for the device
#define M33_CPUID_PARTNO_RESET  _u(0xd21)
#define M33_CPUID_PARTNO_BITS   _u(0x0000fff0)
#define M33_CPUID_PARTNO_MSB    _u(15)
#define M33_CPUID_PARTNO_LSB    _u(4)
#define M33_CPUID_PARTNO_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_CPUID_REVISION
// Description : IMPLEMENTATION DEFINED revision number for the device
#define M33_CPUID_REVISION_RESET  _u(0x0)
#define M33_CPUID_REVISION_BITS   _u(0x0000000f)
#define M33_CPUID_REVISION_MSB    _u(3)
#define M33_CPUID_REVISION_LSB    _u(0)
#define M33_CPUID_REVISION_ACCESS "RO"
// =============================================================================
// Register    : M33_ICSR
// Description : Controls and provides status information for NMI, PendSV,
//               SysTick and interrupts
#define M33_ICSR_OFFSET _u(0x0000ed04)
#define M33_ICSR_BITS   _u(0xdfdff9ff)
#define M33_ICSR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ICSR_PENDNMISET
// Description : Indicates whether the NMI exception is pending
#define M33_ICSR_PENDNMISET_RESET  _u(0x0)
#define M33_ICSR_PENDNMISET_BITS   _u(0x80000000)
#define M33_ICSR_PENDNMISET_MSB    _u(31)
#define M33_ICSR_PENDNMISET_LSB    _u(31)
#define M33_ICSR_PENDNMISET_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ICSR_PENDNMICLR
// Description : Allows the NMI exception pend state to be cleared
#define M33_ICSR_PENDNMICLR_RESET  _u(0x0)
#define M33_ICSR_PENDNMICLR_BITS   _u(0x40000000)
#define M33_ICSR_PENDNMICLR_MSB    _u(30)
#define M33_ICSR_PENDNMICLR_LSB    _u(30)
#define M33_ICSR_PENDNMICLR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_ICSR_PENDSVSET
// Description : Indicates whether the PendSV `FTSSS exception is pending
#define M33_ICSR_PENDSVSET_RESET  _u(0x0)
#define M33_ICSR_PENDSVSET_BITS   _u(0x10000000)
#define M33_ICSR_PENDSVSET_MSB    _u(28)
#define M33_ICSR_PENDSVSET_LSB    _u(28)
#define M33_ICSR_PENDSVSET_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ICSR_PENDSVCLR
// Description : Allows the PendSV exception pend state to be cleared `FTSSS
#define M33_ICSR_PENDSVCLR_RESET  _u(0x0)
#define M33_ICSR_PENDSVCLR_BITS   _u(0x08000000)
#define M33_ICSR_PENDSVCLR_MSB    _u(27)
#define M33_ICSR_PENDSVCLR_LSB    _u(27)
#define M33_ICSR_PENDSVCLR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_ICSR_PENDSTSET
// Description : Indicates whether the SysTick `FTSSS exception is pending
#define M33_ICSR_PENDSTSET_RESET  _u(0x0)
#define M33_ICSR_PENDSTSET_BITS   _u(0x04000000)
#define M33_ICSR_PENDSTSET_MSB    _u(26)
#define M33_ICSR_PENDSTSET_LSB    _u(26)
#define M33_ICSR_PENDSTSET_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ICSR_PENDSTCLR
// Description : Allows the SysTick exception pend state to be cleared `FTSSS
#define M33_ICSR_PENDSTCLR_RESET  _u(0x0)
#define M33_ICSR_PENDSTCLR_BITS   _u(0x02000000)
#define M33_ICSR_PENDSTCLR_MSB    _u(25)
#define M33_ICSR_PENDSTCLR_LSB    _u(25)
#define M33_ICSR_PENDSTCLR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_ICSR_STTNS
// Description : Controls whether in a single SysTick implementation, the
//               SysTick is Secure or Non-secure
#define M33_ICSR_STTNS_RESET  _u(0x0)
#define M33_ICSR_STTNS_BITS   _u(0x01000000)
#define M33_ICSR_STTNS_MSB    _u(24)
#define M33_ICSR_STTNS_LSB    _u(24)
#define M33_ICSR_STTNS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_ICSR_ISRPREEMPT
// Description : Indicates whether a pending exception will be serviced on exit
//               from debug halt state
#define M33_ICSR_ISRPREEMPT_RESET  _u(0x0)
#define M33_ICSR_ISRPREEMPT_BITS   _u(0x00800000)
#define M33_ICSR_ISRPREEMPT_MSB    _u(23)
#define M33_ICSR_ISRPREEMPT_LSB    _u(23)
#define M33_ICSR_ISRPREEMPT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ICSR_ISRPENDING
// Description : Indicates whether an external interrupt, generated by the NVIC,
//               is pending
#define M33_ICSR_ISRPENDING_RESET  _u(0x0)
#define M33_ICSR_ISRPENDING_BITS   _u(0x00400000)
#define M33_ICSR_ISRPENDING_MSB    _u(22)
#define M33_ICSR_ISRPENDING_LSB    _u(22)
#define M33_ICSR_ISRPENDING_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ICSR_VECTPENDING
// Description : The exception number of the highest priority pending and
//               enabled interrupt
#define M33_ICSR_VECTPENDING_RESET  _u(0x000)
#define M33_ICSR_VECTPENDING_BITS   _u(0x001ff000)
#define M33_ICSR_VECTPENDING_MSB    _u(20)
#define M33_ICSR_VECTPENDING_LSB    _u(12)
#define M33_ICSR_VECTPENDING_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ICSR_RETTOBASE
// Description : In Handler mode, indicates whether there is more than one
//               active exception
#define M33_ICSR_RETTOBASE_RESET  _u(0x0)
#define M33_ICSR_RETTOBASE_BITS   _u(0x00000800)
#define M33_ICSR_RETTOBASE_MSB    _u(11)
#define M33_ICSR_RETTOBASE_LSB    _u(11)
#define M33_ICSR_RETTOBASE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ICSR_VECTACTIVE
// Description : The exception number of the current executing exception
#define M33_ICSR_VECTACTIVE_RESET  _u(0x000)
#define M33_ICSR_VECTACTIVE_BITS   _u(0x000001ff)
#define M33_ICSR_VECTACTIVE_MSB    _u(8)
#define M33_ICSR_VECTACTIVE_LSB    _u(0)
#define M33_ICSR_VECTACTIVE_ACCESS "RO"
// =============================================================================
// Register    : M33_VTOR
// Description : The VTOR indicates the offset of the vector table base address
//               from memory address 0x00000000.
#define M33_VTOR_OFFSET _u(0x0000ed08)
#define M33_VTOR_BITS   _u(0xffffff80)
#define M33_VTOR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_VTOR_TBLOFF
// Description : Vector table base offset field. It contains bits[31:7] of the
//               offset of the table base from the bottom of the memory map.
#define M33_VTOR_TBLOFF_RESET  _u(0x0000000)
#define M33_VTOR_TBLOFF_BITS   _u(0xffffff80)
#define M33_VTOR_TBLOFF_MSB    _u(31)
#define M33_VTOR_TBLOFF_LSB    _u(7)
#define M33_VTOR_TBLOFF_ACCESS "RW"
// =============================================================================
// Register    : M33_AIRCR
// Description : Use the Application Interrupt and Reset Control Register to:
//               determine data endianness, clear all active state information
//               from debug halt mode, request a system reset.
#define M33_AIRCR_OFFSET _u(0x0000ed0c)
#define M33_AIRCR_BITS   _u(0xffffe70e)
#define M33_AIRCR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_AIRCR_VECTKEY
// Description : Register key:
//               Reads as Unknown
//               On writes, write 0x05FA to VECTKEY, otherwise the write is
//               ignored.
#define M33_AIRCR_VECTKEY_RESET  _u(0x0000)
#define M33_AIRCR_VECTKEY_BITS   _u(0xffff0000)
#define M33_AIRCR_VECTKEY_MSB    _u(31)
#define M33_AIRCR_VECTKEY_LSB    _u(16)
#define M33_AIRCR_VECTKEY_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_AIRCR_ENDIANESS
// Description : Data endianness implemented:
//               0 = Little-endian.
#define M33_AIRCR_ENDIANESS_RESET  _u(0x0)
#define M33_AIRCR_ENDIANESS_BITS   _u(0x00008000)
#define M33_AIRCR_ENDIANESS_MSB    _u(15)
#define M33_AIRCR_ENDIANESS_LSB    _u(15)
#define M33_AIRCR_ENDIANESS_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_AIRCR_PRIS
// Description : Prioritize Secure exceptions. The value of this bit defines
//               whether Secure exception priority boosting is enabled.
//               0	Priority ranges of Secure and Non-secure exceptions are
//               identical.
//               1	Non-secure exceptions are de-prioritized.
#define M33_AIRCR_PRIS_RESET  _u(0x0)
#define M33_AIRCR_PRIS_BITS   _u(0x00004000)
#define M33_AIRCR_PRIS_MSB    _u(14)
#define M33_AIRCR_PRIS_LSB    _u(14)
#define M33_AIRCR_PRIS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_AIRCR_BFHFNMINS
// Description : BusFault, HardFault, and NMI Non-secure enable.
//               0	BusFault, HardFault, and NMI are Secure.
//               1	BusFault and NMI are Non-secure and exceptions can target
//               Non-secure HardFault.
#define M33_AIRCR_BFHFNMINS_RESET  _u(0x0)
#define M33_AIRCR_BFHFNMINS_BITS   _u(0x00002000)
#define M33_AIRCR_BFHFNMINS_MSB    _u(13)
#define M33_AIRCR_BFHFNMINS_LSB    _u(13)
#define M33_AIRCR_BFHFNMINS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_AIRCR_PRIGROUP
// Description : Interrupt priority grouping field. This field determines the
//               split of group priority from subpriority.
//               See https://developer.arm.com/documentation/100235/0004/the-
//               cortex-m33-peripherals/system-control-block/application-
//               interrupt-and-reset-control-register?lang=en
#define M33_AIRCR_PRIGROUP_RESET  _u(0x0)
#define M33_AIRCR_PRIGROUP_BITS   _u(0x00000700)
#define M33_AIRCR_PRIGROUP_MSB    _u(10)
#define M33_AIRCR_PRIGROUP_LSB    _u(8)
#define M33_AIRCR_PRIGROUP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_AIRCR_SYSRESETREQS
// Description : System reset request, Secure state only.
//               0	SYSRESETREQ functionality is available to both Security
//               states.
//               1 SYSRESETREQ functionality is only available to Secure state.
#define M33_AIRCR_SYSRESETREQS_RESET  _u(0x0)
#define M33_AIRCR_SYSRESETREQS_BITS   _u(0x00000008)
#define M33_AIRCR_SYSRESETREQS_MSB    _u(3)
#define M33_AIRCR_SYSRESETREQS_LSB    _u(3)
#define M33_AIRCR_SYSRESETREQS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_AIRCR_SYSRESETREQ
// Description : Writing 1 to this bit causes the SYSRESETREQ signal to the
//               outer system to be asserted to request a reset. The intention
//               is to force a large system reset of all major components except
//               for debug. The C_HALT bit in the DHCSR is cleared as a result
//               of the system reset requested. The debugger does not lose
//               contact with the device.
#define M33_AIRCR_SYSRESETREQ_RESET  _u(0x0)
#define M33_AIRCR_SYSRESETREQ_BITS   _u(0x00000004)
#define M33_AIRCR_SYSRESETREQ_MSB    _u(2)
#define M33_AIRCR_SYSRESETREQ_LSB    _u(2)
#define M33_AIRCR_SYSRESETREQ_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_AIRCR_VECTCLRACTIVE
// Description : Clears all active state information for fixed and configurable
//               exceptions. This bit: is self-clearing, can only be set by the
//               DAP when the core is halted.  When set: clears all active
//               exception status of the processor, forces a return to Thread
//               mode, forces an IPSR of 0. A debugger must re-initialize the
//               stack.
#define M33_AIRCR_VECTCLRACTIVE_RESET  _u(0x0)
#define M33_AIRCR_VECTCLRACTIVE_BITS   _u(0x00000002)
#define M33_AIRCR_VECTCLRACTIVE_MSB    _u(1)
#define M33_AIRCR_VECTCLRACTIVE_LSB    _u(1)
#define M33_AIRCR_VECTCLRACTIVE_ACCESS "RW"
// =============================================================================
// Register    : M33_SCR
// Description : System Control Register. Use the System Control Register for
//               power-management functions: signal to the system when the
//               processor can enter a low power state, control how the
//               processor enters and exits low power states.
#define M33_SCR_OFFSET _u(0x0000ed10)
#define M33_SCR_BITS   _u(0x0000001e)
#define M33_SCR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_SCR_SEVONPEND
// Description : Send Event on Pending bit:
//               0 = Only enabled interrupts or events can wakeup the processor,
//               disabled interrupts are excluded.
//               1 = Enabled events and all interrupts, including disabled
//               interrupts, can wakeup the processor.
//               When an event or interrupt becomes pending, the event signal
//               wakes up the processor from WFE. If the
//               processor is not waiting for an event, the event is registered
//               and affects the next WFE.
//               The processor also wakes up on execution of an SEV instruction
//               or an external event.
#define M33_SCR_SEVONPEND_RESET  _u(0x0)
#define M33_SCR_SEVONPEND_BITS   _u(0x00000010)
#define M33_SCR_SEVONPEND_MSB    _u(4)
#define M33_SCR_SEVONPEND_LSB    _u(4)
#define M33_SCR_SEVONPEND_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SCR_SLEEPDEEPS
// Description : 0 SLEEPDEEP is available to both security states
//               1 SLEEPDEEP is only available to Secure state
#define M33_SCR_SLEEPDEEPS_RESET  _u(0x0)
#define M33_SCR_SLEEPDEEPS_BITS   _u(0x00000008)
#define M33_SCR_SLEEPDEEPS_MSB    _u(3)
#define M33_SCR_SLEEPDEEPS_LSB    _u(3)
#define M33_SCR_SLEEPDEEPS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SCR_SLEEPDEEP
// Description : Controls whether the processor uses sleep or deep sleep as its
//               low power mode:
//               0 = Sleep.
//               1 = Deep sleep.
#define M33_SCR_SLEEPDEEP_RESET  _u(0x0)
#define M33_SCR_SLEEPDEEP_BITS   _u(0x00000004)
#define M33_SCR_SLEEPDEEP_MSB    _u(2)
#define M33_SCR_SLEEPDEEP_LSB    _u(2)
#define M33_SCR_SLEEPDEEP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SCR_SLEEPONEXIT
// Description : Indicates sleep-on-exit when returning from Handler mode to
//               Thread mode:
//               0 = Do not sleep when returning to Thread mode.
//               1 = Enter sleep, or deep sleep, on return from an ISR to Thread
//               mode.
//               Setting this bit to 1 enables an interrupt driven application
//               to avoid returning to an empty main application.
#define M33_SCR_SLEEPONEXIT_RESET  _u(0x0)
#define M33_SCR_SLEEPONEXIT_BITS   _u(0x00000002)
#define M33_SCR_SLEEPONEXIT_MSB    _u(1)
#define M33_SCR_SLEEPONEXIT_LSB    _u(1)
#define M33_SCR_SLEEPONEXIT_ACCESS "RW"
// =============================================================================
// Register    : M33_CCR
// Description : Sets or returns configuration and control data
#define M33_CCR_OFFSET _u(0x0000ed14)
#define M33_CCR_BITS   _u(0x0007071b)
#define M33_CCR_RESET  _u(0x00000201)
// -----------------------------------------------------------------------------
// Field       : M33_CCR_BP
// Description : Enables program flow prediction `FTSSS
#define M33_CCR_BP_RESET  _u(0x0)
#define M33_CCR_BP_BITS   _u(0x00040000)
#define M33_CCR_BP_MSB    _u(18)
#define M33_CCR_BP_LSB    _u(18)
#define M33_CCR_BP_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_CCR_IC
// Description : This is a global enable bit for instruction caches in the
//               selected Security state
#define M33_CCR_IC_RESET  _u(0x0)
#define M33_CCR_IC_BITS   _u(0x00020000)
#define M33_CCR_IC_MSB    _u(17)
#define M33_CCR_IC_LSB    _u(17)
#define M33_CCR_IC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_CCR_DC
// Description : Enables data caching of all data accesses to Normal memory
//               `FTSSS
#define M33_CCR_DC_RESET  _u(0x0)
#define M33_CCR_DC_BITS   _u(0x00010000)
#define M33_CCR_DC_MSB    _u(16)
#define M33_CCR_DC_LSB    _u(16)
#define M33_CCR_DC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_CCR_STKOFHFNMIGN
// Description : Controls the effect of a stack limit violation while executing
//               at a requested priority less than 0
#define M33_CCR_STKOFHFNMIGN_RESET  _u(0x0)
#define M33_CCR_STKOFHFNMIGN_BITS   _u(0x00000400)
#define M33_CCR_STKOFHFNMIGN_MSB    _u(10)
#define M33_CCR_STKOFHFNMIGN_LSB    _u(10)
#define M33_CCR_STKOFHFNMIGN_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CCR_RES1
// Description : Reserved, RES1
#define M33_CCR_RES1_RESET  _u(0x1)
#define M33_CCR_RES1_BITS   _u(0x00000200)
#define M33_CCR_RES1_MSB    _u(9)
#define M33_CCR_RES1_LSB    _u(9)
#define M33_CCR_RES1_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_CCR_BFHFNMIGN
// Description : Determines the effect of precise BusFaults on handlers running
//               at a requested priority less than 0
#define M33_CCR_BFHFNMIGN_RESET  _u(0x0)
#define M33_CCR_BFHFNMIGN_BITS   _u(0x00000100)
#define M33_CCR_BFHFNMIGN_MSB    _u(8)
#define M33_CCR_BFHFNMIGN_LSB    _u(8)
#define M33_CCR_BFHFNMIGN_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CCR_DIV_0_TRP
// Description : Controls the generation of a DIVBYZERO UsageFault when
//               attempting to perform integer division by zero
#define M33_CCR_DIV_0_TRP_RESET  _u(0x0)
#define M33_CCR_DIV_0_TRP_BITS   _u(0x00000010)
#define M33_CCR_DIV_0_TRP_MSB    _u(4)
#define M33_CCR_DIV_0_TRP_LSB    _u(4)
#define M33_CCR_DIV_0_TRP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CCR_UNALIGN_TRP
// Description : Controls the trapping of unaligned word or halfword accesses
#define M33_CCR_UNALIGN_TRP_RESET  _u(0x0)
#define M33_CCR_UNALIGN_TRP_BITS   _u(0x00000008)
#define M33_CCR_UNALIGN_TRP_MSB    _u(3)
#define M33_CCR_UNALIGN_TRP_LSB    _u(3)
#define M33_CCR_UNALIGN_TRP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CCR_USERSETMPEND
// Description : Determines whether unprivileged accesses are permitted to pend
//               interrupts via the STIR
#define M33_CCR_USERSETMPEND_RESET  _u(0x0)
#define M33_CCR_USERSETMPEND_BITS   _u(0x00000002)
#define M33_CCR_USERSETMPEND_MSB    _u(1)
#define M33_CCR_USERSETMPEND_LSB    _u(1)
#define M33_CCR_USERSETMPEND_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CCR_RES1_1
// Description : Reserved, RES1
#define M33_CCR_RES1_1_RESET  _u(0x1)
#define M33_CCR_RES1_1_BITS   _u(0x00000001)
#define M33_CCR_RES1_1_MSB    _u(0)
#define M33_CCR_RES1_1_LSB    _u(0)
#define M33_CCR_RES1_1_ACCESS "RO"
// =============================================================================
// Register    : M33_SHPR1
// Description : Sets or returns priority for system handlers 4 - 7
#define M33_SHPR1_OFFSET _u(0x0000ed18)
#define M33_SHPR1_BITS   _u(0xe0e0e0e0)
#define M33_SHPR1_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_SHPR1_PRI_7_3
// Description : Priority of system handler 7, SecureFault
#define M33_SHPR1_PRI_7_3_RESET  _u(0x0)
#define M33_SHPR1_PRI_7_3_BITS   _u(0xe0000000)
#define M33_SHPR1_PRI_7_3_MSB    _u(31)
#define M33_SHPR1_PRI_7_3_LSB    _u(29)
#define M33_SHPR1_PRI_7_3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHPR1_PRI_6_3
// Description : Priority of system handler 6, SecureFault
#define M33_SHPR1_PRI_6_3_RESET  _u(0x0)
#define M33_SHPR1_PRI_6_3_BITS   _u(0x00e00000)
#define M33_SHPR1_PRI_6_3_MSB    _u(23)
#define M33_SHPR1_PRI_6_3_LSB    _u(21)
#define M33_SHPR1_PRI_6_3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHPR1_PRI_5_3
// Description : Priority of system handler 5, SecureFault
#define M33_SHPR1_PRI_5_3_RESET  _u(0x0)
#define M33_SHPR1_PRI_5_3_BITS   _u(0x0000e000)
#define M33_SHPR1_PRI_5_3_MSB    _u(15)
#define M33_SHPR1_PRI_5_3_LSB    _u(13)
#define M33_SHPR1_PRI_5_3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHPR1_PRI_4_3
// Description : Priority of system handler 4, SecureFault
#define M33_SHPR1_PRI_4_3_RESET  _u(0x0)
#define M33_SHPR1_PRI_4_3_BITS   _u(0x000000e0)
#define M33_SHPR1_PRI_4_3_MSB    _u(7)
#define M33_SHPR1_PRI_4_3_LSB    _u(5)
#define M33_SHPR1_PRI_4_3_ACCESS "RW"
// =============================================================================
// Register    : M33_SHPR2
// Description : Sets or returns priority for system handlers 8 - 11
#define M33_SHPR2_OFFSET _u(0x0000ed1c)
#define M33_SHPR2_BITS   _u(0xe0ffffff)
#define M33_SHPR2_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_SHPR2_PRI_11_3
// Description : Priority of system handler 11, SecureFault
#define M33_SHPR2_PRI_11_3_RESET  _u(0x0)
#define M33_SHPR2_PRI_11_3_BITS   _u(0xe0000000)
#define M33_SHPR2_PRI_11_3_MSB    _u(31)
#define M33_SHPR2_PRI_11_3_LSB    _u(29)
#define M33_SHPR2_PRI_11_3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHPR2_PRI_10
// Description : Reserved, RES0
#define M33_SHPR2_PRI_10_RESET  _u(0x00)
#define M33_SHPR2_PRI_10_BITS   _u(0x00ff0000)
#define M33_SHPR2_PRI_10_MSB    _u(23)
#define M33_SHPR2_PRI_10_LSB    _u(16)
#define M33_SHPR2_PRI_10_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_SHPR2_PRI_9
// Description : Reserved, RES0
#define M33_SHPR2_PRI_9_RESET  _u(0x00)
#define M33_SHPR2_PRI_9_BITS   _u(0x0000ff00)
#define M33_SHPR2_PRI_9_MSB    _u(15)
#define M33_SHPR2_PRI_9_LSB    _u(8)
#define M33_SHPR2_PRI_9_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_SHPR2_PRI_8
// Description : Reserved, RES0
#define M33_SHPR2_PRI_8_RESET  _u(0x00)
#define M33_SHPR2_PRI_8_BITS   _u(0x000000ff)
#define M33_SHPR2_PRI_8_MSB    _u(7)
#define M33_SHPR2_PRI_8_LSB    _u(0)
#define M33_SHPR2_PRI_8_ACCESS "RO"
// =============================================================================
// Register    : M33_SHPR3
// Description : Sets or returns priority for system handlers 12 - 15
#define M33_SHPR3_OFFSET _u(0x0000ed20)
#define M33_SHPR3_BITS   _u(0xe0e0ffe0)
#define M33_SHPR3_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_SHPR3_PRI_15_3
// Description : Priority of system handler 15, SecureFault
#define M33_SHPR3_PRI_15_3_RESET  _u(0x0)
#define M33_SHPR3_PRI_15_3_BITS   _u(0xe0000000)
#define M33_SHPR3_PRI_15_3_MSB    _u(31)
#define M33_SHPR3_PRI_15_3_LSB    _u(29)
#define M33_SHPR3_PRI_15_3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHPR3_PRI_14_3
// Description : Priority of system handler 14, SecureFault
#define M33_SHPR3_PRI_14_3_RESET  _u(0x0)
#define M33_SHPR3_PRI_14_3_BITS   _u(0x00e00000)
#define M33_SHPR3_PRI_14_3_MSB    _u(23)
#define M33_SHPR3_PRI_14_3_LSB    _u(21)
#define M33_SHPR3_PRI_14_3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHPR3_PRI_13
// Description : Reserved, RES0
#define M33_SHPR3_PRI_13_RESET  _u(0x00)
#define M33_SHPR3_PRI_13_BITS   _u(0x0000ff00)
#define M33_SHPR3_PRI_13_MSB    _u(15)
#define M33_SHPR3_PRI_13_LSB    _u(8)
#define M33_SHPR3_PRI_13_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_SHPR3_PRI_12_3
// Description : Priority of system handler 12, SecureFault
#define M33_SHPR3_PRI_12_3_RESET  _u(0x0)
#define M33_SHPR3_PRI_12_3_BITS   _u(0x000000e0)
#define M33_SHPR3_PRI_12_3_MSB    _u(7)
#define M33_SHPR3_PRI_12_3_LSB    _u(5)
#define M33_SHPR3_PRI_12_3_ACCESS "RW"
// =============================================================================
// Register    : M33_SHCSR
// Description : Provides access to the active and pending status of system
//               exceptions
#define M33_SHCSR_OFFSET _u(0x0000ed24)
#define M33_SHCSR_BITS   _u(0x003ffdbf)
#define M33_SHCSR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_HARDFAULTPENDED
// Description : `IAAMO the pending state of the HardFault exception `CTTSSS
#define M33_SHCSR_HARDFAULTPENDED_RESET  _u(0x0)
#define M33_SHCSR_HARDFAULTPENDED_BITS   _u(0x00200000)
#define M33_SHCSR_HARDFAULTPENDED_MSB    _u(21)
#define M33_SHCSR_HARDFAULTPENDED_LSB    _u(21)
#define M33_SHCSR_HARDFAULTPENDED_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_SECUREFAULTPENDED
// Description : `IAAMO the pending state of the SecureFault exception
#define M33_SHCSR_SECUREFAULTPENDED_RESET  _u(0x0)
#define M33_SHCSR_SECUREFAULTPENDED_BITS   _u(0x00100000)
#define M33_SHCSR_SECUREFAULTPENDED_MSB    _u(20)
#define M33_SHCSR_SECUREFAULTPENDED_LSB    _u(20)
#define M33_SHCSR_SECUREFAULTPENDED_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_SECUREFAULTENA
// Description : `DW the SecureFault exception is enabled
#define M33_SHCSR_SECUREFAULTENA_RESET  _u(0x0)
#define M33_SHCSR_SECUREFAULTENA_BITS   _u(0x00080000)
#define M33_SHCSR_SECUREFAULTENA_MSB    _u(19)
#define M33_SHCSR_SECUREFAULTENA_LSB    _u(19)
#define M33_SHCSR_SECUREFAULTENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_USGFAULTENA
// Description : `DW the UsageFault exception is enabled `FTSSS
#define M33_SHCSR_USGFAULTENA_RESET  _u(0x0)
#define M33_SHCSR_USGFAULTENA_BITS   _u(0x00040000)
#define M33_SHCSR_USGFAULTENA_MSB    _u(18)
#define M33_SHCSR_USGFAULTENA_LSB    _u(18)
#define M33_SHCSR_USGFAULTENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_BUSFAULTENA
// Description : `DW the BusFault exception is enabled
#define M33_SHCSR_BUSFAULTENA_RESET  _u(0x0)
#define M33_SHCSR_BUSFAULTENA_BITS   _u(0x00020000)
#define M33_SHCSR_BUSFAULTENA_MSB    _u(17)
#define M33_SHCSR_BUSFAULTENA_LSB    _u(17)
#define M33_SHCSR_BUSFAULTENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_MEMFAULTENA
// Description : `DW the MemManage exception is enabled `FTSSS
#define M33_SHCSR_MEMFAULTENA_RESET  _u(0x0)
#define M33_SHCSR_MEMFAULTENA_BITS   _u(0x00010000)
#define M33_SHCSR_MEMFAULTENA_MSB    _u(16)
#define M33_SHCSR_MEMFAULTENA_LSB    _u(16)
#define M33_SHCSR_MEMFAULTENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_SVCALLPENDED
// Description : `IAAMO the pending state of the SVCall exception `FTSSS
#define M33_SHCSR_SVCALLPENDED_RESET  _u(0x0)
#define M33_SHCSR_SVCALLPENDED_BITS   _u(0x00008000)
#define M33_SHCSR_SVCALLPENDED_MSB    _u(15)
#define M33_SHCSR_SVCALLPENDED_LSB    _u(15)
#define M33_SHCSR_SVCALLPENDED_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_BUSFAULTPENDED
// Description : `IAAMO the pending state of the BusFault exception
#define M33_SHCSR_BUSFAULTPENDED_RESET  _u(0x0)
#define M33_SHCSR_BUSFAULTPENDED_BITS   _u(0x00004000)
#define M33_SHCSR_BUSFAULTPENDED_MSB    _u(14)
#define M33_SHCSR_BUSFAULTPENDED_LSB    _u(14)
#define M33_SHCSR_BUSFAULTPENDED_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_MEMFAULTPENDED
// Description : `IAAMO the pending state of the MemManage exception `FTSSS
#define M33_SHCSR_MEMFAULTPENDED_RESET  _u(0x0)
#define M33_SHCSR_MEMFAULTPENDED_BITS   _u(0x00002000)
#define M33_SHCSR_MEMFAULTPENDED_MSB    _u(13)
#define M33_SHCSR_MEMFAULTPENDED_LSB    _u(13)
#define M33_SHCSR_MEMFAULTPENDED_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_USGFAULTPENDED
// Description : The UsageFault exception is banked between Security states,
//               `IAAMO the pending state of the UsageFault exception `FTSSS
#define M33_SHCSR_USGFAULTPENDED_RESET  _u(0x0)
#define M33_SHCSR_USGFAULTPENDED_BITS   _u(0x00001000)
#define M33_SHCSR_USGFAULTPENDED_MSB    _u(12)
#define M33_SHCSR_USGFAULTPENDED_LSB    _u(12)
#define M33_SHCSR_USGFAULTPENDED_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_SYSTICKACT
// Description : `IAAMO the active state of the SysTick exception `FTSSS
#define M33_SHCSR_SYSTICKACT_RESET  _u(0x0)
#define M33_SHCSR_SYSTICKACT_BITS   _u(0x00000800)
#define M33_SHCSR_SYSTICKACT_MSB    _u(11)
#define M33_SHCSR_SYSTICKACT_LSB    _u(11)
#define M33_SHCSR_SYSTICKACT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_PENDSVACT
// Description : `IAAMO the active state of the PendSV exception `FTSSS
#define M33_SHCSR_PENDSVACT_RESET  _u(0x0)
#define M33_SHCSR_PENDSVACT_BITS   _u(0x00000400)
#define M33_SHCSR_PENDSVACT_MSB    _u(10)
#define M33_SHCSR_PENDSVACT_LSB    _u(10)
#define M33_SHCSR_PENDSVACT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_MONITORACT
// Description : `IAAMO the active state of the DebugMonitor exception
#define M33_SHCSR_MONITORACT_RESET  _u(0x0)
#define M33_SHCSR_MONITORACT_BITS   _u(0x00000100)
#define M33_SHCSR_MONITORACT_MSB    _u(8)
#define M33_SHCSR_MONITORACT_LSB    _u(8)
#define M33_SHCSR_MONITORACT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_SVCALLACT
// Description : `IAAMO the active state of the SVCall exception `FTSSS
#define M33_SHCSR_SVCALLACT_RESET  _u(0x0)
#define M33_SHCSR_SVCALLACT_BITS   _u(0x00000080)
#define M33_SHCSR_SVCALLACT_MSB    _u(7)
#define M33_SHCSR_SVCALLACT_LSB    _u(7)
#define M33_SHCSR_SVCALLACT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_NMIACT
// Description : `IAAMO the active state of the NMI exception
#define M33_SHCSR_NMIACT_RESET  _u(0x0)
#define M33_SHCSR_NMIACT_BITS   _u(0x00000020)
#define M33_SHCSR_NMIACT_MSB    _u(5)
#define M33_SHCSR_NMIACT_LSB    _u(5)
#define M33_SHCSR_NMIACT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_SECUREFAULTACT
// Description : `IAAMO the active state of the SecureFault exception
#define M33_SHCSR_SECUREFAULTACT_RESET  _u(0x0)
#define M33_SHCSR_SECUREFAULTACT_BITS   _u(0x00000010)
#define M33_SHCSR_SECUREFAULTACT_MSB    _u(4)
#define M33_SHCSR_SECUREFAULTACT_LSB    _u(4)
#define M33_SHCSR_SECUREFAULTACT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_USGFAULTACT
// Description : `IAAMO the active state of the UsageFault exception `FTSSS
#define M33_SHCSR_USGFAULTACT_RESET  _u(0x0)
#define M33_SHCSR_USGFAULTACT_BITS   _u(0x00000008)
#define M33_SHCSR_USGFAULTACT_MSB    _u(3)
#define M33_SHCSR_USGFAULTACT_LSB    _u(3)
#define M33_SHCSR_USGFAULTACT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_HARDFAULTACT
// Description : Indicates and allows limited modification of the active state
//               of the HardFault exception `FTSSS
#define M33_SHCSR_HARDFAULTACT_RESET  _u(0x0)
#define M33_SHCSR_HARDFAULTACT_BITS   _u(0x00000004)
#define M33_SHCSR_HARDFAULTACT_MSB    _u(2)
#define M33_SHCSR_HARDFAULTACT_LSB    _u(2)
#define M33_SHCSR_HARDFAULTACT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_BUSFAULTACT
// Description : `IAAMO the active state of the BusFault exception
#define M33_SHCSR_BUSFAULTACT_RESET  _u(0x0)
#define M33_SHCSR_BUSFAULTACT_BITS   _u(0x00000002)
#define M33_SHCSR_BUSFAULTACT_MSB    _u(1)
#define M33_SHCSR_BUSFAULTACT_LSB    _u(1)
#define M33_SHCSR_BUSFAULTACT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SHCSR_MEMFAULTACT
// Description : `IAAMO the active state of the MemManage exception `FTSSS
#define M33_SHCSR_MEMFAULTACT_RESET  _u(0x0)
#define M33_SHCSR_MEMFAULTACT_BITS   _u(0x00000001)
#define M33_SHCSR_MEMFAULTACT_MSB    _u(0)
#define M33_SHCSR_MEMFAULTACT_LSB    _u(0)
#define M33_SHCSR_MEMFAULTACT_ACCESS "RW"
// =============================================================================
// Register    : M33_CFSR
// Description : Contains the three Configurable Fault Status Registers.
//
//               31:16 UFSR: Provides information on UsageFault exceptions
//
//               15:8 BFSR: Provides information on BusFault exceptions
//
//               7:0 MMFSR: Provides information on MemManage exceptions
#define M33_CFSR_OFFSET _u(0x0000ed28)
#define M33_CFSR_BITS   _u(0x031fbfff)
#define M33_CFSR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CFSR_UFSR_DIVBYZERO
// Description : Sticky flag indicating whether an integer division by zero
//               error has occurred
#define M33_CFSR_UFSR_DIVBYZERO_RESET  _u(0x0)
#define M33_CFSR_UFSR_DIVBYZERO_BITS   _u(0x02000000)
#define M33_CFSR_UFSR_DIVBYZERO_MSB    _u(25)
#define M33_CFSR_UFSR_DIVBYZERO_LSB    _u(25)
#define M33_CFSR_UFSR_DIVBYZERO_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CFSR_UFSR_UNALIGNED
// Description : Sticky flag indicating whether an unaligned access error has
//               occurred
#define M33_CFSR_UFSR_UNALIGNED_RESET  _u(0x0)
#define M33_CFSR_UFSR_UNALIGNED_BITS   _u(0x01000000)
#define M33_CFSR_UFSR_UNALIGNED_MSB    _u(24)
#define M33_CFSR_UFSR_UNALIGNED_LSB    _u(24)
#define M33_CFSR_UFSR_UNALIGNED_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CFSR_UFSR_STKOF
// Description : Sticky flag indicating whether a stack overflow error has
//               occurred
#define M33_CFSR_UFSR_STKOF_RESET  _u(0x0)
#define M33_CFSR_UFSR_STKOF_BITS   _u(0x00100000)
#define M33_CFSR_UFSR_STKOF_MSB    _u(20)
#define M33_CFSR_UFSR_STKOF_LSB    _u(20)
#define M33_CFSR_UFSR_STKOF_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CFSR_UFSR_NOCP
// Description : Sticky flag indicating whether a coprocessor disabled or not
//               present error has occurred
#define M33_CFSR_UFSR_NOCP_RESET  _u(0x0)
#define M33_CFSR_UFSR_NOCP_BITS   _u(0x00080000)
#define M33_CFSR_UFSR_NOCP_MSB    _u(19)
#define M33_CFSR_UFSR_NOCP_LSB    _u(19)
#define M33_CFSR_UFSR_NOCP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CFSR_UFSR_INVPC
// Description : Sticky flag indicating whether an integrity check error has
//               occurred
#define M33_CFSR_UFSR_INVPC_RESET  _u(0x0)
#define M33_CFSR_UFSR_INVPC_BITS   _u(0x00040000)
#define M33_CFSR_UFSR_INVPC_MSB    _u(18)
#define M33_CFSR_UFSR_INVPC_LSB    _u(18)
#define M33_CFSR_UFSR_INVPC_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CFSR_UFSR_INVSTATE
// Description : Sticky flag indicating whether an EPSR.T or EPSR.IT validity
//               error has occurred
#define M33_CFSR_UFSR_INVSTATE_RESET  _u(0x0)
#define M33_CFSR_UFSR_INVSTATE_BITS   _u(0x00020000)
#define M33_CFSR_UFSR_INVSTATE_MSB    _u(17)
#define M33_CFSR_UFSR_INVSTATE_LSB    _u(17)
#define M33_CFSR_UFSR_INVSTATE_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CFSR_UFSR_UNDEFINSTR
// Description : Sticky flag indicating whether an undefined instruction error
//               has occurred
#define M33_CFSR_UFSR_UNDEFINSTR_RESET  _u(0x0)
#define M33_CFSR_UFSR_UNDEFINSTR_BITS   _u(0x00010000)
#define M33_CFSR_UFSR_UNDEFINSTR_MSB    _u(16)
#define M33_CFSR_UFSR_UNDEFINSTR_LSB    _u(16)
#define M33_CFSR_UFSR_UNDEFINSTR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CFSR_BFSR_BFARVALID
// Description : Indicates validity of the contents of the BFAR register
#define M33_CFSR_BFSR_BFARVALID_RESET  _u(0x0)
#define M33_CFSR_BFSR_BFARVALID_BITS   _u(0x00008000)
#define M33_CFSR_BFSR_BFARVALID_MSB    _u(15)
#define M33_CFSR_BFSR_BFARVALID_LSB    _u(15)
#define M33_CFSR_BFSR_BFARVALID_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CFSR_BFSR_LSPERR
// Description : Records whether a BusFault occurred during FP lazy state
//               preservation
#define M33_CFSR_BFSR_LSPERR_RESET  _u(0x0)
#define M33_CFSR_BFSR_LSPERR_BITS   _u(0x00002000)
#define M33_CFSR_BFSR_LSPERR_MSB    _u(13)
#define M33_CFSR_BFSR_LSPERR_LSB    _u(13)
#define M33_CFSR_BFSR_LSPERR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CFSR_BFSR_STKERR
// Description : Records whether a derived BusFault occurred during exception
//               entry stacking
#define M33_CFSR_BFSR_STKERR_RESET  _u(0x0)
#define M33_CFSR_BFSR_STKERR_BITS   _u(0x00001000)
#define M33_CFSR_BFSR_STKERR_MSB    _u(12)
#define M33_CFSR_BFSR_STKERR_LSB    _u(12)
#define M33_CFSR_BFSR_STKERR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CFSR_BFSR_UNSTKERR
// Description : Records whether a derived BusFault occurred during exception
//               return unstacking
#define M33_CFSR_BFSR_UNSTKERR_RESET  _u(0x0)
#define M33_CFSR_BFSR_UNSTKERR_BITS   _u(0x00000800)
#define M33_CFSR_BFSR_UNSTKERR_MSB    _u(11)
#define M33_CFSR_BFSR_UNSTKERR_LSB    _u(11)
#define M33_CFSR_BFSR_UNSTKERR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CFSR_BFSR_IMPRECISERR
// Description : Records whether an imprecise data access error has occurred
#define M33_CFSR_BFSR_IMPRECISERR_RESET  _u(0x0)
#define M33_CFSR_BFSR_IMPRECISERR_BITS   _u(0x00000400)
#define M33_CFSR_BFSR_IMPRECISERR_MSB    _u(10)
#define M33_CFSR_BFSR_IMPRECISERR_LSB    _u(10)
#define M33_CFSR_BFSR_IMPRECISERR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CFSR_BFSR_PRECISERR
// Description : Records whether a precise data access error has occurred
#define M33_CFSR_BFSR_PRECISERR_RESET  _u(0x0)
#define M33_CFSR_BFSR_PRECISERR_BITS   _u(0x00000200)
#define M33_CFSR_BFSR_PRECISERR_MSB    _u(9)
#define M33_CFSR_BFSR_PRECISERR_LSB    _u(9)
#define M33_CFSR_BFSR_PRECISERR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CFSR_BFSR_IBUSERR
// Description : Records whether a BusFault on an instruction prefetch has
//               occurred
#define M33_CFSR_BFSR_IBUSERR_RESET  _u(0x0)
#define M33_CFSR_BFSR_IBUSERR_BITS   _u(0x00000100)
#define M33_CFSR_BFSR_IBUSERR_MSB    _u(8)
#define M33_CFSR_BFSR_IBUSERR_LSB    _u(8)
#define M33_CFSR_BFSR_IBUSERR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CFSR_MMFSR
// Description : Provides information on MemManage exceptions
#define M33_CFSR_MMFSR_RESET  _u(0x00)
#define M33_CFSR_MMFSR_BITS   _u(0x000000ff)
#define M33_CFSR_MMFSR_MSB    _u(7)
#define M33_CFSR_MMFSR_LSB    _u(0)
#define M33_CFSR_MMFSR_ACCESS "RW"
// =============================================================================
// Register    : M33_HFSR
// Description : Shows the cause of any HardFaults
#define M33_HFSR_OFFSET _u(0x0000ed2c)
#define M33_HFSR_BITS   _u(0xc0000002)
#define M33_HFSR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_HFSR_DEBUGEVT
// Description : Indicates when a Debug event has occurred
#define M33_HFSR_DEBUGEVT_RESET  _u(0x0)
#define M33_HFSR_DEBUGEVT_BITS   _u(0x80000000)
#define M33_HFSR_DEBUGEVT_MSB    _u(31)
#define M33_HFSR_DEBUGEVT_LSB    _u(31)
#define M33_HFSR_DEBUGEVT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_HFSR_FORCED
// Description : Indicates that a fault with configurable priority has been
//               escalated to a HardFault exception, because it could not be
//               made active, because of priority, or because it was disabled
#define M33_HFSR_FORCED_RESET  _u(0x0)
#define M33_HFSR_FORCED_BITS   _u(0x40000000)
#define M33_HFSR_FORCED_MSB    _u(30)
#define M33_HFSR_FORCED_LSB    _u(30)
#define M33_HFSR_FORCED_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_HFSR_VECTTBL
// Description : Indicates when a fault has occurred because of a vector table
//               read error on exception processing
#define M33_HFSR_VECTTBL_RESET  _u(0x0)
#define M33_HFSR_VECTTBL_BITS   _u(0x00000002)
#define M33_HFSR_VECTTBL_MSB    _u(1)
#define M33_HFSR_VECTTBL_LSB    _u(1)
#define M33_HFSR_VECTTBL_ACCESS "RW"
// =============================================================================
// Register    : M33_DFSR
// Description : Shows which debug event occurred
#define M33_DFSR_OFFSET _u(0x0000ed30)
#define M33_DFSR_BITS   _u(0x0000001f)
#define M33_DFSR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_DFSR_EXTERNAL
// Description : Sticky flag indicating whether an External debug request debug
//               event has occurred
#define M33_DFSR_EXTERNAL_RESET  _u(0x0)
#define M33_DFSR_EXTERNAL_BITS   _u(0x00000010)
#define M33_DFSR_EXTERNAL_MSB    _u(4)
#define M33_DFSR_EXTERNAL_LSB    _u(4)
#define M33_DFSR_EXTERNAL_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DFSR_VCATCH
// Description : Sticky flag indicating whether a Vector catch debug event has
//               occurred
#define M33_DFSR_VCATCH_RESET  _u(0x0)
#define M33_DFSR_VCATCH_BITS   _u(0x00000008)
#define M33_DFSR_VCATCH_MSB    _u(3)
#define M33_DFSR_VCATCH_LSB    _u(3)
#define M33_DFSR_VCATCH_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DFSR_DWTTRAP
// Description : Sticky flag indicating whether a Watchpoint debug event has
//               occurred
#define M33_DFSR_DWTTRAP_RESET  _u(0x0)
#define M33_DFSR_DWTTRAP_BITS   _u(0x00000004)
#define M33_DFSR_DWTTRAP_MSB    _u(2)
#define M33_DFSR_DWTTRAP_LSB    _u(2)
#define M33_DFSR_DWTTRAP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DFSR_BKPT
// Description : Sticky flag indicating whether a Breakpoint debug event has
//               occurred
#define M33_DFSR_BKPT_RESET  _u(0x0)
#define M33_DFSR_BKPT_BITS   _u(0x00000002)
#define M33_DFSR_BKPT_MSB    _u(1)
#define M33_DFSR_BKPT_LSB    _u(1)
#define M33_DFSR_BKPT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DFSR_HALTED
// Description : Sticky flag indicating that a Halt request debug event or Step
//               debug event has occurred
#define M33_DFSR_HALTED_RESET  _u(0x0)
#define M33_DFSR_HALTED_BITS   _u(0x00000001)
#define M33_DFSR_HALTED_MSB    _u(0)
#define M33_DFSR_HALTED_LSB    _u(0)
#define M33_DFSR_HALTED_ACCESS "RW"
// =============================================================================
// Register    : M33_MMFAR
// Description : Shows the address of the memory location that caused an MPU
//               fault
#define M33_MMFAR_OFFSET _u(0x0000ed34)
#define M33_MMFAR_BITS   _u(0xffffffff)
#define M33_MMFAR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_MMFAR_ADDRESS
// Description : This register is updated with the address of a location that
//               produced a MemManage fault. The MMFSR shows the cause of the
//               fault, and whether this field is valid. This field is valid
//               only when MMFSR.MMARVALID is set, otherwise it is UNKNOWN
#define M33_MMFAR_ADDRESS_RESET  _u(0x00000000)
#define M33_MMFAR_ADDRESS_BITS   _u(0xffffffff)
#define M33_MMFAR_ADDRESS_MSB    _u(31)
#define M33_MMFAR_ADDRESS_LSB    _u(0)
#define M33_MMFAR_ADDRESS_ACCESS "RW"
// =============================================================================
// Register    : M33_BFAR
// Description : Shows the address associated with a precise data access
//               BusFault
#define M33_BFAR_OFFSET _u(0x0000ed38)
#define M33_BFAR_BITS   _u(0xffffffff)
#define M33_BFAR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_BFAR_ADDRESS
// Description : This register is updated with the address of a location that
//               produced a BusFault. The BFSR shows the reason for the fault.
//               This field is valid only when BFSR.BFARVALID is set, otherwise
//               it is UNKNOWN
#define M33_BFAR_ADDRESS_RESET  _u(0x00000000)
#define M33_BFAR_ADDRESS_BITS   _u(0xffffffff)
#define M33_BFAR_ADDRESS_MSB    _u(31)
#define M33_BFAR_ADDRESS_LSB    _u(0)
#define M33_BFAR_ADDRESS_ACCESS "RW"
// =============================================================================
// Register    : M33_ID_PFR0
// Description : Gives top-level information about the instruction set supported
//               by the PE
#define M33_ID_PFR0_OFFSET _u(0x0000ed40)
#define M33_ID_PFR0_BITS   _u(0x000000ff)
#define M33_ID_PFR0_RESET  _u(0x00000030)
// -----------------------------------------------------------------------------
// Field       : M33_ID_PFR0_STATE1
// Description : T32 instruction set support
#define M33_ID_PFR0_STATE1_RESET  _u(0x3)
#define M33_ID_PFR0_STATE1_BITS   _u(0x000000f0)
#define M33_ID_PFR0_STATE1_MSB    _u(7)
#define M33_ID_PFR0_STATE1_LSB    _u(4)
#define M33_ID_PFR0_STATE1_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_PFR0_STATE0
// Description : A32 instruction set support
#define M33_ID_PFR0_STATE0_RESET  _u(0x0)
#define M33_ID_PFR0_STATE0_BITS   _u(0x0000000f)
#define M33_ID_PFR0_STATE0_MSB    _u(3)
#define M33_ID_PFR0_STATE0_LSB    _u(0)
#define M33_ID_PFR0_STATE0_ACCESS "RO"
// =============================================================================
// Register    : M33_ID_PFR1
// Description : Gives information about the programmers' model and Extensions
//               support
#define M33_ID_PFR1_OFFSET _u(0x0000ed44)
#define M33_ID_PFR1_BITS   _u(0x00000ff0)
#define M33_ID_PFR1_RESET  _u(0x00000520)
// -----------------------------------------------------------------------------
// Field       : M33_ID_PFR1_MPROGMOD
// Description : Identifies support for the M-Profile programmers' model support
#define M33_ID_PFR1_MPROGMOD_RESET  _u(0x5)
#define M33_ID_PFR1_MPROGMOD_BITS   _u(0x00000f00)
#define M33_ID_PFR1_MPROGMOD_MSB    _u(11)
#define M33_ID_PFR1_MPROGMOD_LSB    _u(8)
#define M33_ID_PFR1_MPROGMOD_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_PFR1_SECURITY
// Description : Identifies whether the Security Extension is implemented
#define M33_ID_PFR1_SECURITY_RESET  _u(0x2)
#define M33_ID_PFR1_SECURITY_BITS   _u(0x000000f0)
#define M33_ID_PFR1_SECURITY_MSB    _u(7)
#define M33_ID_PFR1_SECURITY_LSB    _u(4)
#define M33_ID_PFR1_SECURITY_ACCESS "RO"
// =============================================================================
// Register    : M33_ID_DFR0
// Description : Provides top level information about the debug system
#define M33_ID_DFR0_OFFSET _u(0x0000ed48)
#define M33_ID_DFR0_BITS   _u(0x00f00000)
#define M33_ID_DFR0_RESET  _u(0x00200000)
// -----------------------------------------------------------------------------
// Field       : M33_ID_DFR0_MPROFDBG
// Description : Indicates the supported M-profile debug architecture
#define M33_ID_DFR0_MPROFDBG_RESET  _u(0x2)
#define M33_ID_DFR0_MPROFDBG_BITS   _u(0x00f00000)
#define M33_ID_DFR0_MPROFDBG_MSB    _u(23)
#define M33_ID_DFR0_MPROFDBG_LSB    _u(20)
#define M33_ID_DFR0_MPROFDBG_ACCESS "RO"
// =============================================================================
// Register    : M33_ID_AFR0
// Description : Provides information about the IMPLEMENTATION DEFINED features
//               of the PE
#define M33_ID_AFR0_OFFSET _u(0x0000ed4c)
#define M33_ID_AFR0_BITS   _u(0x0000ffff)
#define M33_ID_AFR0_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ID_AFR0_IMPDEF3
// Description : IMPLEMENTATION DEFINED meaning
#define M33_ID_AFR0_IMPDEF3_RESET  _u(0x0)
#define M33_ID_AFR0_IMPDEF3_BITS   _u(0x0000f000)
#define M33_ID_AFR0_IMPDEF3_MSB    _u(15)
#define M33_ID_AFR0_IMPDEF3_LSB    _u(12)
#define M33_ID_AFR0_IMPDEF3_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_AFR0_IMPDEF2
// Description : IMPLEMENTATION DEFINED meaning
#define M33_ID_AFR0_IMPDEF2_RESET  _u(0x0)
#define M33_ID_AFR0_IMPDEF2_BITS   _u(0x00000f00)
#define M33_ID_AFR0_IMPDEF2_MSB    _u(11)
#define M33_ID_AFR0_IMPDEF2_LSB    _u(8)
#define M33_ID_AFR0_IMPDEF2_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_AFR0_IMPDEF1
// Description : IMPLEMENTATION DEFINED meaning
#define M33_ID_AFR0_IMPDEF1_RESET  _u(0x0)
#define M33_ID_AFR0_IMPDEF1_BITS   _u(0x000000f0)
#define M33_ID_AFR0_IMPDEF1_MSB    _u(7)
#define M33_ID_AFR0_IMPDEF1_LSB    _u(4)
#define M33_ID_AFR0_IMPDEF1_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_AFR0_IMPDEF0
// Description : IMPLEMENTATION DEFINED meaning
#define M33_ID_AFR0_IMPDEF0_RESET  _u(0x0)
#define M33_ID_AFR0_IMPDEF0_BITS   _u(0x0000000f)
#define M33_ID_AFR0_IMPDEF0_MSB    _u(3)
#define M33_ID_AFR0_IMPDEF0_LSB    _u(0)
#define M33_ID_AFR0_IMPDEF0_ACCESS "RO"
// =============================================================================
// Register    : M33_ID_MMFR0
// Description : Provides information about the implemented memory model and
//               memory management support
#define M33_ID_MMFR0_OFFSET _u(0x0000ed50)
#define M33_ID_MMFR0_BITS   _u(0x00fffff0)
#define M33_ID_MMFR0_RESET  _u(0x00101f40)
// -----------------------------------------------------------------------------
// Field       : M33_ID_MMFR0_AUXREG
// Description : Indicates support for Auxiliary Control Registers
#define M33_ID_MMFR0_AUXREG_RESET  _u(0x1)
#define M33_ID_MMFR0_AUXREG_BITS   _u(0x00f00000)
#define M33_ID_MMFR0_AUXREG_MSB    _u(23)
#define M33_ID_MMFR0_AUXREG_LSB    _u(20)
#define M33_ID_MMFR0_AUXREG_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_MMFR0_TCM
// Description : Indicates support for tightly coupled memories (TCMs)
#define M33_ID_MMFR0_TCM_RESET  _u(0x0)
#define M33_ID_MMFR0_TCM_BITS   _u(0x000f0000)
#define M33_ID_MMFR0_TCM_MSB    _u(19)
#define M33_ID_MMFR0_TCM_LSB    _u(16)
#define M33_ID_MMFR0_TCM_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_MMFR0_SHARELVL
// Description : Indicates the number of shareability levels implemented
#define M33_ID_MMFR0_SHARELVL_RESET  _u(0x1)
#define M33_ID_MMFR0_SHARELVL_BITS   _u(0x0000f000)
#define M33_ID_MMFR0_SHARELVL_MSB    _u(15)
#define M33_ID_MMFR0_SHARELVL_LSB    _u(12)
#define M33_ID_MMFR0_SHARELVL_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_MMFR0_OUTERSHR
// Description : Indicates the outermost shareability domain implemented
#define M33_ID_MMFR0_OUTERSHR_RESET  _u(0xf)
#define M33_ID_MMFR0_OUTERSHR_BITS   _u(0x00000f00)
#define M33_ID_MMFR0_OUTERSHR_MSB    _u(11)
#define M33_ID_MMFR0_OUTERSHR_LSB    _u(8)
#define M33_ID_MMFR0_OUTERSHR_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_MMFR0_PMSA
// Description : Indicates support for the protected memory system architecture
//               (PMSA)
#define M33_ID_MMFR0_PMSA_RESET  _u(0x4)
#define M33_ID_MMFR0_PMSA_BITS   _u(0x000000f0)
#define M33_ID_MMFR0_PMSA_MSB    _u(7)
#define M33_ID_MMFR0_PMSA_LSB    _u(4)
#define M33_ID_MMFR0_PMSA_ACCESS "RO"
// =============================================================================
// Register    : M33_ID_MMFR1
// Description : Provides information about the implemented memory model and
//               memory management support
#define M33_ID_MMFR1_OFFSET _u(0x0000ed54)
#define M33_ID_MMFR1_BITS   _u(0x00000000)
#define M33_ID_MMFR1_RESET  _u(0x00000000)
#define M33_ID_MMFR1_MSB    _u(31)
#define M33_ID_MMFR1_LSB    _u(0)
#define M33_ID_MMFR1_ACCESS "RW"
// =============================================================================
// Register    : M33_ID_MMFR2
// Description : Provides information about the implemented memory model and
//               memory management support
#define M33_ID_MMFR2_OFFSET _u(0x0000ed58)
#define M33_ID_MMFR2_BITS   _u(0x0f000000)
#define M33_ID_MMFR2_RESET  _u(0x01000000)
// -----------------------------------------------------------------------------
// Field       : M33_ID_MMFR2_WFISTALL
// Description : Indicates the support for Wait For Interrupt (WFI) stalling
#define M33_ID_MMFR2_WFISTALL_RESET  _u(0x1)
#define M33_ID_MMFR2_WFISTALL_BITS   _u(0x0f000000)
#define M33_ID_MMFR2_WFISTALL_MSB    _u(27)
#define M33_ID_MMFR2_WFISTALL_LSB    _u(24)
#define M33_ID_MMFR2_WFISTALL_ACCESS "RO"
// =============================================================================
// Register    : M33_ID_MMFR3
// Description : Provides information about the implemented memory model and
//               memory management support
#define M33_ID_MMFR3_OFFSET _u(0x0000ed5c)
#define M33_ID_MMFR3_BITS   _u(0x00000fff)
#define M33_ID_MMFR3_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ID_MMFR3_BPMAINT
// Description : Indicates the supported branch predictor maintenance
#define M33_ID_MMFR3_BPMAINT_RESET  _u(0x0)
#define M33_ID_MMFR3_BPMAINT_BITS   _u(0x00000f00)
#define M33_ID_MMFR3_BPMAINT_MSB    _u(11)
#define M33_ID_MMFR3_BPMAINT_LSB    _u(8)
#define M33_ID_MMFR3_BPMAINT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_MMFR3_CMAINTSW
// Description : Indicates the supported cache maintenance operations by set/way
#define M33_ID_MMFR3_CMAINTSW_RESET  _u(0x0)
#define M33_ID_MMFR3_CMAINTSW_BITS   _u(0x000000f0)
#define M33_ID_MMFR3_CMAINTSW_MSB    _u(7)
#define M33_ID_MMFR3_CMAINTSW_LSB    _u(4)
#define M33_ID_MMFR3_CMAINTSW_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_MMFR3_CMAINTVA
// Description : Indicates the supported cache maintenance operations by address
#define M33_ID_MMFR3_CMAINTVA_RESET  _u(0x0)
#define M33_ID_MMFR3_CMAINTVA_BITS   _u(0x0000000f)
#define M33_ID_MMFR3_CMAINTVA_MSB    _u(3)
#define M33_ID_MMFR3_CMAINTVA_LSB    _u(0)
#define M33_ID_MMFR3_CMAINTVA_ACCESS "RO"
// =============================================================================
// Register    : M33_ID_ISAR0
// Description : Provides information about the instruction set implemented by
//               the PE
#define M33_ID_ISAR0_OFFSET _u(0x0000ed60)
#define M33_ID_ISAR0_BITS   _u(0x0ffffff0)
#define M33_ID_ISAR0_RESET  _u(0x08092300)
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR0_DIVIDE
// Description : Indicates the supported Divide instructions
#define M33_ID_ISAR0_DIVIDE_RESET  _u(0x8)
#define M33_ID_ISAR0_DIVIDE_BITS   _u(0x0f000000)
#define M33_ID_ISAR0_DIVIDE_MSB    _u(27)
#define M33_ID_ISAR0_DIVIDE_LSB    _u(24)
#define M33_ID_ISAR0_DIVIDE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR0_DEBUG
// Description : Indicates the implemented Debug instructions
#define M33_ID_ISAR0_DEBUG_RESET  _u(0x0)
#define M33_ID_ISAR0_DEBUG_BITS   _u(0x00f00000)
#define M33_ID_ISAR0_DEBUG_MSB    _u(23)
#define M33_ID_ISAR0_DEBUG_LSB    _u(20)
#define M33_ID_ISAR0_DEBUG_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR0_COPROC
// Description : Indicates the supported Coprocessor instructions
#define M33_ID_ISAR0_COPROC_RESET  _u(0x9)
#define M33_ID_ISAR0_COPROC_BITS   _u(0x000f0000)
#define M33_ID_ISAR0_COPROC_MSB    _u(19)
#define M33_ID_ISAR0_COPROC_LSB    _u(16)
#define M33_ID_ISAR0_COPROC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR0_CMPBRANCH
// Description : Indicates the supported combined Compare and Branch
//               instructions
#define M33_ID_ISAR0_CMPBRANCH_RESET  _u(0x2)
#define M33_ID_ISAR0_CMPBRANCH_BITS   _u(0x0000f000)
#define M33_ID_ISAR0_CMPBRANCH_MSB    _u(15)
#define M33_ID_ISAR0_CMPBRANCH_LSB    _u(12)
#define M33_ID_ISAR0_CMPBRANCH_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR0_BITFIELD
// Description : Indicates the supported bit field instructions
#define M33_ID_ISAR0_BITFIELD_RESET  _u(0x3)
#define M33_ID_ISAR0_BITFIELD_BITS   _u(0x00000f00)
#define M33_ID_ISAR0_BITFIELD_MSB    _u(11)
#define M33_ID_ISAR0_BITFIELD_LSB    _u(8)
#define M33_ID_ISAR0_BITFIELD_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR0_BITCOUNT
// Description : Indicates the supported bit count instructions
#define M33_ID_ISAR0_BITCOUNT_RESET  _u(0x0)
#define M33_ID_ISAR0_BITCOUNT_BITS   _u(0x000000f0)
#define M33_ID_ISAR0_BITCOUNT_MSB    _u(7)
#define M33_ID_ISAR0_BITCOUNT_LSB    _u(4)
#define M33_ID_ISAR0_BITCOUNT_ACCESS "RO"
// =============================================================================
// Register    : M33_ID_ISAR1
// Description : Provides information about the instruction set implemented by
//               the PE
#define M33_ID_ISAR1_OFFSET _u(0x0000ed64)
#define M33_ID_ISAR1_BITS   _u(0x0ffff000)
#define M33_ID_ISAR1_RESET  _u(0x05725000)
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR1_INTERWORK
// Description : Indicates the implemented Interworking instructions
#define M33_ID_ISAR1_INTERWORK_RESET  _u(0x5)
#define M33_ID_ISAR1_INTERWORK_BITS   _u(0x0f000000)
#define M33_ID_ISAR1_INTERWORK_MSB    _u(27)
#define M33_ID_ISAR1_INTERWORK_LSB    _u(24)
#define M33_ID_ISAR1_INTERWORK_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR1_IMMEDIATE
// Description : Indicates the implemented for data-processing instructions with
//               long immediates
#define M33_ID_ISAR1_IMMEDIATE_RESET  _u(0x7)
#define M33_ID_ISAR1_IMMEDIATE_BITS   _u(0x00f00000)
#define M33_ID_ISAR1_IMMEDIATE_MSB    _u(23)
#define M33_ID_ISAR1_IMMEDIATE_LSB    _u(20)
#define M33_ID_ISAR1_IMMEDIATE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR1_IFTHEN
// Description : Indicates the implemented If-Then instructions
#define M33_ID_ISAR1_IFTHEN_RESET  _u(0x2)
#define M33_ID_ISAR1_IFTHEN_BITS   _u(0x000f0000)
#define M33_ID_ISAR1_IFTHEN_MSB    _u(19)
#define M33_ID_ISAR1_IFTHEN_LSB    _u(16)
#define M33_ID_ISAR1_IFTHEN_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR1_EXTEND
// Description : Indicates the implemented Extend instructions
#define M33_ID_ISAR1_EXTEND_RESET  _u(0x5)
#define M33_ID_ISAR1_EXTEND_BITS   _u(0x0000f000)
#define M33_ID_ISAR1_EXTEND_MSB    _u(15)
#define M33_ID_ISAR1_EXTEND_LSB    _u(12)
#define M33_ID_ISAR1_EXTEND_ACCESS "RO"
// =============================================================================
// Register    : M33_ID_ISAR2
// Description : Provides information about the instruction set implemented by
//               the PE
#define M33_ID_ISAR2_OFFSET _u(0x0000ed68)
#define M33_ID_ISAR2_BITS   _u(0xf0ffffff)
#define M33_ID_ISAR2_RESET  _u(0x30173426)
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR2_REVERSAL
// Description : Indicates the implemented Reversal instructions
#define M33_ID_ISAR2_REVERSAL_RESET  _u(0x3)
#define M33_ID_ISAR2_REVERSAL_BITS   _u(0xf0000000)
#define M33_ID_ISAR2_REVERSAL_MSB    _u(31)
#define M33_ID_ISAR2_REVERSAL_LSB    _u(28)
#define M33_ID_ISAR2_REVERSAL_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR2_MULTU
// Description : Indicates the implemented advanced unsigned Multiply
//               instructions
#define M33_ID_ISAR2_MULTU_RESET  _u(0x1)
#define M33_ID_ISAR2_MULTU_BITS   _u(0x00f00000)
#define M33_ID_ISAR2_MULTU_MSB    _u(23)
#define M33_ID_ISAR2_MULTU_LSB    _u(20)
#define M33_ID_ISAR2_MULTU_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR2_MULTS
// Description : Indicates the implemented advanced signed Multiply instructions
#define M33_ID_ISAR2_MULTS_RESET  _u(0x7)
#define M33_ID_ISAR2_MULTS_BITS   _u(0x000f0000)
#define M33_ID_ISAR2_MULTS_MSB    _u(19)
#define M33_ID_ISAR2_MULTS_LSB    _u(16)
#define M33_ID_ISAR2_MULTS_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR2_MULT
// Description : Indicates the implemented additional Multiply instructions
#define M33_ID_ISAR2_MULT_RESET  _u(0x3)
#define M33_ID_ISAR2_MULT_BITS   _u(0x0000f000)
#define M33_ID_ISAR2_MULT_MSB    _u(15)
#define M33_ID_ISAR2_MULT_LSB    _u(12)
#define M33_ID_ISAR2_MULT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR2_MULTIACCESSINT
// Description : Indicates the support for interruptible multi-access
//               instructions
#define M33_ID_ISAR2_MULTIACCESSINT_RESET  _u(0x4)
#define M33_ID_ISAR2_MULTIACCESSINT_BITS   _u(0x00000f00)
#define M33_ID_ISAR2_MULTIACCESSINT_MSB    _u(11)
#define M33_ID_ISAR2_MULTIACCESSINT_LSB    _u(8)
#define M33_ID_ISAR2_MULTIACCESSINT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR2_MEMHINT
// Description : Indicates the implemented Memory Hint instructions
#define M33_ID_ISAR2_MEMHINT_RESET  _u(0x2)
#define M33_ID_ISAR2_MEMHINT_BITS   _u(0x000000f0)
#define M33_ID_ISAR2_MEMHINT_MSB    _u(7)
#define M33_ID_ISAR2_MEMHINT_LSB    _u(4)
#define M33_ID_ISAR2_MEMHINT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR2_LOADSTORE
// Description : Indicates the implemented additional load/store instructions
#define M33_ID_ISAR2_LOADSTORE_RESET  _u(0x6)
#define M33_ID_ISAR2_LOADSTORE_BITS   _u(0x0000000f)
#define M33_ID_ISAR2_LOADSTORE_MSB    _u(3)
#define M33_ID_ISAR2_LOADSTORE_LSB    _u(0)
#define M33_ID_ISAR2_LOADSTORE_ACCESS "RO"
// =============================================================================
// Register    : M33_ID_ISAR3
// Description : Provides information about the instruction set implemented by
//               the PE
#define M33_ID_ISAR3_OFFSET _u(0x0000ed6c)
#define M33_ID_ISAR3_BITS   _u(0x0fffffff)
#define M33_ID_ISAR3_RESET  _u(0x07895729)
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR3_TRUENOP
// Description : Indicates the implemented true NOP instructions
#define M33_ID_ISAR3_TRUENOP_RESET  _u(0x7)
#define M33_ID_ISAR3_TRUENOP_BITS   _u(0x0f000000)
#define M33_ID_ISAR3_TRUENOP_MSB    _u(27)
#define M33_ID_ISAR3_TRUENOP_LSB    _u(24)
#define M33_ID_ISAR3_TRUENOP_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR3_T32COPY
// Description : Indicates the support for T32 non flag-setting MOV instructions
#define M33_ID_ISAR3_T32COPY_RESET  _u(0x8)
#define M33_ID_ISAR3_T32COPY_BITS   _u(0x00f00000)
#define M33_ID_ISAR3_T32COPY_MSB    _u(23)
#define M33_ID_ISAR3_T32COPY_LSB    _u(20)
#define M33_ID_ISAR3_T32COPY_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR3_TABBRANCH
// Description : Indicates the implemented Table Branch instructions
#define M33_ID_ISAR3_TABBRANCH_RESET  _u(0x9)
#define M33_ID_ISAR3_TABBRANCH_BITS   _u(0x000f0000)
#define M33_ID_ISAR3_TABBRANCH_MSB    _u(19)
#define M33_ID_ISAR3_TABBRANCH_LSB    _u(16)
#define M33_ID_ISAR3_TABBRANCH_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR3_SYNCHPRIM
// Description : Used in conjunction with ID_ISAR4.SynchPrim_frac to indicate
//               the implemented Synchronization Primitive instructions
#define M33_ID_ISAR3_SYNCHPRIM_RESET  _u(0x5)
#define M33_ID_ISAR3_SYNCHPRIM_BITS   _u(0x0000f000)
#define M33_ID_ISAR3_SYNCHPRIM_MSB    _u(15)
#define M33_ID_ISAR3_SYNCHPRIM_LSB    _u(12)
#define M33_ID_ISAR3_SYNCHPRIM_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR3_SVC
// Description : Indicates the implemented SVC instructions
#define M33_ID_ISAR3_SVC_RESET  _u(0x7)
#define M33_ID_ISAR3_SVC_BITS   _u(0x00000f00)
#define M33_ID_ISAR3_SVC_MSB    _u(11)
#define M33_ID_ISAR3_SVC_LSB    _u(8)
#define M33_ID_ISAR3_SVC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR3_SIMD
// Description : Indicates the implemented SIMD instructions
#define M33_ID_ISAR3_SIMD_RESET  _u(0x2)
#define M33_ID_ISAR3_SIMD_BITS   _u(0x000000f0)
#define M33_ID_ISAR3_SIMD_MSB    _u(7)
#define M33_ID_ISAR3_SIMD_LSB    _u(4)
#define M33_ID_ISAR3_SIMD_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR3_SATURATE
// Description : Indicates the implemented saturating instructions
#define M33_ID_ISAR3_SATURATE_RESET  _u(0x9)
#define M33_ID_ISAR3_SATURATE_BITS   _u(0x0000000f)
#define M33_ID_ISAR3_SATURATE_MSB    _u(3)
#define M33_ID_ISAR3_SATURATE_LSB    _u(0)
#define M33_ID_ISAR3_SATURATE_ACCESS "RO"
// =============================================================================
// Register    : M33_ID_ISAR4
// Description : Provides information about the instruction set implemented by
//               the PE
#define M33_ID_ISAR4_OFFSET _u(0x0000ed70)
#define M33_ID_ISAR4_BITS   _u(0x0fff0fff)
#define M33_ID_ISAR4_RESET  _u(0x01310132)
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR4_PSR_M
// Description : Indicates the implemented M profile instructions to modify the
//               PSRs
#define M33_ID_ISAR4_PSR_M_RESET  _u(0x1)
#define M33_ID_ISAR4_PSR_M_BITS   _u(0x0f000000)
#define M33_ID_ISAR4_PSR_M_MSB    _u(27)
#define M33_ID_ISAR4_PSR_M_LSB    _u(24)
#define M33_ID_ISAR4_PSR_M_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR4_SYNCPRIM_FRAC
// Description : Used in conjunction with ID_ISAR3.SynchPrim to indicate the
//               implemented Synchronization Primitive instructions
#define M33_ID_ISAR4_SYNCPRIM_FRAC_RESET  _u(0x3)
#define M33_ID_ISAR4_SYNCPRIM_FRAC_BITS   _u(0x00f00000)
#define M33_ID_ISAR4_SYNCPRIM_FRAC_MSB    _u(23)
#define M33_ID_ISAR4_SYNCPRIM_FRAC_LSB    _u(20)
#define M33_ID_ISAR4_SYNCPRIM_FRAC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR4_BARRIER
// Description : Indicates the implemented Barrier instructions
#define M33_ID_ISAR4_BARRIER_RESET  _u(0x1)
#define M33_ID_ISAR4_BARRIER_BITS   _u(0x000f0000)
#define M33_ID_ISAR4_BARRIER_MSB    _u(19)
#define M33_ID_ISAR4_BARRIER_LSB    _u(16)
#define M33_ID_ISAR4_BARRIER_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR4_WRITEBACK
// Description : Indicates the support for writeback addressing modes
#define M33_ID_ISAR4_WRITEBACK_RESET  _u(0x1)
#define M33_ID_ISAR4_WRITEBACK_BITS   _u(0x00000f00)
#define M33_ID_ISAR4_WRITEBACK_MSB    _u(11)
#define M33_ID_ISAR4_WRITEBACK_LSB    _u(8)
#define M33_ID_ISAR4_WRITEBACK_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR4_WITHSHIFTS
// Description : Indicates the support for writeback addressing modes
#define M33_ID_ISAR4_WITHSHIFTS_RESET  _u(0x3)
#define M33_ID_ISAR4_WITHSHIFTS_BITS   _u(0x000000f0)
#define M33_ID_ISAR4_WITHSHIFTS_MSB    _u(7)
#define M33_ID_ISAR4_WITHSHIFTS_LSB    _u(4)
#define M33_ID_ISAR4_WITHSHIFTS_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_ID_ISAR4_UNPRIV
// Description : Indicates the implemented unprivileged instructions
#define M33_ID_ISAR4_UNPRIV_RESET  _u(0x2)
#define M33_ID_ISAR4_UNPRIV_BITS   _u(0x0000000f)
#define M33_ID_ISAR4_UNPRIV_MSB    _u(3)
#define M33_ID_ISAR4_UNPRIV_LSB    _u(0)
#define M33_ID_ISAR4_UNPRIV_ACCESS "RO"
// =============================================================================
// Register    : M33_ID_ISAR5
// Description : Provides information about the instruction set implemented by
//               the PE
#define M33_ID_ISAR5_OFFSET _u(0x0000ed74)
#define M33_ID_ISAR5_BITS   _u(0x00000000)
#define M33_ID_ISAR5_RESET  _u(0x00000000)
#define M33_ID_ISAR5_MSB    _u(31)
#define M33_ID_ISAR5_LSB    _u(0)
#define M33_ID_ISAR5_ACCESS "RW"
// =============================================================================
// Register    : M33_CTR
// Description : Provides information about the architecture of the caches. CTR
//               is RES0 if CLIDR is zero.
#define M33_CTR_OFFSET _u(0x0000ed7c)
#define M33_CTR_BITS   _u(0x8fffc00f)
#define M33_CTR_RESET  _u(0x8000c000)
// -----------------------------------------------------------------------------
// Field       : M33_CTR_RES1
// Description : Reserved, RES1
#define M33_CTR_RES1_RESET  _u(0x1)
#define M33_CTR_RES1_BITS   _u(0x80000000)
#define M33_CTR_RES1_MSB    _u(31)
#define M33_CTR_RES1_LSB    _u(31)
#define M33_CTR_RES1_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_CTR_CWG
// Description : Log2 of the number of words of the maximum size of memory that
//               can be overwritten as a result of the eviction of a cache entry
//               that has had a memory location in it modified
#define M33_CTR_CWG_RESET  _u(0x0)
#define M33_CTR_CWG_BITS   _u(0x0f000000)
#define M33_CTR_CWG_MSB    _u(27)
#define M33_CTR_CWG_LSB    _u(24)
#define M33_CTR_CWG_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_CTR_ERG
// Description : Log2 of the number of words of the maximum size of the
//               reservation granule that has been implemented for the Load-
//               Exclusive and Store-Exclusive instructions
#define M33_CTR_ERG_RESET  _u(0x0)
#define M33_CTR_ERG_BITS   _u(0x00f00000)
#define M33_CTR_ERG_MSB    _u(23)
#define M33_CTR_ERG_LSB    _u(20)
#define M33_CTR_ERG_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_CTR_DMINLINE
// Description : Log2 of the number of words in the smallest cache line of all
//               the data caches and unified caches that are controlled by the
//               PE
#define M33_CTR_DMINLINE_RESET  _u(0x0)
#define M33_CTR_DMINLINE_BITS   _u(0x000f0000)
#define M33_CTR_DMINLINE_MSB    _u(19)
#define M33_CTR_DMINLINE_LSB    _u(16)
#define M33_CTR_DMINLINE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_CTR_RES1_1
// Description : Reserved, RES1
#define M33_CTR_RES1_1_RESET  _u(0x3)
#define M33_CTR_RES1_1_BITS   _u(0x0000c000)
#define M33_CTR_RES1_1_MSB    _u(15)
#define M33_CTR_RES1_1_LSB    _u(14)
#define M33_CTR_RES1_1_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_CTR_IMINLINE
// Description : Log2 of the number of words in the smallest cache line of all
//               the instruction caches that are controlled by the PE
#define M33_CTR_IMINLINE_RESET  _u(0x0)
#define M33_CTR_IMINLINE_BITS   _u(0x0000000f)
#define M33_CTR_IMINLINE_MSB    _u(3)
#define M33_CTR_IMINLINE_LSB    _u(0)
#define M33_CTR_IMINLINE_ACCESS "RO"
// =============================================================================
// Register    : M33_CPACR
// Description : Specifies the access privileges for coprocessors and the FP
//               Extension
#define M33_CPACR_OFFSET _u(0x0000ed88)
#define M33_CPACR_BITS   _u(0x00f0ffff)
#define M33_CPACR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CPACR_CP11
// Description : The value in this field is ignored. If the implementation does
//               not include the FP Extension, this field is RAZ/WI. If the
//               value of this bit is not programmed to the same value as the
//               CP10 field, then the value is UNKNOWN
#define M33_CPACR_CP11_RESET  _u(0x0)
#define M33_CPACR_CP11_BITS   _u(0x00c00000)
#define M33_CPACR_CP11_MSB    _u(23)
#define M33_CPACR_CP11_LSB    _u(22)
#define M33_CPACR_CP11_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CPACR_CP10
// Description : Defines the access rights for the floating-point functionality
#define M33_CPACR_CP10_RESET  _u(0x0)
#define M33_CPACR_CP10_BITS   _u(0x00300000)
#define M33_CPACR_CP10_MSB    _u(21)
#define M33_CPACR_CP10_LSB    _u(20)
#define M33_CPACR_CP10_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CPACR_CP7
// Description : Controls access privileges for coprocessor 7
#define M33_CPACR_CP7_RESET  _u(0x0)
#define M33_CPACR_CP7_BITS   _u(0x0000c000)
#define M33_CPACR_CP7_MSB    _u(15)
#define M33_CPACR_CP7_LSB    _u(14)
#define M33_CPACR_CP7_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CPACR_CP6
// Description : Controls access privileges for coprocessor 6
#define M33_CPACR_CP6_RESET  _u(0x0)
#define M33_CPACR_CP6_BITS   _u(0x00003000)
#define M33_CPACR_CP6_MSB    _u(13)
#define M33_CPACR_CP6_LSB    _u(12)
#define M33_CPACR_CP6_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CPACR_CP5
// Description : Controls access privileges for coprocessor 5
#define M33_CPACR_CP5_RESET  _u(0x0)
#define M33_CPACR_CP5_BITS   _u(0x00000c00)
#define M33_CPACR_CP5_MSB    _u(11)
#define M33_CPACR_CP5_LSB    _u(10)
#define M33_CPACR_CP5_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CPACR_CP4
// Description : Controls access privileges for coprocessor 4
#define M33_CPACR_CP4_RESET  _u(0x0)
#define M33_CPACR_CP4_BITS   _u(0x00000300)
#define M33_CPACR_CP4_MSB    _u(9)
#define M33_CPACR_CP4_LSB    _u(8)
#define M33_CPACR_CP4_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CPACR_CP3
// Description : Controls access privileges for coprocessor 3
#define M33_CPACR_CP3_RESET  _u(0x0)
#define M33_CPACR_CP3_BITS   _u(0x000000c0)
#define M33_CPACR_CP3_MSB    _u(7)
#define M33_CPACR_CP3_LSB    _u(6)
#define M33_CPACR_CP3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CPACR_CP2
// Description : Controls access privileges for coprocessor 2
#define M33_CPACR_CP2_RESET  _u(0x0)
#define M33_CPACR_CP2_BITS   _u(0x00000030)
#define M33_CPACR_CP2_MSB    _u(5)
#define M33_CPACR_CP2_LSB    _u(4)
#define M33_CPACR_CP2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CPACR_CP1
// Description : Controls access privileges for coprocessor 1
#define M33_CPACR_CP1_RESET  _u(0x0)
#define M33_CPACR_CP1_BITS   _u(0x0000000c)
#define M33_CPACR_CP1_MSB    _u(3)
#define M33_CPACR_CP1_LSB    _u(2)
#define M33_CPACR_CP1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CPACR_CP0
// Description : Controls access privileges for coprocessor 0
#define M33_CPACR_CP0_RESET  _u(0x0)
#define M33_CPACR_CP0_BITS   _u(0x00000003)
#define M33_CPACR_CP0_MSB    _u(1)
#define M33_CPACR_CP0_LSB    _u(0)
#define M33_CPACR_CP0_ACCESS "RW"
// =============================================================================
// Register    : M33_NSACR
// Description : Defines the Non-secure access permissions for both the FP
//               Extension and coprocessors CP0 to CP7
#define M33_NSACR_OFFSET _u(0x0000ed8c)
#define M33_NSACR_BITS   _u(0x00000cff)
#define M33_NSACR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_NSACR_CP11
// Description : Enables Non-secure access to the Floating-point Extension
#define M33_NSACR_CP11_RESET  _u(0x0)
#define M33_NSACR_CP11_BITS   _u(0x00000800)
#define M33_NSACR_CP11_MSB    _u(11)
#define M33_NSACR_CP11_LSB    _u(11)
#define M33_NSACR_CP11_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NSACR_CP10
// Description : Enables Non-secure access to the Floating-point Extension
#define M33_NSACR_CP10_RESET  _u(0x0)
#define M33_NSACR_CP10_BITS   _u(0x00000400)
#define M33_NSACR_CP10_MSB    _u(10)
#define M33_NSACR_CP10_LSB    _u(10)
#define M33_NSACR_CP10_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NSACR_CP7
// Description : Enables Non-secure access to coprocessor CP7
#define M33_NSACR_CP7_RESET  _u(0x0)
#define M33_NSACR_CP7_BITS   _u(0x00000080)
#define M33_NSACR_CP7_MSB    _u(7)
#define M33_NSACR_CP7_LSB    _u(7)
#define M33_NSACR_CP7_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NSACR_CP6
// Description : Enables Non-secure access to coprocessor CP6
#define M33_NSACR_CP6_RESET  _u(0x0)
#define M33_NSACR_CP6_BITS   _u(0x00000040)
#define M33_NSACR_CP6_MSB    _u(6)
#define M33_NSACR_CP6_LSB    _u(6)
#define M33_NSACR_CP6_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NSACR_CP5
// Description : Enables Non-secure access to coprocessor CP5
#define M33_NSACR_CP5_RESET  _u(0x0)
#define M33_NSACR_CP5_BITS   _u(0x00000020)
#define M33_NSACR_CP5_MSB    _u(5)
#define M33_NSACR_CP5_LSB    _u(5)
#define M33_NSACR_CP5_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NSACR_CP4
// Description : Enables Non-secure access to coprocessor CP4
#define M33_NSACR_CP4_RESET  _u(0x0)
#define M33_NSACR_CP4_BITS   _u(0x00000010)
#define M33_NSACR_CP4_MSB    _u(4)
#define M33_NSACR_CP4_LSB    _u(4)
#define M33_NSACR_CP4_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NSACR_CP3
// Description : Enables Non-secure access to coprocessor CP3
#define M33_NSACR_CP3_RESET  _u(0x0)
#define M33_NSACR_CP3_BITS   _u(0x00000008)
#define M33_NSACR_CP3_MSB    _u(3)
#define M33_NSACR_CP3_LSB    _u(3)
#define M33_NSACR_CP3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NSACR_CP2
// Description : Enables Non-secure access to coprocessor CP2
#define M33_NSACR_CP2_RESET  _u(0x0)
#define M33_NSACR_CP2_BITS   _u(0x00000004)
#define M33_NSACR_CP2_MSB    _u(2)
#define M33_NSACR_CP2_LSB    _u(2)
#define M33_NSACR_CP2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NSACR_CP1
// Description : Enables Non-secure access to coprocessor CP1
#define M33_NSACR_CP1_RESET  _u(0x0)
#define M33_NSACR_CP1_BITS   _u(0x00000002)
#define M33_NSACR_CP1_MSB    _u(1)
#define M33_NSACR_CP1_LSB    _u(1)
#define M33_NSACR_CP1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_NSACR_CP0
// Description : Enables Non-secure access to coprocessor CP0
#define M33_NSACR_CP0_RESET  _u(0x0)
#define M33_NSACR_CP0_BITS   _u(0x00000001)
#define M33_NSACR_CP0_MSB    _u(0)
#define M33_NSACR_CP0_LSB    _u(0)
#define M33_NSACR_CP0_ACCESS "RW"
// =============================================================================
// Register    : M33_MPU_TYPE
// Description : The MPU Type Register indicates how many regions the MPU `FTSSS
//               supports
#define M33_MPU_TYPE_OFFSET _u(0x0000ed90)
#define M33_MPU_TYPE_BITS   _u(0x0000ff01)
#define M33_MPU_TYPE_RESET  _u(0x00000800)
// -----------------------------------------------------------------------------
// Field       : M33_MPU_TYPE_DREGION
// Description : Number of regions supported by the MPU
#define M33_MPU_TYPE_DREGION_RESET  _u(0x08)
#define M33_MPU_TYPE_DREGION_BITS   _u(0x0000ff00)
#define M33_MPU_TYPE_DREGION_MSB    _u(15)
#define M33_MPU_TYPE_DREGION_LSB    _u(8)
#define M33_MPU_TYPE_DREGION_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_TYPE_SEPARATE
// Description : Indicates support for separate instructions and data address
//               regions
#define M33_MPU_TYPE_SEPARATE_RESET  _u(0x0)
#define M33_MPU_TYPE_SEPARATE_BITS   _u(0x00000001)
#define M33_MPU_TYPE_SEPARATE_MSB    _u(0)
#define M33_MPU_TYPE_SEPARATE_LSB    _u(0)
#define M33_MPU_TYPE_SEPARATE_ACCESS "RO"
// =============================================================================
// Register    : M33_MPU_CTRL
// Description : Enables the MPU and, when the MPU is enabled, controls whether
//               the default memory map is enabled as a background region for
//               privileged accesses, and whether the MPU is enabled for
//               HardFaults, NMIs, and exception handlers when FAULTMASK is set
//               to 1
#define M33_MPU_CTRL_OFFSET _u(0x0000ed94)
#define M33_MPU_CTRL_BITS   _u(0x00000007)
#define M33_MPU_CTRL_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_MPU_CTRL_PRIVDEFENA
// Description : Controls whether the default memory map is enabled for
//               privileged software
#define M33_MPU_CTRL_PRIVDEFENA_RESET  _u(0x0)
#define M33_MPU_CTRL_PRIVDEFENA_BITS   _u(0x00000004)
#define M33_MPU_CTRL_PRIVDEFENA_MSB    _u(2)
#define M33_MPU_CTRL_PRIVDEFENA_LSB    _u(2)
#define M33_MPU_CTRL_PRIVDEFENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_CTRL_HFNMIENA
// Description : Controls whether handlers executing with priority less than 0
//               access memory with the MPU enabled or disabled. This applies to
//               HardFaults, NMIs, and exception handlers when FAULTMASK is set
//               to 1
#define M33_MPU_CTRL_HFNMIENA_RESET  _u(0x0)
#define M33_MPU_CTRL_HFNMIENA_BITS   _u(0x00000002)
#define M33_MPU_CTRL_HFNMIENA_MSB    _u(1)
#define M33_MPU_CTRL_HFNMIENA_LSB    _u(1)
#define M33_MPU_CTRL_HFNMIENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_CTRL_ENABLE
// Description : Enables the MPU
#define M33_MPU_CTRL_ENABLE_RESET  _u(0x0)
#define M33_MPU_CTRL_ENABLE_BITS   _u(0x00000001)
#define M33_MPU_CTRL_ENABLE_MSB    _u(0)
#define M33_MPU_CTRL_ENABLE_LSB    _u(0)
#define M33_MPU_CTRL_ENABLE_ACCESS "RW"
// =============================================================================
// Register    : M33_MPU_RNR
// Description : Selects the region currently accessed by MPU_RBAR and MPU_RLAR
#define M33_MPU_RNR_OFFSET _u(0x0000ed98)
#define M33_MPU_RNR_BITS   _u(0x00000007)
#define M33_MPU_RNR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RNR_REGION
// Description : Indicates the memory region accessed by MPU_RBAR and MPU_RLAR
#define M33_MPU_RNR_REGION_RESET  _u(0x0)
#define M33_MPU_RNR_REGION_BITS   _u(0x00000007)
#define M33_MPU_RNR_REGION_MSB    _u(2)
#define M33_MPU_RNR_REGION_LSB    _u(0)
#define M33_MPU_RNR_REGION_ACCESS "RW"
// =============================================================================
// Register    : M33_MPU_RBAR
// Description : Provides indirect read and write access to the base address of
//               the currently selected MPU region `FTSSS
#define M33_MPU_RBAR_OFFSET _u(0x0000ed9c)
#define M33_MPU_RBAR_BITS   _u(0xffffffff)
#define M33_MPU_RBAR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RBAR_BASE
// Description : Contains bits [31:5] of the lower inclusive limit of the
//               selected MPU memory region. This value is zero extended to
//               provide the base address to be checked against
#define M33_MPU_RBAR_BASE_RESET  _u(0x0000000)
#define M33_MPU_RBAR_BASE_BITS   _u(0xffffffe0)
#define M33_MPU_RBAR_BASE_MSB    _u(31)
#define M33_MPU_RBAR_BASE_LSB    _u(5)
#define M33_MPU_RBAR_BASE_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RBAR_SH
// Description : Defines the Shareability domain of this region for Normal
//               memory
#define M33_MPU_RBAR_SH_RESET  _u(0x0)
#define M33_MPU_RBAR_SH_BITS   _u(0x00000018)
#define M33_MPU_RBAR_SH_MSB    _u(4)
#define M33_MPU_RBAR_SH_LSB    _u(3)
#define M33_MPU_RBAR_SH_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RBAR_AP
// Description : Defines the access permissions for this region
#define M33_MPU_RBAR_AP_RESET  _u(0x0)
#define M33_MPU_RBAR_AP_BITS   _u(0x00000006)
#define M33_MPU_RBAR_AP_MSB    _u(2)
#define M33_MPU_RBAR_AP_LSB    _u(1)
#define M33_MPU_RBAR_AP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RBAR_XN
// Description : Defines whether code can be executed from this region
#define M33_MPU_RBAR_XN_RESET  _u(0x0)
#define M33_MPU_RBAR_XN_BITS   _u(0x00000001)
#define M33_MPU_RBAR_XN_MSB    _u(0)
#define M33_MPU_RBAR_XN_LSB    _u(0)
#define M33_MPU_RBAR_XN_ACCESS "RW"
// =============================================================================
// Register    : M33_MPU_RLAR
// Description : Provides indirect read and write access to the limit address of
//               the currently selected MPU region `FTSSS
#define M33_MPU_RLAR_OFFSET _u(0x0000eda0)
#define M33_MPU_RLAR_BITS   _u(0xffffffef)
#define M33_MPU_RLAR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RLAR_LIMIT
// Description : Contains bits [31:5] of the upper inclusive limit of the
//               selected MPU memory region. This value is postfixed with 0x1F
//               to provide the limit address to be checked against
#define M33_MPU_RLAR_LIMIT_RESET  _u(0x0000000)
#define M33_MPU_RLAR_LIMIT_BITS   _u(0xffffffe0)
#define M33_MPU_RLAR_LIMIT_MSB    _u(31)
#define M33_MPU_RLAR_LIMIT_LSB    _u(5)
#define M33_MPU_RLAR_LIMIT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RLAR_ATTRINDX
// Description : Associates a set of attributes in the MPU_MAIR0 and MPU_MAIR1
//               fields
#define M33_MPU_RLAR_ATTRINDX_RESET  _u(0x0)
#define M33_MPU_RLAR_ATTRINDX_BITS   _u(0x0000000e)
#define M33_MPU_RLAR_ATTRINDX_MSB    _u(3)
#define M33_MPU_RLAR_ATTRINDX_LSB    _u(1)
#define M33_MPU_RLAR_ATTRINDX_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RLAR_EN
// Description : Region enable
#define M33_MPU_RLAR_EN_RESET  _u(0x0)
#define M33_MPU_RLAR_EN_BITS   _u(0x00000001)
#define M33_MPU_RLAR_EN_MSB    _u(0)
#define M33_MPU_RLAR_EN_LSB    _u(0)
#define M33_MPU_RLAR_EN_ACCESS "RW"
// =============================================================================
// Register    : M33_MPU_RBAR_A1
// Description : Provides indirect read and write access to the base address of
//               the MPU region selected by MPU_RNR[7:2]:(1[1:0]) `FTSSS
#define M33_MPU_RBAR_A1_OFFSET _u(0x0000eda4)
#define M33_MPU_RBAR_A1_BITS   _u(0xffffffff)
#define M33_MPU_RBAR_A1_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RBAR_A1_BASE
// Description : Contains bits [31:5] of the lower inclusive limit of the
//               selected MPU memory region. This value is zero extended to
//               provide the base address to be checked against
#define M33_MPU_RBAR_A1_BASE_RESET  _u(0x0000000)
#define M33_MPU_RBAR_A1_BASE_BITS   _u(0xffffffe0)
#define M33_MPU_RBAR_A1_BASE_MSB    _u(31)
#define M33_MPU_RBAR_A1_BASE_LSB    _u(5)
#define M33_MPU_RBAR_A1_BASE_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RBAR_A1_SH
// Description : Defines the Shareability domain of this region for Normal
//               memory
#define M33_MPU_RBAR_A1_SH_RESET  _u(0x0)
#define M33_MPU_RBAR_A1_SH_BITS   _u(0x00000018)
#define M33_MPU_RBAR_A1_SH_MSB    _u(4)
#define M33_MPU_RBAR_A1_SH_LSB    _u(3)
#define M33_MPU_RBAR_A1_SH_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RBAR_A1_AP
// Description : Defines the access permissions for this region
#define M33_MPU_RBAR_A1_AP_RESET  _u(0x0)
#define M33_MPU_RBAR_A1_AP_BITS   _u(0x00000006)
#define M33_MPU_RBAR_A1_AP_MSB    _u(2)
#define M33_MPU_RBAR_A1_AP_LSB    _u(1)
#define M33_MPU_RBAR_A1_AP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RBAR_A1_XN
// Description : Defines whether code can be executed from this region
#define M33_MPU_RBAR_A1_XN_RESET  _u(0x0)
#define M33_MPU_RBAR_A1_XN_BITS   _u(0x00000001)
#define M33_MPU_RBAR_A1_XN_MSB    _u(0)
#define M33_MPU_RBAR_A1_XN_LSB    _u(0)
#define M33_MPU_RBAR_A1_XN_ACCESS "RW"
// =============================================================================
// Register    : M33_MPU_RLAR_A1
// Description : Provides indirect read and write access to the limit address of
//               the currently selected MPU region selected by
//               MPU_RNR[7:2]:(1[1:0]) `FTSSS
#define M33_MPU_RLAR_A1_OFFSET _u(0x0000eda8)
#define M33_MPU_RLAR_A1_BITS   _u(0xffffffef)
#define M33_MPU_RLAR_A1_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RLAR_A1_LIMIT
// Description : Contains bits [31:5] of the upper inclusive limit of the
//               selected MPU memory region. This value is postfixed with 0x1F
//               to provide the limit address to be checked against
#define M33_MPU_RLAR_A1_LIMIT_RESET  _u(0x0000000)
#define M33_MPU_RLAR_A1_LIMIT_BITS   _u(0xffffffe0)
#define M33_MPU_RLAR_A1_LIMIT_MSB    _u(31)
#define M33_MPU_RLAR_A1_LIMIT_LSB    _u(5)
#define M33_MPU_RLAR_A1_LIMIT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RLAR_A1_ATTRINDX
// Description : Associates a set of attributes in the MPU_MAIR0 and MPU_MAIR1
//               fields
#define M33_MPU_RLAR_A1_ATTRINDX_RESET  _u(0x0)
#define M33_MPU_RLAR_A1_ATTRINDX_BITS   _u(0x0000000e)
#define M33_MPU_RLAR_A1_ATTRINDX_MSB    _u(3)
#define M33_MPU_RLAR_A1_ATTRINDX_LSB    _u(1)
#define M33_MPU_RLAR_A1_ATTRINDX_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RLAR_A1_EN
// Description : Region enable
#define M33_MPU_RLAR_A1_EN_RESET  _u(0x0)
#define M33_MPU_RLAR_A1_EN_BITS   _u(0x00000001)
#define M33_MPU_RLAR_A1_EN_MSB    _u(0)
#define M33_MPU_RLAR_A1_EN_LSB    _u(0)
#define M33_MPU_RLAR_A1_EN_ACCESS "RW"
// =============================================================================
// Register    : M33_MPU_RBAR_A2
// Description : Provides indirect read and write access to the base address of
//               the MPU region selected by MPU_RNR[7:2]:(2[1:0]) `FTSSS
#define M33_MPU_RBAR_A2_OFFSET _u(0x0000edac)
#define M33_MPU_RBAR_A2_BITS   _u(0xffffffff)
#define M33_MPU_RBAR_A2_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RBAR_A2_BASE
// Description : Contains bits [31:5] of the lower inclusive limit of the
//               selected MPU memory region. This value is zero extended to
//               provide the base address to be checked against
#define M33_MPU_RBAR_A2_BASE_RESET  _u(0x0000000)
#define M33_MPU_RBAR_A2_BASE_BITS   _u(0xffffffe0)
#define M33_MPU_RBAR_A2_BASE_MSB    _u(31)
#define M33_MPU_RBAR_A2_BASE_LSB    _u(5)
#define M33_MPU_RBAR_A2_BASE_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RBAR_A2_SH
// Description : Defines the Shareability domain of this region for Normal
//               memory
#define M33_MPU_RBAR_A2_SH_RESET  _u(0x0)
#define M33_MPU_RBAR_A2_SH_BITS   _u(0x00000018)
#define M33_MPU_RBAR_A2_SH_MSB    _u(4)
#define M33_MPU_RBAR_A2_SH_LSB    _u(3)
#define M33_MPU_RBAR_A2_SH_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RBAR_A2_AP
// Description : Defines the access permissions for this region
#define M33_MPU_RBAR_A2_AP_RESET  _u(0x0)
#define M33_MPU_RBAR_A2_AP_BITS   _u(0x00000006)
#define M33_MPU_RBAR_A2_AP_MSB    _u(2)
#define M33_MPU_RBAR_A2_AP_LSB    _u(1)
#define M33_MPU_RBAR_A2_AP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RBAR_A2_XN
// Description : Defines whether code can be executed from this region
#define M33_MPU_RBAR_A2_XN_RESET  _u(0x0)
#define M33_MPU_RBAR_A2_XN_BITS   _u(0x00000001)
#define M33_MPU_RBAR_A2_XN_MSB    _u(0)
#define M33_MPU_RBAR_A2_XN_LSB    _u(0)
#define M33_MPU_RBAR_A2_XN_ACCESS "RW"
// =============================================================================
// Register    : M33_MPU_RLAR_A2
// Description : Provides indirect read and write access to the limit address of
//               the currently selected MPU region selected by
//               MPU_RNR[7:2]:(2[1:0]) `FTSSS
#define M33_MPU_RLAR_A2_OFFSET _u(0x0000edb0)
#define M33_MPU_RLAR_A2_BITS   _u(0xffffffef)
#define M33_MPU_RLAR_A2_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RLAR_A2_LIMIT
// Description : Contains bits [31:5] of the upper inclusive limit of the
//               selected MPU memory region. This value is postfixed with 0x1F
//               to provide the limit address to be checked against
#define M33_MPU_RLAR_A2_LIMIT_RESET  _u(0x0000000)
#define M33_MPU_RLAR_A2_LIMIT_BITS   _u(0xffffffe0)
#define M33_MPU_RLAR_A2_LIMIT_MSB    _u(31)
#define M33_MPU_RLAR_A2_LIMIT_LSB    _u(5)
#define M33_MPU_RLAR_A2_LIMIT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RLAR_A2_ATTRINDX
// Description : Associates a set of attributes in the MPU_MAIR0 and MPU_MAIR1
//               fields
#define M33_MPU_RLAR_A2_ATTRINDX_RESET  _u(0x0)
#define M33_MPU_RLAR_A2_ATTRINDX_BITS   _u(0x0000000e)
#define M33_MPU_RLAR_A2_ATTRINDX_MSB    _u(3)
#define M33_MPU_RLAR_A2_ATTRINDX_LSB    _u(1)
#define M33_MPU_RLAR_A2_ATTRINDX_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RLAR_A2_EN
// Description : Region enable
#define M33_MPU_RLAR_A2_EN_RESET  _u(0x0)
#define M33_MPU_RLAR_A2_EN_BITS   _u(0x00000001)
#define M33_MPU_RLAR_A2_EN_MSB    _u(0)
#define M33_MPU_RLAR_A2_EN_LSB    _u(0)
#define M33_MPU_RLAR_A2_EN_ACCESS "RW"
// =============================================================================
// Register    : M33_MPU_RBAR_A3
// Description : Provides indirect read and write access to the base address of
//               the MPU region selected by MPU_RNR[7:2]:(3[1:0]) `FTSSS
#define M33_MPU_RBAR_A3_OFFSET _u(0x0000edb4)
#define M33_MPU_RBAR_A3_BITS   _u(0xffffffff)
#define M33_MPU_RBAR_A3_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RBAR_A3_BASE
// Description : Contains bits [31:5] of the lower inclusive limit of the
//               selected MPU memory region. This value is zero extended to
//               provide the base address to be checked against
#define M33_MPU_RBAR_A3_BASE_RESET  _u(0x0000000)
#define M33_MPU_RBAR_A3_BASE_BITS   _u(0xffffffe0)
#define M33_MPU_RBAR_A3_BASE_MSB    _u(31)
#define M33_MPU_RBAR_A3_BASE_LSB    _u(5)
#define M33_MPU_RBAR_A3_BASE_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RBAR_A3_SH
// Description : Defines the Shareability domain of this region for Normal
//               memory
#define M33_MPU_RBAR_A3_SH_RESET  _u(0x0)
#define M33_MPU_RBAR_A3_SH_BITS   _u(0x00000018)
#define M33_MPU_RBAR_A3_SH_MSB    _u(4)
#define M33_MPU_RBAR_A3_SH_LSB    _u(3)
#define M33_MPU_RBAR_A3_SH_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RBAR_A3_AP
// Description : Defines the access permissions for this region
#define M33_MPU_RBAR_A3_AP_RESET  _u(0x0)
#define M33_MPU_RBAR_A3_AP_BITS   _u(0x00000006)
#define M33_MPU_RBAR_A3_AP_MSB    _u(2)
#define M33_MPU_RBAR_A3_AP_LSB    _u(1)
#define M33_MPU_RBAR_A3_AP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RBAR_A3_XN
// Description : Defines whether code can be executed from this region
#define M33_MPU_RBAR_A3_XN_RESET  _u(0x0)
#define M33_MPU_RBAR_A3_XN_BITS   _u(0x00000001)
#define M33_MPU_RBAR_A3_XN_MSB    _u(0)
#define M33_MPU_RBAR_A3_XN_LSB    _u(0)
#define M33_MPU_RBAR_A3_XN_ACCESS "RW"
// =============================================================================
// Register    : M33_MPU_RLAR_A3
// Description : Provides indirect read and write access to the limit address of
//               the currently selected MPU region selected by
//               MPU_RNR[7:2]:(3[1:0]) `FTSSS
#define M33_MPU_RLAR_A3_OFFSET _u(0x0000edb8)
#define M33_MPU_RLAR_A3_BITS   _u(0xffffffef)
#define M33_MPU_RLAR_A3_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RLAR_A3_LIMIT
// Description : Contains bits [31:5] of the upper inclusive limit of the
//               selected MPU memory region. This value is postfixed with 0x1F
//               to provide the limit address to be checked against
#define M33_MPU_RLAR_A3_LIMIT_RESET  _u(0x0000000)
#define M33_MPU_RLAR_A3_LIMIT_BITS   _u(0xffffffe0)
#define M33_MPU_RLAR_A3_LIMIT_MSB    _u(31)
#define M33_MPU_RLAR_A3_LIMIT_LSB    _u(5)
#define M33_MPU_RLAR_A3_LIMIT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RLAR_A3_ATTRINDX
// Description : Associates a set of attributes in the MPU_MAIR0 and MPU_MAIR1
//               fields
#define M33_MPU_RLAR_A3_ATTRINDX_RESET  _u(0x0)
#define M33_MPU_RLAR_A3_ATTRINDX_BITS   _u(0x0000000e)
#define M33_MPU_RLAR_A3_ATTRINDX_MSB    _u(3)
#define M33_MPU_RLAR_A3_ATTRINDX_LSB    _u(1)
#define M33_MPU_RLAR_A3_ATTRINDX_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_RLAR_A3_EN
// Description : Region enable
#define M33_MPU_RLAR_A3_EN_RESET  _u(0x0)
#define M33_MPU_RLAR_A3_EN_BITS   _u(0x00000001)
#define M33_MPU_RLAR_A3_EN_MSB    _u(0)
#define M33_MPU_RLAR_A3_EN_LSB    _u(0)
#define M33_MPU_RLAR_A3_EN_ACCESS "RW"
// =============================================================================
// Register    : M33_MPU_MAIR0
// Description : Along with MPU_MAIR1, provides the memory attribute encodings
//               corresponding to the AttrIndex values
#define M33_MPU_MAIR0_OFFSET _u(0x0000edc0)
#define M33_MPU_MAIR0_BITS   _u(0xffffffff)
#define M33_MPU_MAIR0_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_MPU_MAIR0_ATTR3
// Description : Memory attribute encoding for MPU regions with an AttrIndex of
//               3
#define M33_MPU_MAIR0_ATTR3_RESET  _u(0x00)
#define M33_MPU_MAIR0_ATTR3_BITS   _u(0xff000000)
#define M33_MPU_MAIR0_ATTR3_MSB    _u(31)
#define M33_MPU_MAIR0_ATTR3_LSB    _u(24)
#define M33_MPU_MAIR0_ATTR3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_MAIR0_ATTR2
// Description : Memory attribute encoding for MPU regions with an AttrIndex of
//               2
#define M33_MPU_MAIR0_ATTR2_RESET  _u(0x00)
#define M33_MPU_MAIR0_ATTR2_BITS   _u(0x00ff0000)
#define M33_MPU_MAIR0_ATTR2_MSB    _u(23)
#define M33_MPU_MAIR0_ATTR2_LSB    _u(16)
#define M33_MPU_MAIR0_ATTR2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_MAIR0_ATTR1
// Description : Memory attribute encoding for MPU regions with an AttrIndex of
//               1
#define M33_MPU_MAIR0_ATTR1_RESET  _u(0x00)
#define M33_MPU_MAIR0_ATTR1_BITS   _u(0x0000ff00)
#define M33_MPU_MAIR0_ATTR1_MSB    _u(15)
#define M33_MPU_MAIR0_ATTR1_LSB    _u(8)
#define M33_MPU_MAIR0_ATTR1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_MAIR0_ATTR0
// Description : Memory attribute encoding for MPU regions with an AttrIndex of
//               0
#define M33_MPU_MAIR0_ATTR0_RESET  _u(0x00)
#define M33_MPU_MAIR0_ATTR0_BITS   _u(0x000000ff)
#define M33_MPU_MAIR0_ATTR0_MSB    _u(7)
#define M33_MPU_MAIR0_ATTR0_LSB    _u(0)
#define M33_MPU_MAIR0_ATTR0_ACCESS "RW"
// =============================================================================
// Register    : M33_MPU_MAIR1
// Description : Along with MPU_MAIR0, provides the memory attribute encodings
//               corresponding to the AttrIndex values
#define M33_MPU_MAIR1_OFFSET _u(0x0000edc4)
#define M33_MPU_MAIR1_BITS   _u(0xffffffff)
#define M33_MPU_MAIR1_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_MPU_MAIR1_ATTR7
// Description : Memory attribute encoding for MPU regions with an AttrIndex of
//               7
#define M33_MPU_MAIR1_ATTR7_RESET  _u(0x00)
#define M33_MPU_MAIR1_ATTR7_BITS   _u(0xff000000)
#define M33_MPU_MAIR1_ATTR7_MSB    _u(31)
#define M33_MPU_MAIR1_ATTR7_LSB    _u(24)
#define M33_MPU_MAIR1_ATTR7_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_MAIR1_ATTR6
// Description : Memory attribute encoding for MPU regions with an AttrIndex of
//               6
#define M33_MPU_MAIR1_ATTR6_RESET  _u(0x00)
#define M33_MPU_MAIR1_ATTR6_BITS   _u(0x00ff0000)
#define M33_MPU_MAIR1_ATTR6_MSB    _u(23)
#define M33_MPU_MAIR1_ATTR6_LSB    _u(16)
#define M33_MPU_MAIR1_ATTR6_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_MAIR1_ATTR5
// Description : Memory attribute encoding for MPU regions with an AttrIndex of
//               5
#define M33_MPU_MAIR1_ATTR5_RESET  _u(0x00)
#define M33_MPU_MAIR1_ATTR5_BITS   _u(0x0000ff00)
#define M33_MPU_MAIR1_ATTR5_MSB    _u(15)
#define M33_MPU_MAIR1_ATTR5_LSB    _u(8)
#define M33_MPU_MAIR1_ATTR5_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_MPU_MAIR1_ATTR4
// Description : Memory attribute encoding for MPU regions with an AttrIndex of
//               4
#define M33_MPU_MAIR1_ATTR4_RESET  _u(0x00)
#define M33_MPU_MAIR1_ATTR4_BITS   _u(0x000000ff)
#define M33_MPU_MAIR1_ATTR4_MSB    _u(7)
#define M33_MPU_MAIR1_ATTR4_LSB    _u(0)
#define M33_MPU_MAIR1_ATTR4_ACCESS "RW"
// =============================================================================
// Register    : M33_SAU_CTRL
// Description : Allows enabling of the Security Attribution Unit
#define M33_SAU_CTRL_OFFSET _u(0x0000edd0)
#define M33_SAU_CTRL_BITS   _u(0x00000003)
#define M33_SAU_CTRL_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_SAU_CTRL_ALLNS
// Description : When SAU_CTRL.ENABLE is 0 this bit controls if the memory is
//               marked as Non-secure or Secure
#define M33_SAU_CTRL_ALLNS_RESET  _u(0x0)
#define M33_SAU_CTRL_ALLNS_BITS   _u(0x00000002)
#define M33_SAU_CTRL_ALLNS_MSB    _u(1)
#define M33_SAU_CTRL_ALLNS_LSB    _u(1)
#define M33_SAU_CTRL_ALLNS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SAU_CTRL_ENABLE
// Description : Enables the SAU
#define M33_SAU_CTRL_ENABLE_RESET  _u(0x0)
#define M33_SAU_CTRL_ENABLE_BITS   _u(0x00000001)
#define M33_SAU_CTRL_ENABLE_MSB    _u(0)
#define M33_SAU_CTRL_ENABLE_LSB    _u(0)
#define M33_SAU_CTRL_ENABLE_ACCESS "RW"
// =============================================================================
// Register    : M33_SAU_TYPE
// Description : Indicates the number of regions implemented by the Security
//               Attribution Unit
#define M33_SAU_TYPE_OFFSET _u(0x0000edd4)
#define M33_SAU_TYPE_BITS   _u(0x000000ff)
#define M33_SAU_TYPE_RESET  _u(0x00000008)
// -----------------------------------------------------------------------------
// Field       : M33_SAU_TYPE_SREGION
// Description : The number of implemented SAU regions
#define M33_SAU_TYPE_SREGION_RESET  _u(0x08)
#define M33_SAU_TYPE_SREGION_BITS   _u(0x000000ff)
#define M33_SAU_TYPE_SREGION_MSB    _u(7)
#define M33_SAU_TYPE_SREGION_LSB    _u(0)
#define M33_SAU_TYPE_SREGION_ACCESS "RO"
// =============================================================================
// Register    : M33_SAU_RNR
// Description : Selects the region currently accessed by SAU_RBAR and SAU_RLAR
#define M33_SAU_RNR_OFFSET _u(0x0000edd8)
#define M33_SAU_RNR_BITS   _u(0x000000ff)
#define M33_SAU_RNR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_SAU_RNR_REGION
// Description : Indicates the SAU region accessed by SAU_RBAR and SAU_RLAR
#define M33_SAU_RNR_REGION_RESET  _u(0x00)
#define M33_SAU_RNR_REGION_BITS   _u(0x000000ff)
#define M33_SAU_RNR_REGION_MSB    _u(7)
#define M33_SAU_RNR_REGION_LSB    _u(0)
#define M33_SAU_RNR_REGION_ACCESS "RW"
// =============================================================================
// Register    : M33_SAU_RBAR
// Description : Provides indirect read and write access to the base address of
//               the currently selected SAU region
#define M33_SAU_RBAR_OFFSET _u(0x0000eddc)
#define M33_SAU_RBAR_BITS   _u(0xffffffe0)
#define M33_SAU_RBAR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_SAU_RBAR_BADDR
// Description : Holds bits [31:5] of the base address for the selected SAU
//               region
#define M33_SAU_RBAR_BADDR_RESET  _u(0x0000000)
#define M33_SAU_RBAR_BADDR_BITS   _u(0xffffffe0)
#define M33_SAU_RBAR_BADDR_MSB    _u(31)
#define M33_SAU_RBAR_BADDR_LSB    _u(5)
#define M33_SAU_RBAR_BADDR_ACCESS "RW"
// =============================================================================
// Register    : M33_SAU_RLAR
// Description : Provides indirect read and write access to the limit address of
//               the currently selected SAU region
#define M33_SAU_RLAR_OFFSET _u(0x0000ede0)
#define M33_SAU_RLAR_BITS   _u(0xffffffe3)
#define M33_SAU_RLAR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_SAU_RLAR_LADDR
// Description : Holds bits [31:5] of the limit address for the selected SAU
//               region
#define M33_SAU_RLAR_LADDR_RESET  _u(0x0000000)
#define M33_SAU_RLAR_LADDR_BITS   _u(0xffffffe0)
#define M33_SAU_RLAR_LADDR_MSB    _u(31)
#define M33_SAU_RLAR_LADDR_LSB    _u(5)
#define M33_SAU_RLAR_LADDR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SAU_RLAR_NSC
// Description : Controls whether Non-secure state is permitted to execute an SG
//               instruction from this region
#define M33_SAU_RLAR_NSC_RESET  _u(0x0)
#define M33_SAU_RLAR_NSC_BITS   _u(0x00000002)
#define M33_SAU_RLAR_NSC_MSB    _u(1)
#define M33_SAU_RLAR_NSC_LSB    _u(1)
#define M33_SAU_RLAR_NSC_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SAU_RLAR_ENABLE
// Description : SAU region enable
#define M33_SAU_RLAR_ENABLE_RESET  _u(0x0)
#define M33_SAU_RLAR_ENABLE_BITS   _u(0x00000001)
#define M33_SAU_RLAR_ENABLE_MSB    _u(0)
#define M33_SAU_RLAR_ENABLE_LSB    _u(0)
#define M33_SAU_RLAR_ENABLE_ACCESS "RW"
// =============================================================================
// Register    : M33_SFSR
// Description : Provides information about any security related faults
#define M33_SFSR_OFFSET _u(0x0000ede4)
#define M33_SFSR_BITS   _u(0x000000ff)
#define M33_SFSR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_SFSR_LSERR
// Description : Sticky flag indicating that an error occurred during lazy state
//               activation or deactivation
#define M33_SFSR_LSERR_RESET  _u(0x0)
#define M33_SFSR_LSERR_BITS   _u(0x00000080)
#define M33_SFSR_LSERR_MSB    _u(7)
#define M33_SFSR_LSERR_LSB    _u(7)
#define M33_SFSR_LSERR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SFSR_SFARVALID
// Description : This bit is set when the SFAR register contains a valid value.
//               As with similar fields, such as BFSR.BFARVALID and
//               MMFSR.MMARVALID, this bit can be cleared by other exceptions,
//               such as BusFault
#define M33_SFSR_SFARVALID_RESET  _u(0x0)
#define M33_SFSR_SFARVALID_BITS   _u(0x00000040)
#define M33_SFSR_SFARVALID_MSB    _u(6)
#define M33_SFSR_SFARVALID_LSB    _u(6)
#define M33_SFSR_SFARVALID_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SFSR_LSPERR
// Description : Stick flag indicating that an SAU or IDAU violation occurred
//               during the lazy preservation of floating-point state
#define M33_SFSR_LSPERR_RESET  _u(0x0)
#define M33_SFSR_LSPERR_BITS   _u(0x00000020)
#define M33_SFSR_LSPERR_MSB    _u(5)
#define M33_SFSR_LSPERR_LSB    _u(5)
#define M33_SFSR_LSPERR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SFSR_INVTRAN
// Description : Sticky flag indicating that an exception was raised due to a
//               branch that was not flagged as being domain crossing causing a
//               transition from Secure to Non-secure memory
#define M33_SFSR_INVTRAN_RESET  _u(0x0)
#define M33_SFSR_INVTRAN_BITS   _u(0x00000010)
#define M33_SFSR_INVTRAN_MSB    _u(4)
#define M33_SFSR_INVTRAN_LSB    _u(4)
#define M33_SFSR_INVTRAN_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SFSR_AUVIOL
// Description : Sticky flag indicating that an attempt was made to access parts
//               of the address space that are marked as Secure with NS-Req for
//               the transaction set to Non-secure. This bit is not set if the
//               violation occurred during lazy state preservation. See LSPERR
#define M33_SFSR_AUVIOL_RESET  _u(0x0)
#define M33_SFSR_AUVIOL_BITS   _u(0x00000008)
#define M33_SFSR_AUVIOL_MSB    _u(3)
#define M33_SFSR_AUVIOL_LSB    _u(3)
#define M33_SFSR_AUVIOL_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SFSR_INVER
// Description : This can be caused by EXC_RETURN.DCRS being set to 0 when
//               returning from an exception in the Non-secure state, or by
//               EXC_RETURN.ES being set to 1 when returning from an exception
//               in the Non-secure state
#define M33_SFSR_INVER_RESET  _u(0x0)
#define M33_SFSR_INVER_BITS   _u(0x00000004)
#define M33_SFSR_INVER_MSB    _u(2)
#define M33_SFSR_INVER_LSB    _u(2)
#define M33_SFSR_INVER_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SFSR_INVIS
// Description : This bit is set if the integrity signature in an exception
//               stack frame is found to be invalid during the unstacking
//               operation
#define M33_SFSR_INVIS_RESET  _u(0x0)
#define M33_SFSR_INVIS_BITS   _u(0x00000002)
#define M33_SFSR_INVIS_MSB    _u(1)
#define M33_SFSR_INVIS_LSB    _u(1)
#define M33_SFSR_INVIS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_SFSR_INVEP
// Description : This bit is set if a function call from the Non-secure state or
//               exception targets a non-SG instruction in the Secure state.
//               This bit is also set if the target address is a SG instruction,
//               but there is no matching SAU/IDAU region with the NSC flag set
#define M33_SFSR_INVEP_RESET  _u(0x0)
#define M33_SFSR_INVEP_BITS   _u(0x00000001)
#define M33_SFSR_INVEP_MSB    _u(0)
#define M33_SFSR_INVEP_LSB    _u(0)
#define M33_SFSR_INVEP_ACCESS "RW"
// =============================================================================
// Register    : M33_SFAR
// Description : Shows the address of the memory location that caused a Security
//               violation
#define M33_SFAR_OFFSET _u(0x0000ede8)
#define M33_SFAR_BITS   _u(0xffffffff)
#define M33_SFAR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_SFAR_ADDRESS
// Description : The address of an access that caused a attribution unit
//               violation. This field is only valid when SFSR.SFARVALID is set.
//               This allows the actual flip flops associated with this register
//               to be shared with other fault address registers. If an
//               implementation chooses to share the storage in this way, care
//               must be taken to not leak Secure address information to the
//               Non-secure state. One way of achieving this is to share the
//               SFAR register with the MMFAR_S register, which is not
//               accessible to the Non-secure state
#define M33_SFAR_ADDRESS_RESET  _u(0x00000000)
#define M33_SFAR_ADDRESS_BITS   _u(0xffffffff)
#define M33_SFAR_ADDRESS_MSB    _u(31)
#define M33_SFAR_ADDRESS_LSB    _u(0)
#define M33_SFAR_ADDRESS_ACCESS "RW"
// =============================================================================
// Register    : M33_DHCSR
// Description : Controls halting debug
#define M33_DHCSR_OFFSET _u(0x0000edf0)
#define M33_DHCSR_BITS   _u(0x071f002f)
#define M33_DHCSR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_DHCSR_S_RESTART_ST
// Description : Indicates the PE has processed a request to clear DHCSR.C_HALT
//               to 0. That is, either a write to DHCSR that clears DHCSR.C_HALT
//               from 1 to 0, or an External Restart Request
#define M33_DHCSR_S_RESTART_ST_RESET  _u(0x0)
#define M33_DHCSR_S_RESTART_ST_BITS   _u(0x04000000)
#define M33_DHCSR_S_RESTART_ST_MSB    _u(26)
#define M33_DHCSR_S_RESTART_ST_LSB    _u(26)
#define M33_DHCSR_S_RESTART_ST_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DHCSR_S_RESET_ST
// Description : Indicates whether the PE has been reset since the last read of
//               the DHCSR
#define M33_DHCSR_S_RESET_ST_RESET  _u(0x0)
#define M33_DHCSR_S_RESET_ST_BITS   _u(0x02000000)
#define M33_DHCSR_S_RESET_ST_MSB    _u(25)
#define M33_DHCSR_S_RESET_ST_LSB    _u(25)
#define M33_DHCSR_S_RESET_ST_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DHCSR_S_RETIRE_ST
// Description : Set to 1 every time the PE retires one of more instructions
#define M33_DHCSR_S_RETIRE_ST_RESET  _u(0x0)
#define M33_DHCSR_S_RETIRE_ST_BITS   _u(0x01000000)
#define M33_DHCSR_S_RETIRE_ST_MSB    _u(24)
#define M33_DHCSR_S_RETIRE_ST_LSB    _u(24)
#define M33_DHCSR_S_RETIRE_ST_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DHCSR_S_SDE
// Description : Indicates whether Secure invasive debug is allowed
#define M33_DHCSR_S_SDE_RESET  _u(0x0)
#define M33_DHCSR_S_SDE_BITS   _u(0x00100000)
#define M33_DHCSR_S_SDE_MSB    _u(20)
#define M33_DHCSR_S_SDE_LSB    _u(20)
#define M33_DHCSR_S_SDE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DHCSR_S_LOCKUP
// Description : Indicates whether the PE is in Lockup state
#define M33_DHCSR_S_LOCKUP_RESET  _u(0x0)
#define M33_DHCSR_S_LOCKUP_BITS   _u(0x00080000)
#define M33_DHCSR_S_LOCKUP_MSB    _u(19)
#define M33_DHCSR_S_LOCKUP_LSB    _u(19)
#define M33_DHCSR_S_LOCKUP_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DHCSR_S_SLEEP
// Description : Indicates whether the PE is sleeping
#define M33_DHCSR_S_SLEEP_RESET  _u(0x0)
#define M33_DHCSR_S_SLEEP_BITS   _u(0x00040000)
#define M33_DHCSR_S_SLEEP_MSB    _u(18)
#define M33_DHCSR_S_SLEEP_LSB    _u(18)
#define M33_DHCSR_S_SLEEP_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DHCSR_S_HALT
// Description : Indicates whether the PE is in Debug state
#define M33_DHCSR_S_HALT_RESET  _u(0x0)
#define M33_DHCSR_S_HALT_BITS   _u(0x00020000)
#define M33_DHCSR_S_HALT_MSB    _u(17)
#define M33_DHCSR_S_HALT_LSB    _u(17)
#define M33_DHCSR_S_HALT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DHCSR_S_REGRDY
// Description : Handshake flag to transfers through the DCRDR
#define M33_DHCSR_S_REGRDY_RESET  _u(0x0)
#define M33_DHCSR_S_REGRDY_BITS   _u(0x00010000)
#define M33_DHCSR_S_REGRDY_MSB    _u(16)
#define M33_DHCSR_S_REGRDY_LSB    _u(16)
#define M33_DHCSR_S_REGRDY_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DHCSR_C_SNAPSTALL
// Description : Allow imprecise entry to Debug state
#define M33_DHCSR_C_SNAPSTALL_RESET  _u(0x0)
#define M33_DHCSR_C_SNAPSTALL_BITS   _u(0x00000020)
#define M33_DHCSR_C_SNAPSTALL_MSB    _u(5)
#define M33_DHCSR_C_SNAPSTALL_LSB    _u(5)
#define M33_DHCSR_C_SNAPSTALL_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DHCSR_C_MASKINTS
// Description : When debug is enabled, the debugger can write to this bit to
//               mask PendSV, SysTick and external configurable interrupts
#define M33_DHCSR_C_MASKINTS_RESET  _u(0x0)
#define M33_DHCSR_C_MASKINTS_BITS   _u(0x00000008)
#define M33_DHCSR_C_MASKINTS_MSB    _u(3)
#define M33_DHCSR_C_MASKINTS_LSB    _u(3)
#define M33_DHCSR_C_MASKINTS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DHCSR_C_STEP
// Description : Enable single instruction step
#define M33_DHCSR_C_STEP_RESET  _u(0x0)
#define M33_DHCSR_C_STEP_BITS   _u(0x00000004)
#define M33_DHCSR_C_STEP_MSB    _u(2)
#define M33_DHCSR_C_STEP_LSB    _u(2)
#define M33_DHCSR_C_STEP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DHCSR_C_HALT
// Description : PE enter Debug state halt request
#define M33_DHCSR_C_HALT_RESET  _u(0x0)
#define M33_DHCSR_C_HALT_BITS   _u(0x00000002)
#define M33_DHCSR_C_HALT_MSB    _u(1)
#define M33_DHCSR_C_HALT_LSB    _u(1)
#define M33_DHCSR_C_HALT_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DHCSR_C_DEBUGEN
// Description : Enable Halting debug
#define M33_DHCSR_C_DEBUGEN_RESET  _u(0x0)
#define M33_DHCSR_C_DEBUGEN_BITS   _u(0x00000001)
#define M33_DHCSR_C_DEBUGEN_MSB    _u(0)
#define M33_DHCSR_C_DEBUGEN_LSB    _u(0)
#define M33_DHCSR_C_DEBUGEN_ACCESS "RW"
// =============================================================================
// Register    : M33_DCRSR
// Description : With the DCRDR, provides debug access to the general-purpose
//               registers, special-purpose registers, and the FP extension
//               registers. A write to the DCRSR specifies the register to
//               transfer, whether the transfer is a read or write, and starts
//               the transfer
#define M33_DCRSR_OFFSET _u(0x0000edf4)
#define M33_DCRSR_BITS   _u(0x0001007f)
#define M33_DCRSR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_DCRSR_REGWNR
// Description : Specifies the access type for the transfer
#define M33_DCRSR_REGWNR_RESET  _u(0x0)
#define M33_DCRSR_REGWNR_BITS   _u(0x00010000)
#define M33_DCRSR_REGWNR_MSB    _u(16)
#define M33_DCRSR_REGWNR_LSB    _u(16)
#define M33_DCRSR_REGWNR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DCRSR_REGSEL
// Description : Specifies the general-purpose register, special-purpose
//               register, or FP register to transfer
#define M33_DCRSR_REGSEL_RESET  _u(0x00)
#define M33_DCRSR_REGSEL_BITS   _u(0x0000007f)
#define M33_DCRSR_REGSEL_MSB    _u(6)
#define M33_DCRSR_REGSEL_LSB    _u(0)
#define M33_DCRSR_REGSEL_ACCESS "RW"
// =============================================================================
// Register    : M33_DCRDR
// Description : With the DCRSR, provides debug access to the general-purpose
//               registers, special-purpose registers, and the FP Extension
//               registers. If the Main Extension is implemented, it can also be
//               used for message passing between an external debugger and a
//               debug agent running on the PE
#define M33_DCRDR_OFFSET _u(0x0000edf8)
#define M33_DCRDR_BITS   _u(0xffffffff)
#define M33_DCRDR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_DCRDR_DBGTMP
// Description : Provides debug access for reading and writing the general-
//               purpose registers, special-purpose registers, and Floating-
//               point Extension registers
#define M33_DCRDR_DBGTMP_RESET  _u(0x00000000)
#define M33_DCRDR_DBGTMP_BITS   _u(0xffffffff)
#define M33_DCRDR_DBGTMP_MSB    _u(31)
#define M33_DCRDR_DBGTMP_LSB    _u(0)
#define M33_DCRDR_DBGTMP_ACCESS "RW"
// =============================================================================
// Register    : M33_DEMCR
// Description : Manages vector catch behavior and DebugMonitor handling when
//               debugging
#define M33_DEMCR_OFFSET _u(0x0000edfc)
#define M33_DEMCR_BITS   _u(0x011f0ff1)
#define M33_DEMCR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_DEMCR_TRCENA
// Description : Global enable for all DWT and ITM features
#define M33_DEMCR_TRCENA_RESET  _u(0x0)
#define M33_DEMCR_TRCENA_BITS   _u(0x01000000)
#define M33_DEMCR_TRCENA_MSB    _u(24)
#define M33_DEMCR_TRCENA_LSB    _u(24)
#define M33_DEMCR_TRCENA_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DEMCR_SDME
// Description : Indicates whether the DebugMonitor targets the Secure or the
//               Non-secure state and whether debug events are allowed in Secure
//               state
#define M33_DEMCR_SDME_RESET  _u(0x0)
#define M33_DEMCR_SDME_BITS   _u(0x00100000)
#define M33_DEMCR_SDME_MSB    _u(20)
#define M33_DEMCR_SDME_LSB    _u(20)
#define M33_DEMCR_SDME_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DEMCR_MON_REQ
// Description : DebugMonitor semaphore bit
#define M33_DEMCR_MON_REQ_RESET  _u(0x0)
#define M33_DEMCR_MON_REQ_BITS   _u(0x00080000)
#define M33_DEMCR_MON_REQ_MSB    _u(19)
#define M33_DEMCR_MON_REQ_LSB    _u(19)
#define M33_DEMCR_MON_REQ_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DEMCR_MON_STEP
// Description : Enable DebugMonitor stepping
#define M33_DEMCR_MON_STEP_RESET  _u(0x0)
#define M33_DEMCR_MON_STEP_BITS   _u(0x00040000)
#define M33_DEMCR_MON_STEP_MSB    _u(18)
#define M33_DEMCR_MON_STEP_LSB    _u(18)
#define M33_DEMCR_MON_STEP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DEMCR_MON_PEND
// Description : Sets or clears the pending state of the DebugMonitor exception
#define M33_DEMCR_MON_PEND_RESET  _u(0x0)
#define M33_DEMCR_MON_PEND_BITS   _u(0x00020000)
#define M33_DEMCR_MON_PEND_MSB    _u(17)
#define M33_DEMCR_MON_PEND_LSB    _u(17)
#define M33_DEMCR_MON_PEND_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DEMCR_MON_EN
// Description : Enable the DebugMonitor exception
#define M33_DEMCR_MON_EN_RESET  _u(0x0)
#define M33_DEMCR_MON_EN_BITS   _u(0x00010000)
#define M33_DEMCR_MON_EN_MSB    _u(16)
#define M33_DEMCR_MON_EN_LSB    _u(16)
#define M33_DEMCR_MON_EN_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DEMCR_VC_SFERR
// Description : SecureFault exception halting debug vector catch enable
#define M33_DEMCR_VC_SFERR_RESET  _u(0x0)
#define M33_DEMCR_VC_SFERR_BITS   _u(0x00000800)
#define M33_DEMCR_VC_SFERR_MSB    _u(11)
#define M33_DEMCR_VC_SFERR_LSB    _u(11)
#define M33_DEMCR_VC_SFERR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DEMCR_VC_HARDERR
// Description : HardFault exception halting debug vector catch enable
#define M33_DEMCR_VC_HARDERR_RESET  _u(0x0)
#define M33_DEMCR_VC_HARDERR_BITS   _u(0x00000400)
#define M33_DEMCR_VC_HARDERR_MSB    _u(10)
#define M33_DEMCR_VC_HARDERR_LSB    _u(10)
#define M33_DEMCR_VC_HARDERR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DEMCR_VC_INTERR
// Description : Enable halting debug vector catch for faults during exception
//               entry and return
#define M33_DEMCR_VC_INTERR_RESET  _u(0x0)
#define M33_DEMCR_VC_INTERR_BITS   _u(0x00000200)
#define M33_DEMCR_VC_INTERR_MSB    _u(9)
#define M33_DEMCR_VC_INTERR_LSB    _u(9)
#define M33_DEMCR_VC_INTERR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DEMCR_VC_BUSERR
// Description : BusFault exception halting debug vector catch enable
#define M33_DEMCR_VC_BUSERR_RESET  _u(0x0)
#define M33_DEMCR_VC_BUSERR_BITS   _u(0x00000100)
#define M33_DEMCR_VC_BUSERR_MSB    _u(8)
#define M33_DEMCR_VC_BUSERR_LSB    _u(8)
#define M33_DEMCR_VC_BUSERR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DEMCR_VC_STATERR
// Description : Enable halting debug trap on a UsageFault exception caused by a
//               state information error, for example an Undefined Instruction
//               exception
#define M33_DEMCR_VC_STATERR_RESET  _u(0x0)
#define M33_DEMCR_VC_STATERR_BITS   _u(0x00000080)
#define M33_DEMCR_VC_STATERR_MSB    _u(7)
#define M33_DEMCR_VC_STATERR_LSB    _u(7)
#define M33_DEMCR_VC_STATERR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DEMCR_VC_CHKERR
// Description : Enable halting debug trap on a UsageFault exception caused by a
//               checking error, for example an alignment check error
#define M33_DEMCR_VC_CHKERR_RESET  _u(0x0)
#define M33_DEMCR_VC_CHKERR_BITS   _u(0x00000040)
#define M33_DEMCR_VC_CHKERR_MSB    _u(6)
#define M33_DEMCR_VC_CHKERR_LSB    _u(6)
#define M33_DEMCR_VC_CHKERR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DEMCR_VC_NOCPERR
// Description : Enable halting debug trap on a UsageFault caused by an access
//               to a coprocessor
#define M33_DEMCR_VC_NOCPERR_RESET  _u(0x0)
#define M33_DEMCR_VC_NOCPERR_BITS   _u(0x00000020)
#define M33_DEMCR_VC_NOCPERR_MSB    _u(5)
#define M33_DEMCR_VC_NOCPERR_LSB    _u(5)
#define M33_DEMCR_VC_NOCPERR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DEMCR_VC_MMERR
// Description : Enable halting debug trap on a MemManage exception
#define M33_DEMCR_VC_MMERR_RESET  _u(0x0)
#define M33_DEMCR_VC_MMERR_BITS   _u(0x00000010)
#define M33_DEMCR_VC_MMERR_MSB    _u(4)
#define M33_DEMCR_VC_MMERR_LSB    _u(4)
#define M33_DEMCR_VC_MMERR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DEMCR_VC_CORERESET
// Description : Enable Reset Vector Catch. This causes a warm reset to halt a
//               running system
#define M33_DEMCR_VC_CORERESET_RESET  _u(0x0)
#define M33_DEMCR_VC_CORERESET_BITS   _u(0x00000001)
#define M33_DEMCR_VC_CORERESET_MSB    _u(0)
#define M33_DEMCR_VC_CORERESET_LSB    _u(0)
#define M33_DEMCR_VC_CORERESET_ACCESS "RW"
// =============================================================================
// Register    : M33_DSCSR
// Description : Provides control and status information for Secure debug
#define M33_DSCSR_OFFSET _u(0x0000ee08)
#define M33_DSCSR_BITS   _u(0x00030003)
#define M33_DSCSR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_DSCSR_CDSKEY
// Description : Writes to the CDS bit are ignored unless CDSKEY is concurrently
//               written to zero
#define M33_DSCSR_CDSKEY_RESET  _u(0x0)
#define M33_DSCSR_CDSKEY_BITS   _u(0x00020000)
#define M33_DSCSR_CDSKEY_MSB    _u(17)
#define M33_DSCSR_CDSKEY_LSB    _u(17)
#define M33_DSCSR_CDSKEY_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DSCSR_CDS
// Description : This field indicates the current Security state of the
//               processor
#define M33_DSCSR_CDS_RESET  _u(0x0)
#define M33_DSCSR_CDS_BITS   _u(0x00010000)
#define M33_DSCSR_CDS_MSB    _u(16)
#define M33_DSCSR_CDS_LSB    _u(16)
#define M33_DSCSR_CDS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DSCSR_SBRSEL
// Description : If SBRSELEN is 1 this bit selects whether the Non-secure or the
//               Secure version of the memory-mapped Banked registers are
//               accessible to the debugger
#define M33_DSCSR_SBRSEL_RESET  _u(0x0)
#define M33_DSCSR_SBRSEL_BITS   _u(0x00000002)
#define M33_DSCSR_SBRSEL_MSB    _u(1)
#define M33_DSCSR_SBRSEL_LSB    _u(1)
#define M33_DSCSR_SBRSEL_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_DSCSR_SBRSELEN
// Description : Controls whether the SBRSEL field or the current Security state
//               of the processor selects which version of the memory-mapped
//               Banked registers are accessed to the debugger
#define M33_DSCSR_SBRSELEN_RESET  _u(0x0)
#define M33_DSCSR_SBRSELEN_BITS   _u(0x00000001)
#define M33_DSCSR_SBRSELEN_MSB    _u(0)
#define M33_DSCSR_SBRSELEN_LSB    _u(0)
#define M33_DSCSR_SBRSELEN_ACCESS "RW"
// =============================================================================
// Register    : M33_STIR
// Description : Provides a mechanism for software to generate an interrupt
#define M33_STIR_OFFSET _u(0x0000ef00)
#define M33_STIR_BITS   _u(0x000001ff)
#define M33_STIR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_STIR_INTID
// Description : Indicates the interrupt to be pended. The value written is
//               (ExceptionNumber - 16)
#define M33_STIR_INTID_RESET  _u(0x000)
#define M33_STIR_INTID_BITS   _u(0x000001ff)
#define M33_STIR_INTID_MSB    _u(8)
#define M33_STIR_INTID_LSB    _u(0)
#define M33_STIR_INTID_ACCESS "RW"
// =============================================================================
// Register    : M33_FPCCR
// Description : Holds control data for the Floating-point extension
#define M33_FPCCR_OFFSET _u(0x0000ef34)
#define M33_FPCCR_BITS   _u(0xfc0007ff)
#define M33_FPCCR_RESET  _u(0x20000472)
// -----------------------------------------------------------------------------
// Field       : M33_FPCCR_ASPEN
// Description : When this bit is set to 1, execution of a floating-point
//               instruction sets the CONTROL.FPCA bit to 1
#define M33_FPCCR_ASPEN_RESET  _u(0x0)
#define M33_FPCCR_ASPEN_BITS   _u(0x80000000)
#define M33_FPCCR_ASPEN_MSB    _u(31)
#define M33_FPCCR_ASPEN_LSB    _u(31)
#define M33_FPCCR_ASPEN_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPCCR_LSPEN
// Description : Enables lazy context save of floating-point state
#define M33_FPCCR_LSPEN_RESET  _u(0x0)
#define M33_FPCCR_LSPEN_BITS   _u(0x40000000)
#define M33_FPCCR_LSPEN_MSB    _u(30)
#define M33_FPCCR_LSPEN_LSB    _u(30)
#define M33_FPCCR_LSPEN_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPCCR_LSPENS
// Description : This bit controls whether the LSPEN bit is writeable from the
//               Non-secure state
#define M33_FPCCR_LSPENS_RESET  _u(0x1)
#define M33_FPCCR_LSPENS_BITS   _u(0x20000000)
#define M33_FPCCR_LSPENS_MSB    _u(29)
#define M33_FPCCR_LSPENS_LSB    _u(29)
#define M33_FPCCR_LSPENS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPCCR_CLRONRET
// Description : Clear floating-point caller saved registers on exception return
#define M33_FPCCR_CLRONRET_RESET  _u(0x0)
#define M33_FPCCR_CLRONRET_BITS   _u(0x10000000)
#define M33_FPCCR_CLRONRET_MSB    _u(28)
#define M33_FPCCR_CLRONRET_LSB    _u(28)
#define M33_FPCCR_CLRONRET_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPCCR_CLRONRETS
// Description : This bit controls whether the CLRONRET bit is writeable from
//               the Non-secure state
#define M33_FPCCR_CLRONRETS_RESET  _u(0x0)
#define M33_FPCCR_CLRONRETS_BITS   _u(0x08000000)
#define M33_FPCCR_CLRONRETS_MSB    _u(27)
#define M33_FPCCR_CLRONRETS_LSB    _u(27)
#define M33_FPCCR_CLRONRETS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPCCR_TS
// Description : Treat floating-point registers as Secure enable
#define M33_FPCCR_TS_RESET  _u(0x0)
#define M33_FPCCR_TS_BITS   _u(0x04000000)
#define M33_FPCCR_TS_MSB    _u(26)
#define M33_FPCCR_TS_LSB    _u(26)
#define M33_FPCCR_TS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPCCR_UFRDY
// Description : Indicates whether the software executing when the PE allocated
//               the floating-point stack frame was able to set the UsageFault
//               exception to pending
#define M33_FPCCR_UFRDY_RESET  _u(0x1)
#define M33_FPCCR_UFRDY_BITS   _u(0x00000400)
#define M33_FPCCR_UFRDY_MSB    _u(10)
#define M33_FPCCR_UFRDY_LSB    _u(10)
#define M33_FPCCR_UFRDY_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPCCR_SPLIMVIOL
// Description : This bit is banked between the Security states and indicates
//               whether the floating-point context violates the stack pointer
//               limit that was active when lazy state preservation was
//               activated. SPLIMVIOL modifies the lazy floating-point state
//               preservation behavior
#define M33_FPCCR_SPLIMVIOL_RESET  _u(0x0)
#define M33_FPCCR_SPLIMVIOL_BITS   _u(0x00000200)
#define M33_FPCCR_SPLIMVIOL_MSB    _u(9)
#define M33_FPCCR_SPLIMVIOL_LSB    _u(9)
#define M33_FPCCR_SPLIMVIOL_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPCCR_MONRDY
// Description : Indicates whether the software executing when the PE allocated
//               the floating-point stack frame was able to set the DebugMonitor
//               exception to pending
#define M33_FPCCR_MONRDY_RESET  _u(0x0)
#define M33_FPCCR_MONRDY_BITS   _u(0x00000100)
#define M33_FPCCR_MONRDY_MSB    _u(8)
#define M33_FPCCR_MONRDY_LSB    _u(8)
#define M33_FPCCR_MONRDY_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPCCR_SFRDY
// Description : Indicates whether the software executing when the PE allocated
//               the floating-point stack frame was able to set the SecureFault
//               exception to pending. This bit is only present in the Secure
//               version of the register, and behaves as RAZ/WI when accessed
//               from the Non-secure state
#define M33_FPCCR_SFRDY_RESET  _u(0x0)
#define M33_FPCCR_SFRDY_BITS   _u(0x00000080)
#define M33_FPCCR_SFRDY_MSB    _u(7)
#define M33_FPCCR_SFRDY_LSB    _u(7)
#define M33_FPCCR_SFRDY_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPCCR_BFRDY
// Description : Indicates whether the software executing when the PE allocated
//               the floating-point stack frame was able to set the BusFault
//               exception to pending
#define M33_FPCCR_BFRDY_RESET  _u(0x1)
#define M33_FPCCR_BFRDY_BITS   _u(0x00000040)
#define M33_FPCCR_BFRDY_MSB    _u(6)
#define M33_FPCCR_BFRDY_LSB    _u(6)
#define M33_FPCCR_BFRDY_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPCCR_MMRDY
// Description : Indicates whether the software executing when the PE allocated
//               the floating-point stack frame was able to set the MemManage
//               exception to pending
#define M33_FPCCR_MMRDY_RESET  _u(0x1)
#define M33_FPCCR_MMRDY_BITS   _u(0x00000020)
#define M33_FPCCR_MMRDY_MSB    _u(5)
#define M33_FPCCR_MMRDY_LSB    _u(5)
#define M33_FPCCR_MMRDY_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPCCR_HFRDY
// Description : Indicates whether the software executing when the PE allocated
//               the floating-point stack frame was able to set the HardFault
//               exception to pending
#define M33_FPCCR_HFRDY_RESET  _u(0x1)
#define M33_FPCCR_HFRDY_BITS   _u(0x00000010)
#define M33_FPCCR_HFRDY_MSB    _u(4)
#define M33_FPCCR_HFRDY_LSB    _u(4)
#define M33_FPCCR_HFRDY_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPCCR_THREAD
// Description : Indicates the PE mode when it allocated the floating-point
//               stack frame
#define M33_FPCCR_THREAD_RESET  _u(0x0)
#define M33_FPCCR_THREAD_BITS   _u(0x00000008)
#define M33_FPCCR_THREAD_MSB    _u(3)
#define M33_FPCCR_THREAD_LSB    _u(3)
#define M33_FPCCR_THREAD_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPCCR_S
// Description : Security status of the floating-point context. This bit is only
//               present in the Secure version of the register, and behaves as
//               RAZ/WI when accessed from the Non-secure state. This bit is
//               updated whenever lazy state preservation is activated, or when
//               a floating-point instruction is executed
#define M33_FPCCR_S_RESET  _u(0x0)
#define M33_FPCCR_S_BITS   _u(0x00000004)
#define M33_FPCCR_S_MSB    _u(2)
#define M33_FPCCR_S_LSB    _u(2)
#define M33_FPCCR_S_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPCCR_USER
// Description : Indicates the privilege level of the software executing when
//               the PE allocated the floating-point stack frame
#define M33_FPCCR_USER_RESET  _u(0x1)
#define M33_FPCCR_USER_BITS   _u(0x00000002)
#define M33_FPCCR_USER_MSB    _u(1)
#define M33_FPCCR_USER_LSB    _u(1)
#define M33_FPCCR_USER_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPCCR_LSPACT
// Description : Indicates whether lazy preservation of the floating-point state
//               is active
#define M33_FPCCR_LSPACT_RESET  _u(0x0)
#define M33_FPCCR_LSPACT_BITS   _u(0x00000001)
#define M33_FPCCR_LSPACT_MSB    _u(0)
#define M33_FPCCR_LSPACT_LSB    _u(0)
#define M33_FPCCR_LSPACT_ACCESS "RW"
// =============================================================================
// Register    : M33_FPCAR
// Description : Holds the location of the unpopulated floating-point register
//               space allocated on an exception stack frame
#define M33_FPCAR_OFFSET _u(0x0000ef38)
#define M33_FPCAR_BITS   _u(0xfffffff8)
#define M33_FPCAR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_FPCAR_ADDRESS
// Description : The location of the unpopulated floating-point register space
//               allocated on an exception stack frame
#define M33_FPCAR_ADDRESS_RESET  _u(0x00000000)
#define M33_FPCAR_ADDRESS_BITS   _u(0xfffffff8)
#define M33_FPCAR_ADDRESS_MSB    _u(31)
#define M33_FPCAR_ADDRESS_LSB    _u(3)
#define M33_FPCAR_ADDRESS_ACCESS "RW"
// =============================================================================
// Register    : M33_FPDSCR
// Description : Holds the default values for the floating-point status control
//               data that the PE assigns to the FPSCR when it creates a new
//               floating-point context
#define M33_FPDSCR_OFFSET _u(0x0000ef3c)
#define M33_FPDSCR_BITS   _u(0x07c00000)
#define M33_FPDSCR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_FPDSCR_AHP
// Description : Default value for FPSCR.AHP
#define M33_FPDSCR_AHP_RESET  _u(0x0)
#define M33_FPDSCR_AHP_BITS   _u(0x04000000)
#define M33_FPDSCR_AHP_MSB    _u(26)
#define M33_FPDSCR_AHP_LSB    _u(26)
#define M33_FPDSCR_AHP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPDSCR_DN
// Description : Default value for FPSCR.DN
#define M33_FPDSCR_DN_RESET  _u(0x0)
#define M33_FPDSCR_DN_BITS   _u(0x02000000)
#define M33_FPDSCR_DN_MSB    _u(25)
#define M33_FPDSCR_DN_LSB    _u(25)
#define M33_FPDSCR_DN_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPDSCR_FZ
// Description : Default value for FPSCR.FZ
#define M33_FPDSCR_FZ_RESET  _u(0x0)
#define M33_FPDSCR_FZ_BITS   _u(0x01000000)
#define M33_FPDSCR_FZ_MSB    _u(24)
#define M33_FPDSCR_FZ_LSB    _u(24)
#define M33_FPDSCR_FZ_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_FPDSCR_RMODE
// Description : Default value for FPSCR.RMode
#define M33_FPDSCR_RMODE_RESET  _u(0x0)
#define M33_FPDSCR_RMODE_BITS   _u(0x00c00000)
#define M33_FPDSCR_RMODE_MSB    _u(23)
#define M33_FPDSCR_RMODE_LSB    _u(22)
#define M33_FPDSCR_RMODE_ACCESS "RW"
// =============================================================================
// Register    : M33_MVFR0
// Description : Describes the features provided by the Floating-point Extension
#define M33_MVFR0_OFFSET _u(0x0000ef40)
#define M33_MVFR0_BITS   _u(0xf0ff0fff)
#define M33_MVFR0_RESET  _u(0x60540601)
// -----------------------------------------------------------------------------
// Field       : M33_MVFR0_FPROUND
// Description : Indicates the rounding modes supported by the FP Extension
#define M33_MVFR0_FPROUND_RESET  _u(0x6)
#define M33_MVFR0_FPROUND_BITS   _u(0xf0000000)
#define M33_MVFR0_FPROUND_MSB    _u(31)
#define M33_MVFR0_FPROUND_LSB    _u(28)
#define M33_MVFR0_FPROUND_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_MVFR0_FPSQRT
// Description : Indicates the support for FP square root operations
#define M33_MVFR0_FPSQRT_RESET  _u(0x5)
#define M33_MVFR0_FPSQRT_BITS   _u(0x00f00000)
#define M33_MVFR0_FPSQRT_MSB    _u(23)
#define M33_MVFR0_FPSQRT_LSB    _u(20)
#define M33_MVFR0_FPSQRT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_MVFR0_FPDIVIDE
// Description : Indicates the support for FP divide operations
#define M33_MVFR0_FPDIVIDE_RESET  _u(0x4)
#define M33_MVFR0_FPDIVIDE_BITS   _u(0x000f0000)
#define M33_MVFR0_FPDIVIDE_MSB    _u(19)
#define M33_MVFR0_FPDIVIDE_LSB    _u(16)
#define M33_MVFR0_FPDIVIDE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_MVFR0_FPDP
// Description : Indicates support for FP double-precision operations
#define M33_MVFR0_FPDP_RESET  _u(0x6)
#define M33_MVFR0_FPDP_BITS   _u(0x00000f00)
#define M33_MVFR0_FPDP_MSB    _u(11)
#define M33_MVFR0_FPDP_LSB    _u(8)
#define M33_MVFR0_FPDP_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_MVFR0_FPSP
// Description : Indicates support for FP single-precision operations
#define M33_MVFR0_FPSP_RESET  _u(0x0)
#define M33_MVFR0_FPSP_BITS   _u(0x000000f0)
#define M33_MVFR0_FPSP_MSB    _u(7)
#define M33_MVFR0_FPSP_LSB    _u(4)
#define M33_MVFR0_FPSP_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_MVFR0_SIMDREG
// Description : Indicates size of FP register file
#define M33_MVFR0_SIMDREG_RESET  _u(0x1)
#define M33_MVFR0_SIMDREG_BITS   _u(0x0000000f)
#define M33_MVFR0_SIMDREG_MSB    _u(3)
#define M33_MVFR0_SIMDREG_LSB    _u(0)
#define M33_MVFR0_SIMDREG_ACCESS "RO"
// =============================================================================
// Register    : M33_MVFR1
// Description : Describes the features provided by the Floating-point Extension
#define M33_MVFR1_OFFSET _u(0x0000ef44)
#define M33_MVFR1_BITS   _u(0xff0000ff)
#define M33_MVFR1_RESET  _u(0x85000089)
// -----------------------------------------------------------------------------
// Field       : M33_MVFR1_FMAC
// Description : Indicates whether the FP Extension implements the fused
//               multiply accumulate instructions
#define M33_MVFR1_FMAC_RESET  _u(0x8)
#define M33_MVFR1_FMAC_BITS   _u(0xf0000000)
#define M33_MVFR1_FMAC_MSB    _u(31)
#define M33_MVFR1_FMAC_LSB    _u(28)
#define M33_MVFR1_FMAC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_MVFR1_FPHP
// Description : Indicates whether the FP Extension implements half-precision FP
//               conversion instructions
#define M33_MVFR1_FPHP_RESET  _u(0x5)
#define M33_MVFR1_FPHP_BITS   _u(0x0f000000)
#define M33_MVFR1_FPHP_MSB    _u(27)
#define M33_MVFR1_FPHP_LSB    _u(24)
#define M33_MVFR1_FPHP_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_MVFR1_FPDNAN
// Description : Indicates whether the FP hardware implementation supports NaN
//               propagation
#define M33_MVFR1_FPDNAN_RESET  _u(0x8)
#define M33_MVFR1_FPDNAN_BITS   _u(0x000000f0)
#define M33_MVFR1_FPDNAN_MSB    _u(7)
#define M33_MVFR1_FPDNAN_LSB    _u(4)
#define M33_MVFR1_FPDNAN_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_MVFR1_FPFTZ
// Description : Indicates whether subnormals are always flushed-to-zero
#define M33_MVFR1_FPFTZ_RESET  _u(0x9)
#define M33_MVFR1_FPFTZ_BITS   _u(0x0000000f)
#define M33_MVFR1_FPFTZ_MSB    _u(3)
#define M33_MVFR1_FPFTZ_LSB    _u(0)
#define M33_MVFR1_FPFTZ_ACCESS "RO"
// =============================================================================
// Register    : M33_MVFR2
// Description : Describes the features provided by the Floating-point Extension
#define M33_MVFR2_OFFSET _u(0x0000ef48)
#define M33_MVFR2_BITS   _u(0x000000f0)
#define M33_MVFR2_RESET  _u(0x00000060)
// -----------------------------------------------------------------------------
// Field       : M33_MVFR2_FPMISC
// Description : Indicates support for miscellaneous FP features
#define M33_MVFR2_FPMISC_RESET  _u(0x6)
#define M33_MVFR2_FPMISC_BITS   _u(0x000000f0)
#define M33_MVFR2_FPMISC_MSB    _u(7)
#define M33_MVFR2_FPMISC_LSB    _u(4)
#define M33_MVFR2_FPMISC_ACCESS "RO"
// =============================================================================
// Register    : M33_DDEVARCH
// Description : Provides CoreSight discovery information for the SCS
#define M33_DDEVARCH_OFFSET _u(0x0000efbc)
#define M33_DDEVARCH_BITS   _u(0xffffffff)
#define M33_DDEVARCH_RESET  _u(0x47702a04)
// -----------------------------------------------------------------------------
// Field       : M33_DDEVARCH_ARCHITECT
// Description : Defines the architect of the component. Bits [31:28] are the
//               JEP106 continuation code (JEP106 bank ID, minus 1) and bits
//               [27:21] are the JEP106 ID code.
#define M33_DDEVARCH_ARCHITECT_RESET  _u(0x23b)
#define M33_DDEVARCH_ARCHITECT_BITS   _u(0xffe00000)
#define M33_DDEVARCH_ARCHITECT_MSB    _u(31)
#define M33_DDEVARCH_ARCHITECT_LSB    _u(21)
#define M33_DDEVARCH_ARCHITECT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DDEVARCH_PRESENT
// Description : Defines that the DEVARCH register is present
#define M33_DDEVARCH_PRESENT_RESET  _u(0x1)
#define M33_DDEVARCH_PRESENT_BITS   _u(0x00100000)
#define M33_DDEVARCH_PRESENT_MSB    _u(20)
#define M33_DDEVARCH_PRESENT_LSB    _u(20)
#define M33_DDEVARCH_PRESENT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DDEVARCH_REVISION
// Description : Defines the architecture revision of the component
#define M33_DDEVARCH_REVISION_RESET  _u(0x0)
#define M33_DDEVARCH_REVISION_BITS   _u(0x000f0000)
#define M33_DDEVARCH_REVISION_MSB    _u(19)
#define M33_DDEVARCH_REVISION_LSB    _u(16)
#define M33_DDEVARCH_REVISION_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DDEVARCH_ARCHVER
// Description : Defines the architecture version of the component
#define M33_DDEVARCH_ARCHVER_RESET  _u(0x2)
#define M33_DDEVARCH_ARCHVER_BITS   _u(0x0000f000)
#define M33_DDEVARCH_ARCHVER_MSB    _u(15)
#define M33_DDEVARCH_ARCHVER_LSB    _u(12)
#define M33_DDEVARCH_ARCHVER_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DDEVARCH_ARCHPART
// Description : Defines the architecture of the component
#define M33_DDEVARCH_ARCHPART_RESET  _u(0xa04)
#define M33_DDEVARCH_ARCHPART_BITS   _u(0x00000fff)
#define M33_DDEVARCH_ARCHPART_MSB    _u(11)
#define M33_DDEVARCH_ARCHPART_LSB    _u(0)
#define M33_DDEVARCH_ARCHPART_ACCESS "RO"
// =============================================================================
// Register    : M33_DDEVTYPE
// Description : Provides CoreSight discovery information for the SCS
#define M33_DDEVTYPE_OFFSET _u(0x0000efcc)
#define M33_DDEVTYPE_BITS   _u(0x000000ff)
#define M33_DDEVTYPE_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_DDEVTYPE_SUB
// Description : Component sub-type
#define M33_DDEVTYPE_SUB_RESET  _u(0x0)
#define M33_DDEVTYPE_SUB_BITS   _u(0x000000f0)
#define M33_DDEVTYPE_SUB_MSB    _u(7)
#define M33_DDEVTYPE_SUB_LSB    _u(4)
#define M33_DDEVTYPE_SUB_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DDEVTYPE_MAJOR
// Description : CoreSight major type
#define M33_DDEVTYPE_MAJOR_RESET  _u(0x0)
#define M33_DDEVTYPE_MAJOR_BITS   _u(0x0000000f)
#define M33_DDEVTYPE_MAJOR_MSB    _u(3)
#define M33_DDEVTYPE_MAJOR_LSB    _u(0)
#define M33_DDEVTYPE_MAJOR_ACCESS "RO"
// =============================================================================
// Register    : M33_DPIDR4
// Description : Provides CoreSight discovery information for the SCS
#define M33_DPIDR4_OFFSET _u(0x0000efd0)
#define M33_DPIDR4_BITS   _u(0x000000ff)
#define M33_DPIDR4_RESET  _u(0x00000004)
// -----------------------------------------------------------------------------
// Field       : M33_DPIDR4_SIZE
// Description : See CoreSight Architecture Specification
#define M33_DPIDR4_SIZE_RESET  _u(0x0)
#define M33_DPIDR4_SIZE_BITS   _u(0x000000f0)
#define M33_DPIDR4_SIZE_MSB    _u(7)
#define M33_DPIDR4_SIZE_LSB    _u(4)
#define M33_DPIDR4_SIZE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DPIDR4_DES_2
// Description : See CoreSight Architecture Specification
#define M33_DPIDR4_DES_2_RESET  _u(0x4)
#define M33_DPIDR4_DES_2_BITS   _u(0x0000000f)
#define M33_DPIDR4_DES_2_MSB    _u(3)
#define M33_DPIDR4_DES_2_LSB    _u(0)
#define M33_DPIDR4_DES_2_ACCESS "RO"
// =============================================================================
// Register    : M33_DPIDR5
// Description : Provides CoreSight discovery information for the SCS
#define M33_DPIDR5_OFFSET _u(0x0000efd4)
#define M33_DPIDR5_BITS   _u(0x00000000)
#define M33_DPIDR5_RESET  _u(0x00000000)
#define M33_DPIDR5_MSB    _u(31)
#define M33_DPIDR5_LSB    _u(0)
#define M33_DPIDR5_ACCESS "RW"
// =============================================================================
// Register    : M33_DPIDR6
// Description : Provides CoreSight discovery information for the SCS
#define M33_DPIDR6_OFFSET _u(0x0000efd8)
#define M33_DPIDR6_BITS   _u(0x00000000)
#define M33_DPIDR6_RESET  _u(0x00000000)
#define M33_DPIDR6_MSB    _u(31)
#define M33_DPIDR6_LSB    _u(0)
#define M33_DPIDR6_ACCESS "RW"
// =============================================================================
// Register    : M33_DPIDR7
// Description : Provides CoreSight discovery information for the SCS
#define M33_DPIDR7_OFFSET _u(0x0000efdc)
#define M33_DPIDR7_BITS   _u(0x00000000)
#define M33_DPIDR7_RESET  _u(0x00000000)
#define M33_DPIDR7_MSB    _u(31)
#define M33_DPIDR7_LSB    _u(0)
#define M33_DPIDR7_ACCESS "RW"
// =============================================================================
// Register    : M33_DPIDR0
// Description : Provides CoreSight discovery information for the SCS
#define M33_DPIDR0_OFFSET _u(0x0000efe0)
#define M33_DPIDR0_BITS   _u(0x000000ff)
#define M33_DPIDR0_RESET  _u(0x00000021)
// -----------------------------------------------------------------------------
// Field       : M33_DPIDR0_PART_0
// Description : See CoreSight Architecture Specification
#define M33_DPIDR0_PART_0_RESET  _u(0x21)
#define M33_DPIDR0_PART_0_BITS   _u(0x000000ff)
#define M33_DPIDR0_PART_0_MSB    _u(7)
#define M33_DPIDR0_PART_0_LSB    _u(0)
#define M33_DPIDR0_PART_0_ACCESS "RO"
// =============================================================================
// Register    : M33_DPIDR1
// Description : Provides CoreSight discovery information for the SCS
#define M33_DPIDR1_OFFSET _u(0x0000efe4)
#define M33_DPIDR1_BITS   _u(0x000000ff)
#define M33_DPIDR1_RESET  _u(0x000000bd)
// -----------------------------------------------------------------------------
// Field       : M33_DPIDR1_DES_0
// Description : See CoreSight Architecture Specification
#define M33_DPIDR1_DES_0_RESET  _u(0xb)
#define M33_DPIDR1_DES_0_BITS   _u(0x000000f0)
#define M33_DPIDR1_DES_0_MSB    _u(7)
#define M33_DPIDR1_DES_0_LSB    _u(4)
#define M33_DPIDR1_DES_0_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DPIDR1_PART_1
// Description : See CoreSight Architecture Specification
#define M33_DPIDR1_PART_1_RESET  _u(0xd)
#define M33_DPIDR1_PART_1_BITS   _u(0x0000000f)
#define M33_DPIDR1_PART_1_MSB    _u(3)
#define M33_DPIDR1_PART_1_LSB    _u(0)
#define M33_DPIDR1_PART_1_ACCESS "RO"
// =============================================================================
// Register    : M33_DPIDR2
// Description : Provides CoreSight discovery information for the SCS
#define M33_DPIDR2_OFFSET _u(0x0000efe8)
#define M33_DPIDR2_BITS   _u(0x000000ff)
#define M33_DPIDR2_RESET  _u(0x0000000b)
// -----------------------------------------------------------------------------
// Field       : M33_DPIDR2_REVISION
// Description : See CoreSight Architecture Specification
#define M33_DPIDR2_REVISION_RESET  _u(0x0)
#define M33_DPIDR2_REVISION_BITS   _u(0x000000f0)
#define M33_DPIDR2_REVISION_MSB    _u(7)
#define M33_DPIDR2_REVISION_LSB    _u(4)
#define M33_DPIDR2_REVISION_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DPIDR2_JEDEC
// Description : See CoreSight Architecture Specification
#define M33_DPIDR2_JEDEC_RESET  _u(0x1)
#define M33_DPIDR2_JEDEC_BITS   _u(0x00000008)
#define M33_DPIDR2_JEDEC_MSB    _u(3)
#define M33_DPIDR2_JEDEC_LSB    _u(3)
#define M33_DPIDR2_JEDEC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DPIDR2_DES_1
// Description : See CoreSight Architecture Specification
#define M33_DPIDR2_DES_1_RESET  _u(0x3)
#define M33_DPIDR2_DES_1_BITS   _u(0x00000007)
#define M33_DPIDR2_DES_1_MSB    _u(2)
#define M33_DPIDR2_DES_1_LSB    _u(0)
#define M33_DPIDR2_DES_1_ACCESS "RO"
// =============================================================================
// Register    : M33_DPIDR3
// Description : Provides CoreSight discovery information for the SCS
#define M33_DPIDR3_OFFSET _u(0x0000efec)
#define M33_DPIDR3_BITS   _u(0x000000ff)
#define M33_DPIDR3_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_DPIDR3_REVAND
// Description : See CoreSight Architecture Specification
#define M33_DPIDR3_REVAND_RESET  _u(0x0)
#define M33_DPIDR3_REVAND_BITS   _u(0x000000f0)
#define M33_DPIDR3_REVAND_MSB    _u(7)
#define M33_DPIDR3_REVAND_LSB    _u(4)
#define M33_DPIDR3_REVAND_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DPIDR3_CMOD
// Description : See CoreSight Architecture Specification
#define M33_DPIDR3_CMOD_RESET  _u(0x0)
#define M33_DPIDR3_CMOD_BITS   _u(0x0000000f)
#define M33_DPIDR3_CMOD_MSB    _u(3)
#define M33_DPIDR3_CMOD_LSB    _u(0)
#define M33_DPIDR3_CMOD_ACCESS "RO"
// =============================================================================
// Register    : M33_DCIDR0
// Description : Provides CoreSight discovery information for the SCS
#define M33_DCIDR0_OFFSET _u(0x0000eff0)
#define M33_DCIDR0_BITS   _u(0x000000ff)
#define M33_DCIDR0_RESET  _u(0x0000000d)
// -----------------------------------------------------------------------------
// Field       : M33_DCIDR0_PRMBL_0
// Description : See CoreSight Architecture Specification
#define M33_DCIDR0_PRMBL_0_RESET  _u(0x0d)
#define M33_DCIDR0_PRMBL_0_BITS   _u(0x000000ff)
#define M33_DCIDR0_PRMBL_0_MSB    _u(7)
#define M33_DCIDR0_PRMBL_0_LSB    _u(0)
#define M33_DCIDR0_PRMBL_0_ACCESS "RO"
// =============================================================================
// Register    : M33_DCIDR1
// Description : Provides CoreSight discovery information for the SCS
#define M33_DCIDR1_OFFSET _u(0x0000eff4)
#define M33_DCIDR1_BITS   _u(0x000000ff)
#define M33_DCIDR1_RESET  _u(0x00000090)
// -----------------------------------------------------------------------------
// Field       : M33_DCIDR1_CLASS
// Description : See CoreSight Architecture Specification
#define M33_DCIDR1_CLASS_RESET  _u(0x9)
#define M33_DCIDR1_CLASS_BITS   _u(0x000000f0)
#define M33_DCIDR1_CLASS_MSB    _u(7)
#define M33_DCIDR1_CLASS_LSB    _u(4)
#define M33_DCIDR1_CLASS_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DCIDR1_PRMBL_1
// Description : See CoreSight Architecture Specification
#define M33_DCIDR1_PRMBL_1_RESET  _u(0x0)
#define M33_DCIDR1_PRMBL_1_BITS   _u(0x0000000f)
#define M33_DCIDR1_PRMBL_1_MSB    _u(3)
#define M33_DCIDR1_PRMBL_1_LSB    _u(0)
#define M33_DCIDR1_PRMBL_1_ACCESS "RO"
// =============================================================================
// Register    : M33_DCIDR2
// Description : Provides CoreSight discovery information for the SCS
#define M33_DCIDR2_OFFSET _u(0x0000eff8)
#define M33_DCIDR2_BITS   _u(0x000000ff)
#define M33_DCIDR2_RESET  _u(0x00000005)
// -----------------------------------------------------------------------------
// Field       : M33_DCIDR2_PRMBL_2
// Description : See CoreSight Architecture Specification
#define M33_DCIDR2_PRMBL_2_RESET  _u(0x05)
#define M33_DCIDR2_PRMBL_2_BITS   _u(0x000000ff)
#define M33_DCIDR2_PRMBL_2_MSB    _u(7)
#define M33_DCIDR2_PRMBL_2_LSB    _u(0)
#define M33_DCIDR2_PRMBL_2_ACCESS "RO"
// =============================================================================
// Register    : M33_DCIDR3
// Description : Provides CoreSight discovery information for the SCS
#define M33_DCIDR3_OFFSET _u(0x0000effc)
#define M33_DCIDR3_BITS   _u(0x000000ff)
#define M33_DCIDR3_RESET  _u(0x000000b1)
// -----------------------------------------------------------------------------
// Field       : M33_DCIDR3_PRMBL_3
// Description : See CoreSight Architecture Specification
#define M33_DCIDR3_PRMBL_3_RESET  _u(0xb1)
#define M33_DCIDR3_PRMBL_3_BITS   _u(0x000000ff)
#define M33_DCIDR3_PRMBL_3_MSB    _u(7)
#define M33_DCIDR3_PRMBL_3_LSB    _u(0)
#define M33_DCIDR3_PRMBL_3_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCPRGCTLR
// Description : Programming Control Register
#define M33_TRCPRGCTLR_OFFSET _u(0x00041004)
#define M33_TRCPRGCTLR_BITS   _u(0x00000001)
#define M33_TRCPRGCTLR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCPRGCTLR_EN
// Description : Trace Unit Enable
#define M33_TRCPRGCTLR_EN_RESET  _u(0x0)
#define M33_TRCPRGCTLR_EN_BITS   _u(0x00000001)
#define M33_TRCPRGCTLR_EN_MSB    _u(0)
#define M33_TRCPRGCTLR_EN_LSB    _u(0)
#define M33_TRCPRGCTLR_EN_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCSTATR
// Description : The TRCSTATR indicates the ETM-Teal status
#define M33_TRCSTATR_OFFSET _u(0x0004100c)
#define M33_TRCSTATR_BITS   _u(0x00000003)
#define M33_TRCSTATR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCSTATR_PMSTABLE
// Description : Indicates whether the ETM-Teal registers are stable and can be
//               read
#define M33_TRCSTATR_PMSTABLE_RESET  _u(0x0)
#define M33_TRCSTATR_PMSTABLE_BITS   _u(0x00000002)
#define M33_TRCSTATR_PMSTABLE_MSB    _u(1)
#define M33_TRCSTATR_PMSTABLE_LSB    _u(1)
#define M33_TRCSTATR_PMSTABLE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCSTATR_IDLE
// Description : Indicates that the trace unit is inactive
#define M33_TRCSTATR_IDLE_RESET  _u(0x0)
#define M33_TRCSTATR_IDLE_BITS   _u(0x00000001)
#define M33_TRCSTATR_IDLE_MSB    _u(0)
#define M33_TRCSTATR_IDLE_LSB    _u(0)
#define M33_TRCSTATR_IDLE_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCCONFIGR
// Description : The TRCCONFIGR sets the basic tracing options for the trace
//               unit
#define M33_TRCCONFIGR_OFFSET _u(0x00041010)
#define M33_TRCCONFIGR_BITS   _u(0x00001ff8)
#define M33_TRCCONFIGR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCCONFIGR_RS
// Description : Return stack enable
#define M33_TRCCONFIGR_RS_RESET  _u(0x0)
#define M33_TRCCONFIGR_RS_BITS   _u(0x00001000)
#define M33_TRCCONFIGR_RS_MSB    _u(12)
#define M33_TRCCONFIGR_RS_LSB    _u(12)
#define M33_TRCCONFIGR_RS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCCONFIGR_TS
// Description : Global timestamp tracing
#define M33_TRCCONFIGR_TS_RESET  _u(0x0)
#define M33_TRCCONFIGR_TS_BITS   _u(0x00000800)
#define M33_TRCCONFIGR_TS_MSB    _u(11)
#define M33_TRCCONFIGR_TS_LSB    _u(11)
#define M33_TRCCONFIGR_TS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCCONFIGR_COND
// Description : Conditional instruction tracing
#define M33_TRCCONFIGR_COND_RESET  _u(0x00)
#define M33_TRCCONFIGR_COND_BITS   _u(0x000007e0)
#define M33_TRCCONFIGR_COND_MSB    _u(10)
#define M33_TRCCONFIGR_COND_LSB    _u(5)
#define M33_TRCCONFIGR_COND_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCCONFIGR_CCI
// Description : Cycle counting in instruction trace
#define M33_TRCCONFIGR_CCI_RESET  _u(0x0)
#define M33_TRCCONFIGR_CCI_BITS   _u(0x00000010)
#define M33_TRCCONFIGR_CCI_MSB    _u(4)
#define M33_TRCCONFIGR_CCI_LSB    _u(4)
#define M33_TRCCONFIGR_CCI_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCCONFIGR_BB
// Description : Branch broadcast mode
#define M33_TRCCONFIGR_BB_RESET  _u(0x0)
#define M33_TRCCONFIGR_BB_BITS   _u(0x00000008)
#define M33_TRCCONFIGR_BB_MSB    _u(3)
#define M33_TRCCONFIGR_BB_LSB    _u(3)
#define M33_TRCCONFIGR_BB_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCEVENTCTL0R
// Description : The TRCEVENTCTL0R controls the tracing of events in the trace
//               stream. The events also drive the ETM-Teal external outputs.
#define M33_TRCEVENTCTL0R_OFFSET _u(0x00041020)
#define M33_TRCEVENTCTL0R_BITS   _u(0x00008787)
#define M33_TRCEVENTCTL0R_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCEVENTCTL0R_TYPE1
// Description : Selects the resource type for event 1
#define M33_TRCEVENTCTL0R_TYPE1_RESET  _u(0x0)
#define M33_TRCEVENTCTL0R_TYPE1_BITS   _u(0x00008000)
#define M33_TRCEVENTCTL0R_TYPE1_MSB    _u(15)
#define M33_TRCEVENTCTL0R_TYPE1_LSB    _u(15)
#define M33_TRCEVENTCTL0R_TYPE1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCEVENTCTL0R_SEL1
// Description : Selects the resource number, based on the value of TYPE1: When
//               TYPE1 is 0, selects a single selected resource from 0-15
//               defined by SEL1[2:0].  When TYPE1 is 1, selects a Boolean
//               combined resource pair from 0-7 defined by SEL1[2:0]
#define M33_TRCEVENTCTL0R_SEL1_RESET  _u(0x0)
#define M33_TRCEVENTCTL0R_SEL1_BITS   _u(0x00000700)
#define M33_TRCEVENTCTL0R_SEL1_MSB    _u(10)
#define M33_TRCEVENTCTL0R_SEL1_LSB    _u(8)
#define M33_TRCEVENTCTL0R_SEL1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCEVENTCTL0R_TYPE0
// Description : Selects the resource type for event 0
#define M33_TRCEVENTCTL0R_TYPE0_RESET  _u(0x0)
#define M33_TRCEVENTCTL0R_TYPE0_BITS   _u(0x00000080)
#define M33_TRCEVENTCTL0R_TYPE0_MSB    _u(7)
#define M33_TRCEVENTCTL0R_TYPE0_LSB    _u(7)
#define M33_TRCEVENTCTL0R_TYPE0_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCEVENTCTL0R_SEL0
// Description : Selects the resource number, based on the value of TYPE0: When
//               TYPE1 is 0, selects a single selected resource from 0-15
//               defined by SEL0[2:0].  When TYPE1 is 1, selects a Boolean
//               combined resource pair from 0-7 defined by SEL0[2:0]
#define M33_TRCEVENTCTL0R_SEL0_RESET  _u(0x0)
#define M33_TRCEVENTCTL0R_SEL0_BITS   _u(0x00000007)
#define M33_TRCEVENTCTL0R_SEL0_MSB    _u(2)
#define M33_TRCEVENTCTL0R_SEL0_LSB    _u(0)
#define M33_TRCEVENTCTL0R_SEL0_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCEVENTCTL1R
// Description : The TRCEVENTCTL1R controls how the events selected by
//               TRCEVENTCTL0R behave
#define M33_TRCEVENTCTL1R_OFFSET _u(0x00041024)
#define M33_TRCEVENTCTL1R_BITS   _u(0x00001803)
#define M33_TRCEVENTCTL1R_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCEVENTCTL1R_LPOVERRIDE
// Description : Low power state behavior override
#define M33_TRCEVENTCTL1R_LPOVERRIDE_RESET  _u(0x0)
#define M33_TRCEVENTCTL1R_LPOVERRIDE_BITS   _u(0x00001000)
#define M33_TRCEVENTCTL1R_LPOVERRIDE_MSB    _u(12)
#define M33_TRCEVENTCTL1R_LPOVERRIDE_LSB    _u(12)
#define M33_TRCEVENTCTL1R_LPOVERRIDE_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCEVENTCTL1R_ATB
// Description : ATB enabled
#define M33_TRCEVENTCTL1R_ATB_RESET  _u(0x0)
#define M33_TRCEVENTCTL1R_ATB_BITS   _u(0x00000800)
#define M33_TRCEVENTCTL1R_ATB_MSB    _u(11)
#define M33_TRCEVENTCTL1R_ATB_LSB    _u(11)
#define M33_TRCEVENTCTL1R_ATB_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCEVENTCTL1R_INSTEN1
// Description : One bit per event, to enable generation of an event element in
//               the instruction trace stream when the selected event occurs
#define M33_TRCEVENTCTL1R_INSTEN1_RESET  _u(0x0)
#define M33_TRCEVENTCTL1R_INSTEN1_BITS   _u(0x00000002)
#define M33_TRCEVENTCTL1R_INSTEN1_MSB    _u(1)
#define M33_TRCEVENTCTL1R_INSTEN1_LSB    _u(1)
#define M33_TRCEVENTCTL1R_INSTEN1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCEVENTCTL1R_INSTEN0
// Description : One bit per event, to enable generation of an event element in
//               the instruction trace stream when the selected event occurs
#define M33_TRCEVENTCTL1R_INSTEN0_RESET  _u(0x0)
#define M33_TRCEVENTCTL1R_INSTEN0_BITS   _u(0x00000001)
#define M33_TRCEVENTCTL1R_INSTEN0_MSB    _u(0)
#define M33_TRCEVENTCTL1R_INSTEN0_LSB    _u(0)
#define M33_TRCEVENTCTL1R_INSTEN0_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCSTALLCTLR
// Description : The TRCSTALLCTLR enables ETM-Teal to stall the processor if the
//               ETM-Teal FIFO goes over the programmed level to minimize risk
//               of overflow
#define M33_TRCSTALLCTLR_OFFSET _u(0x0004102c)
#define M33_TRCSTALLCTLR_BITS   _u(0x0000050c)
#define M33_TRCSTALLCTLR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCSTALLCTLR_INSTPRIORITY
// Description : Reserved, RES0
#define M33_TRCSTALLCTLR_INSTPRIORITY_RESET  _u(0x0)
#define M33_TRCSTALLCTLR_INSTPRIORITY_BITS   _u(0x00000400)
#define M33_TRCSTALLCTLR_INSTPRIORITY_MSB    _u(10)
#define M33_TRCSTALLCTLR_INSTPRIORITY_LSB    _u(10)
#define M33_TRCSTALLCTLR_INSTPRIORITY_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCSTALLCTLR_ISTALL
// Description : Stall processor based on instruction trace buffer space
#define M33_TRCSTALLCTLR_ISTALL_RESET  _u(0x0)
#define M33_TRCSTALLCTLR_ISTALL_BITS   _u(0x00000100)
#define M33_TRCSTALLCTLR_ISTALL_MSB    _u(8)
#define M33_TRCSTALLCTLR_ISTALL_LSB    _u(8)
#define M33_TRCSTALLCTLR_ISTALL_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCSTALLCTLR_LEVEL
// Description : Threshold at which stalling becomes active. This provides four
//               levels. This level can be varied to optimize the level of
//               invasion caused by stalling, balanced against the risk of a
//               FIFO overflow
#define M33_TRCSTALLCTLR_LEVEL_RESET  _u(0x0)
#define M33_TRCSTALLCTLR_LEVEL_BITS   _u(0x0000000c)
#define M33_TRCSTALLCTLR_LEVEL_MSB    _u(3)
#define M33_TRCSTALLCTLR_LEVEL_LSB    _u(2)
#define M33_TRCSTALLCTLR_LEVEL_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCTSCTLR
// Description : The TRCTSCTLR controls the insertion of global timestamps into
//               the trace stream. A timestamp is always inserted into the
//               instruction trace stream
#define M33_TRCTSCTLR_OFFSET _u(0x00041030)
#define M33_TRCTSCTLR_BITS   _u(0x00000083)
#define M33_TRCTSCTLR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCTSCTLR_TYPE0
// Description : Selects the resource type for event 0
#define M33_TRCTSCTLR_TYPE0_RESET  _u(0x0)
#define M33_TRCTSCTLR_TYPE0_BITS   _u(0x00000080)
#define M33_TRCTSCTLR_TYPE0_MSB    _u(7)
#define M33_TRCTSCTLR_TYPE0_LSB    _u(7)
#define M33_TRCTSCTLR_TYPE0_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCTSCTLR_SEL0
// Description : Selects the resource number, based on the value of TYPE0: When
//               TYPE1 is 0, selects a single selected resource from 0-15
//               defined by SEL0[2:0].  When TYPE1 is 1, selects a Boolean
//               combined resource pair from 0-7 defined by SEL0[2:0]
#define M33_TRCTSCTLR_SEL0_RESET  _u(0x0)
#define M33_TRCTSCTLR_SEL0_BITS   _u(0x00000003)
#define M33_TRCTSCTLR_SEL0_MSB    _u(1)
#define M33_TRCTSCTLR_SEL0_LSB    _u(0)
#define M33_TRCTSCTLR_SEL0_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCSYNCPR
// Description : The TRCSYNCPR specifies the period of trace synchronization of
//               the trace streams. TRCSYNCPR defines a number of bytes of trace
//               between requests for trace synchronization. This value is
//               always a power of two
#define M33_TRCSYNCPR_OFFSET _u(0x00041034)
#define M33_TRCSYNCPR_BITS   _u(0x0000001f)
#define M33_TRCSYNCPR_RESET  _u(0x0000000a)
// -----------------------------------------------------------------------------
// Field       : M33_TRCSYNCPR_PERIOD
// Description : Defines the number of bytes of trace between trace
//               synchronization requests as a total of the number of bytes
//               generated by the instruction stream. The number of bytes is 2N
//               where N is the value of this field: - A value of zero disables
//               these periodic trace synchronization requests, but does not
//               disable other trace synchronization requests.  - The minimum
//               value that can be programmed, other than zero, is 8, providing
//               a minimum trace synchronization period of 256 bytes.  - The
//               maximum value is 20, providing a maximum trace synchronization
//               period of 2^20 bytes
#define M33_TRCSYNCPR_PERIOD_RESET  _u(0x0a)
#define M33_TRCSYNCPR_PERIOD_BITS   _u(0x0000001f)
#define M33_TRCSYNCPR_PERIOD_MSB    _u(4)
#define M33_TRCSYNCPR_PERIOD_LSB    _u(0)
#define M33_TRCSYNCPR_PERIOD_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCCCCTLR
// Description : The TRCCCCTLR sets the threshold value for instruction trace
//               cycle counting. The threshold represents the minimum interval
//               between cycle count trace packets
#define M33_TRCCCCTLR_OFFSET _u(0x00041038)
#define M33_TRCCCCTLR_BITS   _u(0x00000fff)
#define M33_TRCCCCTLR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCCCCTLR_THRESHOLD
// Description : Instruction trace cycle count threshold
#define M33_TRCCCCTLR_THRESHOLD_RESET  _u(0x000)
#define M33_TRCCCCTLR_THRESHOLD_BITS   _u(0x00000fff)
#define M33_TRCCCCTLR_THRESHOLD_MSB    _u(11)
#define M33_TRCCCCTLR_THRESHOLD_LSB    _u(0)
#define M33_TRCCCCTLR_THRESHOLD_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCVICTLR
// Description : The TRCVICTLR controls instruction trace filtering
#define M33_TRCVICTLR_OFFSET _u(0x00041080)
#define M33_TRCVICTLR_BITS   _u(0x00090e83)
#define M33_TRCVICTLR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCVICTLR_EXLEVEL_S3
// Description : In Secure state, each bit controls whether instruction tracing
//               is enabled for the corresponding exception level
#define M33_TRCVICTLR_EXLEVEL_S3_RESET  _u(0x0)
#define M33_TRCVICTLR_EXLEVEL_S3_BITS   _u(0x00080000)
#define M33_TRCVICTLR_EXLEVEL_S3_MSB    _u(19)
#define M33_TRCVICTLR_EXLEVEL_S3_LSB    _u(19)
#define M33_TRCVICTLR_EXLEVEL_S3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCVICTLR_EXLEVEL_S0
// Description : In Secure state, each bit controls whether instruction tracing
//               is enabled for the corresponding exception level
#define M33_TRCVICTLR_EXLEVEL_S0_RESET  _u(0x0)
#define M33_TRCVICTLR_EXLEVEL_S0_BITS   _u(0x00010000)
#define M33_TRCVICTLR_EXLEVEL_S0_MSB    _u(16)
#define M33_TRCVICTLR_EXLEVEL_S0_LSB    _u(16)
#define M33_TRCVICTLR_EXLEVEL_S0_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCVICTLR_TRCERR
// Description : Selects whether a system error exception must always be traced
#define M33_TRCVICTLR_TRCERR_RESET  _u(0x0)
#define M33_TRCVICTLR_TRCERR_BITS   _u(0x00000800)
#define M33_TRCVICTLR_TRCERR_MSB    _u(11)
#define M33_TRCVICTLR_TRCERR_LSB    _u(11)
#define M33_TRCVICTLR_TRCERR_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCVICTLR_TRCRESET
// Description : Selects whether a reset exception must always be traced
#define M33_TRCVICTLR_TRCRESET_RESET  _u(0x0)
#define M33_TRCVICTLR_TRCRESET_BITS   _u(0x00000400)
#define M33_TRCVICTLR_TRCRESET_MSB    _u(10)
#define M33_TRCVICTLR_TRCRESET_LSB    _u(10)
#define M33_TRCVICTLR_TRCRESET_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCVICTLR_SSSTATUS
// Description : Indicates the current status of the start/stop logic
#define M33_TRCVICTLR_SSSTATUS_RESET  _u(0x0)
#define M33_TRCVICTLR_SSSTATUS_BITS   _u(0x00000200)
#define M33_TRCVICTLR_SSSTATUS_MSB    _u(9)
#define M33_TRCVICTLR_SSSTATUS_LSB    _u(9)
#define M33_TRCVICTLR_SSSTATUS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCVICTLR_TYPE0
// Description : Selects the resource type for event 0
#define M33_TRCVICTLR_TYPE0_RESET  _u(0x0)
#define M33_TRCVICTLR_TYPE0_BITS   _u(0x00000080)
#define M33_TRCVICTLR_TYPE0_MSB    _u(7)
#define M33_TRCVICTLR_TYPE0_LSB    _u(7)
#define M33_TRCVICTLR_TYPE0_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCVICTLR_SEL0
// Description : Selects the resource number, based on the value of TYPE0: When
//               TYPE1 is 0, selects a single selected resource from 0-15
//               defined by SEL0[2:0].  When TYPE1 is 1, selects a Boolean
//               combined resource pair from 0-7 defined by SEL0[2:0]
#define M33_TRCVICTLR_SEL0_RESET  _u(0x0)
#define M33_TRCVICTLR_SEL0_BITS   _u(0x00000003)
#define M33_TRCVICTLR_SEL0_MSB    _u(1)
#define M33_TRCVICTLR_SEL0_LSB    _u(0)
#define M33_TRCVICTLR_SEL0_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCCNTRLDVR0
// Description : The TRCCNTRLDVR defines the reload value for the reduced
//               function counter
#define M33_TRCCNTRLDVR0_OFFSET _u(0x00041140)
#define M33_TRCCNTRLDVR0_BITS   _u(0x0000ffff)
#define M33_TRCCNTRLDVR0_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCCNTRLDVR0_VALUE
// Description : Defines the reload value for the counter. This value is loaded
//               into the counter each time the reload event occurs
#define M33_TRCCNTRLDVR0_VALUE_RESET  _u(0x0000)
#define M33_TRCCNTRLDVR0_VALUE_BITS   _u(0x0000ffff)
#define M33_TRCCNTRLDVR0_VALUE_MSB    _u(15)
#define M33_TRCCNTRLDVR0_VALUE_LSB    _u(0)
#define M33_TRCCNTRLDVR0_VALUE_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCIDR8
// Description : TRCIDR8
#define M33_TRCIDR8_OFFSET _u(0x00041180)
#define M33_TRCIDR8_BITS   _u(0xffffffff)
#define M33_TRCIDR8_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR8_MAXSPEC
// Description : reads as `ImpDef
#define M33_TRCIDR8_MAXSPEC_RESET  _u(0x00000000)
#define M33_TRCIDR8_MAXSPEC_BITS   _u(0xffffffff)
#define M33_TRCIDR8_MAXSPEC_MSB    _u(31)
#define M33_TRCIDR8_MAXSPEC_LSB    _u(0)
#define M33_TRCIDR8_MAXSPEC_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCIDR9
// Description : TRCIDR9
#define M33_TRCIDR9_OFFSET _u(0x00041184)
#define M33_TRCIDR9_BITS   _u(0xffffffff)
#define M33_TRCIDR9_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR9_NUMP0KEY
// Description : reads as `ImpDef
#define M33_TRCIDR9_NUMP0KEY_RESET  _u(0x00000000)
#define M33_TRCIDR9_NUMP0KEY_BITS   _u(0xffffffff)
#define M33_TRCIDR9_NUMP0KEY_MSB    _u(31)
#define M33_TRCIDR9_NUMP0KEY_LSB    _u(0)
#define M33_TRCIDR9_NUMP0KEY_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCIDR10
// Description : TRCIDR10
#define M33_TRCIDR10_OFFSET _u(0x00041188)
#define M33_TRCIDR10_BITS   _u(0xffffffff)
#define M33_TRCIDR10_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR10_NUMP1KEY
// Description : reads as `ImpDef
#define M33_TRCIDR10_NUMP1KEY_RESET  _u(0x00000000)
#define M33_TRCIDR10_NUMP1KEY_BITS   _u(0xffffffff)
#define M33_TRCIDR10_NUMP1KEY_MSB    _u(31)
#define M33_TRCIDR10_NUMP1KEY_LSB    _u(0)
#define M33_TRCIDR10_NUMP1KEY_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCIDR11
// Description : TRCIDR11
#define M33_TRCIDR11_OFFSET _u(0x0004118c)
#define M33_TRCIDR11_BITS   _u(0xffffffff)
#define M33_TRCIDR11_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR11_NUMP1SPC
// Description : reads as `ImpDef
#define M33_TRCIDR11_NUMP1SPC_RESET  _u(0x00000000)
#define M33_TRCIDR11_NUMP1SPC_BITS   _u(0xffffffff)
#define M33_TRCIDR11_NUMP1SPC_MSB    _u(31)
#define M33_TRCIDR11_NUMP1SPC_LSB    _u(0)
#define M33_TRCIDR11_NUMP1SPC_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCIDR12
// Description : TRCIDR12
#define M33_TRCIDR12_OFFSET _u(0x00041190)
#define M33_TRCIDR12_BITS   _u(0xffffffff)
#define M33_TRCIDR12_RESET  _u(0x00000001)
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR12_NUMCONDKEY
// Description : reads as `ImpDef
#define M33_TRCIDR12_NUMCONDKEY_RESET  _u(0x00000001)
#define M33_TRCIDR12_NUMCONDKEY_BITS   _u(0xffffffff)
#define M33_TRCIDR12_NUMCONDKEY_MSB    _u(31)
#define M33_TRCIDR12_NUMCONDKEY_LSB    _u(0)
#define M33_TRCIDR12_NUMCONDKEY_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCIDR13
// Description : TRCIDR13
#define M33_TRCIDR13_OFFSET _u(0x00041194)
#define M33_TRCIDR13_BITS   _u(0xffffffff)
#define M33_TRCIDR13_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR13_NUMCONDSPC
// Description : reads as `ImpDef
#define M33_TRCIDR13_NUMCONDSPC_RESET  _u(0x00000000)
#define M33_TRCIDR13_NUMCONDSPC_BITS   _u(0xffffffff)
#define M33_TRCIDR13_NUMCONDSPC_MSB    _u(31)
#define M33_TRCIDR13_NUMCONDSPC_LSB    _u(0)
#define M33_TRCIDR13_NUMCONDSPC_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCIMSPEC
// Description : The TRCIMSPEC shows the presence of any IMPLEMENTATION SPECIFIC
//               features, and enables any features that are provided
#define M33_TRCIMSPEC_OFFSET _u(0x000411c0)
#define M33_TRCIMSPEC_BITS   _u(0x0000000f)
#define M33_TRCIMSPEC_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCIMSPEC_SUPPORT
// Description : Reserved, RES0
#define M33_TRCIMSPEC_SUPPORT_RESET  _u(0x0)
#define M33_TRCIMSPEC_SUPPORT_BITS   _u(0x0000000f)
#define M33_TRCIMSPEC_SUPPORT_MSB    _u(3)
#define M33_TRCIMSPEC_SUPPORT_LSB    _u(0)
#define M33_TRCIMSPEC_SUPPORT_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCIDR0
// Description : TRCIDR0
#define M33_TRCIDR0_OFFSET _u(0x000411e0)
#define M33_TRCIDR0_BITS   _u(0x3f03feff)
#define M33_TRCIDR0_RESET  _u(0x280006e1)
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR0_COMMOPT
// Description : reads as `ImpDef
#define M33_TRCIDR0_COMMOPT_RESET  _u(0x1)
#define M33_TRCIDR0_COMMOPT_BITS   _u(0x20000000)
#define M33_TRCIDR0_COMMOPT_MSB    _u(29)
#define M33_TRCIDR0_COMMOPT_LSB    _u(29)
#define M33_TRCIDR0_COMMOPT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR0_TSSIZE
// Description : reads as `ImpDef
#define M33_TRCIDR0_TSSIZE_RESET  _u(0x08)
#define M33_TRCIDR0_TSSIZE_BITS   _u(0x1f000000)
#define M33_TRCIDR0_TSSIZE_MSB    _u(28)
#define M33_TRCIDR0_TSSIZE_LSB    _u(24)
#define M33_TRCIDR0_TSSIZE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR0_TRCEXDATA
// Description : reads as `ImpDef
#define M33_TRCIDR0_TRCEXDATA_RESET  _u(0x0)
#define M33_TRCIDR0_TRCEXDATA_BITS   _u(0x00020000)
#define M33_TRCIDR0_TRCEXDATA_MSB    _u(17)
#define M33_TRCIDR0_TRCEXDATA_LSB    _u(17)
#define M33_TRCIDR0_TRCEXDATA_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR0_QSUPP
// Description : reads as `ImpDef
#define M33_TRCIDR0_QSUPP_RESET  _u(0x0)
#define M33_TRCIDR0_QSUPP_BITS   _u(0x00018000)
#define M33_TRCIDR0_QSUPP_MSB    _u(16)
#define M33_TRCIDR0_QSUPP_LSB    _u(15)
#define M33_TRCIDR0_QSUPP_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR0_QFILT
// Description : reads as `ImpDef
#define M33_TRCIDR0_QFILT_RESET  _u(0x0)
#define M33_TRCIDR0_QFILT_BITS   _u(0x00004000)
#define M33_TRCIDR0_QFILT_MSB    _u(14)
#define M33_TRCIDR0_QFILT_LSB    _u(14)
#define M33_TRCIDR0_QFILT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR0_CONDTYPE
// Description : reads as `ImpDef
#define M33_TRCIDR0_CONDTYPE_RESET  _u(0x0)
#define M33_TRCIDR0_CONDTYPE_BITS   _u(0x00003000)
#define M33_TRCIDR0_CONDTYPE_MSB    _u(13)
#define M33_TRCIDR0_CONDTYPE_LSB    _u(12)
#define M33_TRCIDR0_CONDTYPE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR0_NUMEVENT
// Description : reads as `ImpDef
#define M33_TRCIDR0_NUMEVENT_RESET  _u(0x1)
#define M33_TRCIDR0_NUMEVENT_BITS   _u(0x00000c00)
#define M33_TRCIDR0_NUMEVENT_MSB    _u(11)
#define M33_TRCIDR0_NUMEVENT_LSB    _u(10)
#define M33_TRCIDR0_NUMEVENT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR0_RETSTACK
// Description : reads as `ImpDef
#define M33_TRCIDR0_RETSTACK_RESET  _u(0x1)
#define M33_TRCIDR0_RETSTACK_BITS   _u(0x00000200)
#define M33_TRCIDR0_RETSTACK_MSB    _u(9)
#define M33_TRCIDR0_RETSTACK_LSB    _u(9)
#define M33_TRCIDR0_RETSTACK_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR0_TRCCCI
// Description : reads as `ImpDef
#define M33_TRCIDR0_TRCCCI_RESET  _u(0x1)
#define M33_TRCIDR0_TRCCCI_BITS   _u(0x00000080)
#define M33_TRCIDR0_TRCCCI_MSB    _u(7)
#define M33_TRCIDR0_TRCCCI_LSB    _u(7)
#define M33_TRCIDR0_TRCCCI_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR0_TRCCOND
// Description : reads as `ImpDef
#define M33_TRCIDR0_TRCCOND_RESET  _u(0x1)
#define M33_TRCIDR0_TRCCOND_BITS   _u(0x00000040)
#define M33_TRCIDR0_TRCCOND_MSB    _u(6)
#define M33_TRCIDR0_TRCCOND_LSB    _u(6)
#define M33_TRCIDR0_TRCCOND_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR0_TRCBB
// Description : reads as `ImpDef
#define M33_TRCIDR0_TRCBB_RESET  _u(0x1)
#define M33_TRCIDR0_TRCBB_BITS   _u(0x00000020)
#define M33_TRCIDR0_TRCBB_MSB    _u(5)
#define M33_TRCIDR0_TRCBB_LSB    _u(5)
#define M33_TRCIDR0_TRCBB_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR0_TRCDATA
// Description : reads as `ImpDef
#define M33_TRCIDR0_TRCDATA_RESET  _u(0x0)
#define M33_TRCIDR0_TRCDATA_BITS   _u(0x00000018)
#define M33_TRCIDR0_TRCDATA_MSB    _u(4)
#define M33_TRCIDR0_TRCDATA_LSB    _u(3)
#define M33_TRCIDR0_TRCDATA_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR0_INSTP0
// Description : reads as `ImpDef
#define M33_TRCIDR0_INSTP0_RESET  _u(0x0)
#define M33_TRCIDR0_INSTP0_BITS   _u(0x00000006)
#define M33_TRCIDR0_INSTP0_MSB    _u(2)
#define M33_TRCIDR0_INSTP0_LSB    _u(1)
#define M33_TRCIDR0_INSTP0_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR0_RES1
// Description : Reserved, RES1
#define M33_TRCIDR0_RES1_RESET  _u(0x1)
#define M33_TRCIDR0_RES1_BITS   _u(0x00000001)
#define M33_TRCIDR0_RES1_MSB    _u(0)
#define M33_TRCIDR0_RES1_LSB    _u(0)
#define M33_TRCIDR0_RES1_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCIDR1
// Description : TRCIDR1
#define M33_TRCIDR1_OFFSET _u(0x000411e4)
#define M33_TRCIDR1_BITS   _u(0xff00ffff)
#define M33_TRCIDR1_RESET  _u(0x4100f421)
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR1_DESIGNER
// Description : reads as `ImpDef
#define M33_TRCIDR1_DESIGNER_RESET  _u(0x41)
#define M33_TRCIDR1_DESIGNER_BITS   _u(0xff000000)
#define M33_TRCIDR1_DESIGNER_MSB    _u(31)
#define M33_TRCIDR1_DESIGNER_LSB    _u(24)
#define M33_TRCIDR1_DESIGNER_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR1_RES1
// Description : Reserved, RES1
#define M33_TRCIDR1_RES1_RESET  _u(0xf)
#define M33_TRCIDR1_RES1_BITS   _u(0x0000f000)
#define M33_TRCIDR1_RES1_MSB    _u(15)
#define M33_TRCIDR1_RES1_LSB    _u(12)
#define M33_TRCIDR1_RES1_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR1_TRCARCHMAJ
// Description : reads as 0b0100
#define M33_TRCIDR1_TRCARCHMAJ_RESET  _u(0x4)
#define M33_TRCIDR1_TRCARCHMAJ_BITS   _u(0x00000f00)
#define M33_TRCIDR1_TRCARCHMAJ_MSB    _u(11)
#define M33_TRCIDR1_TRCARCHMAJ_LSB    _u(8)
#define M33_TRCIDR1_TRCARCHMAJ_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR1_TRCARCHMIN
// Description : reads as 0b0000
#define M33_TRCIDR1_TRCARCHMIN_RESET  _u(0x2)
#define M33_TRCIDR1_TRCARCHMIN_BITS   _u(0x000000f0)
#define M33_TRCIDR1_TRCARCHMIN_MSB    _u(7)
#define M33_TRCIDR1_TRCARCHMIN_LSB    _u(4)
#define M33_TRCIDR1_TRCARCHMIN_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR1_REVISION
// Description : reads as `ImpDef
#define M33_TRCIDR1_REVISION_RESET  _u(0x1)
#define M33_TRCIDR1_REVISION_BITS   _u(0x0000000f)
#define M33_TRCIDR1_REVISION_MSB    _u(3)
#define M33_TRCIDR1_REVISION_LSB    _u(0)
#define M33_TRCIDR1_REVISION_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCIDR2
// Description : TRCIDR2
#define M33_TRCIDR2_OFFSET _u(0x000411e8)
#define M33_TRCIDR2_BITS   _u(0x1fffffff)
#define M33_TRCIDR2_RESET  _u(0x00000004)
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR2_CCSIZE
// Description : reads as `ImpDef
#define M33_TRCIDR2_CCSIZE_RESET  _u(0x0)
#define M33_TRCIDR2_CCSIZE_BITS   _u(0x1e000000)
#define M33_TRCIDR2_CCSIZE_MSB    _u(28)
#define M33_TRCIDR2_CCSIZE_LSB    _u(25)
#define M33_TRCIDR2_CCSIZE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR2_DVSIZE
// Description : reads as `ImpDef
#define M33_TRCIDR2_DVSIZE_RESET  _u(0x00)
#define M33_TRCIDR2_DVSIZE_BITS   _u(0x01f00000)
#define M33_TRCIDR2_DVSIZE_MSB    _u(24)
#define M33_TRCIDR2_DVSIZE_LSB    _u(20)
#define M33_TRCIDR2_DVSIZE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR2_DASIZE
// Description : reads as `ImpDef
#define M33_TRCIDR2_DASIZE_RESET  _u(0x00)
#define M33_TRCIDR2_DASIZE_BITS   _u(0x000f8000)
#define M33_TRCIDR2_DASIZE_MSB    _u(19)
#define M33_TRCIDR2_DASIZE_LSB    _u(15)
#define M33_TRCIDR2_DASIZE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR2_VMIDSIZE
// Description : reads as `ImpDef
#define M33_TRCIDR2_VMIDSIZE_RESET  _u(0x00)
#define M33_TRCIDR2_VMIDSIZE_BITS   _u(0x00007c00)
#define M33_TRCIDR2_VMIDSIZE_MSB    _u(14)
#define M33_TRCIDR2_VMIDSIZE_LSB    _u(10)
#define M33_TRCIDR2_VMIDSIZE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR2_CIDSIZE
// Description : reads as `ImpDef
#define M33_TRCIDR2_CIDSIZE_RESET  _u(0x00)
#define M33_TRCIDR2_CIDSIZE_BITS   _u(0x000003e0)
#define M33_TRCIDR2_CIDSIZE_MSB    _u(9)
#define M33_TRCIDR2_CIDSIZE_LSB    _u(5)
#define M33_TRCIDR2_CIDSIZE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR2_IASIZE
// Description : reads as `ImpDef
#define M33_TRCIDR2_IASIZE_RESET  _u(0x04)
#define M33_TRCIDR2_IASIZE_BITS   _u(0x0000001f)
#define M33_TRCIDR2_IASIZE_MSB    _u(4)
#define M33_TRCIDR2_IASIZE_LSB    _u(0)
#define M33_TRCIDR2_IASIZE_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCIDR3
// Description : TRCIDR3
#define M33_TRCIDR3_OFFSET _u(0x000411ec)
#define M33_TRCIDR3_BITS   _u(0xffff0fff)
#define M33_TRCIDR3_RESET  _u(0x0f090004)
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR3_NOOVERFLOW
// Description : reads as `ImpDef
#define M33_TRCIDR3_NOOVERFLOW_RESET  _u(0x0)
#define M33_TRCIDR3_NOOVERFLOW_BITS   _u(0x80000000)
#define M33_TRCIDR3_NOOVERFLOW_MSB    _u(31)
#define M33_TRCIDR3_NOOVERFLOW_LSB    _u(31)
#define M33_TRCIDR3_NOOVERFLOW_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR3_NUMPROC
// Description : reads as `ImpDef
#define M33_TRCIDR3_NUMPROC_RESET  _u(0x0)
#define M33_TRCIDR3_NUMPROC_BITS   _u(0x70000000)
#define M33_TRCIDR3_NUMPROC_MSB    _u(30)
#define M33_TRCIDR3_NUMPROC_LSB    _u(28)
#define M33_TRCIDR3_NUMPROC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR3_SYSSTALL
// Description : reads as `ImpDef
#define M33_TRCIDR3_SYSSTALL_RESET  _u(0x1)
#define M33_TRCIDR3_SYSSTALL_BITS   _u(0x08000000)
#define M33_TRCIDR3_SYSSTALL_MSB    _u(27)
#define M33_TRCIDR3_SYSSTALL_LSB    _u(27)
#define M33_TRCIDR3_SYSSTALL_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR3_STALLCTL
// Description : reads as `ImpDef
#define M33_TRCIDR3_STALLCTL_RESET  _u(0x1)
#define M33_TRCIDR3_STALLCTL_BITS   _u(0x04000000)
#define M33_TRCIDR3_STALLCTL_MSB    _u(26)
#define M33_TRCIDR3_STALLCTL_LSB    _u(26)
#define M33_TRCIDR3_STALLCTL_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR3_SYNCPR
// Description : reads as `ImpDef
#define M33_TRCIDR3_SYNCPR_RESET  _u(0x1)
#define M33_TRCIDR3_SYNCPR_BITS   _u(0x02000000)
#define M33_TRCIDR3_SYNCPR_MSB    _u(25)
#define M33_TRCIDR3_SYNCPR_LSB    _u(25)
#define M33_TRCIDR3_SYNCPR_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR3_TRCERR
// Description : reads as `ImpDef
#define M33_TRCIDR3_TRCERR_RESET  _u(0x1)
#define M33_TRCIDR3_TRCERR_BITS   _u(0x01000000)
#define M33_TRCIDR3_TRCERR_MSB    _u(24)
#define M33_TRCIDR3_TRCERR_LSB    _u(24)
#define M33_TRCIDR3_TRCERR_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR3_EXLEVEL_NS
// Description : reads as `ImpDef
#define M33_TRCIDR3_EXLEVEL_NS_RESET  _u(0x0)
#define M33_TRCIDR3_EXLEVEL_NS_BITS   _u(0x00f00000)
#define M33_TRCIDR3_EXLEVEL_NS_MSB    _u(23)
#define M33_TRCIDR3_EXLEVEL_NS_LSB    _u(20)
#define M33_TRCIDR3_EXLEVEL_NS_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR3_EXLEVEL_S
// Description : reads as `ImpDef
#define M33_TRCIDR3_EXLEVEL_S_RESET  _u(0x9)
#define M33_TRCIDR3_EXLEVEL_S_BITS   _u(0x000f0000)
#define M33_TRCIDR3_EXLEVEL_S_MSB    _u(19)
#define M33_TRCIDR3_EXLEVEL_S_LSB    _u(16)
#define M33_TRCIDR3_EXLEVEL_S_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR3_CCITMIN
// Description : reads as `ImpDef
#define M33_TRCIDR3_CCITMIN_RESET  _u(0x004)
#define M33_TRCIDR3_CCITMIN_BITS   _u(0x00000fff)
#define M33_TRCIDR3_CCITMIN_MSB    _u(11)
#define M33_TRCIDR3_CCITMIN_LSB    _u(0)
#define M33_TRCIDR3_CCITMIN_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCIDR4
// Description : TRCIDR4
#define M33_TRCIDR4_OFFSET _u(0x000411f0)
#define M33_TRCIDR4_BITS   _u(0xfffff1ff)
#define M33_TRCIDR4_RESET  _u(0x00114000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR4_NUMVMIDC
// Description : reads as `ImpDef
#define M33_TRCIDR4_NUMVMIDC_RESET  _u(0x0)
#define M33_TRCIDR4_NUMVMIDC_BITS   _u(0xf0000000)
#define M33_TRCIDR4_NUMVMIDC_MSB    _u(31)
#define M33_TRCIDR4_NUMVMIDC_LSB    _u(28)
#define M33_TRCIDR4_NUMVMIDC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR4_NUMCIDC
// Description : reads as `ImpDef
#define M33_TRCIDR4_NUMCIDC_RESET  _u(0x0)
#define M33_TRCIDR4_NUMCIDC_BITS   _u(0x0f000000)
#define M33_TRCIDR4_NUMCIDC_MSB    _u(27)
#define M33_TRCIDR4_NUMCIDC_LSB    _u(24)
#define M33_TRCIDR4_NUMCIDC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR4_NUMSSCC
// Description : reads as `ImpDef
#define M33_TRCIDR4_NUMSSCC_RESET  _u(0x1)
#define M33_TRCIDR4_NUMSSCC_BITS   _u(0x00f00000)
#define M33_TRCIDR4_NUMSSCC_MSB    _u(23)
#define M33_TRCIDR4_NUMSSCC_LSB    _u(20)
#define M33_TRCIDR4_NUMSSCC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR4_NUMRSPAIR
// Description : reads as `ImpDef
#define M33_TRCIDR4_NUMRSPAIR_RESET  _u(0x1)
#define M33_TRCIDR4_NUMRSPAIR_BITS   _u(0x000f0000)
#define M33_TRCIDR4_NUMRSPAIR_MSB    _u(19)
#define M33_TRCIDR4_NUMRSPAIR_LSB    _u(16)
#define M33_TRCIDR4_NUMRSPAIR_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR4_NUMPC
// Description : reads as `ImpDef
#define M33_TRCIDR4_NUMPC_RESET  _u(0x4)
#define M33_TRCIDR4_NUMPC_BITS   _u(0x0000f000)
#define M33_TRCIDR4_NUMPC_MSB    _u(15)
#define M33_TRCIDR4_NUMPC_LSB    _u(12)
#define M33_TRCIDR4_NUMPC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR4_SUPPDAC
// Description : reads as `ImpDef
#define M33_TRCIDR4_SUPPDAC_RESET  _u(0x0)
#define M33_TRCIDR4_SUPPDAC_BITS   _u(0x00000100)
#define M33_TRCIDR4_SUPPDAC_MSB    _u(8)
#define M33_TRCIDR4_SUPPDAC_LSB    _u(8)
#define M33_TRCIDR4_SUPPDAC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR4_NUMDVC
// Description : reads as `ImpDef
#define M33_TRCIDR4_NUMDVC_RESET  _u(0x0)
#define M33_TRCIDR4_NUMDVC_BITS   _u(0x000000f0)
#define M33_TRCIDR4_NUMDVC_MSB    _u(7)
#define M33_TRCIDR4_NUMDVC_LSB    _u(4)
#define M33_TRCIDR4_NUMDVC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR4_NUMACPAIRS
// Description : reads as `ImpDef
#define M33_TRCIDR4_NUMACPAIRS_RESET  _u(0x0)
#define M33_TRCIDR4_NUMACPAIRS_BITS   _u(0x0000000f)
#define M33_TRCIDR4_NUMACPAIRS_MSB    _u(3)
#define M33_TRCIDR4_NUMACPAIRS_LSB    _u(0)
#define M33_TRCIDR4_NUMACPAIRS_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCIDR5
// Description : TRCIDR5
#define M33_TRCIDR5_OFFSET _u(0x000411f4)
#define M33_TRCIDR5_BITS   _u(0xfeff0fff)
#define M33_TRCIDR5_RESET  _u(0x90c70004)
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR5_REDFUNCNTR
// Description : reads as `ImpDef
#define M33_TRCIDR5_REDFUNCNTR_RESET  _u(0x1)
#define M33_TRCIDR5_REDFUNCNTR_BITS   _u(0x80000000)
#define M33_TRCIDR5_REDFUNCNTR_MSB    _u(31)
#define M33_TRCIDR5_REDFUNCNTR_LSB    _u(31)
#define M33_TRCIDR5_REDFUNCNTR_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR5_NUMCNTR
// Description : reads as `ImpDef
#define M33_TRCIDR5_NUMCNTR_RESET  _u(0x1)
#define M33_TRCIDR5_NUMCNTR_BITS   _u(0x70000000)
#define M33_TRCIDR5_NUMCNTR_MSB    _u(30)
#define M33_TRCIDR5_NUMCNTR_LSB    _u(28)
#define M33_TRCIDR5_NUMCNTR_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR5_NUMSEQSTATE
// Description : reads as `ImpDef
#define M33_TRCIDR5_NUMSEQSTATE_RESET  _u(0x0)
#define M33_TRCIDR5_NUMSEQSTATE_BITS   _u(0x0e000000)
#define M33_TRCIDR5_NUMSEQSTATE_MSB    _u(27)
#define M33_TRCIDR5_NUMSEQSTATE_LSB    _u(25)
#define M33_TRCIDR5_NUMSEQSTATE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR5_LPOVERRIDE
// Description : reads as `ImpDef
#define M33_TRCIDR5_LPOVERRIDE_RESET  _u(0x1)
#define M33_TRCIDR5_LPOVERRIDE_BITS   _u(0x00800000)
#define M33_TRCIDR5_LPOVERRIDE_MSB    _u(23)
#define M33_TRCIDR5_LPOVERRIDE_LSB    _u(23)
#define M33_TRCIDR5_LPOVERRIDE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR5_ATBTRIG
// Description : reads as `ImpDef
#define M33_TRCIDR5_ATBTRIG_RESET  _u(0x1)
#define M33_TRCIDR5_ATBTRIG_BITS   _u(0x00400000)
#define M33_TRCIDR5_ATBTRIG_MSB    _u(22)
#define M33_TRCIDR5_ATBTRIG_LSB    _u(22)
#define M33_TRCIDR5_ATBTRIG_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR5_TRACEIDSIZE
// Description : reads as 0x07
#define M33_TRCIDR5_TRACEIDSIZE_RESET  _u(0x07)
#define M33_TRCIDR5_TRACEIDSIZE_BITS   _u(0x003f0000)
#define M33_TRCIDR5_TRACEIDSIZE_MSB    _u(21)
#define M33_TRCIDR5_TRACEIDSIZE_LSB    _u(16)
#define M33_TRCIDR5_TRACEIDSIZE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR5_NUMEXTINSEL
// Description : reads as `ImpDef
#define M33_TRCIDR5_NUMEXTINSEL_RESET  _u(0x0)
#define M33_TRCIDR5_NUMEXTINSEL_BITS   _u(0x00000e00)
#define M33_TRCIDR5_NUMEXTINSEL_MSB    _u(11)
#define M33_TRCIDR5_NUMEXTINSEL_LSB    _u(9)
#define M33_TRCIDR5_NUMEXTINSEL_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCIDR5_NUMEXTIN
// Description : reads as `ImpDef
#define M33_TRCIDR5_NUMEXTIN_RESET  _u(0x004)
#define M33_TRCIDR5_NUMEXTIN_BITS   _u(0x000001ff)
#define M33_TRCIDR5_NUMEXTIN_MSB    _u(8)
#define M33_TRCIDR5_NUMEXTIN_LSB    _u(0)
#define M33_TRCIDR5_NUMEXTIN_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCIDR6
// Description : TRCIDR6
#define M33_TRCIDR6_OFFSET _u(0x000411f8)
#define M33_TRCIDR6_BITS   _u(0x00000000)
#define M33_TRCIDR6_RESET  _u(0x00000000)
#define M33_TRCIDR6_MSB    _u(31)
#define M33_TRCIDR6_LSB    _u(0)
#define M33_TRCIDR6_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCIDR7
// Description : TRCIDR7
#define M33_TRCIDR7_OFFSET _u(0x000411fc)
#define M33_TRCIDR7_BITS   _u(0x00000000)
#define M33_TRCIDR7_RESET  _u(0x00000000)
#define M33_TRCIDR7_MSB    _u(31)
#define M33_TRCIDR7_LSB    _u(0)
#define M33_TRCIDR7_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCRSCTLR2
// Description : The TRCRSCTLR controls the trace resources
#define M33_TRCRSCTLR2_OFFSET _u(0x00041208)
#define M33_TRCRSCTLR2_BITS   _u(0x003700ff)
#define M33_TRCRSCTLR2_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCRSCTLR2_PAIRINV
// Description : Inverts the result of a combined pair of resources.  This bit
//               is only implemented on the lower register for a pair of
//               resource selectors
#define M33_TRCRSCTLR2_PAIRINV_RESET  _u(0x0)
#define M33_TRCRSCTLR2_PAIRINV_BITS   _u(0x00200000)
#define M33_TRCRSCTLR2_PAIRINV_MSB    _u(21)
#define M33_TRCRSCTLR2_PAIRINV_LSB    _u(21)
#define M33_TRCRSCTLR2_PAIRINV_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCRSCTLR2_INV
// Description : Inverts the selected resources
#define M33_TRCRSCTLR2_INV_RESET  _u(0x0)
#define M33_TRCRSCTLR2_INV_BITS   _u(0x00100000)
#define M33_TRCRSCTLR2_INV_MSB    _u(20)
#define M33_TRCRSCTLR2_INV_LSB    _u(20)
#define M33_TRCRSCTLR2_INV_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCRSCTLR2_GROUP
// Description : Selects a group of resource
#define M33_TRCRSCTLR2_GROUP_RESET  _u(0x0)
#define M33_TRCRSCTLR2_GROUP_BITS   _u(0x00070000)
#define M33_TRCRSCTLR2_GROUP_MSB    _u(18)
#define M33_TRCRSCTLR2_GROUP_LSB    _u(16)
#define M33_TRCRSCTLR2_GROUP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCRSCTLR2_SELECT
// Description : Selects one or more resources from the wanted group. One bit is
//               provided per resource from the group
#define M33_TRCRSCTLR2_SELECT_RESET  _u(0x00)
#define M33_TRCRSCTLR2_SELECT_BITS   _u(0x000000ff)
#define M33_TRCRSCTLR2_SELECT_MSB    _u(7)
#define M33_TRCRSCTLR2_SELECT_LSB    _u(0)
#define M33_TRCRSCTLR2_SELECT_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCRSCTLR3
// Description : The TRCRSCTLR controls the trace resources
#define M33_TRCRSCTLR3_OFFSET _u(0x0004120c)
#define M33_TRCRSCTLR3_BITS   _u(0x003700ff)
#define M33_TRCRSCTLR3_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCRSCTLR3_PAIRINV
// Description : Inverts the result of a combined pair of resources.  This bit
//               is only implemented on the lower register for a pair of
//               resource selectors
#define M33_TRCRSCTLR3_PAIRINV_RESET  _u(0x0)
#define M33_TRCRSCTLR3_PAIRINV_BITS   _u(0x00200000)
#define M33_TRCRSCTLR3_PAIRINV_MSB    _u(21)
#define M33_TRCRSCTLR3_PAIRINV_LSB    _u(21)
#define M33_TRCRSCTLR3_PAIRINV_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCRSCTLR3_INV
// Description : Inverts the selected resources
#define M33_TRCRSCTLR3_INV_RESET  _u(0x0)
#define M33_TRCRSCTLR3_INV_BITS   _u(0x00100000)
#define M33_TRCRSCTLR3_INV_MSB    _u(20)
#define M33_TRCRSCTLR3_INV_LSB    _u(20)
#define M33_TRCRSCTLR3_INV_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCRSCTLR3_GROUP
// Description : Selects a group of resource
#define M33_TRCRSCTLR3_GROUP_RESET  _u(0x0)
#define M33_TRCRSCTLR3_GROUP_BITS   _u(0x00070000)
#define M33_TRCRSCTLR3_GROUP_MSB    _u(18)
#define M33_TRCRSCTLR3_GROUP_LSB    _u(16)
#define M33_TRCRSCTLR3_GROUP_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCRSCTLR3_SELECT
// Description : Selects one or more resources from the wanted group. One bit is
//               provided per resource from the group
#define M33_TRCRSCTLR3_SELECT_RESET  _u(0x00)
#define M33_TRCRSCTLR3_SELECT_BITS   _u(0x000000ff)
#define M33_TRCRSCTLR3_SELECT_MSB    _u(7)
#define M33_TRCRSCTLR3_SELECT_LSB    _u(0)
#define M33_TRCRSCTLR3_SELECT_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCSSCSR
// Description : Controls the corresponding single-shot comparator resource
#define M33_TRCSSCSR_OFFSET _u(0x000412a0)
#define M33_TRCSSCSR_BITS   _u(0x8000000f)
#define M33_TRCSSCSR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCSSCSR_STATUS
// Description : Single-shot status bit. Indicates if any of the comparators,
//               that TRCSSCCRn.SAC or TRCSSCCRn.ARC selects, have matched
#define M33_TRCSSCSR_STATUS_RESET  _u(0x0)
#define M33_TRCSSCSR_STATUS_BITS   _u(0x80000000)
#define M33_TRCSSCSR_STATUS_MSB    _u(31)
#define M33_TRCSSCSR_STATUS_LSB    _u(31)
#define M33_TRCSSCSR_STATUS_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCSSCSR_PC
// Description : Reserved, RES1
#define M33_TRCSSCSR_PC_RESET  _u(0x0)
#define M33_TRCSSCSR_PC_BITS   _u(0x00000008)
#define M33_TRCSSCSR_PC_MSB    _u(3)
#define M33_TRCSSCSR_PC_LSB    _u(3)
#define M33_TRCSSCSR_PC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCSSCSR_DV
// Description : Reserved, RES0
#define M33_TRCSSCSR_DV_RESET  _u(0x0)
#define M33_TRCSSCSR_DV_BITS   _u(0x00000004)
#define M33_TRCSSCSR_DV_MSB    _u(2)
#define M33_TRCSSCSR_DV_LSB    _u(2)
#define M33_TRCSSCSR_DV_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCSSCSR_DA
// Description : Reserved, RES0
#define M33_TRCSSCSR_DA_RESET  _u(0x0)
#define M33_TRCSSCSR_DA_BITS   _u(0x00000002)
#define M33_TRCSSCSR_DA_MSB    _u(1)
#define M33_TRCSSCSR_DA_LSB    _u(1)
#define M33_TRCSSCSR_DA_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCSSCSR_INST
// Description : Reserved, RES0
#define M33_TRCSSCSR_INST_RESET  _u(0x0)
#define M33_TRCSSCSR_INST_BITS   _u(0x00000001)
#define M33_TRCSSCSR_INST_MSB    _u(0)
#define M33_TRCSSCSR_INST_LSB    _u(0)
#define M33_TRCSSCSR_INST_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCSSPCICR
// Description : Selects the PE comparator inputs for Single-shot control
#define M33_TRCSSPCICR_OFFSET _u(0x000412c0)
#define M33_TRCSSPCICR_BITS   _u(0x0000000f)
#define M33_TRCSSPCICR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCSSPCICR_PC
// Description : Selects one or more PE comparator inputs for Single-shot
//               control.  TRCIDR4.NUMPC defines the size of the PC field.  1
//               bit is provided for each implemented PE comparator input.  For
//               example, if bit[1] == 1 this selects PE comparator input 1 for
//               Single-shot control
#define M33_TRCSSPCICR_PC_RESET  _u(0x0)
#define M33_TRCSSPCICR_PC_BITS   _u(0x0000000f)
#define M33_TRCSSPCICR_PC_MSB    _u(3)
#define M33_TRCSSPCICR_PC_LSB    _u(0)
#define M33_TRCSSPCICR_PC_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCPDCR
// Description : Requests the system to provide power to the trace unit
#define M33_TRCPDCR_OFFSET _u(0x00041310)
#define M33_TRCPDCR_BITS   _u(0x00000008)
#define M33_TRCPDCR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCPDCR_PU
// Description : Powerup request bit:
#define M33_TRCPDCR_PU_RESET  _u(0x0)
#define M33_TRCPDCR_PU_BITS   _u(0x00000008)
#define M33_TRCPDCR_PU_MSB    _u(3)
#define M33_TRCPDCR_PU_LSB    _u(3)
#define M33_TRCPDCR_PU_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCPDSR
// Description : Returns the following information about the trace unit: - OS
//               Lock status.  - Core power domain status.  - Power interruption
//               status
#define M33_TRCPDSR_OFFSET _u(0x00041314)
#define M33_TRCPDSR_BITS   _u(0x00000023)
#define M33_TRCPDSR_RESET  _u(0x00000003)
// -----------------------------------------------------------------------------
// Field       : M33_TRCPDSR_OSLK
// Description : OS Lock status bit:
#define M33_TRCPDSR_OSLK_RESET  _u(0x0)
#define M33_TRCPDSR_OSLK_BITS   _u(0x00000020)
#define M33_TRCPDSR_OSLK_MSB    _u(5)
#define M33_TRCPDSR_OSLK_LSB    _u(5)
#define M33_TRCPDSR_OSLK_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCPDSR_STICKYPD
// Description : Sticky powerdown status bit. Indicates whether the trace
//               register state is valid:
#define M33_TRCPDSR_STICKYPD_RESET  _u(0x1)
#define M33_TRCPDSR_STICKYPD_BITS   _u(0x00000002)
#define M33_TRCPDSR_STICKYPD_MSB    _u(1)
#define M33_TRCPDSR_STICKYPD_LSB    _u(1)
#define M33_TRCPDSR_STICKYPD_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCPDSR_POWER
// Description : Power status bit:
#define M33_TRCPDSR_POWER_RESET  _u(0x1)
#define M33_TRCPDSR_POWER_BITS   _u(0x00000001)
#define M33_TRCPDSR_POWER_MSB    _u(0)
#define M33_TRCPDSR_POWER_LSB    _u(0)
#define M33_TRCPDSR_POWER_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCITATBIDR
// Description : Trace Integration ATB Identification Register
#define M33_TRCITATBIDR_OFFSET _u(0x00041ee4)
#define M33_TRCITATBIDR_BITS   _u(0x0000007f)
#define M33_TRCITATBIDR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCITATBIDR_ID
// Description : Trace ID
#define M33_TRCITATBIDR_ID_RESET  _u(0x00)
#define M33_TRCITATBIDR_ID_BITS   _u(0x0000007f)
#define M33_TRCITATBIDR_ID_MSB    _u(6)
#define M33_TRCITATBIDR_ID_LSB    _u(0)
#define M33_TRCITATBIDR_ID_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCITIATBINR
// Description : Trace Integration Instruction ATB In Register
#define M33_TRCITIATBINR_OFFSET _u(0x00041ef4)
#define M33_TRCITIATBINR_BITS   _u(0x00000003)
#define M33_TRCITIATBINR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCITIATBINR_AFVALIDM
// Description : Integration Mode instruction AFVALIDM in
#define M33_TRCITIATBINR_AFVALIDM_RESET  _u(0x0)
#define M33_TRCITIATBINR_AFVALIDM_BITS   _u(0x00000002)
#define M33_TRCITIATBINR_AFVALIDM_MSB    _u(1)
#define M33_TRCITIATBINR_AFVALIDM_LSB    _u(1)
#define M33_TRCITIATBINR_AFVALIDM_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCITIATBINR_ATREADYM
// Description : Integration Mode instruction ATREADYM in
#define M33_TRCITIATBINR_ATREADYM_RESET  _u(0x0)
#define M33_TRCITIATBINR_ATREADYM_BITS   _u(0x00000001)
#define M33_TRCITIATBINR_ATREADYM_MSB    _u(0)
#define M33_TRCITIATBINR_ATREADYM_LSB    _u(0)
#define M33_TRCITIATBINR_ATREADYM_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCITIATBOUTR
// Description : Trace Integration Instruction ATB Out Register
#define M33_TRCITIATBOUTR_OFFSET _u(0x00041efc)
#define M33_TRCITIATBOUTR_BITS   _u(0x00000003)
#define M33_TRCITIATBOUTR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCITIATBOUTR_AFREADY
// Description : Integration Mode instruction AFREADY out
#define M33_TRCITIATBOUTR_AFREADY_RESET  _u(0x0)
#define M33_TRCITIATBOUTR_AFREADY_BITS   _u(0x00000002)
#define M33_TRCITIATBOUTR_AFREADY_MSB    _u(1)
#define M33_TRCITIATBOUTR_AFREADY_LSB    _u(1)
#define M33_TRCITIATBOUTR_AFREADY_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCITIATBOUTR_ATVALID
// Description : Integration Mode instruction ATVALID out
#define M33_TRCITIATBOUTR_ATVALID_RESET  _u(0x0)
#define M33_TRCITIATBOUTR_ATVALID_BITS   _u(0x00000001)
#define M33_TRCITIATBOUTR_ATVALID_MSB    _u(0)
#define M33_TRCITIATBOUTR_ATVALID_LSB    _u(0)
#define M33_TRCITIATBOUTR_ATVALID_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCCLAIMSET
// Description : Claim Tag Set Register
#define M33_TRCCLAIMSET_OFFSET _u(0x00041fa0)
#define M33_TRCCLAIMSET_BITS   _u(0x0000000f)
#define M33_TRCCLAIMSET_RESET  _u(0x0000000f)
// -----------------------------------------------------------------------------
// Field       : M33_TRCCLAIMSET_SET3
// Description : When a write to one of these bits occurs, with the value:
#define M33_TRCCLAIMSET_SET3_RESET  _u(0x1)
#define M33_TRCCLAIMSET_SET3_BITS   _u(0x00000008)
#define M33_TRCCLAIMSET_SET3_MSB    _u(3)
#define M33_TRCCLAIMSET_SET3_LSB    _u(3)
#define M33_TRCCLAIMSET_SET3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCCLAIMSET_SET2
// Description : When a write to one of these bits occurs, with the value:
#define M33_TRCCLAIMSET_SET2_RESET  _u(0x1)
#define M33_TRCCLAIMSET_SET2_BITS   _u(0x00000004)
#define M33_TRCCLAIMSET_SET2_MSB    _u(2)
#define M33_TRCCLAIMSET_SET2_LSB    _u(2)
#define M33_TRCCLAIMSET_SET2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCCLAIMSET_SET1
// Description : When a write to one of these bits occurs, with the value:
#define M33_TRCCLAIMSET_SET1_RESET  _u(0x1)
#define M33_TRCCLAIMSET_SET1_BITS   _u(0x00000002)
#define M33_TRCCLAIMSET_SET1_MSB    _u(1)
#define M33_TRCCLAIMSET_SET1_LSB    _u(1)
#define M33_TRCCLAIMSET_SET1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCCLAIMSET_SET0
// Description : When a write to one of these bits occurs, with the value:
#define M33_TRCCLAIMSET_SET0_RESET  _u(0x1)
#define M33_TRCCLAIMSET_SET0_BITS   _u(0x00000001)
#define M33_TRCCLAIMSET_SET0_MSB    _u(0)
#define M33_TRCCLAIMSET_SET0_LSB    _u(0)
#define M33_TRCCLAIMSET_SET0_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCCLAIMCLR
// Description : Claim Tag Clear Register
#define M33_TRCCLAIMCLR_OFFSET _u(0x00041fa4)
#define M33_TRCCLAIMCLR_BITS   _u(0x0000000f)
#define M33_TRCCLAIMCLR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCCLAIMCLR_CLR3
// Description : When a write to one of these bits occurs, with the value:
#define M33_TRCCLAIMCLR_CLR3_RESET  _u(0x0)
#define M33_TRCCLAIMCLR_CLR3_BITS   _u(0x00000008)
#define M33_TRCCLAIMCLR_CLR3_MSB    _u(3)
#define M33_TRCCLAIMCLR_CLR3_LSB    _u(3)
#define M33_TRCCLAIMCLR_CLR3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCCLAIMCLR_CLR2
// Description : When a write to one of these bits occurs, with the value:
#define M33_TRCCLAIMCLR_CLR2_RESET  _u(0x0)
#define M33_TRCCLAIMCLR_CLR2_BITS   _u(0x00000004)
#define M33_TRCCLAIMCLR_CLR2_MSB    _u(2)
#define M33_TRCCLAIMCLR_CLR2_LSB    _u(2)
#define M33_TRCCLAIMCLR_CLR2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCCLAIMCLR_CLR1
// Description : When a write to one of these bits occurs, with the value:
#define M33_TRCCLAIMCLR_CLR1_RESET  _u(0x0)
#define M33_TRCCLAIMCLR_CLR1_BITS   _u(0x00000002)
#define M33_TRCCLAIMCLR_CLR1_MSB    _u(1)
#define M33_TRCCLAIMCLR_CLR1_LSB    _u(1)
#define M33_TRCCLAIMCLR_CLR1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_TRCCLAIMCLR_CLR0
// Description : When a write to one of these bits occurs, with the value:
#define M33_TRCCLAIMCLR_CLR0_RESET  _u(0x0)
#define M33_TRCCLAIMCLR_CLR0_BITS   _u(0x00000001)
#define M33_TRCCLAIMCLR_CLR0_MSB    _u(0)
#define M33_TRCCLAIMCLR_CLR0_LSB    _u(0)
#define M33_TRCCLAIMCLR_CLR0_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCAUTHSTATUS
// Description : Returns the level of tracing that the trace unit can support
#define M33_TRCAUTHSTATUS_OFFSET _u(0x00041fb8)
#define M33_TRCAUTHSTATUS_BITS   _u(0x000000ff)
#define M33_TRCAUTHSTATUS_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCAUTHSTATUS_SNID
// Description : Indicates whether the system enables the trace unit to support
//               Secure non-invasive debug:
#define M33_TRCAUTHSTATUS_SNID_RESET  _u(0x0)
#define M33_TRCAUTHSTATUS_SNID_BITS   _u(0x000000c0)
#define M33_TRCAUTHSTATUS_SNID_MSB    _u(7)
#define M33_TRCAUTHSTATUS_SNID_LSB    _u(6)
#define M33_TRCAUTHSTATUS_SNID_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCAUTHSTATUS_SID
// Description : Indicates whether the trace unit supports Secure invasive
//               debug:
#define M33_TRCAUTHSTATUS_SID_RESET  _u(0x0)
#define M33_TRCAUTHSTATUS_SID_BITS   _u(0x00000030)
#define M33_TRCAUTHSTATUS_SID_MSB    _u(5)
#define M33_TRCAUTHSTATUS_SID_LSB    _u(4)
#define M33_TRCAUTHSTATUS_SID_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCAUTHSTATUS_NSNID
// Description : Indicates whether the system enables the trace unit to support
//               Non-secure non-invasive debug:
#define M33_TRCAUTHSTATUS_NSNID_RESET  _u(0x0)
#define M33_TRCAUTHSTATUS_NSNID_BITS   _u(0x0000000c)
#define M33_TRCAUTHSTATUS_NSNID_MSB    _u(3)
#define M33_TRCAUTHSTATUS_NSNID_LSB    _u(2)
#define M33_TRCAUTHSTATUS_NSNID_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCAUTHSTATUS_NSID
// Description : Indicates whether the trace unit supports Non-secure invasive
//               debug:
#define M33_TRCAUTHSTATUS_NSID_RESET  _u(0x0)
#define M33_TRCAUTHSTATUS_NSID_BITS   _u(0x00000003)
#define M33_TRCAUTHSTATUS_NSID_MSB    _u(1)
#define M33_TRCAUTHSTATUS_NSID_LSB    _u(0)
#define M33_TRCAUTHSTATUS_NSID_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCDEVARCH
// Description : TRCDEVARCH
#define M33_TRCDEVARCH_OFFSET _u(0x00041fbc)
#define M33_TRCDEVARCH_BITS   _u(0xffffffff)
#define M33_TRCDEVARCH_RESET  _u(0x47724a13)
// -----------------------------------------------------------------------------
// Field       : M33_TRCDEVARCH_ARCHITECT
// Description : reads as 0b01000111011
#define M33_TRCDEVARCH_ARCHITECT_RESET  _u(0x23b)
#define M33_TRCDEVARCH_ARCHITECT_BITS   _u(0xffe00000)
#define M33_TRCDEVARCH_ARCHITECT_MSB    _u(31)
#define M33_TRCDEVARCH_ARCHITECT_LSB    _u(21)
#define M33_TRCDEVARCH_ARCHITECT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCDEVARCH_PRESENT
// Description : reads as 0b1
#define M33_TRCDEVARCH_PRESENT_RESET  _u(0x1)
#define M33_TRCDEVARCH_PRESENT_BITS   _u(0x00100000)
#define M33_TRCDEVARCH_PRESENT_MSB    _u(20)
#define M33_TRCDEVARCH_PRESENT_LSB    _u(20)
#define M33_TRCDEVARCH_PRESENT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCDEVARCH_REVISION
// Description : reads as 0b0000
#define M33_TRCDEVARCH_REVISION_RESET  _u(0x2)
#define M33_TRCDEVARCH_REVISION_BITS   _u(0x000f0000)
#define M33_TRCDEVARCH_REVISION_MSB    _u(19)
#define M33_TRCDEVARCH_REVISION_LSB    _u(16)
#define M33_TRCDEVARCH_REVISION_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCDEVARCH_ARCHID
// Description : reads as 0b0100101000010011
#define M33_TRCDEVARCH_ARCHID_RESET  _u(0x4a13)
#define M33_TRCDEVARCH_ARCHID_BITS   _u(0x0000ffff)
#define M33_TRCDEVARCH_ARCHID_MSB    _u(15)
#define M33_TRCDEVARCH_ARCHID_LSB    _u(0)
#define M33_TRCDEVARCH_ARCHID_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCDEVID
// Description : TRCDEVID
#define M33_TRCDEVID_OFFSET _u(0x00041fc8)
#define M33_TRCDEVID_BITS   _u(0x00000000)
#define M33_TRCDEVID_RESET  _u(0x00000000)
#define M33_TRCDEVID_MSB    _u(31)
#define M33_TRCDEVID_LSB    _u(0)
#define M33_TRCDEVID_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCDEVTYPE
// Description : TRCDEVTYPE
#define M33_TRCDEVTYPE_OFFSET _u(0x00041fcc)
#define M33_TRCDEVTYPE_BITS   _u(0x000000ff)
#define M33_TRCDEVTYPE_RESET  _u(0x00000013)
// -----------------------------------------------------------------------------
// Field       : M33_TRCDEVTYPE_SUB
// Description : reads as 0b0001
#define M33_TRCDEVTYPE_SUB_RESET  _u(0x1)
#define M33_TRCDEVTYPE_SUB_BITS   _u(0x000000f0)
#define M33_TRCDEVTYPE_SUB_MSB    _u(7)
#define M33_TRCDEVTYPE_SUB_LSB    _u(4)
#define M33_TRCDEVTYPE_SUB_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCDEVTYPE_MAJOR
// Description : reads as 0b0011
#define M33_TRCDEVTYPE_MAJOR_RESET  _u(0x3)
#define M33_TRCDEVTYPE_MAJOR_BITS   _u(0x0000000f)
#define M33_TRCDEVTYPE_MAJOR_MSB    _u(3)
#define M33_TRCDEVTYPE_MAJOR_LSB    _u(0)
#define M33_TRCDEVTYPE_MAJOR_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCPIDR4
// Description : TRCPIDR4
#define M33_TRCPIDR4_OFFSET _u(0x00041fd0)
#define M33_TRCPIDR4_BITS   _u(0x000000ff)
#define M33_TRCPIDR4_RESET  _u(0x00000004)
// -----------------------------------------------------------------------------
// Field       : M33_TRCPIDR4_SIZE
// Description : reads as `ImpDef
#define M33_TRCPIDR4_SIZE_RESET  _u(0x0)
#define M33_TRCPIDR4_SIZE_BITS   _u(0x000000f0)
#define M33_TRCPIDR4_SIZE_MSB    _u(7)
#define M33_TRCPIDR4_SIZE_LSB    _u(4)
#define M33_TRCPIDR4_SIZE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCPIDR4_DES_2
// Description : reads as `ImpDef
#define M33_TRCPIDR4_DES_2_RESET  _u(0x4)
#define M33_TRCPIDR4_DES_2_BITS   _u(0x0000000f)
#define M33_TRCPIDR4_DES_2_MSB    _u(3)
#define M33_TRCPIDR4_DES_2_LSB    _u(0)
#define M33_TRCPIDR4_DES_2_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCPIDR5
// Description : TRCPIDR5
#define M33_TRCPIDR5_OFFSET _u(0x00041fd4)
#define M33_TRCPIDR5_BITS   _u(0x00000000)
#define M33_TRCPIDR5_RESET  _u(0x00000000)
#define M33_TRCPIDR5_MSB    _u(31)
#define M33_TRCPIDR5_LSB    _u(0)
#define M33_TRCPIDR5_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCPIDR6
// Description : TRCPIDR6
#define M33_TRCPIDR6_OFFSET _u(0x00041fd8)
#define M33_TRCPIDR6_BITS   _u(0x00000000)
#define M33_TRCPIDR6_RESET  _u(0x00000000)
#define M33_TRCPIDR6_MSB    _u(31)
#define M33_TRCPIDR6_LSB    _u(0)
#define M33_TRCPIDR6_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCPIDR7
// Description : TRCPIDR7
#define M33_TRCPIDR7_OFFSET _u(0x00041fdc)
#define M33_TRCPIDR7_BITS   _u(0x00000000)
#define M33_TRCPIDR7_RESET  _u(0x00000000)
#define M33_TRCPIDR7_MSB    _u(31)
#define M33_TRCPIDR7_LSB    _u(0)
#define M33_TRCPIDR7_ACCESS "RW"
// =============================================================================
// Register    : M33_TRCPIDR0
// Description : TRCPIDR0
#define M33_TRCPIDR0_OFFSET _u(0x00041fe0)
#define M33_TRCPIDR0_BITS   _u(0x000000ff)
#define M33_TRCPIDR0_RESET  _u(0x00000021)
// -----------------------------------------------------------------------------
// Field       : M33_TRCPIDR0_PART_0
// Description : reads as `ImpDef
#define M33_TRCPIDR0_PART_0_RESET  _u(0x21)
#define M33_TRCPIDR0_PART_0_BITS   _u(0x000000ff)
#define M33_TRCPIDR0_PART_0_MSB    _u(7)
#define M33_TRCPIDR0_PART_0_LSB    _u(0)
#define M33_TRCPIDR0_PART_0_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCPIDR1
// Description : TRCPIDR1
#define M33_TRCPIDR1_OFFSET _u(0x00041fe4)
#define M33_TRCPIDR1_BITS   _u(0x000000ff)
#define M33_TRCPIDR1_RESET  _u(0x000000bd)
// -----------------------------------------------------------------------------
// Field       : M33_TRCPIDR1_DES_0
// Description : reads as `ImpDef
#define M33_TRCPIDR1_DES_0_RESET  _u(0xb)
#define M33_TRCPIDR1_DES_0_BITS   _u(0x000000f0)
#define M33_TRCPIDR1_DES_0_MSB    _u(7)
#define M33_TRCPIDR1_DES_0_LSB    _u(4)
#define M33_TRCPIDR1_DES_0_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCPIDR1_PART_0
// Description : reads as `ImpDef
#define M33_TRCPIDR1_PART_0_RESET  _u(0xd)
#define M33_TRCPIDR1_PART_0_BITS   _u(0x0000000f)
#define M33_TRCPIDR1_PART_0_MSB    _u(3)
#define M33_TRCPIDR1_PART_0_LSB    _u(0)
#define M33_TRCPIDR1_PART_0_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCPIDR2
// Description : TRCPIDR2
#define M33_TRCPIDR2_OFFSET _u(0x00041fe8)
#define M33_TRCPIDR2_BITS   _u(0x000000ff)
#define M33_TRCPIDR2_RESET  _u(0x0000002b)
// -----------------------------------------------------------------------------
// Field       : M33_TRCPIDR2_REVISION
// Description : reads as `ImpDef
#define M33_TRCPIDR2_REVISION_RESET  _u(0x2)
#define M33_TRCPIDR2_REVISION_BITS   _u(0x000000f0)
#define M33_TRCPIDR2_REVISION_MSB    _u(7)
#define M33_TRCPIDR2_REVISION_LSB    _u(4)
#define M33_TRCPIDR2_REVISION_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCPIDR2_JEDEC
// Description : reads as 0b1
#define M33_TRCPIDR2_JEDEC_RESET  _u(0x1)
#define M33_TRCPIDR2_JEDEC_BITS   _u(0x00000008)
#define M33_TRCPIDR2_JEDEC_MSB    _u(3)
#define M33_TRCPIDR2_JEDEC_LSB    _u(3)
#define M33_TRCPIDR2_JEDEC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCPIDR2_DES_0
// Description : reads as `ImpDef
#define M33_TRCPIDR2_DES_0_RESET  _u(0x3)
#define M33_TRCPIDR2_DES_0_BITS   _u(0x00000007)
#define M33_TRCPIDR2_DES_0_MSB    _u(2)
#define M33_TRCPIDR2_DES_0_LSB    _u(0)
#define M33_TRCPIDR2_DES_0_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCPIDR3
// Description : TRCPIDR3
#define M33_TRCPIDR3_OFFSET _u(0x00041fec)
#define M33_TRCPIDR3_BITS   _u(0x000000ff)
#define M33_TRCPIDR3_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_TRCPIDR3_REVAND
// Description : reads as `ImpDef
#define M33_TRCPIDR3_REVAND_RESET  _u(0x0)
#define M33_TRCPIDR3_REVAND_BITS   _u(0x000000f0)
#define M33_TRCPIDR3_REVAND_MSB    _u(7)
#define M33_TRCPIDR3_REVAND_LSB    _u(4)
#define M33_TRCPIDR3_REVAND_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCPIDR3_CMOD
// Description : reads as `ImpDef
#define M33_TRCPIDR3_CMOD_RESET  _u(0x0)
#define M33_TRCPIDR3_CMOD_BITS   _u(0x0000000f)
#define M33_TRCPIDR3_CMOD_MSB    _u(3)
#define M33_TRCPIDR3_CMOD_LSB    _u(0)
#define M33_TRCPIDR3_CMOD_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCCIDR0
// Description : TRCCIDR0
#define M33_TRCCIDR0_OFFSET _u(0x00041ff0)
#define M33_TRCCIDR0_BITS   _u(0x000000ff)
#define M33_TRCCIDR0_RESET  _u(0x0000000d)
// -----------------------------------------------------------------------------
// Field       : M33_TRCCIDR0_PRMBL_0
// Description : reads as 0b00001101
#define M33_TRCCIDR0_PRMBL_0_RESET  _u(0x0d)
#define M33_TRCCIDR0_PRMBL_0_BITS   _u(0x000000ff)
#define M33_TRCCIDR0_PRMBL_0_MSB    _u(7)
#define M33_TRCCIDR0_PRMBL_0_LSB    _u(0)
#define M33_TRCCIDR0_PRMBL_0_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCCIDR1
// Description : TRCCIDR1
#define M33_TRCCIDR1_OFFSET _u(0x00041ff4)
#define M33_TRCCIDR1_BITS   _u(0x000000ff)
#define M33_TRCCIDR1_RESET  _u(0x00000090)
// -----------------------------------------------------------------------------
// Field       : M33_TRCCIDR1_CLASS
// Description : reads as 0b1001
#define M33_TRCCIDR1_CLASS_RESET  _u(0x9)
#define M33_TRCCIDR1_CLASS_BITS   _u(0x000000f0)
#define M33_TRCCIDR1_CLASS_MSB    _u(7)
#define M33_TRCCIDR1_CLASS_LSB    _u(4)
#define M33_TRCCIDR1_CLASS_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_TRCCIDR1_PRMBL_1
// Description : reads as 0b0000
#define M33_TRCCIDR1_PRMBL_1_RESET  _u(0x0)
#define M33_TRCCIDR1_PRMBL_1_BITS   _u(0x0000000f)
#define M33_TRCCIDR1_PRMBL_1_MSB    _u(3)
#define M33_TRCCIDR1_PRMBL_1_LSB    _u(0)
#define M33_TRCCIDR1_PRMBL_1_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCCIDR2
// Description : TRCCIDR2
#define M33_TRCCIDR2_OFFSET _u(0x00041ff8)
#define M33_TRCCIDR2_BITS   _u(0x000000ff)
#define M33_TRCCIDR2_RESET  _u(0x00000005)
// -----------------------------------------------------------------------------
// Field       : M33_TRCCIDR2_PRMBL_2
// Description : reads as 0b00000101
#define M33_TRCCIDR2_PRMBL_2_RESET  _u(0x05)
#define M33_TRCCIDR2_PRMBL_2_BITS   _u(0x000000ff)
#define M33_TRCCIDR2_PRMBL_2_MSB    _u(7)
#define M33_TRCCIDR2_PRMBL_2_LSB    _u(0)
#define M33_TRCCIDR2_PRMBL_2_ACCESS "RO"
// =============================================================================
// Register    : M33_TRCCIDR3
// Description : TRCCIDR3
#define M33_TRCCIDR3_OFFSET _u(0x00041ffc)
#define M33_TRCCIDR3_BITS   _u(0x000000ff)
#define M33_TRCCIDR3_RESET  _u(0x000000b1)
// -----------------------------------------------------------------------------
// Field       : M33_TRCCIDR3_PRMBL_3
// Description : reads as 0b10110001
#define M33_TRCCIDR3_PRMBL_3_RESET  _u(0xb1)
#define M33_TRCCIDR3_PRMBL_3_BITS   _u(0x000000ff)
#define M33_TRCCIDR3_PRMBL_3_MSB    _u(7)
#define M33_TRCCIDR3_PRMBL_3_LSB    _u(0)
#define M33_TRCCIDR3_PRMBL_3_ACCESS "RO"
// =============================================================================
// Register    : M33_CTICONTROL
// Description : CTI Control Register
#define M33_CTICONTROL_OFFSET _u(0x00042000)
#define M33_CTICONTROL_BITS   _u(0x00000001)
#define M33_CTICONTROL_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTICONTROL_GLBEN
// Description : Enables or disables the CTI
#define M33_CTICONTROL_GLBEN_RESET  _u(0x0)
#define M33_CTICONTROL_GLBEN_BITS   _u(0x00000001)
#define M33_CTICONTROL_GLBEN_MSB    _u(0)
#define M33_CTICONTROL_GLBEN_LSB    _u(0)
#define M33_CTICONTROL_GLBEN_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIINTACK
// Description : CTI Interrupt Acknowledge Register
#define M33_CTIINTACK_OFFSET _u(0x00042010)
#define M33_CTIINTACK_BITS   _u(0x000000ff)
#define M33_CTIINTACK_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIINTACK_INTACK
// Description : Acknowledges the corresponding ctitrigout output. There is one
//               bit of the register for each ctitrigout output. When a 1 is
//               written to a bit in this register, the corresponding ctitrigout
//               is acknowledged, causing it to be cleared.
#define M33_CTIINTACK_INTACK_RESET  _u(0x00)
#define M33_CTIINTACK_INTACK_BITS   _u(0x000000ff)
#define M33_CTIINTACK_INTACK_MSB    _u(7)
#define M33_CTIINTACK_INTACK_LSB    _u(0)
#define M33_CTIINTACK_INTACK_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIAPPSET
// Description : CTI Application Trigger Set Register
#define M33_CTIAPPSET_OFFSET _u(0x00042014)
#define M33_CTIAPPSET_BITS   _u(0x0000000f)
#define M33_CTIAPPSET_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIAPPSET_APPSET
// Description : Setting a bit HIGH generates a channel event for the selected
//               channel. There is one bit of the register for each channel
#define M33_CTIAPPSET_APPSET_RESET  _u(0x0)
#define M33_CTIAPPSET_APPSET_BITS   _u(0x0000000f)
#define M33_CTIAPPSET_APPSET_MSB    _u(3)
#define M33_CTIAPPSET_APPSET_LSB    _u(0)
#define M33_CTIAPPSET_APPSET_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIAPPCLEAR
// Description : CTI Application Trigger Clear Register
#define M33_CTIAPPCLEAR_OFFSET _u(0x00042018)
#define M33_CTIAPPCLEAR_BITS   _u(0x0000000f)
#define M33_CTIAPPCLEAR_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIAPPCLEAR_APPCLEAR
// Description : Sets the corresponding bits in the CTIAPPSET to 0. There is one
//               bit of the register for each channel.
#define M33_CTIAPPCLEAR_APPCLEAR_RESET  _u(0x0)
#define M33_CTIAPPCLEAR_APPCLEAR_BITS   _u(0x0000000f)
#define M33_CTIAPPCLEAR_APPCLEAR_MSB    _u(3)
#define M33_CTIAPPCLEAR_APPCLEAR_LSB    _u(0)
#define M33_CTIAPPCLEAR_APPCLEAR_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIAPPPULSE
// Description : CTI Application Pulse Register
#define M33_CTIAPPPULSE_OFFSET _u(0x0004201c)
#define M33_CTIAPPPULSE_BITS   _u(0x0000000f)
#define M33_CTIAPPPULSE_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIAPPPULSE_APPULSE
// Description : Setting a bit HIGH generates a channel event pulse for the
//               selected channel. There is one bit of the register for each
//               channel.
#define M33_CTIAPPPULSE_APPULSE_RESET  _u(0x0)
#define M33_CTIAPPPULSE_APPULSE_BITS   _u(0x0000000f)
#define M33_CTIAPPPULSE_APPULSE_MSB    _u(3)
#define M33_CTIAPPPULSE_APPULSE_LSB    _u(0)
#define M33_CTIAPPPULSE_APPULSE_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIINEN0
// Description : CTI Trigger to Channel Enable Registers
#define M33_CTIINEN0_OFFSET _u(0x00042020)
#define M33_CTIINEN0_BITS   _u(0x0000000f)
#define M33_CTIINEN0_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIINEN0_TRIGINEN
// Description : Enables a cross trigger event to the corresponding channel when
//               a ctitrigin input is activated. There is one bit of the field
//               for each of the four channels
#define M33_CTIINEN0_TRIGINEN_RESET  _u(0x0)
#define M33_CTIINEN0_TRIGINEN_BITS   _u(0x0000000f)
#define M33_CTIINEN0_TRIGINEN_MSB    _u(3)
#define M33_CTIINEN0_TRIGINEN_LSB    _u(0)
#define M33_CTIINEN0_TRIGINEN_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIINEN1
// Description : CTI Trigger to Channel Enable Registers
#define M33_CTIINEN1_OFFSET _u(0x00042024)
#define M33_CTIINEN1_BITS   _u(0x0000000f)
#define M33_CTIINEN1_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIINEN1_TRIGINEN
// Description : Enables a cross trigger event to the corresponding channel when
//               a ctitrigin input is activated. There is one bit of the field
//               for each of the four channels
#define M33_CTIINEN1_TRIGINEN_RESET  _u(0x0)
#define M33_CTIINEN1_TRIGINEN_BITS   _u(0x0000000f)
#define M33_CTIINEN1_TRIGINEN_MSB    _u(3)
#define M33_CTIINEN1_TRIGINEN_LSB    _u(0)
#define M33_CTIINEN1_TRIGINEN_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIINEN2
// Description : CTI Trigger to Channel Enable Registers
#define M33_CTIINEN2_OFFSET _u(0x00042028)
#define M33_CTIINEN2_BITS   _u(0x0000000f)
#define M33_CTIINEN2_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIINEN2_TRIGINEN
// Description : Enables a cross trigger event to the corresponding channel when
//               a ctitrigin input is activated. There is one bit of the field
//               for each of the four channels
#define M33_CTIINEN2_TRIGINEN_RESET  _u(0x0)
#define M33_CTIINEN2_TRIGINEN_BITS   _u(0x0000000f)
#define M33_CTIINEN2_TRIGINEN_MSB    _u(3)
#define M33_CTIINEN2_TRIGINEN_LSB    _u(0)
#define M33_CTIINEN2_TRIGINEN_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIINEN3
// Description : CTI Trigger to Channel Enable Registers
#define M33_CTIINEN3_OFFSET _u(0x0004202c)
#define M33_CTIINEN3_BITS   _u(0x0000000f)
#define M33_CTIINEN3_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIINEN3_TRIGINEN
// Description : Enables a cross trigger event to the corresponding channel when
//               a ctitrigin input is activated. There is one bit of the field
//               for each of the four channels
#define M33_CTIINEN3_TRIGINEN_RESET  _u(0x0)
#define M33_CTIINEN3_TRIGINEN_BITS   _u(0x0000000f)
#define M33_CTIINEN3_TRIGINEN_MSB    _u(3)
#define M33_CTIINEN3_TRIGINEN_LSB    _u(0)
#define M33_CTIINEN3_TRIGINEN_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIINEN4
// Description : CTI Trigger to Channel Enable Registers
#define M33_CTIINEN4_OFFSET _u(0x00042030)
#define M33_CTIINEN4_BITS   _u(0x0000000f)
#define M33_CTIINEN4_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIINEN4_TRIGINEN
// Description : Enables a cross trigger event to the corresponding channel when
//               a ctitrigin input is activated. There is one bit of the field
//               for each of the four channels
#define M33_CTIINEN4_TRIGINEN_RESET  _u(0x0)
#define M33_CTIINEN4_TRIGINEN_BITS   _u(0x0000000f)
#define M33_CTIINEN4_TRIGINEN_MSB    _u(3)
#define M33_CTIINEN4_TRIGINEN_LSB    _u(0)
#define M33_CTIINEN4_TRIGINEN_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIINEN5
// Description : CTI Trigger to Channel Enable Registers
#define M33_CTIINEN5_OFFSET _u(0x00042034)
#define M33_CTIINEN5_BITS   _u(0x0000000f)
#define M33_CTIINEN5_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIINEN5_TRIGINEN
// Description : Enables a cross trigger event to the corresponding channel when
//               a ctitrigin input is activated. There is one bit of the field
//               for each of the four channels
#define M33_CTIINEN5_TRIGINEN_RESET  _u(0x0)
#define M33_CTIINEN5_TRIGINEN_BITS   _u(0x0000000f)
#define M33_CTIINEN5_TRIGINEN_MSB    _u(3)
#define M33_CTIINEN5_TRIGINEN_LSB    _u(0)
#define M33_CTIINEN5_TRIGINEN_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIINEN6
// Description : CTI Trigger to Channel Enable Registers
#define M33_CTIINEN6_OFFSET _u(0x00042038)
#define M33_CTIINEN6_BITS   _u(0x0000000f)
#define M33_CTIINEN6_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIINEN6_TRIGINEN
// Description : Enables a cross trigger event to the corresponding channel when
//               a ctitrigin input is activated. There is one bit of the field
//               for each of the four channels
#define M33_CTIINEN6_TRIGINEN_RESET  _u(0x0)
#define M33_CTIINEN6_TRIGINEN_BITS   _u(0x0000000f)
#define M33_CTIINEN6_TRIGINEN_MSB    _u(3)
#define M33_CTIINEN6_TRIGINEN_LSB    _u(0)
#define M33_CTIINEN6_TRIGINEN_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIINEN7
// Description : CTI Trigger to Channel Enable Registers
#define M33_CTIINEN7_OFFSET _u(0x0004203c)
#define M33_CTIINEN7_BITS   _u(0x0000000f)
#define M33_CTIINEN7_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIINEN7_TRIGINEN
// Description : Enables a cross trigger event to the corresponding channel when
//               a ctitrigin input is activated. There is one bit of the field
//               for each of the four channels
#define M33_CTIINEN7_TRIGINEN_RESET  _u(0x0)
#define M33_CTIINEN7_TRIGINEN_BITS   _u(0x0000000f)
#define M33_CTIINEN7_TRIGINEN_MSB    _u(3)
#define M33_CTIINEN7_TRIGINEN_LSB    _u(0)
#define M33_CTIINEN7_TRIGINEN_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIOUTEN0
// Description : CTI Trigger to Channel Enable Registers
#define M33_CTIOUTEN0_OFFSET _u(0x000420a0)
#define M33_CTIOUTEN0_BITS   _u(0x0000000f)
#define M33_CTIOUTEN0_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIOUTEN0_TRIGOUTEN
// Description : Enables a cross trigger event to ctitrigout when the
//               corresponding channel is activated. There is one bit of the
//               field for each of the four channels.
#define M33_CTIOUTEN0_TRIGOUTEN_RESET  _u(0x0)
#define M33_CTIOUTEN0_TRIGOUTEN_BITS   _u(0x0000000f)
#define M33_CTIOUTEN0_TRIGOUTEN_MSB    _u(3)
#define M33_CTIOUTEN0_TRIGOUTEN_LSB    _u(0)
#define M33_CTIOUTEN0_TRIGOUTEN_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIOUTEN1
// Description : CTI Trigger to Channel Enable Registers
#define M33_CTIOUTEN1_OFFSET _u(0x000420a4)
#define M33_CTIOUTEN1_BITS   _u(0x0000000f)
#define M33_CTIOUTEN1_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIOUTEN1_TRIGOUTEN
// Description : Enables a cross trigger event to ctitrigout when the
//               corresponding channel is activated. There is one bit of the
//               field for each of the four channels.
#define M33_CTIOUTEN1_TRIGOUTEN_RESET  _u(0x0)
#define M33_CTIOUTEN1_TRIGOUTEN_BITS   _u(0x0000000f)
#define M33_CTIOUTEN1_TRIGOUTEN_MSB    _u(3)
#define M33_CTIOUTEN1_TRIGOUTEN_LSB    _u(0)
#define M33_CTIOUTEN1_TRIGOUTEN_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIOUTEN2
// Description : CTI Trigger to Channel Enable Registers
#define M33_CTIOUTEN2_OFFSET _u(0x000420a8)
#define M33_CTIOUTEN2_BITS   _u(0x0000000f)
#define M33_CTIOUTEN2_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIOUTEN2_TRIGOUTEN
// Description : Enables a cross trigger event to ctitrigout when the
//               corresponding channel is activated. There is one bit of the
//               field for each of the four channels.
#define M33_CTIOUTEN2_TRIGOUTEN_RESET  _u(0x0)
#define M33_CTIOUTEN2_TRIGOUTEN_BITS   _u(0x0000000f)
#define M33_CTIOUTEN2_TRIGOUTEN_MSB    _u(3)
#define M33_CTIOUTEN2_TRIGOUTEN_LSB    _u(0)
#define M33_CTIOUTEN2_TRIGOUTEN_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIOUTEN3
// Description : CTI Trigger to Channel Enable Registers
#define M33_CTIOUTEN3_OFFSET _u(0x000420ac)
#define M33_CTIOUTEN3_BITS   _u(0x0000000f)
#define M33_CTIOUTEN3_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIOUTEN3_TRIGOUTEN
// Description : Enables a cross trigger event to ctitrigout when the
//               corresponding channel is activated. There is one bit of the
//               field for each of the four channels.
#define M33_CTIOUTEN3_TRIGOUTEN_RESET  _u(0x0)
#define M33_CTIOUTEN3_TRIGOUTEN_BITS   _u(0x0000000f)
#define M33_CTIOUTEN3_TRIGOUTEN_MSB    _u(3)
#define M33_CTIOUTEN3_TRIGOUTEN_LSB    _u(0)
#define M33_CTIOUTEN3_TRIGOUTEN_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIOUTEN4
// Description : CTI Trigger to Channel Enable Registers
#define M33_CTIOUTEN4_OFFSET _u(0x000420b0)
#define M33_CTIOUTEN4_BITS   _u(0x0000000f)
#define M33_CTIOUTEN4_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIOUTEN4_TRIGOUTEN
// Description : Enables a cross trigger event to ctitrigout when the
//               corresponding channel is activated. There is one bit of the
//               field for each of the four channels.
#define M33_CTIOUTEN4_TRIGOUTEN_RESET  _u(0x0)
#define M33_CTIOUTEN4_TRIGOUTEN_BITS   _u(0x0000000f)
#define M33_CTIOUTEN4_TRIGOUTEN_MSB    _u(3)
#define M33_CTIOUTEN4_TRIGOUTEN_LSB    _u(0)
#define M33_CTIOUTEN4_TRIGOUTEN_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIOUTEN5
// Description : CTI Trigger to Channel Enable Registers
#define M33_CTIOUTEN5_OFFSET _u(0x000420b4)
#define M33_CTIOUTEN5_BITS   _u(0x0000000f)
#define M33_CTIOUTEN5_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIOUTEN5_TRIGOUTEN
// Description : Enables a cross trigger event to ctitrigout when the
//               corresponding channel is activated. There is one bit of the
//               field for each of the four channels.
#define M33_CTIOUTEN5_TRIGOUTEN_RESET  _u(0x0)
#define M33_CTIOUTEN5_TRIGOUTEN_BITS   _u(0x0000000f)
#define M33_CTIOUTEN5_TRIGOUTEN_MSB    _u(3)
#define M33_CTIOUTEN5_TRIGOUTEN_LSB    _u(0)
#define M33_CTIOUTEN5_TRIGOUTEN_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIOUTEN6
// Description : CTI Trigger to Channel Enable Registers
#define M33_CTIOUTEN6_OFFSET _u(0x000420b8)
#define M33_CTIOUTEN6_BITS   _u(0x0000000f)
#define M33_CTIOUTEN6_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIOUTEN6_TRIGOUTEN
// Description : Enables a cross trigger event to ctitrigout when the
//               corresponding channel is activated. There is one bit of the
//               field for each of the four channels.
#define M33_CTIOUTEN6_TRIGOUTEN_RESET  _u(0x0)
#define M33_CTIOUTEN6_TRIGOUTEN_BITS   _u(0x0000000f)
#define M33_CTIOUTEN6_TRIGOUTEN_MSB    _u(3)
#define M33_CTIOUTEN6_TRIGOUTEN_LSB    _u(0)
#define M33_CTIOUTEN6_TRIGOUTEN_ACCESS "RW"
// =============================================================================
// Register    : M33_CTIOUTEN7
// Description : CTI Trigger to Channel Enable Registers
#define M33_CTIOUTEN7_OFFSET _u(0x000420bc)
#define M33_CTIOUTEN7_BITS   _u(0x0000000f)
#define M33_CTIOUTEN7_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTIOUTEN7_TRIGOUTEN
// Description : Enables a cross trigger event to ctitrigout when the
//               corresponding channel is activated. There is one bit of the
//               field for each of the four channels.
#define M33_CTIOUTEN7_TRIGOUTEN_RESET  _u(0x0)
#define M33_CTIOUTEN7_TRIGOUTEN_BITS   _u(0x0000000f)
#define M33_CTIOUTEN7_TRIGOUTEN_MSB    _u(3)
#define M33_CTIOUTEN7_TRIGOUTEN_LSB    _u(0)
#define M33_CTIOUTEN7_TRIGOUTEN_ACCESS "RW"
// =============================================================================
// Register    : M33_CTITRIGINSTATUS
// Description : CTI Trigger to Channel Enable Registers
#define M33_CTITRIGINSTATUS_OFFSET _u(0x00042130)
#define M33_CTITRIGINSTATUS_BITS   _u(0x000000ff)
#define M33_CTITRIGINSTATUS_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTITRIGINSTATUS_TRIGINSTATUS
// Description : Shows the status of the ctitrigin inputs. There is one bit of
//               the field for each trigger input.Because the register provides
//               a view of the raw ctitrigin inputs, the reset value is UNKNOWN.
#define M33_CTITRIGINSTATUS_TRIGINSTATUS_RESET  _u(0x00)
#define M33_CTITRIGINSTATUS_TRIGINSTATUS_BITS   _u(0x000000ff)
#define M33_CTITRIGINSTATUS_TRIGINSTATUS_MSB    _u(7)
#define M33_CTITRIGINSTATUS_TRIGINSTATUS_LSB    _u(0)
#define M33_CTITRIGINSTATUS_TRIGINSTATUS_ACCESS "RO"
// =============================================================================
// Register    : M33_CTITRIGOUTSTATUS
// Description : CTI Trigger In Status Register
#define M33_CTITRIGOUTSTATUS_OFFSET _u(0x00042134)
#define M33_CTITRIGOUTSTATUS_BITS   _u(0x000000ff)
#define M33_CTITRIGOUTSTATUS_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTITRIGOUTSTATUS_TRIGOUTSTATUS
// Description : Shows the status of the ctitrigout outputs. There is one bit of
//               the field for each trigger output.
#define M33_CTITRIGOUTSTATUS_TRIGOUTSTATUS_RESET  _u(0x00)
#define M33_CTITRIGOUTSTATUS_TRIGOUTSTATUS_BITS   _u(0x000000ff)
#define M33_CTITRIGOUTSTATUS_TRIGOUTSTATUS_MSB    _u(7)
#define M33_CTITRIGOUTSTATUS_TRIGOUTSTATUS_LSB    _u(0)
#define M33_CTITRIGOUTSTATUS_TRIGOUTSTATUS_ACCESS "RO"
// =============================================================================
// Register    : M33_CTICHINSTATUS
// Description : CTI Channel In Status Register
#define M33_CTICHINSTATUS_OFFSET _u(0x00042138)
#define M33_CTICHINSTATUS_BITS   _u(0x0000000f)
#define M33_CTICHINSTATUS_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_CTICHINSTATUS_CTICHOUTSTATUS
// Description : Shows the status of the ctichout outputs. There is one bit of
//               the field for each channel output
#define M33_CTICHINSTATUS_CTICHOUTSTATUS_RESET  _u(0x0)
#define M33_CTICHINSTATUS_CTICHOUTSTATUS_BITS   _u(0x0000000f)
#define M33_CTICHINSTATUS_CTICHOUTSTATUS_MSB    _u(3)
#define M33_CTICHINSTATUS_CTICHOUTSTATUS_LSB    _u(0)
#define M33_CTICHINSTATUS_CTICHOUTSTATUS_ACCESS "RO"
// =============================================================================
// Register    : M33_CTIGATE
// Description : Enable CTI Channel Gate register
#define M33_CTIGATE_OFFSET _u(0x00042140)
#define M33_CTIGATE_BITS   _u(0x0000000f)
#define M33_CTIGATE_RESET  _u(0x0000000f)
// -----------------------------------------------------------------------------
// Field       : M33_CTIGATE_CTIGATEEN3
// Description : Enable ctichout3. Set to 0 to disable channel propagation.
#define M33_CTIGATE_CTIGATEEN3_RESET  _u(0x1)
#define M33_CTIGATE_CTIGATEEN3_BITS   _u(0x00000008)
#define M33_CTIGATE_CTIGATEEN3_MSB    _u(3)
#define M33_CTIGATE_CTIGATEEN3_LSB    _u(3)
#define M33_CTIGATE_CTIGATEEN3_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CTIGATE_CTIGATEEN2
// Description : Enable ctichout2. Set to 0 to disable channel propagation.
#define M33_CTIGATE_CTIGATEEN2_RESET  _u(0x1)
#define M33_CTIGATE_CTIGATEEN2_BITS   _u(0x00000004)
#define M33_CTIGATE_CTIGATEEN2_MSB    _u(2)
#define M33_CTIGATE_CTIGATEEN2_LSB    _u(2)
#define M33_CTIGATE_CTIGATEEN2_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CTIGATE_CTIGATEEN1
// Description : Enable ctichout1. Set to 0 to disable channel propagation.
#define M33_CTIGATE_CTIGATEEN1_RESET  _u(0x1)
#define M33_CTIGATE_CTIGATEEN1_BITS   _u(0x00000002)
#define M33_CTIGATE_CTIGATEEN1_MSB    _u(1)
#define M33_CTIGATE_CTIGATEEN1_LSB    _u(1)
#define M33_CTIGATE_CTIGATEEN1_ACCESS "RW"
// -----------------------------------------------------------------------------
// Field       : M33_CTIGATE_CTIGATEEN0
// Description : Enable ctichout0. Set to 0 to disable channel propagation.
#define M33_CTIGATE_CTIGATEEN0_RESET  _u(0x1)
#define M33_CTIGATE_CTIGATEEN0_BITS   _u(0x00000001)
#define M33_CTIGATE_CTIGATEEN0_MSB    _u(0)
#define M33_CTIGATE_CTIGATEEN0_LSB    _u(0)
#define M33_CTIGATE_CTIGATEEN0_ACCESS "RW"
// =============================================================================
// Register    : M33_ASICCTL
// Description : External Multiplexer Control register
#define M33_ASICCTL_OFFSET _u(0x00042144)
#define M33_ASICCTL_BITS   _u(0x00000000)
#define M33_ASICCTL_RESET  _u(0x00000000)
#define M33_ASICCTL_MSB    _u(31)
#define M33_ASICCTL_LSB    _u(0)
#define M33_ASICCTL_ACCESS "RW"
// =============================================================================
// Register    : M33_ITCHOUT
// Description : Integration Test Channel Output register
#define M33_ITCHOUT_OFFSET _u(0x00042ee4)
#define M33_ITCHOUT_BITS   _u(0x0000000f)
#define M33_ITCHOUT_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITCHOUT_CTCHOUT
// Description : Sets the value of the ctichout outputs
#define M33_ITCHOUT_CTCHOUT_RESET  _u(0x0)
#define M33_ITCHOUT_CTCHOUT_BITS   _u(0x0000000f)
#define M33_ITCHOUT_CTCHOUT_MSB    _u(3)
#define M33_ITCHOUT_CTCHOUT_LSB    _u(0)
#define M33_ITCHOUT_CTCHOUT_ACCESS "RW"
// =============================================================================
// Register    : M33_ITTRIGOUT
// Description : Integration Test Trigger Output register
#define M33_ITTRIGOUT_OFFSET _u(0x00042ee8)
#define M33_ITTRIGOUT_BITS   _u(0x000000ff)
#define M33_ITTRIGOUT_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITTRIGOUT_CTTRIGOUT
// Description : Sets the value of the ctitrigout outputs
#define M33_ITTRIGOUT_CTTRIGOUT_RESET  _u(0x00)
#define M33_ITTRIGOUT_CTTRIGOUT_BITS   _u(0x000000ff)
#define M33_ITTRIGOUT_CTTRIGOUT_MSB    _u(7)
#define M33_ITTRIGOUT_CTTRIGOUT_LSB    _u(0)
#define M33_ITTRIGOUT_CTTRIGOUT_ACCESS "RW"
// =============================================================================
// Register    : M33_ITCHIN
// Description : Integration Test Channel Input register
#define M33_ITCHIN_OFFSET _u(0x00042ef4)
#define M33_ITCHIN_BITS   _u(0x0000000f)
#define M33_ITCHIN_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITCHIN_CTCHIN
// Description : Reads the value of the ctichin inputs.
#define M33_ITCHIN_CTCHIN_RESET  _u(0x0)
#define M33_ITCHIN_CTCHIN_BITS   _u(0x0000000f)
#define M33_ITCHIN_CTCHIN_MSB    _u(3)
#define M33_ITCHIN_CTCHIN_LSB    _u(0)
#define M33_ITCHIN_CTCHIN_ACCESS "RO"
// =============================================================================
// Register    : M33_ITCTRL
// Description : Integration Mode Control register
#define M33_ITCTRL_OFFSET _u(0x00042f00)
#define M33_ITCTRL_BITS   _u(0x00000001)
#define M33_ITCTRL_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_ITCTRL_IME
// Description : Integration Mode Enable
#define M33_ITCTRL_IME_RESET  _u(0x0)
#define M33_ITCTRL_IME_BITS   _u(0x00000001)
#define M33_ITCTRL_IME_MSB    _u(0)
#define M33_ITCTRL_IME_LSB    _u(0)
#define M33_ITCTRL_IME_ACCESS "RW"
// =============================================================================
// Register    : M33_DEVARCH
// Description : Device Architecture register
#define M33_DEVARCH_OFFSET _u(0x00042fbc)
#define M33_DEVARCH_BITS   _u(0xffffffff)
#define M33_DEVARCH_RESET  _u(0x47701a14)
// -----------------------------------------------------------------------------
// Field       : M33_DEVARCH_ARCHITECT
// Description : Indicates the component architect
#define M33_DEVARCH_ARCHITECT_RESET  _u(0x23b)
#define M33_DEVARCH_ARCHITECT_BITS   _u(0xffe00000)
#define M33_DEVARCH_ARCHITECT_MSB    _u(31)
#define M33_DEVARCH_ARCHITECT_LSB    _u(21)
#define M33_DEVARCH_ARCHITECT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DEVARCH_PRESENT
// Description : Indicates whether the DEVARCH register is present
#define M33_DEVARCH_PRESENT_RESET  _u(0x1)
#define M33_DEVARCH_PRESENT_BITS   _u(0x00100000)
#define M33_DEVARCH_PRESENT_MSB    _u(20)
#define M33_DEVARCH_PRESENT_LSB    _u(20)
#define M33_DEVARCH_PRESENT_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DEVARCH_REVISION
// Description : Indicates the architecture revision
#define M33_DEVARCH_REVISION_RESET  _u(0x0)
#define M33_DEVARCH_REVISION_BITS   _u(0x000f0000)
#define M33_DEVARCH_REVISION_MSB    _u(19)
#define M33_DEVARCH_REVISION_LSB    _u(16)
#define M33_DEVARCH_REVISION_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DEVARCH_ARCHID
// Description : Indicates the component
#define M33_DEVARCH_ARCHID_RESET  _u(0x1a14)
#define M33_DEVARCH_ARCHID_BITS   _u(0x0000ffff)
#define M33_DEVARCH_ARCHID_MSB    _u(15)
#define M33_DEVARCH_ARCHID_LSB    _u(0)
#define M33_DEVARCH_ARCHID_ACCESS "RO"
// =============================================================================
// Register    : M33_DEVID
// Description : Device Configuration register
#define M33_DEVID_OFFSET _u(0x00042fc8)
#define M33_DEVID_BITS   _u(0x000fff1f)
#define M33_DEVID_RESET  _u(0x00040800)
// -----------------------------------------------------------------------------
// Field       : M33_DEVID_NUMCH
// Description : Number of ECT channels available
#define M33_DEVID_NUMCH_RESET  _u(0x4)
#define M33_DEVID_NUMCH_BITS   _u(0x000f0000)
#define M33_DEVID_NUMCH_MSB    _u(19)
#define M33_DEVID_NUMCH_LSB    _u(16)
#define M33_DEVID_NUMCH_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DEVID_NUMTRIG
// Description : Number of ECT triggers available.
#define M33_DEVID_NUMTRIG_RESET  _u(0x08)
#define M33_DEVID_NUMTRIG_BITS   _u(0x0000ff00)
#define M33_DEVID_NUMTRIG_MSB    _u(15)
#define M33_DEVID_NUMTRIG_LSB    _u(8)
#define M33_DEVID_NUMTRIG_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DEVID_EXTMUXNUM
// Description : Indicates the number of multiplexers available on Trigger
//               Inputs and Trigger Outputs that are using asicctl. The default
//               value of 0b00000 indicates that no multiplexing is present.
//               This value of this bit depends on the Verilog define EXTMUXNUM
//               that you must change accordingly.
#define M33_DEVID_EXTMUXNUM_RESET  _u(0x00)
#define M33_DEVID_EXTMUXNUM_BITS   _u(0x0000001f)
#define M33_DEVID_EXTMUXNUM_MSB    _u(4)
#define M33_DEVID_EXTMUXNUM_LSB    _u(0)
#define M33_DEVID_EXTMUXNUM_ACCESS "RO"
// =============================================================================
// Register    : M33_DEVTYPE
// Description : Device Type Identifier register
#define M33_DEVTYPE_OFFSET _u(0x00042fcc)
#define M33_DEVTYPE_BITS   _u(0x000000ff)
#define M33_DEVTYPE_RESET  _u(0x00000014)
// -----------------------------------------------------------------------------
// Field       : M33_DEVTYPE_SUB
// Description : Sub-classification of the type of the debug component as
//               specified in the ARM Architecture Specification within the
//               major classification as specified in the MAJOR field.
#define M33_DEVTYPE_SUB_RESET  _u(0x1)
#define M33_DEVTYPE_SUB_BITS   _u(0x000000f0)
#define M33_DEVTYPE_SUB_MSB    _u(7)
#define M33_DEVTYPE_SUB_LSB    _u(4)
#define M33_DEVTYPE_SUB_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_DEVTYPE_MAJOR
// Description : Major classification of the type of the debug component as
//               specified in the ARM Architecture Specification for this debug
//               and trace component.
#define M33_DEVTYPE_MAJOR_RESET  _u(0x4)
#define M33_DEVTYPE_MAJOR_BITS   _u(0x0000000f)
#define M33_DEVTYPE_MAJOR_MSB    _u(3)
#define M33_DEVTYPE_MAJOR_LSB    _u(0)
#define M33_DEVTYPE_MAJOR_ACCESS "RO"
// =============================================================================
// Register    : M33_PIDR4
// Description : CoreSight Peripheral ID4
#define M33_PIDR4_OFFSET _u(0x00042fd0)
#define M33_PIDR4_BITS   _u(0x000000ff)
#define M33_PIDR4_RESET  _u(0x00000004)
// -----------------------------------------------------------------------------
// Field       : M33_PIDR4_SIZE
// Description : Always 0b0000. Indicates that the device only occupies 4KB of
//               memory
#define M33_PIDR4_SIZE_RESET  _u(0x0)
#define M33_PIDR4_SIZE_BITS   _u(0x000000f0)
#define M33_PIDR4_SIZE_MSB    _u(7)
#define M33_PIDR4_SIZE_LSB    _u(4)
#define M33_PIDR4_SIZE_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_PIDR4_DES_2
// Description : Together, PIDR1.DES_0, PIDR2.DES_1, and PIDR4.DES_2 identify
//               the designer of the component.
#define M33_PIDR4_DES_2_RESET  _u(0x4)
#define M33_PIDR4_DES_2_BITS   _u(0x0000000f)
#define M33_PIDR4_DES_2_MSB    _u(3)
#define M33_PIDR4_DES_2_LSB    _u(0)
#define M33_PIDR4_DES_2_ACCESS "RO"
// =============================================================================
// Register    : M33_PIDR5
// Description : CoreSight Peripheral ID5
#define M33_PIDR5_OFFSET _u(0x00042fd4)
#define M33_PIDR5_BITS   _u(0x00000000)
#define M33_PIDR5_RESET  _u(0x00000000)
#define M33_PIDR5_MSB    _u(31)
#define M33_PIDR5_LSB    _u(0)
#define M33_PIDR5_ACCESS "RW"
// =============================================================================
// Register    : M33_PIDR6
// Description : CoreSight Peripheral ID6
#define M33_PIDR6_OFFSET _u(0x00042fd8)
#define M33_PIDR6_BITS   _u(0x00000000)
#define M33_PIDR6_RESET  _u(0x00000000)
#define M33_PIDR6_MSB    _u(31)
#define M33_PIDR6_LSB    _u(0)
#define M33_PIDR6_ACCESS "RW"
// =============================================================================
// Register    : M33_PIDR7
// Description : CoreSight Peripheral ID7
#define M33_PIDR7_OFFSET _u(0x00042fdc)
#define M33_PIDR7_BITS   _u(0x00000000)
#define M33_PIDR7_RESET  _u(0x00000000)
#define M33_PIDR7_MSB    _u(31)
#define M33_PIDR7_LSB    _u(0)
#define M33_PIDR7_ACCESS "RW"
// =============================================================================
// Register    : M33_PIDR0
// Description : CoreSight Peripheral ID0
#define M33_PIDR0_OFFSET _u(0x00042fe0)
#define M33_PIDR0_BITS   _u(0x000000ff)
#define M33_PIDR0_RESET  _u(0x00000021)
// -----------------------------------------------------------------------------
// Field       : M33_PIDR0_PART_0
// Description : Bits[7:0] of the 12-bit part number of the component. The
//               designer of the component assigns this part number.
#define M33_PIDR0_PART_0_RESET  _u(0x21)
#define M33_PIDR0_PART_0_BITS   _u(0x000000ff)
#define M33_PIDR0_PART_0_MSB    _u(7)
#define M33_PIDR0_PART_0_LSB    _u(0)
#define M33_PIDR0_PART_0_ACCESS "RO"
// =============================================================================
// Register    : M33_PIDR1
// Description : CoreSight Peripheral ID1
#define M33_PIDR1_OFFSET _u(0x00042fe4)
#define M33_PIDR1_BITS   _u(0x000000ff)
#define M33_PIDR1_RESET  _u(0x000000bd)
// -----------------------------------------------------------------------------
// Field       : M33_PIDR1_DES_0
// Description : Together, PIDR1.DES_0, PIDR2.DES_1, and PIDR4.DES_2 identify
//               the designer of the component.
#define M33_PIDR1_DES_0_RESET  _u(0xb)
#define M33_PIDR1_DES_0_BITS   _u(0x000000f0)
#define M33_PIDR1_DES_0_MSB    _u(7)
#define M33_PIDR1_DES_0_LSB    _u(4)
#define M33_PIDR1_DES_0_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_PIDR1_PART_1
// Description : Bits[11:8] of the 12-bit part number of the component. The
//               designer of the component assigns this part number.
#define M33_PIDR1_PART_1_RESET  _u(0xd)
#define M33_PIDR1_PART_1_BITS   _u(0x0000000f)
#define M33_PIDR1_PART_1_MSB    _u(3)
#define M33_PIDR1_PART_1_LSB    _u(0)
#define M33_PIDR1_PART_1_ACCESS "RO"
// =============================================================================
// Register    : M33_PIDR2
// Description : CoreSight Peripheral ID2
#define M33_PIDR2_OFFSET _u(0x00042fe8)
#define M33_PIDR2_BITS   _u(0x000000ff)
#define M33_PIDR2_RESET  _u(0x0000000b)
// -----------------------------------------------------------------------------
// Field       : M33_PIDR2_REVISION
// Description : This device is at r1p0
#define M33_PIDR2_REVISION_RESET  _u(0x0)
#define M33_PIDR2_REVISION_BITS   _u(0x000000f0)
#define M33_PIDR2_REVISION_MSB    _u(7)
#define M33_PIDR2_REVISION_LSB    _u(4)
#define M33_PIDR2_REVISION_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_PIDR2_JEDEC
// Description : Always 1. Indicates that the JEDEC-assigned designer ID is
//               used.
#define M33_PIDR2_JEDEC_RESET  _u(0x1)
#define M33_PIDR2_JEDEC_BITS   _u(0x00000008)
#define M33_PIDR2_JEDEC_MSB    _u(3)
#define M33_PIDR2_JEDEC_LSB    _u(3)
#define M33_PIDR2_JEDEC_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_PIDR2_DES_1
// Description : Together, PIDR1.DES_0, PIDR2.DES_1, and PIDR4.DES_2 identify
//               the designer of the component.
#define M33_PIDR2_DES_1_RESET  _u(0x3)
#define M33_PIDR2_DES_1_BITS   _u(0x00000007)
#define M33_PIDR2_DES_1_MSB    _u(2)
#define M33_PIDR2_DES_1_LSB    _u(0)
#define M33_PIDR2_DES_1_ACCESS "RO"
// =============================================================================
// Register    : M33_PIDR3
// Description : CoreSight Peripheral ID3
#define M33_PIDR3_OFFSET _u(0x00042fec)
#define M33_PIDR3_BITS   _u(0x000000ff)
#define M33_PIDR3_RESET  _u(0x00000000)
// -----------------------------------------------------------------------------
// Field       : M33_PIDR3_REVAND
// Description : Indicates minor errata fixes specific to the revision of the
//               component being used, for example metal fixes after
//               implementation. In most cases, this field is 0b0000. ARM
//               recommends that the component designers ensure that a metal fix
//               can change this field if required, for example, by driving it
//               from registers that reset to 0b0000.
#define M33_PIDR3_REVAND_RESET  _u(0x0)
#define M33_PIDR3_REVAND_BITS   _u(0x000000f0)
#define M33_PIDR3_REVAND_MSB    _u(7)
#define M33_PIDR3_REVAND_LSB    _u(4)
#define M33_PIDR3_REVAND_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_PIDR3_CMOD
// Description : Customer Modified. Indicates whether the customer has modified
//               the behavior of the component. In most cases, this field is
//               0b0000. Customers change this value when they make authorized
//               modifications to this component.
#define M33_PIDR3_CMOD_RESET  _u(0x0)
#define M33_PIDR3_CMOD_BITS   _u(0x0000000f)
#define M33_PIDR3_CMOD_MSB    _u(3)
#define M33_PIDR3_CMOD_LSB    _u(0)
#define M33_PIDR3_CMOD_ACCESS "RO"
// =============================================================================
// Register    : M33_CIDR0
// Description : CoreSight Component ID0
#define M33_CIDR0_OFFSET _u(0x00042ff0)
#define M33_CIDR0_BITS   _u(0x000000ff)
#define M33_CIDR0_RESET  _u(0x0000000d)
// -----------------------------------------------------------------------------
// Field       : M33_CIDR0_PRMBL_0
// Description : Preamble[0]. Contains bits[7:0] of the component identification
//               code
#define M33_CIDR0_PRMBL_0_RESET  _u(0x0d)
#define M33_CIDR0_PRMBL_0_BITS   _u(0x000000ff)
#define M33_CIDR0_PRMBL_0_MSB    _u(7)
#define M33_CIDR0_PRMBL_0_LSB    _u(0)
#define M33_CIDR0_PRMBL_0_ACCESS "RO"
// =============================================================================
// Register    : M33_CIDR1
// Description : CoreSight Component ID1
#define M33_CIDR1_OFFSET _u(0x00042ff4)
#define M33_CIDR1_BITS   _u(0x000000ff)
#define M33_CIDR1_RESET  _u(0x00000090)
// -----------------------------------------------------------------------------
// Field       : M33_CIDR1_CLASS
// Description : Class of the component, for example, whether the component is a
//               ROM table or a generic CoreSight component. Contains
//               bits[15:12] of the component identification code.
#define M33_CIDR1_CLASS_RESET  _u(0x9)
#define M33_CIDR1_CLASS_BITS   _u(0x000000f0)
#define M33_CIDR1_CLASS_MSB    _u(7)
#define M33_CIDR1_CLASS_LSB    _u(4)
#define M33_CIDR1_CLASS_ACCESS "RO"
// -----------------------------------------------------------------------------
// Field       : M33_CIDR1_PRMBL_1
// Description : Preamble[1]. Contains bits[11:8] of the component
//               identification code.
#define M33_CIDR1_PRMBL_1_RESET  _u(0x0)
#define M33_CIDR1_PRMBL_1_BITS   _u(0x0000000f)
#define M33_CIDR1_PRMBL_1_MSB    _u(3)
#define M33_CIDR1_PRMBL_1_LSB    _u(0)
#define M33_CIDR1_PRMBL_1_ACCESS "RO"
// =============================================================================
// Register    : M33_CIDR2
// Description : CoreSight Component ID2
#define M33_CIDR2_OFFSET _u(0x00042ff8)
#define M33_CIDR2_BITS   _u(0x000000ff)
#define M33_CIDR2_RESET  _u(0x00000005)
// -----------------------------------------------------------------------------
// Field       : M33_CIDR2_PRMBL_2
// Description : Preamble[2]. Contains bits[23:16] of the component
//               identification code.
#define M33_CIDR2_PRMBL_2_RESET  _u(0x05)
#define M33_CIDR2_PRMBL_2_BITS   _u(0x000000ff)
#define M33_CIDR2_PRMBL_2_MSB    _u(7)
#define M33_CIDR2_PRMBL_2_LSB    _u(0)
#define M33_CIDR2_PRMBL_2_ACCESS "RO"
// =============================================================================
// Register    : M33_CIDR3
// Description : CoreSight Component ID3
#define M33_CIDR3_OFFSET _u(0x00042ffc)
#define M33_CIDR3_BITS   _u(0x000000ff)
#define M33_CIDR3_RESET  _u(0x000000b1)
// -----------------------------------------------------------------------------
// Field       : M33_CIDR3_PRMBL_3
// Description : Preamble[3]. Contains bits[31:24] of the component
//               identification code.
#define M33_CIDR3_PRMBL_3_RESET  _u(0xb1)
#define M33_CIDR3_PRMBL_3_BITS   _u(0x000000ff)
#define M33_CIDR3_PRMBL_3_MSB    _u(7)
#define M33_CIDR3_PRMBL_3_LSB    _u(0)
#define M33_CIDR3_PRMBL_3_ACCESS "RO"
// =============================================================================
#endif // _HARDWARE_REGS_M33_H

