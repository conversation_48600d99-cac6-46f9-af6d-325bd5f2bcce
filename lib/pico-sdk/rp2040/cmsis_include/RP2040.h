/*
 * Copyright (c) 2024 Raspberry Pi Ltd. SPDX-License-Identifier: BSD-3-Clause
 *
 * @file     src/rp2_common/cmsis/stub/CMSIS/Device/RP2040/Include/RP2040.h
 * @brief    CMSIS HeaderFile
 * @version  0.1
 * @date     Tue Aug  6 18:22:05 2024
 * @note     Generated by SVDConv V3.3.47
 *           from File 'src/rp2_common/cmsis/../../rp2040/hardware_regs/RP2040.svd',
 *           last modified on Tue Aug  6 17:58:50 2024
 */


/** @addtogroup Raspberry Pi
  * @{
  */


/** @addtogroup RP2040
  * @{
  */


#ifndef RP2040_H
#define RP2040_H

#ifdef __cplusplus
extern "C" {
#endif


/** @addtogroup Configuration_of_CMSIS
  * @{
  */



/* =========================================================================================================================== */
/* ================                                Interrupt Number Definition                                ================ */
/* =========================================================================================================================== */

typedef enum {
/* =======================================  ARM Cortex-M0+ Specific Interrupt Numbers  ======================================= */
  Reset_IRQn                = -15,              /*!< -15  Reset Vector, invoked on Power up and warm reset                     */
  NonMaskableInt_IRQn       = -14,              /*!< -14  Non maskable Interrupt, cannot be stopped or preempted               */
  HardFault_IRQn            = -13,              /*!< -13  Hard Fault, all classes of Fault                                     */
  SVCall_IRQn               =  -5,              /*!< -5 System Service Call via SVC instruction                                */
  PendSV_IRQn               =  -2,              /*!< -2 Pendable request for system service                                    */
  SysTick_IRQn              =  -1,              /*!< -1 System Tick Timer                                                      */
/* ===========================================  RP2040 Specific Interrupt Numbers  =========================================== */
  TIMER_IRQ_0_IRQn          =   0,              /*!< 0  TIMER_IRQ_0                                                            */
  TIMER_IRQ_1_IRQn          =   1,              /*!< 1  TIMER_IRQ_1                                                            */
  TIMER_IRQ_2_IRQn          =   2,              /*!< 2  TIMER_IRQ_2                                                            */
  TIMER_IRQ_3_IRQn          =   3,              /*!< 3  TIMER_IRQ_3                                                            */
  PWM_IRQ_WRAP_IRQn         =   4,              /*!< 4  PWM_IRQ_WRAP                                                           */
  USBCTRL_IRQ_IRQn          =   5,              /*!< 5  USBCTRL_IRQ                                                            */
  XIP_IRQ_IRQn              =   6,              /*!< 6  XIP_IRQ                                                                */
  PIO0_IRQ_0_IRQn           =   7,              /*!< 7  PIO0_IRQ_0                                                             */
  PIO0_IRQ_1_IRQn           =   8,              /*!< 8  PIO0_IRQ_1                                                             */
  PIO1_IRQ_0_IRQn           =   9,              /*!< 9  PIO1_IRQ_0                                                             */
  PIO1_IRQ_1_IRQn           =  10,              /*!< 10 PIO1_IRQ_1                                                             */
  DMA_IRQ_0_IRQn            =  11,              /*!< 11 DMA_IRQ_0                                                              */
  DMA_IRQ_1_IRQn            =  12,              /*!< 12 DMA_IRQ_1                                                              */
  IO_IRQ_BANK0_IRQn         =  13,              /*!< 13 IO_IRQ_BANK0                                                           */
  IO_IRQ_QSPI_IRQn          =  14,              /*!< 14 IO_IRQ_QSPI                                                            */
  SIO_IRQ_PROC0_IRQn        =  15,              /*!< 15 SIO_IRQ_PROC0                                                          */
  SIO_IRQ_PROC1_IRQn        =  16,              /*!< 16 SIO_IRQ_PROC1                                                          */
  CLOCKS_IRQ_IRQn           =  17,              /*!< 17 CLOCKS_IRQ                                                             */
  SPI0_IRQ_IRQn             =  18,              /*!< 18 SPI0_IRQ                                                               */
  SPI1_IRQ_IRQn             =  19,              /*!< 19 SPI1_IRQ                                                               */
  UART0_IRQ_IRQn            =  20,              /*!< 20 UART0_IRQ                                                              */
  UART1_IRQ_IRQn            =  21,              /*!< 21 UART1_IRQ                                                              */
  ADC_IRQ_FIFO_IRQn         =  22,              /*!< 22 ADC_IRQ_FIFO                                                           */
  I2C0_IRQ_IRQn             =  23,              /*!< 23 I2C0_IRQ                                                               */
  I2C1_IRQ_IRQn             =  24,              /*!< 24 I2C1_IRQ                                                               */
  RTC_IRQ_IRQn              =  25               /*!< 25 RTC_IRQ                                                                */
} IRQn_Type;



/* =========================================================================================================================== */
/* ================                           Processor and Core Peripheral Section                           ================ */
/* =========================================================================================================================== */

/* ==========================  Configuration of the ARM Cortex-M0+ Processor and Core Peripherals  =========================== */
#define __CM0PLUS_REV                 0x0001U   /*!< CM0PLUS Core Revision                                                     */
#define __NVIC_PRIO_BITS               2        /*!< Number of Bits used for Priority Levels                                   */
#define __Vendor_SysTickConfig         0        /*!< Set to 1 if different SysTick Config is used                              */
#define __VTOR_PRESENT                 1        /*!< Set to 1 if CPU supports Vector Table Offset Register                     */
#define __MPU_PRESENT                  1        /*!< MPU present                                                               */
#define __FPU_PRESENT                  0        /*!< FPU present                                                               */


/** @} */ /* End of group Configuration_of_CMSIS */

#include "core_cm0plus.h"                       /*!< ARM Cortex-M0+ processor and core peripherals                             */
#include "system_RP2040.h"                      /*!< RP2040 System                                                             */

#ifndef __IM                                    /*!< Fallback for older CMSIS versions                                         */
  #define __IM   __I
#endif
#ifndef __OM                                    /*!< Fallback for older CMSIS versions                                         */
  #define __OM   __O
#endif
#ifndef __IOM                                   /*!< Fallback for older CMSIS versions                                         */
  #define __IOM  __IO
#endif


/* =========================================================================================================================== */
/* ================                            Device Specific Peripheral Section                             ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_peripherals
  * @{
  */



/* =========================================================================================================================== */
/* ================                                          RESETS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief RESETS (RESETS)
  */

typedef struct {                                /*!< RESETS Structure                                                          */
  __IOM uint32_t  RESET;                        /*!< Reset control. If a bit is set it means the peripheral is in
                                                     reset. 0 means the peripheral's reset is deasserted.                      */
  __IOM uint32_t  WDSEL;                        /*!< Watchdog select. If a bit is set then the watchdog will reset
                                                     this peripheral when the watchdog fires.                                  */
  __IOM uint32_t  RESET_DONE;                   /*!< Reset done. If a bit is set then a reset done signal has been
                                                     returned by the peripheral. This indicates that the peripheral's
                                                     registers are ready to be accessed.                                       */
} RESETS_Type;                                  /*!< Size = 12 (0xc)                                                           */



/* =========================================================================================================================== */
/* ================                                            PSM                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief PSM (PSM)
  */

typedef struct {                                /*!< PSM Structure                                                             */
  __IOM uint32_t  FRCE_ON;                      /*!< Force block out of reset (i.e. power it on)                               */
  __IOM uint32_t  FRCE_OFF;                     /*!< Force into reset (i.e. power it off)                                      */
  __IOM uint32_t  WDSEL;                        /*!< Set to 1 if this peripheral should be reset when the watchdog
                                                     fires.                                                                    */
  __IOM uint32_t  DONE;                         /*!< Indicates the peripheral's registers are ready to access.                 */
} PSM_Type;                                     /*!< Size = 16 (0x10)                                                          */



/* =========================================================================================================================== */
/* ================                                          CLOCKS                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief CLOCKS (CLOCKS)
  */

typedef struct {                                /*!< CLOCKS Structure                                                          */
  __IOM uint32_t  CLK_GPOUT0_CTRL;              /*!< Clock control, can be changed on-the-fly (except for auxsrc)              */
  __IOM uint32_t  CLK_GPOUT0_DIV;               /*!< Clock divisor, can be changed on-the-fly                                  */
  __IOM uint32_t  CLK_GPOUT0_SELECTED;          /*!< Indicates which SRC is currently selected by the glitchless
                                                     mux (one-hot).                                                            */
  __IOM uint32_t  CLK_GPOUT1_CTRL;              /*!< Clock control, can be changed on-the-fly (except for auxsrc)              */
  __IOM uint32_t  CLK_GPOUT1_DIV;               /*!< Clock divisor, can be changed on-the-fly                                  */
  __IOM uint32_t  CLK_GPOUT1_SELECTED;          /*!< Indicates which SRC is currently selected by the glitchless
                                                     mux (one-hot).                                                            */
  __IOM uint32_t  CLK_GPOUT2_CTRL;              /*!< Clock control, can be changed on-the-fly (except for auxsrc)              */
  __IOM uint32_t  CLK_GPOUT2_DIV;               /*!< Clock divisor, can be changed on-the-fly                                  */
  __IOM uint32_t  CLK_GPOUT2_SELECTED;          /*!< Indicates which SRC is currently selected by the glitchless
                                                     mux (one-hot).                                                            */
  __IOM uint32_t  CLK_GPOUT3_CTRL;              /*!< Clock control, can be changed on-the-fly (except for auxsrc)              */
  __IOM uint32_t  CLK_GPOUT3_DIV;               /*!< Clock divisor, can be changed on-the-fly                                  */
  __IOM uint32_t  CLK_GPOUT3_SELECTED;          /*!< Indicates which SRC is currently selected by the glitchless
                                                     mux (one-hot).                                                            */
  __IOM uint32_t  CLK_REF_CTRL;                 /*!< Clock control, can be changed on-the-fly (except for auxsrc)              */
  __IOM uint32_t  CLK_REF_DIV;                  /*!< Clock divisor, can be changed on-the-fly                                  */
  __IOM uint32_t  CLK_REF_SELECTED;             /*!< Indicates which SRC is currently selected by the glitchless
                                                     mux (one-hot).                                                            */
  __IOM uint32_t  CLK_SYS_CTRL;                 /*!< Clock control, can be changed on-the-fly (except for auxsrc)              */
  __IOM uint32_t  CLK_SYS_DIV;                  /*!< Clock divisor, can be changed on-the-fly                                  */
  __IOM uint32_t  CLK_SYS_SELECTED;             /*!< Indicates which SRC is currently selected by the glitchless
                                                     mux (one-hot).                                                            */
  __IOM uint32_t  CLK_PERI_CTRL;                /*!< Clock control, can be changed on-the-fly (except for auxsrc)              */
  __IOM uint32_t  CLK_PERI_DIV;                 /*!< Clock divisor, can be changed on-the-fly                                  */
  __IOM uint32_t  CLK_PERI_SELECTED;            /*!< Indicates which SRC is currently selected by the glitchless
                                                     mux (one-hot).                                                            */
  __IOM uint32_t  CLK_USB_CTRL;                 /*!< Clock control, can be changed on-the-fly (except for auxsrc)              */
  __IOM uint32_t  CLK_USB_DIV;                  /*!< Clock divisor, can be changed on-the-fly                                  */
  __IOM uint32_t  CLK_USB_SELECTED;             /*!< Indicates which SRC is currently selected by the glitchless
                                                     mux (one-hot).                                                            */
  __IOM uint32_t  CLK_ADC_CTRL;                 /*!< Clock control, can be changed on-the-fly (except for auxsrc)              */
  __IOM uint32_t  CLK_ADC_DIV;                  /*!< Clock divisor, can be changed on-the-fly                                  */
  __IOM uint32_t  CLK_ADC_SELECTED;             /*!< Indicates which SRC is currently selected by the glitchless
                                                     mux (one-hot).                                                            */
  __IOM uint32_t  CLK_RTC_CTRL;                 /*!< Clock control, can be changed on-the-fly (except for auxsrc)              */
  __IOM uint32_t  CLK_RTC_DIV;                  /*!< Clock divisor, can be changed on-the-fly                                  */
  __IOM uint32_t  CLK_RTC_SELECTED;             /*!< Indicates which SRC is currently selected by the glitchless
                                                     mux (one-hot).                                                            */
  __IOM uint32_t  CLK_SYS_RESUS_CTRL;           /*!< CLK_SYS_RESUS_CTRL                                                        */
  __IOM uint32_t  CLK_SYS_RESUS_STATUS;         /*!< CLK_SYS_RESUS_STATUS                                                      */
  __IOM uint32_t  FC0_REF_KHZ;                  /*!< Reference clock frequency in kHz                                          */
  __IOM uint32_t  FC0_MIN_KHZ;                  /*!< Minimum pass frequency in kHz. This is optional. Set to 0 if
                                                     you are not using the pass/fail flags                                     */
  __IOM uint32_t  FC0_MAX_KHZ;                  /*!< Maximum pass frequency in kHz. This is optional. Set to 0x1ffffff
                                                     if you are not using the pass/fail flags                                  */
  __IOM uint32_t  FC0_DELAY;                    /*!< Delays the start of frequency counting to allow the mux to settle
                                                     Delay is measured in multiples of the reference clock period              */
  __IOM uint32_t  FC0_INTERVAL;                 /*!< The test interval is 0.98us * 2**interval, but let's call it
                                                     1us * 2**interval The default gives a test interval of
                                                     250us                                                                     */
  __IOM uint32_t  FC0_SRC;                      /*!< Clock sent to frequency counter, set to 0 when not required
                                                     Writing to this register initiates the frequency count                    */
  __IOM uint32_t  FC0_STATUS;                   /*!< Frequency counter status                                                  */
  __IOM uint32_t  FC0_RESULT;                   /*!< Result of frequency measurement, only valid when status_done=1            */
  __IOM uint32_t  WAKE_EN0;                     /*!< enable clock in wake mode                                                 */
  __IOM uint32_t  WAKE_EN1;                     /*!< enable clock in wake mode                                                 */
  __IOM uint32_t  SLEEP_EN0;                    /*!< enable clock in sleep mode                                                */
  __IOM uint32_t  SLEEP_EN1;                    /*!< enable clock in sleep mode                                                */
  __IOM uint32_t  ENABLED0;                     /*!< indicates the state of the clock enable                                   */
  __IOM uint32_t  ENABLED1;                     /*!< indicates the state of the clock enable                                   */
  __IOM uint32_t  INTR;                         /*!< Raw Interrupts                                                            */
  __IOM uint32_t  INTE;                         /*!< Interrupt Enable                                                          */
  __IOM uint32_t  INTF;                         /*!< Interrupt Force                                                           */
  __IOM uint32_t  INTS;                         /*!< Interrupt status after masking & forcing                                  */
} CLOCKS_Type;                                  /*!< Size = 200 (0xc8)                                                         */



/* =========================================================================================================================== */
/* ================                                        PADS_BANK0                                         ================ */
/* =========================================================================================================================== */


/**
  * @brief PADS_BANK0 (PADS_BANK0)
  */

typedef struct {                                /*!< PADS_BANK0 Structure                                                      */
  __IOM uint32_t  VOLTAGE_SELECT;               /*!< Voltage select. Per bank control                                          */
  __IOM uint32_t  GPIO0;                        /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO1;                        /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO2;                        /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO3;                        /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO4;                        /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO5;                        /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO6;                        /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO7;                        /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO8;                        /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO9;                        /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO10;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO11;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO12;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO13;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO14;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO15;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO16;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO17;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO18;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO19;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO20;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO21;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO22;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO23;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO24;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO25;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO26;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO27;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO28;                       /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO29;                       /*!< Pad control register                                                      */
  __IOM uint32_t  SWCLK;                        /*!< Pad control register                                                      */
  __IOM uint32_t  SWD;                          /*!< Pad control register                                                      */
} PADS_BANK0_Type;                              /*!< Size = 132 (0x84)                                                         */



/* =========================================================================================================================== */
/* ================                                         PADS_QSPI                                         ================ */
/* =========================================================================================================================== */


/**
  * @brief PADS_QSPI (PADS_QSPI)
  */

typedef struct {                                /*!< PADS_QSPI Structure                                                       */
  __IOM uint32_t  VOLTAGE_SELECT;               /*!< Voltage select. Per bank control                                          */
  __IOM uint32_t  GPIO_QSPI_SCLK;               /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO_QSPI_SD0;                /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO_QSPI_SD1;                /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO_QSPI_SD2;                /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO_QSPI_SD3;                /*!< Pad control register                                                      */
  __IOM uint32_t  GPIO_QSPI_SS;                 /*!< Pad control register                                                      */
} PADS_QSPI_Type;                               /*!< Size = 28 (0x1c)                                                          */



/* =========================================================================================================================== */
/* ================                                          IO_QSPI                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief IO_QSPI (IO_QSPI)
  */

typedef struct {                                /*!< IO_QSPI Structure                                                         */
  __IOM uint32_t  GPIO_QSPI_SCLK_STATUS;        /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO_QSPI_SCLK_CTRL;          /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO_QSPI_SS_STATUS;          /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO_QSPI_SS_CTRL;            /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO_QSPI_SD0_STATUS;         /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO_QSPI_SD0_CTRL;           /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO_QSPI_SD1_STATUS;         /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO_QSPI_SD1_CTRL;           /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO_QSPI_SD2_STATUS;         /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO_QSPI_SD2_CTRL;           /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO_QSPI_SD3_STATUS;         /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO_QSPI_SD3_CTRL;           /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  INTR;                         /*!< Raw Interrupts                                                            */
  __IOM uint32_t  PROC0_INTE;                   /*!< Interrupt Enable for proc0                                                */
  __IOM uint32_t  PROC0_INTF;                   /*!< Interrupt Force for proc0                                                 */
  __IOM uint32_t  PROC0_INTS;                   /*!< Interrupt status after masking & forcing for proc0                        */
  __IOM uint32_t  PROC1_INTE;                   /*!< Interrupt Enable for proc1                                                */
  __IOM uint32_t  PROC1_INTF;                   /*!< Interrupt Force for proc1                                                 */
  __IOM uint32_t  PROC1_INTS;                   /*!< Interrupt status after masking & forcing for proc1                        */
  __IOM uint32_t  DORMANT_WAKE_INTE;            /*!< Interrupt Enable for dormant_wake                                         */
  __IOM uint32_t  DORMANT_WAKE_INTF;            /*!< Interrupt Force for dormant_wake                                          */
  __IOM uint32_t  DORMANT_WAKE_INTS;            /*!< Interrupt status after masking & forcing for dormant_wake                 */
} IO_QSPI_Type;                                 /*!< Size = 88 (0x58)                                                          */



/* =========================================================================================================================== */
/* ================                                         IO_BANK0                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief IO_BANK0 (IO_BANK0)
  */

typedef struct {                                /*!< IO_BANK0 Structure                                                        */
  __IOM uint32_t  GPIO0_STATUS;                 /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO0_CTRL;                   /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO1_STATUS;                 /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO1_CTRL;                   /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO2_STATUS;                 /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO2_CTRL;                   /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO3_STATUS;                 /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO3_CTRL;                   /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO4_STATUS;                 /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO4_CTRL;                   /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO5_STATUS;                 /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO5_CTRL;                   /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO6_STATUS;                 /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO6_CTRL;                   /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO7_STATUS;                 /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO7_CTRL;                   /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO8_STATUS;                 /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO8_CTRL;                   /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO9_STATUS;                 /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO9_CTRL;                   /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO10_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO10_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO11_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO11_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO12_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO12_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO13_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO13_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO14_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO14_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO15_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO15_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO16_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO16_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO17_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO17_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO18_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO18_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO19_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO19_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO20_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO20_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO21_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO21_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO22_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO22_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO23_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO23_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO24_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO24_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO25_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO25_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO26_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO26_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO27_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO27_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO28_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO28_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  GPIO29_STATUS;                /*!< GPIO status                                                               */
  __IOM uint32_t  GPIO29_CTRL;                  /*!< GPIO control including function select and overrides.                     */
  __IOM uint32_t  INTR0;                        /*!< Raw Interrupts                                                            */
  __IOM uint32_t  INTR1;                        /*!< Raw Interrupts                                                            */
  __IOM uint32_t  INTR2;                        /*!< Raw Interrupts                                                            */
  __IOM uint32_t  INTR3;                        /*!< Raw Interrupts                                                            */
  __IOM uint32_t  PROC0_INTE0;                  /*!< Interrupt Enable for proc0                                                */
  __IOM uint32_t  PROC0_INTE1;                  /*!< Interrupt Enable for proc0                                                */
  __IOM uint32_t  PROC0_INTE2;                  /*!< Interrupt Enable for proc0                                                */
  __IOM uint32_t  PROC0_INTE3;                  /*!< Interrupt Enable for proc0                                                */
  __IOM uint32_t  PROC0_INTF0;                  /*!< Interrupt Force for proc0                                                 */
  __IOM uint32_t  PROC0_INTF1;                  /*!< Interrupt Force for proc0                                                 */
  __IOM uint32_t  PROC0_INTF2;                  /*!< Interrupt Force for proc0                                                 */
  __IOM uint32_t  PROC0_INTF3;                  /*!< Interrupt Force for proc0                                                 */
  __IOM uint32_t  PROC0_INTS0;                  /*!< Interrupt status after masking & forcing for proc0                        */
  __IOM uint32_t  PROC0_INTS1;                  /*!< Interrupt status after masking & forcing for proc0                        */
  __IOM uint32_t  PROC0_INTS2;                  /*!< Interrupt status after masking & forcing for proc0                        */
  __IOM uint32_t  PROC0_INTS3;                  /*!< Interrupt status after masking & forcing for proc0                        */
  __IOM uint32_t  PROC1_INTE0;                  /*!< Interrupt Enable for proc1                                                */
  __IOM uint32_t  PROC1_INTE1;                  /*!< Interrupt Enable for proc1                                                */
  __IOM uint32_t  PROC1_INTE2;                  /*!< Interrupt Enable for proc1                                                */
  __IOM uint32_t  PROC1_INTE3;                  /*!< Interrupt Enable for proc1                                                */
  __IOM uint32_t  PROC1_INTF0;                  /*!< Interrupt Force for proc1                                                 */
  __IOM uint32_t  PROC1_INTF1;                  /*!< Interrupt Force for proc1                                                 */
  __IOM uint32_t  PROC1_INTF2;                  /*!< Interrupt Force for proc1                                                 */
  __IOM uint32_t  PROC1_INTF3;                  /*!< Interrupt Force for proc1                                                 */
  __IOM uint32_t  PROC1_INTS0;                  /*!< Interrupt status after masking & forcing for proc1                        */
  __IOM uint32_t  PROC1_INTS1;                  /*!< Interrupt status after masking & forcing for proc1                        */
  __IOM uint32_t  PROC1_INTS2;                  /*!< Interrupt status after masking & forcing for proc1                        */
  __IOM uint32_t  PROC1_INTS3;                  /*!< Interrupt status after masking & forcing for proc1                        */
  __IOM uint32_t  DORMANT_WAKE_INTE0;           /*!< Interrupt Enable for dormant_wake                                         */
  __IOM uint32_t  DORMANT_WAKE_INTE1;           /*!< Interrupt Enable for dormant_wake                                         */
  __IOM uint32_t  DORMANT_WAKE_INTE2;           /*!< Interrupt Enable for dormant_wake                                         */
  __IOM uint32_t  DORMANT_WAKE_INTE3;           /*!< Interrupt Enable for dormant_wake                                         */
  __IOM uint32_t  DORMANT_WAKE_INTF0;           /*!< Interrupt Force for dormant_wake                                          */
  __IOM uint32_t  DORMANT_WAKE_INTF1;           /*!< Interrupt Force for dormant_wake                                          */
  __IOM uint32_t  DORMANT_WAKE_INTF2;           /*!< Interrupt Force for dormant_wake                                          */
  __IOM uint32_t  DORMANT_WAKE_INTF3;           /*!< Interrupt Force for dormant_wake                                          */
  __IOM uint32_t  DORMANT_WAKE_INTS0;           /*!< Interrupt status after masking & forcing for dormant_wake                 */
  __IOM uint32_t  DORMANT_WAKE_INTS1;           /*!< Interrupt status after masking & forcing for dormant_wake                 */
  __IOM uint32_t  DORMANT_WAKE_INTS2;           /*!< Interrupt status after masking & forcing for dormant_wake                 */
  __IOM uint32_t  DORMANT_WAKE_INTS3;           /*!< Interrupt status after masking & forcing for dormant_wake                 */
} IO_BANK0_Type;                                /*!< Size = 400 (0x190)                                                        */



/* =========================================================================================================================== */
/* ================                                          SYSINFO                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief SYSINFO (SYSINFO)
  */

typedef struct {                                /*!< SYSINFO Structure                                                         */
  __IOM uint32_t  CHIP_ID;                      /*!< JEDEC JEP-106 compliant chip identifier.                                  */
  __IOM uint32_t  PLATFORM;                     /*!< Platform register. Allows software to know what environment
                                                     it is running in.                                                         */
  __IM  uint32_t  RESERVED[2];
  __IOM uint32_t  GITREF_RP2040;                /*!< Git hash of the chip source. Used to identify chip version.               */
} SYSINFO_Type;                                 /*!< Size = 20 (0x14)                                                          */



/* =========================================================================================================================== */
/* ================                                            PPB                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief PPB (PPB)
  */

typedef struct {                                /*!< PPB Structure                                                             */
  __IM  uint32_t  RESERVED[14340];
  __IOM uint32_t  SYST_CSR;                     /*!< Use the SysTick Control and Status Register to enable the SysTick
                                                     features.                                                                 */
  __IOM uint32_t  SYST_RVR;                     /*!< Use the SysTick Reload Value Register to specify the start value
                                                     to load into the current value register when the counter
                                                     reaches 0. It can be any value between 0 and 0x00FFFFFF.
                                                     A start value of 0 is possible, but has no effect because
                                                     the SysTick interrupt and COUNTFLAG are activated when
                                                     counting from 1 to 0. The reset value of this register
                                                     is UNKNOWN. To generate a multi-shot timer with a period
                                                     of N processor clock cycles, use a RELOAD value of N-1.
                                                     For example, if the SysTick interrupt is required every
                                                     100 clock pulses, set RELOAD to 99.                                       */
  __IOM uint32_t  SYST_CVR;                     /*!< Use the SysTick Current Value Register to find the current value
                                                     in the register. The reset value of this register is UNKNOWN.             */
  __IOM uint32_t  SYST_CALIB;                   /*!< Use the SysTick Calibration Value Register to enable software
                                                     to scale to any required speed using divide and multiply.                 */
  __IM  uint32_t  RESERVED1[56];
  __IOM uint32_t  NVIC_ISER;                    /*!< Use the Interrupt Set-Enable Register to enable interrupts and
                                                     determine which interrupts are currently enabled. If a
                                                     pending interrupt is enabled, the NVIC activates the interrupt
                                                     based on its priority. If an interrupt is not enabled,
                                                     asserting its interrupt signal changes the interrupt state
                                                     to pending, but the NVIC never activates the interrupt,
                                                     regardless of its priority.                                               */
  __IM  uint32_t  RESERVED2[31];
  __IOM uint32_t  NVIC_ICER;                    /*!< Use the Interrupt Clear-Enable Registers to disable interrupts
                                                     and determine which interrupts are currently enabled.                     */
  __IM  uint32_t  RESERVED3[31];
  __IOM uint32_t  NVIC_ISPR;                    /*!< The NVIC_ISPR forces interrupts into the pending state, and
                                                     shows which interrupts are pending.                                       */
  __IM  uint32_t  RESERVED4[31];
  __IOM uint32_t  NVIC_ICPR;                    /*!< Use the Interrupt Clear-Pending Register to clear pending interrupts
                                                     and determine which interrupts are currently pending.                     */
  __IM  uint32_t  RESERVED5[95];
  __IOM uint32_t  NVIC_IPR0;                    /*!< Use the Interrupt Priority Registers to assign a priority from
                                                     0 to 3 to each of the available interrupts. 0 is the highest
                                                     priority, and 3 is the lowest. Note: Writing 1 to an NVIC_ICPR
                                                     bit does not affect the active state of the corresponding
                                                     interrupt. These registers are only word-accessible                       */
  __IOM uint32_t  NVIC_IPR1;                    /*!< Use the Interrupt Priority Registers to assign a priority from
                                                     0 to 3 to each of the available interrupts. 0 is the highest
                                                     priority, and 3 is the lowest.                                            */
  __IOM uint32_t  NVIC_IPR2;                    /*!< Use the Interrupt Priority Registers to assign a priority from
                                                     0 to 3 to each of the available interrupts. 0 is the highest
                                                     priority, and 3 is the lowest.                                            */
  __IOM uint32_t  NVIC_IPR3;                    /*!< Use the Interrupt Priority Registers to assign a priority from
                                                     0 to 3 to each of the available interrupts. 0 is the highest
                                                     priority, and 3 is the lowest.                                            */
  __IOM uint32_t  NVIC_IPR4;                    /*!< Use the Interrupt Priority Registers to assign a priority from
                                                     0 to 3 to each of the available interrupts. 0 is the highest
                                                     priority, and 3 is the lowest.                                            */
  __IOM uint32_t  NVIC_IPR5;                    /*!< Use the Interrupt Priority Registers to assign a priority from
                                                     0 to 3 to each of the available interrupts. 0 is the highest
                                                     priority, and 3 is the lowest.                                            */
  __IOM uint32_t  NVIC_IPR6;                    /*!< Use the Interrupt Priority Registers to assign a priority from
                                                     0 to 3 to each of the available interrupts. 0 is the highest
                                                     priority, and 3 is the lowest.                                            */
  __IOM uint32_t  NVIC_IPR7;                    /*!< Use the Interrupt Priority Registers to assign a priority from
                                                     0 to 3 to each of the available interrupts. 0 is the highest
                                                     priority, and 3 is the lowest.                                            */
  __IM  uint32_t  RESERVED6[568];
  __IOM uint32_t  CPUID;                        /*!< Read the CPU ID Base Register to determine: the ID number of
                                                     the processor core, the version number of the processor
                                                     core, the implementation details of the processor core.                   */
  __IOM uint32_t  ICSR;                         /*!< Use the Interrupt Control State Register to set a pending Non-Maskable
                                                     Interrupt (NMI), set or clear a pending PendSV, set or
                                                     clear a pending SysTick, check for pending exceptions,
                                                     check the vector number of the highest priority pended
                                                     exception, check the vector number of the active exception.               */
  __IOM uint32_t  VTOR;                         /*!< The VTOR holds the vector table offset address.                           */
  __IOM uint32_t  AIRCR;                        /*!< Use the Application Interrupt and Reset Control Register to:
                                                     determine data endianness, clear all active state information
                                                     from debug halt mode, request a system reset.                             */
  __IOM uint32_t  SCR;                          /*!< System Control Register. Use the System Control Register for
                                                     power-management functions: signal to the system when the
                                                     processor can enter a low power state, control how the
                                                     processor enters and exits low power states.                              */
  __IOM uint32_t  CCR;                          /*!< The Configuration and Control Register permanently enables stack
                                                     alignment and causes unaligned accesses to result in a
                                                     Hard Fault.                                                               */
  __IM  uint32_t  RESERVED7;
  __IOM uint32_t  SHPR2;                        /*!< System handlers are a special class of exception handler that
                                                     can have their priority set to any of the priority levels.
                                                     Use the System Handler Priority Register 2 to set the priority
                                                     of SVCall.                                                                */
  __IOM uint32_t  SHPR3;                        /*!< System handlers are a special class of exception handler that
                                                     can have their priority set to any of the priority levels.
                                                     Use the System Handler Priority Register 3 to set the priority
                                                     of PendSV and SysTick.                                                    */
  __IOM uint32_t  SHCSR;                        /*!< Use the System Handler Control and State Register to determine
                                                     or clear the pending status of SVCall.                                    */
  __IM  uint32_t  RESERVED8[26];
  __IOM uint32_t  MPU_TYPE;                     /*!< Read the MPU Type Register to determine if the processor implements
                                                     an MPU, and how many regions the MPU supports.                            */
  __IOM uint32_t  MPU_CTRL;                     /*!< Use the MPU Control Register to enable and disable the MPU,
                                                     and to control whether the default memory map is enabled
                                                     as a background region for privileged accesses, and whether
                                                     the MPU is enabled for HardFaults and NMIs.                               */
  __IOM uint32_t  MPU_RNR;                      /*!< Use the MPU Region Number Register to select the region currently
                                                     accessed by MPU_RBAR and MPU_RASR.                                        */
  __IOM uint32_t  MPU_RBAR;                     /*!< Read the MPU Region Base Address Register to determine the base
                                                     address of the region identified by MPU_RNR. Write to update
                                                     the base address of said region or that of a specified
                                                     region, with whose number MPU_RNR will also be updated.                   */
  __IOM uint32_t  MPU_RASR;                     /*!< Use the MPU Region Attribute and Size Register to define the
                                                     size, access behaviour and memory type of the region identified
                                                     by MPU_RNR, and enable that region.                                       */
} PPB_Type;                                     /*!< Size = 60836 (0xeda4)                                                     */



/* =========================================================================================================================== */
/* ================                                            SSI                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief DW_apb_ssi has the following features: 
            * APB interface – Allows for easy integration into a DesignWare Synthesizable Components for AMBA 2 implementation. 
            * APB3 and APB4 protocol support. 
            * Scalable APB data bus width – Supports APB data bus widths of 8, 16, and 32 bits. 
            * Serial-master or serial-slave operation – Enables serial communication with serial-master or serial-slave peripheral devices. 
            * Programmable Dual/Quad/Octal SPI support in Master Mode. 
            * Dual Data Rate (DDR) and Read Data Strobe (RDS) Support - Enables the DW_apb_ssi master to perform operations with the device in DDR and RDS modes when working in Dual/Quad/Octal mode of operation. 
            * Data Mask Support - Enables the DW_apb_ssi to selectively update the bytes in the device. This feature is applicable only in enhanced SPI modes. 
            * eXecute-In-Place (XIP) support - Enables the DW_apb_ssi master to behave as a memory mapped I/O and fetches the data from the device based on the APB read request. This feature is applicable only in enhanced SPI modes. 
            * DMA Controller Interface – Enables the DW_apb_ssi to interface to a DMA controller over the bus using a handshaking interface for transfer requests. 
            * Independent masking of interrupts – Master collision, transmit FIFO overflow, transmit FIFO empty, receive FIFO full, receive FIFO underflow, and receive FIFO overflow interrupts can all be masked independently. 
            * Multi-master contention detection – Informs the processor of multiple serial-master accesses on the serial bus. 
            * Bypass of meta-stability flip-flops for synchronous clocks – When the APB clock (pclk) and the DW_apb_ssi serial clock (ssi_clk) are synchronous, meta-stable flip-flops are not used when transferring control signals across these clock domains. 
            * Programmable delay on the sample time of the received serial data bit (rxd); enables programmable control of routing delays resulting in higher serial data-bit rates. 
            * Programmable features: 
            - Serial interface operation – Choice of Motorola SPI, Texas Instruments Synchronous Serial Protocol or National Semiconductor Microwire. 
            - Clock bit-rate – Dynamic control of the serial bit rate of the data transfer; used in only serial-master mode of operation. 
            - Data Item size (4 to 32 bits) – Item size of each data transfer under the control of the programmer. 
            * Configured features: 
            - FIFO depth – 16 words deep. The FIFO width is fixed at 32 bits. 
            - 1 slave select output. 
            - Hardware slave-select – Dedicated hardware slave-select line. 
            - Combined interrupt line - one combined interrupt line from the DW_apb_ssi to the interrupt controller. 
            - Interrupt polarity – active high interrupt lines. 
            - Serial clock polarity – low serial-clock polarity directly after reset. 
            - Serial clock phase – capture on first edge of serial-clock directly after reset. (SSI)
  */

typedef struct {                                /*!< SSI Structure                                                             */
  __IOM uint32_t  CTRLR0;                       /*!< Control register 0                                                        */
  __IOM uint32_t  CTRLR1;                       /*!< Master Control register 1                                                 */
  __IOM uint32_t  SSIENR;                       /*!< SSI Enable                                                                */
  __IOM uint32_t  MWCR;                         /*!< Microwire Control                                                         */
  __IOM uint32_t  SER;                          /*!< Slave enable                                                              */
  __IOM uint32_t  BAUDR;                        /*!< Baud rate                                                                 */
  __IOM uint32_t  TXFTLR;                       /*!< TX FIFO threshold level                                                   */
  __IOM uint32_t  RXFTLR;                       /*!< RX FIFO threshold level                                                   */
  __IOM uint32_t  TXFLR;                        /*!< TX FIFO level                                                             */
  __IOM uint32_t  RXFLR;                        /*!< RX FIFO level                                                             */
  __IOM uint32_t  SR;                           /*!< Status register                                                           */
  __IOM uint32_t  IMR;                          /*!< Interrupt mask                                                            */
  __IOM uint32_t  ISR;                          /*!< Interrupt status                                                          */
  __IOM uint32_t  RISR;                         /*!< Raw interrupt status                                                      */
  __IOM uint32_t  TXOICR;                       /*!< TX FIFO overflow interrupt clear                                          */
  __IOM uint32_t  RXOICR;                       /*!< RX FIFO overflow interrupt clear                                          */
  __IOM uint32_t  RXUICR;                       /*!< RX FIFO underflow interrupt clear                                         */
  __IOM uint32_t  MSTICR;                       /*!< Multi-master interrupt clear                                              */
  __IOM uint32_t  ICR;                          /*!< Interrupt clear                                                           */
  __IOM uint32_t  DMACR;                        /*!< DMA control                                                               */
  __IOM uint32_t  DMATDLR;                      /*!< DMA TX data level                                                         */
  __IOM uint32_t  DMARDLR;                      /*!< DMA RX data level                                                         */
  __IOM uint32_t  IDR;                          /*!< Identification register                                                   */
  __IOM uint32_t  SSI_VERSION_ID;               /*!< Version ID                                                                */
  __IOM uint32_t  DR0;                          /*!< Data Register 0 (of 36)                                                   */
  __IM  uint32_t  RESERVED[35];
  __IOM uint32_t  RX_SAMPLE_DLY;                /*!< RX sample delay                                                           */
  __IOM uint32_t  SPI_CTRLR0;                   /*!< SPI control                                                               */
  __IOM uint32_t  TXD_DRIVE_EDGE;               /*!< TX drive edge                                                             */
} SSI_Type;                                     /*!< Size = 252 (0xfc)                                                         */



/* =========================================================================================================================== */
/* ================                                         XIP_CTRL                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief QSPI flash execute-in-place block (XIP_CTRL)
  */

typedef struct {                                /*!< XIP_CTRL Structure                                                        */
  __IOM uint32_t  CTRL;                         /*!< Cache control                                                             */
  __IOM uint32_t  FLUSH;                        /*!< Cache Flush control                                                       */
  __IOM uint32_t  STAT;                         /*!< Cache Status                                                              */
  __IOM uint32_t  CTR_HIT;                      /*!< Cache Hit counter                                                         */
  __IOM uint32_t  CTR_ACC;                      /*!< Cache Access counter                                                      */
  __IOM uint32_t  STREAM_ADDR;                  /*!< FIFO stream address                                                       */
  __IOM uint32_t  STREAM_CTR;                   /*!< FIFO stream control                                                       */
  __IOM uint32_t  STREAM_FIFO;                  /*!< FIFO stream data                                                          */
} XIP_CTRL_Type;                                /*!< Size = 32 (0x20)                                                          */



/* =========================================================================================================================== */
/* ================                                          SYSCFG                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Register block for various chip control signals (SYSCFG)
  */

typedef struct {                                /*!< SYSCFG Structure                                                          */
  __IOM uint32_t  PROC0_NMI_MASK;               /*!< Processor core 0 NMI source mask                                          */
  __IOM uint32_t  PROC1_NMI_MASK;               /*!< Processor core 1 NMI source mask                                          */
  __IOM uint32_t  PROC_CONFIG;                  /*!< Configuration for processors                                              */
  __IOM uint32_t  PROC_IN_SYNC_BYPASS;          /*!< For each bit, if 1, bypass the input synchronizer between that
                                                     GPIO and the GPIO input register in the SIO. The input
                                                     synchronizers should generally be unbypassed, to avoid
                                                     injecting metastabilities into processors. If you're feeling
                                                     brave, you can bypass to save two cycles of input latency.
                                                     This register applies to GPIO 0...29.                                     */
  __IOM uint32_t  PROC_IN_SYNC_BYPASS_HI;       /*!< For each bit, if 1, bypass the input synchronizer between that
                                                     GPIO and the GPIO input register in the SIO. The input
                                                     synchronizers should generally be unbypassed, to avoid
                                                     injecting metastabilities into processors. If you're feeling
                                                     brave, you can bypass to save two cycles of input latency.
                                                     This register applies to GPIO 30...35 (the QSPI IOs).                     */
  __IOM uint32_t  DBGFORCE;                     /*!< Directly control the SWD debug port of either processor                   */
  __IOM uint32_t  MEMPOWERDOWN;                 /*!< Control power downs to memories. Set high to power down memories.
                                                     Use with extreme caution                                                  */
} SYSCFG_Type;                                  /*!< Size = 28 (0x1c)                                                          */



/* =========================================================================================================================== */
/* ================                                           XOSC                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Controls the crystal oscillator (XOSC)
  */

typedef struct {                                /*!< XOSC Structure                                                            */
  __IOM uint32_t  CTRL;                         /*!< Crystal Oscillator Control                                                */
  __IOM uint32_t  STATUS;                       /*!< Crystal Oscillator Status                                                 */
  __IOM uint32_t  DORMANT;                      /*!< Crystal Oscillator pause control                                          */
  __IOM uint32_t  STARTUP;                      /*!< Controls the startup delay                                                */
  __IM  uint32_t  RESERVED[3];
  __IOM uint32_t  COUNT;                        /*!< A down counter running at the xosc frequency which counts to
                                                     zero and stops. To start the counter write a non-zero value.
                                                     Can be used for short software pauses when setting up time
                                                     sensitive hardware.                                                       */
} XOSC_Type;                                    /*!< Size = 32 (0x20)                                                          */



/* =========================================================================================================================== */
/* ================                                          PLL_SYS                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief PLL_SYS (PLL_SYS)
  */

typedef struct {                                /*!< PLL_SYS Structure                                                         */
  __IOM uint32_t  CS;                           /*!< Control and Status GENERAL CONSTRAINTS: Reference clock frequency
                                                     min=5MHz, max=800MHz Feedback divider min=16, max=320 VCO
                                                     frequency min=750MHz, max=1600MHz                                         */
  __IOM uint32_t  PWR;                          /*!< Controls the PLL power modes.                                             */
  __IOM uint32_t  FBDIV_INT;                    /*!< Feedback divisor (note: this PLL does not support fractional
                                                     division)                                                                 */
  __IOM uint32_t  PRIM;                         /*!< Controls the PLL post dividers for the primary output (note:
                                                     this PLL does not have a secondary output) the primary
                                                     output is driven from VCO divided by postdiv1*postdiv2                    */
} PLL_SYS_Type;                                 /*!< Size = 16 (0x10)                                                          */



/* =========================================================================================================================== */
/* ================                                           UART0                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief UART0 (UART0)
  */

typedef struct {                                /*!< UART0 Structure                                                           */
  __IOM uint32_t  UARTDR;                       /*!< Data Register, UARTDR                                                     */
  __IOM uint32_t  UARTRSR;                      /*!< Receive Status Register/Error Clear Register, UARTRSR/UARTECR             */
  __IM  uint32_t  RESERVED[4];
  __IOM uint32_t  UARTFR;                       /*!< Flag Register, UARTFR                                                     */
  __IM  uint32_t  RESERVED1;
  __IOM uint32_t  UARTILPR;                     /*!< IrDA Low-Power Counter Register, UARTILPR                                 */
  __IOM uint32_t  UARTIBRD;                     /*!< Integer Baud Rate Register, UARTIBRD                                      */
  __IOM uint32_t  UARTFBRD;                     /*!< Fractional Baud Rate Register, UARTFBRD                                   */
  __IOM uint32_t  UARTLCR_H;                    /*!< Line Control Register, UARTLCR_H                                          */
  __IOM uint32_t  UARTCR;                       /*!< Control Register, UARTCR                                                  */
  __IOM uint32_t  UARTIFLS;                     /*!< Interrupt FIFO Level Select Register, UARTIFLS                            */
  __IOM uint32_t  UARTIMSC;                     /*!< Interrupt Mask Set/Clear Register, UARTIMSC                               */
  __IOM uint32_t  UARTRIS;                      /*!< Raw Interrupt Status Register, UARTRIS                                    */
  __IOM uint32_t  UARTMIS;                      /*!< Masked Interrupt Status Register, UARTMIS                                 */
  __IOM uint32_t  UARTICR;                      /*!< Interrupt Clear Register, UARTICR                                         */
  __IOM uint32_t  UARTDMACR;                    /*!< DMA Control Register, UARTDMACR                                           */
  __IM  uint32_t  RESERVED2[997];
  __IOM uint32_t  UARTPERIPHID0;                /*!< UARTPeriphID0 Register                                                    */
  __IOM uint32_t  UARTPERIPHID1;                /*!< UARTPeriphID1 Register                                                    */
  __IOM uint32_t  UARTPERIPHID2;                /*!< UARTPeriphID2 Register                                                    */
  __IOM uint32_t  UARTPERIPHID3;                /*!< UARTPeriphID3 Register                                                    */
  __IOM uint32_t  UARTPCELLID0;                 /*!< UARTPCellID0 Register                                                     */
  __IOM uint32_t  UARTPCELLID1;                 /*!< UARTPCellID1 Register                                                     */
  __IOM uint32_t  UARTPCELLID2;                 /*!< UARTPCellID2 Register                                                     */
  __IOM uint32_t  UARTPCELLID3;                 /*!< UARTPCellID3 Register                                                     */
} UART0_Type;                                   /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                           ROSC                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief ROSC (ROSC)
  */

typedef struct {                                /*!< ROSC Structure                                                            */
  __IOM uint32_t  CTRL;                         /*!< Ring Oscillator control                                                   */
  __IOM uint32_t  FREQA;                        /*!< The FREQA & FREQB registers control the frequency by controlling
                                                     the drive strength of each stage The drive strength has
                                                     4 levels determined by the number of bits set Increasing
                                                     the number of bits set increases the drive strength and
                                                     increases the oscillation frequency 0 bits set is the default
                                                     drive strength 1 bit set doubles the drive strength 2 bits
                                                     set triples drive strength 3 bits set quadruples drive
                                                     strength                                                                  */
  __IOM uint32_t  FREQB;                        /*!< For a detailed description see freqa register                             */
  __IOM uint32_t  DORMANT;                      /*!< Ring Oscillator pause control                                             */
  __IOM uint32_t  DIV;                          /*!< Controls the output divider                                               */
  __IOM uint32_t  PHASE;                        /*!< Controls the phase shifted output                                         */
  __IOM uint32_t  STATUS;                       /*!< Ring Oscillator Status                                                    */
  __IOM uint32_t  RANDOMBIT;                    /*!< This just reads the state of the oscillator output so randomness
                                                     is compromised if the ring oscillator is stopped or run
                                                     at a harmonic of the bus frequency                                        */
  __IOM uint32_t  COUNT;                        /*!< A down counter running at the ROSC frequency which counts to
                                                     zero and stops. To start the counter write a non-zero value.
                                                     Can be used for short software pauses when setting up time
                                                     sensitive hardware.                                                       */
} ROSC_Type;                                    /*!< Size = 36 (0x24)                                                          */



/* =========================================================================================================================== */
/* ================                                         WATCHDOG                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief WATCHDOG (WATCHDOG)
  */

typedef struct {                                /*!< WATCHDOG Structure                                                        */
  __IOM uint32_t  CTRL;                         /*!< Watchdog control The rst_wdsel register determines which subsystems
                                                     are reset when the watchdog is triggered. The watchdog
                                                     can be triggered in software.                                             */
  __IOM uint32_t  LOAD;                         /*!< Load the watchdog timer. The maximum setting is 0xffffff which
                                                     corresponds to 0xffffff / 2 ticks before triggering a watchdog
                                                     reset (see errata RP2040-E1).                                             */
  __IOM uint32_t  REASON;                       /*!< Logs the reason for the last reset. Both bits are zero for the
                                                     case of a hardware reset.                                                 */
  __IOM uint32_t  SCRATCH0;                     /*!< Scratch register. Information persists through soft reset of
                                                     the chip.                                                                 */
  __IOM uint32_t  SCRATCH1;                     /*!< Scratch register. Information persists through soft reset of
                                                     the chip.                                                                 */
  __IOM uint32_t  SCRATCH2;                     /*!< Scratch register. Information persists through soft reset of
                                                     the chip.                                                                 */
  __IOM uint32_t  SCRATCH3;                     /*!< Scratch register. Information persists through soft reset of
                                                     the chip.                                                                 */
  __IOM uint32_t  SCRATCH4;                     /*!< Scratch register. Information persists through soft reset of
                                                     the chip.                                                                 */
  __IOM uint32_t  SCRATCH5;                     /*!< Scratch register. Information persists through soft reset of
                                                     the chip.                                                                 */
  __IOM uint32_t  SCRATCH6;                     /*!< Scratch register. Information persists through soft reset of
                                                     the chip.                                                                 */
  __IOM uint32_t  SCRATCH7;                     /*!< Scratch register. Information persists through soft reset of
                                                     the chip.                                                                 */
  __IOM uint32_t  TICK;                         /*!< Controls the tick generator                                               */
} WATCHDOG_Type;                                /*!< Size = 48 (0x30)                                                          */



/* =========================================================================================================================== */
/* ================                                            DMA                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief DMA with separate read and write masters (DMA)
  */

typedef struct {                                /*!< DMA Structure                                                             */
  __IOM uint32_t  CH0_READ_ADDR;                /*!< DMA Channel 0 Read Address pointer                                        */
  __IOM uint32_t  CH0_WRITE_ADDR;               /*!< DMA Channel 0 Write Address pointer                                       */
  __IOM uint32_t  CH0_TRANS_COUNT;              /*!< DMA Channel 0 Transfer Count                                              */
  __IOM uint32_t  CH0_CTRL_TRIG;                /*!< DMA Channel 0 Control and Status                                          */
  __IOM uint32_t  CH0_AL1_CTRL;                 /*!< Alias for channel 0 CTRL register                                         */
  __IOM uint32_t  CH0_AL1_READ_ADDR;            /*!< Alias for channel 0 READ_ADDR register                                    */
  __IOM uint32_t  CH0_AL1_WRITE_ADDR;           /*!< Alias for channel 0 WRITE_ADDR register                                   */
  __IOM uint32_t  CH0_AL1_TRANS_COUNT_TRIG;     /*!< Alias for channel 0 TRANS_COUNT register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH0_AL2_CTRL;                 /*!< Alias for channel 0 CTRL register                                         */
  __IOM uint32_t  CH0_AL2_TRANS_COUNT;          /*!< Alias for channel 0 TRANS_COUNT register                                  */
  __IOM uint32_t  CH0_AL2_READ_ADDR;            /*!< Alias for channel 0 READ_ADDR register                                    */
  __IOM uint32_t  CH0_AL2_WRITE_ADDR_TRIG;      /*!< Alias for channel 0 WRITE_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH0_AL3_CTRL;                 /*!< Alias for channel 0 CTRL register                                         */
  __IOM uint32_t  CH0_AL3_WRITE_ADDR;           /*!< Alias for channel 0 WRITE_ADDR register                                   */
  __IOM uint32_t  CH0_AL3_TRANS_COUNT;          /*!< Alias for channel 0 TRANS_COUNT register                                  */
  __IOM uint32_t  CH0_AL3_READ_ADDR_TRIG;       /*!< Alias for channel 0 READ_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH1_READ_ADDR;                /*!< DMA Channel 1 Read Address pointer                                        */
  __IOM uint32_t  CH1_WRITE_ADDR;               /*!< DMA Channel 1 Write Address pointer                                       */
  __IOM uint32_t  CH1_TRANS_COUNT;              /*!< DMA Channel 1 Transfer Count                                              */
  __IOM uint32_t  CH1_CTRL_TRIG;                /*!< DMA Channel 1 Control and Status                                          */
  __IOM uint32_t  CH1_AL1_CTRL;                 /*!< Alias for channel 1 CTRL register                                         */
  __IOM uint32_t  CH1_AL1_READ_ADDR;            /*!< Alias for channel 1 READ_ADDR register                                    */
  __IOM uint32_t  CH1_AL1_WRITE_ADDR;           /*!< Alias for channel 1 WRITE_ADDR register                                   */
  __IOM uint32_t  CH1_AL1_TRANS_COUNT_TRIG;     /*!< Alias for channel 1 TRANS_COUNT register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH1_AL2_CTRL;                 /*!< Alias for channel 1 CTRL register                                         */
  __IOM uint32_t  CH1_AL2_TRANS_COUNT;          /*!< Alias for channel 1 TRANS_COUNT register                                  */
  __IOM uint32_t  CH1_AL2_READ_ADDR;            /*!< Alias for channel 1 READ_ADDR register                                    */
  __IOM uint32_t  CH1_AL2_WRITE_ADDR_TRIG;      /*!< Alias for channel 1 WRITE_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH1_AL3_CTRL;                 /*!< Alias for channel 1 CTRL register                                         */
  __IOM uint32_t  CH1_AL3_WRITE_ADDR;           /*!< Alias for channel 1 WRITE_ADDR register                                   */
  __IOM uint32_t  CH1_AL3_TRANS_COUNT;          /*!< Alias for channel 1 TRANS_COUNT register                                  */
  __IOM uint32_t  CH1_AL3_READ_ADDR_TRIG;       /*!< Alias for channel 1 READ_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH2_READ_ADDR;                /*!< DMA Channel 2 Read Address pointer                                        */
  __IOM uint32_t  CH2_WRITE_ADDR;               /*!< DMA Channel 2 Write Address pointer                                       */
  __IOM uint32_t  CH2_TRANS_COUNT;              /*!< DMA Channel 2 Transfer Count                                              */
  __IOM uint32_t  CH2_CTRL_TRIG;                /*!< DMA Channel 2 Control and Status                                          */
  __IOM uint32_t  CH2_AL1_CTRL;                 /*!< Alias for channel 2 CTRL register                                         */
  __IOM uint32_t  CH2_AL1_READ_ADDR;            /*!< Alias for channel 2 READ_ADDR register                                    */
  __IOM uint32_t  CH2_AL1_WRITE_ADDR;           /*!< Alias for channel 2 WRITE_ADDR register                                   */
  __IOM uint32_t  CH2_AL1_TRANS_COUNT_TRIG;     /*!< Alias for channel 2 TRANS_COUNT register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH2_AL2_CTRL;                 /*!< Alias for channel 2 CTRL register                                         */
  __IOM uint32_t  CH2_AL2_TRANS_COUNT;          /*!< Alias for channel 2 TRANS_COUNT register                                  */
  __IOM uint32_t  CH2_AL2_READ_ADDR;            /*!< Alias for channel 2 READ_ADDR register                                    */
  __IOM uint32_t  CH2_AL2_WRITE_ADDR_TRIG;      /*!< Alias for channel 2 WRITE_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH2_AL3_CTRL;                 /*!< Alias for channel 2 CTRL register                                         */
  __IOM uint32_t  CH2_AL3_WRITE_ADDR;           /*!< Alias for channel 2 WRITE_ADDR register                                   */
  __IOM uint32_t  CH2_AL3_TRANS_COUNT;          /*!< Alias for channel 2 TRANS_COUNT register                                  */
  __IOM uint32_t  CH2_AL3_READ_ADDR_TRIG;       /*!< Alias for channel 2 READ_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH3_READ_ADDR;                /*!< DMA Channel 3 Read Address pointer                                        */
  __IOM uint32_t  CH3_WRITE_ADDR;               /*!< DMA Channel 3 Write Address pointer                                       */
  __IOM uint32_t  CH3_TRANS_COUNT;              /*!< DMA Channel 3 Transfer Count                                              */
  __IOM uint32_t  CH3_CTRL_TRIG;                /*!< DMA Channel 3 Control and Status                                          */
  __IOM uint32_t  CH3_AL1_CTRL;                 /*!< Alias for channel 3 CTRL register                                         */
  __IOM uint32_t  CH3_AL1_READ_ADDR;            /*!< Alias for channel 3 READ_ADDR register                                    */
  __IOM uint32_t  CH3_AL1_WRITE_ADDR;           /*!< Alias for channel 3 WRITE_ADDR register                                   */
  __IOM uint32_t  CH3_AL1_TRANS_COUNT_TRIG;     /*!< Alias for channel 3 TRANS_COUNT register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH3_AL2_CTRL;                 /*!< Alias for channel 3 CTRL register                                         */
  __IOM uint32_t  CH3_AL2_TRANS_COUNT;          /*!< Alias for channel 3 TRANS_COUNT register                                  */
  __IOM uint32_t  CH3_AL2_READ_ADDR;            /*!< Alias for channel 3 READ_ADDR register                                    */
  __IOM uint32_t  CH3_AL2_WRITE_ADDR_TRIG;      /*!< Alias for channel 3 WRITE_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH3_AL3_CTRL;                 /*!< Alias for channel 3 CTRL register                                         */
  __IOM uint32_t  CH3_AL3_WRITE_ADDR;           /*!< Alias for channel 3 WRITE_ADDR register                                   */
  __IOM uint32_t  CH3_AL3_TRANS_COUNT;          /*!< Alias for channel 3 TRANS_COUNT register                                  */
  __IOM uint32_t  CH3_AL3_READ_ADDR_TRIG;       /*!< Alias for channel 3 READ_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH4_READ_ADDR;                /*!< DMA Channel 4 Read Address pointer                                        */
  __IOM uint32_t  CH4_WRITE_ADDR;               /*!< DMA Channel 4 Write Address pointer                                       */
  __IOM uint32_t  CH4_TRANS_COUNT;              /*!< DMA Channel 4 Transfer Count                                              */
  __IOM uint32_t  CH4_CTRL_TRIG;                /*!< DMA Channel 4 Control and Status                                          */
  __IOM uint32_t  CH4_AL1_CTRL;                 /*!< Alias for channel 4 CTRL register                                         */
  __IOM uint32_t  CH4_AL1_READ_ADDR;            /*!< Alias for channel 4 READ_ADDR register                                    */
  __IOM uint32_t  CH4_AL1_WRITE_ADDR;           /*!< Alias for channel 4 WRITE_ADDR register                                   */
  __IOM uint32_t  CH4_AL1_TRANS_COUNT_TRIG;     /*!< Alias for channel 4 TRANS_COUNT register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH4_AL2_CTRL;                 /*!< Alias for channel 4 CTRL register                                         */
  __IOM uint32_t  CH4_AL2_TRANS_COUNT;          /*!< Alias for channel 4 TRANS_COUNT register                                  */
  __IOM uint32_t  CH4_AL2_READ_ADDR;            /*!< Alias for channel 4 READ_ADDR register                                    */
  __IOM uint32_t  CH4_AL2_WRITE_ADDR_TRIG;      /*!< Alias for channel 4 WRITE_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH4_AL3_CTRL;                 /*!< Alias for channel 4 CTRL register                                         */
  __IOM uint32_t  CH4_AL3_WRITE_ADDR;           /*!< Alias for channel 4 WRITE_ADDR register                                   */
  __IOM uint32_t  CH4_AL3_TRANS_COUNT;          /*!< Alias for channel 4 TRANS_COUNT register                                  */
  __IOM uint32_t  CH4_AL3_READ_ADDR_TRIG;       /*!< Alias for channel 4 READ_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH5_READ_ADDR;                /*!< DMA Channel 5 Read Address pointer                                        */
  __IOM uint32_t  CH5_WRITE_ADDR;               /*!< DMA Channel 5 Write Address pointer                                       */
  __IOM uint32_t  CH5_TRANS_COUNT;              /*!< DMA Channel 5 Transfer Count                                              */
  __IOM uint32_t  CH5_CTRL_TRIG;                /*!< DMA Channel 5 Control and Status                                          */
  __IOM uint32_t  CH5_AL1_CTRL;                 /*!< Alias for channel 5 CTRL register                                         */
  __IOM uint32_t  CH5_AL1_READ_ADDR;            /*!< Alias for channel 5 READ_ADDR register                                    */
  __IOM uint32_t  CH5_AL1_WRITE_ADDR;           /*!< Alias for channel 5 WRITE_ADDR register                                   */
  __IOM uint32_t  CH5_AL1_TRANS_COUNT_TRIG;     /*!< Alias for channel 5 TRANS_COUNT register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH5_AL2_CTRL;                 /*!< Alias for channel 5 CTRL register                                         */
  __IOM uint32_t  CH5_AL2_TRANS_COUNT;          /*!< Alias for channel 5 TRANS_COUNT register                                  */
  __IOM uint32_t  CH5_AL2_READ_ADDR;            /*!< Alias for channel 5 READ_ADDR register                                    */
  __IOM uint32_t  CH5_AL2_WRITE_ADDR_TRIG;      /*!< Alias for channel 5 WRITE_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH5_AL3_CTRL;                 /*!< Alias for channel 5 CTRL register                                         */
  __IOM uint32_t  CH5_AL3_WRITE_ADDR;           /*!< Alias for channel 5 WRITE_ADDR register                                   */
  __IOM uint32_t  CH5_AL3_TRANS_COUNT;          /*!< Alias for channel 5 TRANS_COUNT register                                  */
  __IOM uint32_t  CH5_AL3_READ_ADDR_TRIG;       /*!< Alias for channel 5 READ_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH6_READ_ADDR;                /*!< DMA Channel 6 Read Address pointer                                        */
  __IOM uint32_t  CH6_WRITE_ADDR;               /*!< DMA Channel 6 Write Address pointer                                       */
  __IOM uint32_t  CH6_TRANS_COUNT;              /*!< DMA Channel 6 Transfer Count                                              */
  __IOM uint32_t  CH6_CTRL_TRIG;                /*!< DMA Channel 6 Control and Status                                          */
  __IOM uint32_t  CH6_AL1_CTRL;                 /*!< Alias for channel 6 CTRL register                                         */
  __IOM uint32_t  CH6_AL1_READ_ADDR;            /*!< Alias for channel 6 READ_ADDR register                                    */
  __IOM uint32_t  CH6_AL1_WRITE_ADDR;           /*!< Alias for channel 6 WRITE_ADDR register                                   */
  __IOM uint32_t  CH6_AL1_TRANS_COUNT_TRIG;     /*!< Alias for channel 6 TRANS_COUNT register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH6_AL2_CTRL;                 /*!< Alias for channel 6 CTRL register                                         */
  __IOM uint32_t  CH6_AL2_TRANS_COUNT;          /*!< Alias for channel 6 TRANS_COUNT register                                  */
  __IOM uint32_t  CH6_AL2_READ_ADDR;            /*!< Alias for channel 6 READ_ADDR register                                    */
  __IOM uint32_t  CH6_AL2_WRITE_ADDR_TRIG;      /*!< Alias for channel 6 WRITE_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH6_AL3_CTRL;                 /*!< Alias for channel 6 CTRL register                                         */
  __IOM uint32_t  CH6_AL3_WRITE_ADDR;           /*!< Alias for channel 6 WRITE_ADDR register                                   */
  __IOM uint32_t  CH6_AL3_TRANS_COUNT;          /*!< Alias for channel 6 TRANS_COUNT register                                  */
  __IOM uint32_t  CH6_AL3_READ_ADDR_TRIG;       /*!< Alias for channel 6 READ_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH7_READ_ADDR;                /*!< DMA Channel 7 Read Address pointer                                        */
  __IOM uint32_t  CH7_WRITE_ADDR;               /*!< DMA Channel 7 Write Address pointer                                       */
  __IOM uint32_t  CH7_TRANS_COUNT;              /*!< DMA Channel 7 Transfer Count                                              */
  __IOM uint32_t  CH7_CTRL_TRIG;                /*!< DMA Channel 7 Control and Status                                          */
  __IOM uint32_t  CH7_AL1_CTRL;                 /*!< Alias for channel 7 CTRL register                                         */
  __IOM uint32_t  CH7_AL1_READ_ADDR;            /*!< Alias for channel 7 READ_ADDR register                                    */
  __IOM uint32_t  CH7_AL1_WRITE_ADDR;           /*!< Alias for channel 7 WRITE_ADDR register                                   */
  __IOM uint32_t  CH7_AL1_TRANS_COUNT_TRIG;     /*!< Alias for channel 7 TRANS_COUNT register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH7_AL2_CTRL;                 /*!< Alias for channel 7 CTRL register                                         */
  __IOM uint32_t  CH7_AL2_TRANS_COUNT;          /*!< Alias for channel 7 TRANS_COUNT register                                  */
  __IOM uint32_t  CH7_AL2_READ_ADDR;            /*!< Alias for channel 7 READ_ADDR register                                    */
  __IOM uint32_t  CH7_AL2_WRITE_ADDR_TRIG;      /*!< Alias for channel 7 WRITE_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH7_AL3_CTRL;                 /*!< Alias for channel 7 CTRL register                                         */
  __IOM uint32_t  CH7_AL3_WRITE_ADDR;           /*!< Alias for channel 7 WRITE_ADDR register                                   */
  __IOM uint32_t  CH7_AL3_TRANS_COUNT;          /*!< Alias for channel 7 TRANS_COUNT register                                  */
  __IOM uint32_t  CH7_AL3_READ_ADDR_TRIG;       /*!< Alias for channel 7 READ_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH8_READ_ADDR;                /*!< DMA Channel 8 Read Address pointer                                        */
  __IOM uint32_t  CH8_WRITE_ADDR;               /*!< DMA Channel 8 Write Address pointer                                       */
  __IOM uint32_t  CH8_TRANS_COUNT;              /*!< DMA Channel 8 Transfer Count                                              */
  __IOM uint32_t  CH8_CTRL_TRIG;                /*!< DMA Channel 8 Control and Status                                          */
  __IOM uint32_t  CH8_AL1_CTRL;                 /*!< Alias for channel 8 CTRL register                                         */
  __IOM uint32_t  CH8_AL1_READ_ADDR;            /*!< Alias for channel 8 READ_ADDR register                                    */
  __IOM uint32_t  CH8_AL1_WRITE_ADDR;           /*!< Alias for channel 8 WRITE_ADDR register                                   */
  __IOM uint32_t  CH8_AL1_TRANS_COUNT_TRIG;     /*!< Alias for channel 8 TRANS_COUNT register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH8_AL2_CTRL;                 /*!< Alias for channel 8 CTRL register                                         */
  __IOM uint32_t  CH8_AL2_TRANS_COUNT;          /*!< Alias for channel 8 TRANS_COUNT register                                  */
  __IOM uint32_t  CH8_AL2_READ_ADDR;            /*!< Alias for channel 8 READ_ADDR register                                    */
  __IOM uint32_t  CH8_AL2_WRITE_ADDR_TRIG;      /*!< Alias for channel 8 WRITE_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH8_AL3_CTRL;                 /*!< Alias for channel 8 CTRL register                                         */
  __IOM uint32_t  CH8_AL3_WRITE_ADDR;           /*!< Alias for channel 8 WRITE_ADDR register                                   */
  __IOM uint32_t  CH8_AL3_TRANS_COUNT;          /*!< Alias for channel 8 TRANS_COUNT register                                  */
  __IOM uint32_t  CH8_AL3_READ_ADDR_TRIG;       /*!< Alias for channel 8 READ_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH9_READ_ADDR;                /*!< DMA Channel 9 Read Address pointer                                        */
  __IOM uint32_t  CH9_WRITE_ADDR;               /*!< DMA Channel 9 Write Address pointer                                       */
  __IOM uint32_t  CH9_TRANS_COUNT;              /*!< DMA Channel 9 Transfer Count                                              */
  __IOM uint32_t  CH9_CTRL_TRIG;                /*!< DMA Channel 9 Control and Status                                          */
  __IOM uint32_t  CH9_AL1_CTRL;                 /*!< Alias for channel 9 CTRL register                                         */
  __IOM uint32_t  CH9_AL1_READ_ADDR;            /*!< Alias for channel 9 READ_ADDR register                                    */
  __IOM uint32_t  CH9_AL1_WRITE_ADDR;           /*!< Alias for channel 9 WRITE_ADDR register                                   */
  __IOM uint32_t  CH9_AL1_TRANS_COUNT_TRIG;     /*!< Alias for channel 9 TRANS_COUNT register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH9_AL2_CTRL;                 /*!< Alias for channel 9 CTRL register                                         */
  __IOM uint32_t  CH9_AL2_TRANS_COUNT;          /*!< Alias for channel 9 TRANS_COUNT register                                  */
  __IOM uint32_t  CH9_AL2_READ_ADDR;            /*!< Alias for channel 9 READ_ADDR register                                    */
  __IOM uint32_t  CH9_AL2_WRITE_ADDR_TRIG;      /*!< Alias for channel 9 WRITE_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH9_AL3_CTRL;                 /*!< Alias for channel 9 CTRL register                                         */
  __IOM uint32_t  CH9_AL3_WRITE_ADDR;           /*!< Alias for channel 9 WRITE_ADDR register                                   */
  __IOM uint32_t  CH9_AL3_TRANS_COUNT;          /*!< Alias for channel 9 TRANS_COUNT register                                  */
  __IOM uint32_t  CH9_AL3_READ_ADDR_TRIG;       /*!< Alias for channel 9 READ_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH10_READ_ADDR;               /*!< DMA Channel 10 Read Address pointer                                       */
  __IOM uint32_t  CH10_WRITE_ADDR;              /*!< DMA Channel 10 Write Address pointer                                      */
  __IOM uint32_t  CH10_TRANS_COUNT;             /*!< DMA Channel 10 Transfer Count                                             */
  __IOM uint32_t  CH10_CTRL_TRIG;               /*!< DMA Channel 10 Control and Status                                         */
  __IOM uint32_t  CH10_AL1_CTRL;                /*!< Alias for channel 10 CTRL register                                        */
  __IOM uint32_t  CH10_AL1_READ_ADDR;           /*!< Alias for channel 10 READ_ADDR register                                   */
  __IOM uint32_t  CH10_AL1_WRITE_ADDR;          /*!< Alias for channel 10 WRITE_ADDR register                                  */
  __IOM uint32_t  CH10_AL1_TRANS_COUNT_TRIG;    /*!< Alias for channel 10 TRANS_COUNT register This is a trigger
                                                     register (0xc). Writing a nonzero value will reload the
                                                     channel counter and start the channel.                                    */
  __IOM uint32_t  CH10_AL2_CTRL;                /*!< Alias for channel 10 CTRL register                                        */
  __IOM uint32_t  CH10_AL2_TRANS_COUNT;         /*!< Alias for channel 10 TRANS_COUNT register                                 */
  __IOM uint32_t  CH10_AL2_READ_ADDR;           /*!< Alias for channel 10 READ_ADDR register                                   */
  __IOM uint32_t  CH10_AL2_WRITE_ADDR_TRIG;     /*!< Alias for channel 10 WRITE_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH10_AL3_CTRL;                /*!< Alias for channel 10 CTRL register                                        */
  __IOM uint32_t  CH10_AL3_WRITE_ADDR;          /*!< Alias for channel 10 WRITE_ADDR register                                  */
  __IOM uint32_t  CH10_AL3_TRANS_COUNT;         /*!< Alias for channel 10 TRANS_COUNT register                                 */
  __IOM uint32_t  CH10_AL3_READ_ADDR_TRIG;      /*!< Alias for channel 10 READ_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH11_READ_ADDR;               /*!< DMA Channel 11 Read Address pointer                                       */
  __IOM uint32_t  CH11_WRITE_ADDR;              /*!< DMA Channel 11 Write Address pointer                                      */
  __IOM uint32_t  CH11_TRANS_COUNT;             /*!< DMA Channel 11 Transfer Count                                             */
  __IOM uint32_t  CH11_CTRL_TRIG;               /*!< DMA Channel 11 Control and Status                                         */
  __IOM uint32_t  CH11_AL1_CTRL;                /*!< Alias for channel 11 CTRL register                                        */
  __IOM uint32_t  CH11_AL1_READ_ADDR;           /*!< Alias for channel 11 READ_ADDR register                                   */
  __IOM uint32_t  CH11_AL1_WRITE_ADDR;          /*!< Alias for channel 11 WRITE_ADDR register                                  */
  __IOM uint32_t  CH11_AL1_TRANS_COUNT_TRIG;    /*!< Alias for channel 11 TRANS_COUNT register This is a trigger
                                                     register (0xc). Writing a nonzero value will reload the
                                                     channel counter and start the channel.                                    */
  __IOM uint32_t  CH11_AL2_CTRL;                /*!< Alias for channel 11 CTRL register                                        */
  __IOM uint32_t  CH11_AL2_TRANS_COUNT;         /*!< Alias for channel 11 TRANS_COUNT register                                 */
  __IOM uint32_t  CH11_AL2_READ_ADDR;           /*!< Alias for channel 11 READ_ADDR register                                   */
  __IOM uint32_t  CH11_AL2_WRITE_ADDR_TRIG;     /*!< Alias for channel 11 WRITE_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IOM uint32_t  CH11_AL3_CTRL;                /*!< Alias for channel 11 CTRL register                                        */
  __IOM uint32_t  CH11_AL3_WRITE_ADDR;          /*!< Alias for channel 11 WRITE_ADDR register                                  */
  __IOM uint32_t  CH11_AL3_TRANS_COUNT;         /*!< Alias for channel 11 TRANS_COUNT register                                 */
  __IOM uint32_t  CH11_AL3_READ_ADDR_TRIG;      /*!< Alias for channel 11 READ_ADDR register This is a trigger register
                                                     (0xc). Writing a nonzero value will reload the channel
                                                     counter and start the channel.                                            */
  __IM  uint32_t  RESERVED[64];
  __IOM uint32_t  INTR;                         /*!< Interrupt Status (raw)                                                    */
  __IOM uint32_t  INTE0;                        /*!< Interrupt Enables for IRQ 0                                               */
  __IOM uint32_t  INTF0;                        /*!< Force Interrupts                                                          */
  __IOM uint32_t  INTS0;                        /*!< Interrupt Status for IRQ 0                                                */
  __IOM uint32_t  INTR1;                        /*!< Interrupt Status (raw)                                                    */
  __IOM uint32_t  INTE1;                        /*!< Interrupt Enables for IRQ 1                                               */
  __IOM uint32_t  INTF1;                        /*!< Force Interrupts for IRQ 1                                                */
  __IOM uint32_t  INTS1;                        /*!< Interrupt Status (masked) for IRQ 1                                       */
  __IOM uint32_t  TIMER0;                       /*!< Pacing (X/Y) Fractional Timer The pacing timer produces TREQ
                                                     assertions at a rate set by ((X/Y) * sys_clk). This equation
                                                     is evaluated every sys_clk cycles and therefore can only
                                                     generate TREQs at a rate of 1 per sys_clk (i.e. permanent
                                                     TREQ) or less.                                                            */
  __IOM uint32_t  TIMER1;                       /*!< Pacing (X/Y) Fractional Timer The pacing timer produces TREQ
                                                     assertions at a rate set by ((X/Y) * sys_clk). This equation
                                                     is evaluated every sys_clk cycles and therefore can only
                                                     generate TREQs at a rate of 1 per sys_clk (i.e. permanent
                                                     TREQ) or less.                                                            */
  __IOM uint32_t  TIMER2;                       /*!< Pacing (X/Y) Fractional Timer The pacing timer produces TREQ
                                                     assertions at a rate set by ((X/Y) * sys_clk). This equation
                                                     is evaluated every sys_clk cycles and therefore can only
                                                     generate TREQs at a rate of 1 per sys_clk (i.e. permanent
                                                     TREQ) or less.                                                            */
  __IOM uint32_t  TIMER3;                       /*!< Pacing (X/Y) Fractional Timer The pacing timer produces TREQ
                                                     assertions at a rate set by ((X/Y) * sys_clk). This equation
                                                     is evaluated every sys_clk cycles and therefore can only
                                                     generate TREQs at a rate of 1 per sys_clk (i.e. permanent
                                                     TREQ) or less.                                                            */
  __IOM uint32_t  MULTI_CHAN_TRIGGER;           /*!< Trigger one or more channels simultaneously                               */
  __IOM uint32_t  SNIFF_CTRL;                   /*!< Sniffer Control                                                           */
  __IOM uint32_t  SNIFF_DATA;                   /*!< Data accumulator for sniff hardware                                       */
  __IM  uint32_t  RESERVED1;
  __IOM uint32_t  FIFO_LEVELS;                  /*!< Debug RAF, WAF, TDF levels                                                */
  __IOM uint32_t  CHAN_ABORT;                   /*!< Abort an in-progress transfer sequence on one or more channels            */
  __IOM uint32_t  N_CHANNELS;                   /*!< The number of channels this DMA instance is equipped with. This
                                                     DMA supports up to 16 hardware channels, but can be configured
                                                     with as few as one, to minimise silicon area.                             */
  __IM  uint32_t  RESERVED2[237];
  __IOM uint32_t  CH0_DBG_CTDREQ;               /*!< Read: get channel DREQ counter (i.e. how many accesses the DMA
                                                     expects it can perform on the peripheral without overflow/underflow.
                                                     Write any value: clears the counter, and cause channel
                                                     to re-initiate DREQ handshake.                                            */
  __IOM uint32_t  CH0_DBG_TCR;                  /*!< Read to get channel TRANS_COUNT reload value, i.e. the length
                                                     of the next transfer                                                      */
  __IM  uint32_t  RESERVED3[14];
  __IOM uint32_t  CH1_DBG_CTDREQ;               /*!< Read: get channel DREQ counter (i.e. how many accesses the DMA
                                                     expects it can perform on the peripheral without overflow/underflow.
                                                     Write any value: clears the counter, and cause channel
                                                     to re-initiate DREQ handshake.                                            */
  __IOM uint32_t  CH1_DBG_TCR;                  /*!< Read to get channel TRANS_COUNT reload value, i.e. the length
                                                     of the next transfer                                                      */
  __IM  uint32_t  RESERVED4[14];
  __IOM uint32_t  CH2_DBG_CTDREQ;               /*!< Read: get channel DREQ counter (i.e. how many accesses the DMA
                                                     expects it can perform on the peripheral without overflow/underflow.
                                                     Write any value: clears the counter, and cause channel
                                                     to re-initiate DREQ handshake.                                            */
  __IOM uint32_t  CH2_DBG_TCR;                  /*!< Read to get channel TRANS_COUNT reload value, i.e. the length
                                                     of the next transfer                                                      */
  __IM  uint32_t  RESERVED5[14];
  __IOM uint32_t  CH3_DBG_CTDREQ;               /*!< Read: get channel DREQ counter (i.e. how many accesses the DMA
                                                     expects it can perform on the peripheral without overflow/underflow.
                                                     Write any value: clears the counter, and cause channel
                                                     to re-initiate DREQ handshake.                                            */
  __IOM uint32_t  CH3_DBG_TCR;                  /*!< Read to get channel TRANS_COUNT reload value, i.e. the length
                                                     of the next transfer                                                      */
  __IM  uint32_t  RESERVED6[14];
  __IOM uint32_t  CH4_DBG_CTDREQ;               /*!< Read: get channel DREQ counter (i.e. how many accesses the DMA
                                                     expects it can perform on the peripheral without overflow/underflow.
                                                     Write any value: clears the counter, and cause channel
                                                     to re-initiate DREQ handshake.                                            */
  __IOM uint32_t  CH4_DBG_TCR;                  /*!< Read to get channel TRANS_COUNT reload value, i.e. the length
                                                     of the next transfer                                                      */
  __IM  uint32_t  RESERVED7[14];
  __IOM uint32_t  CH5_DBG_CTDREQ;               /*!< Read: get channel DREQ counter (i.e. how many accesses the DMA
                                                     expects it can perform on the peripheral without overflow/underflow.
                                                     Write any value: clears the counter, and cause channel
                                                     to re-initiate DREQ handshake.                                            */
  __IOM uint32_t  CH5_DBG_TCR;                  /*!< Read to get channel TRANS_COUNT reload value, i.e. the length
                                                     of the next transfer                                                      */
  __IM  uint32_t  RESERVED8[14];
  __IOM uint32_t  CH6_DBG_CTDREQ;               /*!< Read: get channel DREQ counter (i.e. how many accesses the DMA
                                                     expects it can perform on the peripheral without overflow/underflow.
                                                     Write any value: clears the counter, and cause channel
                                                     to re-initiate DREQ handshake.                                            */
  __IOM uint32_t  CH6_DBG_TCR;                  /*!< Read to get channel TRANS_COUNT reload value, i.e. the length
                                                     of the next transfer                                                      */
  __IM  uint32_t  RESERVED9[14];
  __IOM uint32_t  CH7_DBG_CTDREQ;               /*!< Read: get channel DREQ counter (i.e. how many accesses the DMA
                                                     expects it can perform on the peripheral without overflow/underflow.
                                                     Write any value: clears the counter, and cause channel
                                                     to re-initiate DREQ handshake.                                            */
  __IOM uint32_t  CH7_DBG_TCR;                  /*!< Read to get channel TRANS_COUNT reload value, i.e. the length
                                                     of the next transfer                                                      */
  __IM  uint32_t  RESERVED10[14];
  __IOM uint32_t  CH8_DBG_CTDREQ;               /*!< Read: get channel DREQ counter (i.e. how many accesses the DMA
                                                     expects it can perform on the peripheral without overflow/underflow.
                                                     Write any value: clears the counter, and cause channel
                                                     to re-initiate DREQ handshake.                                            */
  __IOM uint32_t  CH8_DBG_TCR;                  /*!< Read to get channel TRANS_COUNT reload value, i.e. the length
                                                     of the next transfer                                                      */
  __IM  uint32_t  RESERVED11[14];
  __IOM uint32_t  CH9_DBG_CTDREQ;               /*!< Read: get channel DREQ counter (i.e. how many accesses the DMA
                                                     expects it can perform on the peripheral without overflow/underflow.
                                                     Write any value: clears the counter, and cause channel
                                                     to re-initiate DREQ handshake.                                            */
  __IOM uint32_t  CH9_DBG_TCR;                  /*!< Read to get channel TRANS_COUNT reload value, i.e. the length
                                                     of the next transfer                                                      */
  __IM  uint32_t  RESERVED12[14];
  __IOM uint32_t  CH10_DBG_CTDREQ;              /*!< Read: get channel DREQ counter (i.e. how many accesses the DMA
                                                     expects it can perform on the peripheral without overflow/underflow.
                                                     Write any value: clears the counter, and cause channel
                                                     to re-initiate DREQ handshake.                                            */
  __IOM uint32_t  CH10_DBG_TCR;                 /*!< Read to get channel TRANS_COUNT reload value, i.e. the length
                                                     of the next transfer                                                      */
  __IM  uint32_t  RESERVED13[14];
  __IOM uint32_t  CH11_DBG_CTDREQ;              /*!< Read: get channel DREQ counter (i.e. how many accesses the DMA
                                                     expects it can perform on the peripheral without overflow/underflow.
                                                     Write any value: clears the counter, and cause channel
                                                     to re-initiate DREQ handshake.                                            */
  __IOM uint32_t  CH11_DBG_TCR;                 /*!< Read to get channel TRANS_COUNT reload value, i.e. the length
                                                     of the next transfer                                                      */
} DMA_Type;                                     /*!< Size = 2760 (0xac8)                                                       */



/* =========================================================================================================================== */
/* ================                                           TIMER                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Controls time and alarms 
            time is a 64 bit value indicating the time in usec since power-on 
            timeh is the top 32 bits of time & timel is the bottom 32 bits 
            to change time write to timelw before timehw 
            to read time read from timelr before timehr 
            An alarm is set by setting alarm_enable and writing to the corresponding alarm register 
            When an alarm is pending, the corresponding alarm_running signal will be high 
            An alarm can be cancelled before it has finished by clearing the alarm_enable 
            When an alarm fires, the corresponding alarm_irq is set and alarm_running is cleared 
            To clear the interrupt write a 1 to the corresponding alarm_irq (TIMER)
  */

typedef struct {                                /*!< TIMER Structure                                                           */
  __IOM uint32_t  TIMEHW;                       /*!< Write to bits 63:32 of time always write timelw before timehw             */
  __IOM uint32_t  TIMELW;                       /*!< Write to bits 31:0 of time writes do not get copied to time
                                                     until timehw is written                                                   */
  __IOM uint32_t  TIMEHR;                       /*!< Read from bits 63:32 of time always read timelr before timehr             */
  __IOM uint32_t  TIMELR;                       /*!< Read from bits 31:0 of time                                               */
  __IOM uint32_t  ALARM0;                       /*!< Arm alarm 0, and configure the time it will fire. Once armed,
                                                     the alarm fires when TIMER_ALARM0 == TIMELR. The alarm
                                                     will disarm itself once it fires, and can be disarmed early
                                                     using the ARMED status register.                                          */
  __IOM uint32_t  ALARM1;                       /*!< Arm alarm 1, and configure the time it will fire. Once armed,
                                                     the alarm fires when TIMER_ALARM1 == TIMELR. The alarm
                                                     will disarm itself once it fires, and can be disarmed early
                                                     using the ARMED status register.                                          */
  __IOM uint32_t  ALARM2;                       /*!< Arm alarm 2, and configure the time it will fire. Once armed,
                                                     the alarm fires when TIMER_ALARM2 == TIMELR. The alarm
                                                     will disarm itself once it fires, and can be disarmed early
                                                     using the ARMED status register.                                          */
  __IOM uint32_t  ALARM3;                       /*!< Arm alarm 3, and configure the time it will fire. Once armed,
                                                     the alarm fires when TIMER_ALARM3 == TIMELR. The alarm
                                                     will disarm itself once it fires, and can be disarmed early
                                                     using the ARMED status register.                                          */
  __IOM uint32_t  ARMED;                        /*!< Indicates the armed/disarmed status of each alarm. A write to
                                                     the corresponding ALARMx register arms the alarm. Alarms
                                                     automatically disarm upon firing, but writing ones here
                                                     will disarm immediately without waiting to fire.                          */
  __IOM uint32_t  TIMERAWH;                     /*!< Raw read from bits 63:32 of time (no side effects)                        */
  __IOM uint32_t  TIMERAWL;                     /*!< Raw read from bits 31:0 of time (no side effects)                         */
  __IOM uint32_t  DBGPAUSE;                     /*!< Set bits high to enable pause when the corresponding debug ports
                                                     are active                                                                */
  __IOM uint32_t  PAUSE;                        /*!< Set high to pause the timer                                               */
  __IOM uint32_t  INTR;                         /*!< Raw Interrupts                                                            */
  __IOM uint32_t  INTE;                         /*!< Interrupt Enable                                                          */
  __IOM uint32_t  INTF;                         /*!< Interrupt Force                                                           */
  __IOM uint32_t  INTS;                         /*!< Interrupt status after masking & forcing                                  */
} TIMER_Type;                                   /*!< Size = 68 (0x44)                                                          */



/* =========================================================================================================================== */
/* ================                                            PWM                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Simple PWM (PWM)
  */

typedef struct {                                /*!< PWM Structure                                                             */
  __IOM uint32_t  CH0_CSR;                      /*!< Control and status register                                               */
  __IOM uint32_t  CH0_DIV;                      /*!< INT and FRAC form a fixed-point fractional number. Counting
                                                     rate is system clock frequency divided by this number.
                                                     Fractional division uses simple 1st-order sigma-delta.                    */
  __IOM uint32_t  CH0_CTR;                      /*!< Direct access to the PWM counter                                          */
  __IOM uint32_t  CH0_CC;                       /*!< Counter compare values                                                    */
  __IOM uint32_t  CH0_TOP;                      /*!< Counter wrap value                                                        */
  __IOM uint32_t  CH1_CSR;                      /*!< Control and status register                                               */
  __IOM uint32_t  CH1_DIV;                      /*!< INT and FRAC form a fixed-point fractional number. Counting
                                                     rate is system clock frequency divided by this number.
                                                     Fractional division uses simple 1st-order sigma-delta.                    */
  __IOM uint32_t  CH1_CTR;                      /*!< Direct access to the PWM counter                                          */
  __IOM uint32_t  CH1_CC;                       /*!< Counter compare values                                                    */
  __IOM uint32_t  CH1_TOP;                      /*!< Counter wrap value                                                        */
  __IOM uint32_t  CH2_CSR;                      /*!< Control and status register                                               */
  __IOM uint32_t  CH2_DIV;                      /*!< INT and FRAC form a fixed-point fractional number. Counting
                                                     rate is system clock frequency divided by this number.
                                                     Fractional division uses simple 1st-order sigma-delta.                    */
  __IOM uint32_t  CH2_CTR;                      /*!< Direct access to the PWM counter                                          */
  __IOM uint32_t  CH2_CC;                       /*!< Counter compare values                                                    */
  __IOM uint32_t  CH2_TOP;                      /*!< Counter wrap value                                                        */
  __IOM uint32_t  CH3_CSR;                      /*!< Control and status register                                               */
  __IOM uint32_t  CH3_DIV;                      /*!< INT and FRAC form a fixed-point fractional number. Counting
                                                     rate is system clock frequency divided by this number.
                                                     Fractional division uses simple 1st-order sigma-delta.                    */
  __IOM uint32_t  CH3_CTR;                      /*!< Direct access to the PWM counter                                          */
  __IOM uint32_t  CH3_CC;                       /*!< Counter compare values                                                    */
  __IOM uint32_t  CH3_TOP;                      /*!< Counter wrap value                                                        */
  __IOM uint32_t  CH4_CSR;                      /*!< Control and status register                                               */
  __IOM uint32_t  CH4_DIV;                      /*!< INT and FRAC form a fixed-point fractional number. Counting
                                                     rate is system clock frequency divided by this number.
                                                     Fractional division uses simple 1st-order sigma-delta.                    */
  __IOM uint32_t  CH4_CTR;                      /*!< Direct access to the PWM counter                                          */
  __IOM uint32_t  CH4_CC;                       /*!< Counter compare values                                                    */
  __IOM uint32_t  CH4_TOP;                      /*!< Counter wrap value                                                        */
  __IOM uint32_t  CH5_CSR;                      /*!< Control and status register                                               */
  __IOM uint32_t  CH5_DIV;                      /*!< INT and FRAC form a fixed-point fractional number. Counting
                                                     rate is system clock frequency divided by this number.
                                                     Fractional division uses simple 1st-order sigma-delta.                    */
  __IOM uint32_t  CH5_CTR;                      /*!< Direct access to the PWM counter                                          */
  __IOM uint32_t  CH5_CC;                       /*!< Counter compare values                                                    */
  __IOM uint32_t  CH5_TOP;                      /*!< Counter wrap value                                                        */
  __IOM uint32_t  CH6_CSR;                      /*!< Control and status register                                               */
  __IOM uint32_t  CH6_DIV;                      /*!< INT and FRAC form a fixed-point fractional number. Counting
                                                     rate is system clock frequency divided by this number.
                                                     Fractional division uses simple 1st-order sigma-delta.                    */
  __IOM uint32_t  CH6_CTR;                      /*!< Direct access to the PWM counter                                          */
  __IOM uint32_t  CH6_CC;                       /*!< Counter compare values                                                    */
  __IOM uint32_t  CH6_TOP;                      /*!< Counter wrap value                                                        */
  __IOM uint32_t  CH7_CSR;                      /*!< Control and status register                                               */
  __IOM uint32_t  CH7_DIV;                      /*!< INT and FRAC form a fixed-point fractional number. Counting
                                                     rate is system clock frequency divided by this number.
                                                     Fractional division uses simple 1st-order sigma-delta.                    */
  __IOM uint32_t  CH7_CTR;                      /*!< Direct access to the PWM counter                                          */
  __IOM uint32_t  CH7_CC;                       /*!< Counter compare values                                                    */
  __IOM uint32_t  CH7_TOP;                      /*!< Counter wrap value                                                        */
  __IOM uint32_t  EN;                           /*!< This register aliases the CSR_EN bits for all channels. Writing
                                                     to this register allows multiple channels to be enabled
                                                     or disabled simultaneously, so they can run in perfect
                                                     sync. For each channel, there is only one physical EN register
                                                     bit, which can be accessed through here or CHx_CSR.                       */
  __IOM uint32_t  INTR;                         /*!< Raw Interrupts                                                            */
  __IOM uint32_t  INTE;                         /*!< Interrupt Enable                                                          */
  __IOM uint32_t  INTF;                         /*!< Interrupt Force                                                           */
  __IOM uint32_t  INTS;                         /*!< Interrupt status after masking & forcing                                  */
} PWM_Type;                                     /*!< Size = 180 (0xb4)                                                         */



/* =========================================================================================================================== */
/* ================                                            ADC                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Control and data interface to SAR ADC (ADC)
  */

typedef struct {                                /*!< ADC Structure                                                             */
  __IOM uint32_t  CS;                           /*!< ADC Control and Status                                                    */
  __IOM uint32_t  RESULT;                       /*!< Result of most recent ADC conversion                                      */
  __IOM uint32_t  FCS;                          /*!< FIFO control and status                                                   */
  __IOM uint32_t  FIFO;                         /*!< Conversion result FIFO                                                    */
  __IOM uint32_t  DIV;                          /*!< Clock divider. If non-zero, CS_START_MANY will start conversions
                                                     at regular intervals rather than back-to-back. The divider
                                                     is reset when either of these fields are written. Total
                                                     period is 1 + INT + FRAC / 256                                            */
  __IOM uint32_t  INTR;                         /*!< Raw Interrupts                                                            */
  __IOM uint32_t  INTE;                         /*!< Interrupt Enable                                                          */
  __IOM uint32_t  INTF;                         /*!< Interrupt Force                                                           */
  __IOM uint32_t  INTS;                         /*!< Interrupt status after masking & forcing                                  */
} ADC_Type;                                     /*!< Size = 36 (0x24)                                                          */



/* =========================================================================================================================== */
/* ================                                           I2C0                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief DW_apb_i2c address block

            List of configuration constants for the Synopsys I2C hardware (you may see references to these in I2C register header; these are *fixed* values, set at hardware design time):

            IC_ULTRA_FAST_MODE ................ 0x0 
            IC_UFM_TBUF_CNT_DEFAULT ........... 0x8 
            IC_UFM_SCL_LOW_COUNT .............. 0x0008 
            IC_UFM_SCL_HIGH_COUNT ............. 0x0006 
            IC_TX_TL .......................... 0x0 
            IC_TX_CMD_BLOCK ................... 0x1 
            IC_HAS_DMA ........................ 0x1 
            IC_HAS_ASYNC_FIFO ................. 0x0 
            IC_SMBUS_ARP ...................... 0x0 
            IC_FIRST_DATA_BYTE_STATUS ......... 0x1 
            IC_INTR_IO ........................ 0x1 
            IC_MASTER_MODE .................... 0x1 
            IC_DEFAULT_ACK_GENERAL_CALL ....... 0x1 
            IC_INTR_POL ....................... 0x1 
            IC_OPTIONAL_SAR ................... 0x0 
            IC_DEFAULT_TAR_SLAVE_ADDR ......... 0x055 
            IC_DEFAULT_SLAVE_ADDR ............. 0x055 
            IC_DEFAULT_HS_SPKLEN .............. 0x1 
            IC_FS_SCL_HIGH_COUNT .............. 0x0006 
            IC_HS_SCL_LOW_COUNT ............... 0x0008 
            IC_DEVICE_ID_VALUE ................ 0x0 
            IC_10BITADDR_MASTER ............... 0x0 
            IC_CLK_FREQ_OPTIMIZATION .......... 0x0 
            IC_DEFAULT_FS_SPKLEN .............. 0x7 
            IC_ADD_ENCODED_PARAMS ............. 0x0 
            IC_DEFAULT_SDA_HOLD ............... 0x000001 
            IC_DEFAULT_SDA_SETUP .............. 0x64 
            IC_AVOID_RX_FIFO_FLUSH_ON_TX_ABRT . 0x0 
            IC_CLOCK_PERIOD ................... 100 
            IC_EMPTYFIFO_HOLD_MASTER_EN ....... 1 
            IC_RESTART_EN ..................... 0x1 
            IC_TX_CMD_BLOCK_DEFAULT ........... 0x0 
            IC_BUS_CLEAR_FEATURE .............. 0x0 
            IC_CAP_LOADING .................... 100 
            IC_FS_SCL_LOW_COUNT ............... 0x000d 
            APB_DATA_WIDTH .................... 32 
            IC_SDA_STUCK_TIMEOUT_DEFAULT ...... 0xffffffff 
            IC_SLV_DATA_NACK_ONLY ............. 0x1 
            IC_10BITADDR_SLAVE ................ 0x0 
            IC_CLK_TYPE ....................... 0x0 
            IC_SMBUS_UDID_MSB ................. 0x0 
            IC_SMBUS_SUSPEND_ALERT ............ 0x0 
            IC_HS_SCL_HIGH_COUNT .............. 0x0006 
            IC_SLV_RESTART_DET_EN ............. 0x1 
            IC_SMBUS .......................... 0x0 
            IC_OPTIONAL_SAR_DEFAULT ........... 0x0 
            IC_PERSISTANT_SLV_ADDR_DEFAULT .... 0x0 
            IC_USE_COUNTS ..................... 0x0 
            IC_RX_BUFFER_DEPTH ................ 16 
            IC_SCL_STUCK_TIMEOUT_DEFAULT ...... 0xffffffff 
            IC_RX_FULL_HLD_BUS_EN ............. 0x1 
            IC_SLAVE_DISABLE .................. 0x1 
            IC_RX_TL .......................... 0x0 
            IC_DEVICE_ID ...................... 0x0 
            IC_HC_COUNT_VALUES ................ 0x0 
            I2C_DYNAMIC_TAR_UPDATE ............ 0 
            IC_SMBUS_CLK_LOW_MEXT_DEFAULT ..... 0xffffffff 
            IC_SMBUS_CLK_LOW_SEXT_DEFAULT ..... 0xffffffff 
            IC_HS_MASTER_CODE ................. 0x1 
            IC_SMBUS_RST_IDLE_CNT_DEFAULT ..... 0xffff 
            IC_SMBUS_UDID_LSB_DEFAULT ......... 0xffffffff 
            IC_SS_SCL_HIGH_COUNT .............. 0x0028 
            IC_SS_SCL_LOW_COUNT ............... 0x002f 
            IC_MAX_SPEED_MODE ................. 0x2 
            IC_STAT_FOR_CLK_STRETCH ........... 0x0 
            IC_STOP_DET_IF_MASTER_ACTIVE ...... 0x0 
            IC_DEFAULT_UFM_SPKLEN ............. 0x1 
            IC_TX_BUFFER_DEPTH ................ 16 (I2C0)
  */

typedef struct {                                /*!< I2C0 Structure                                                            */
  __IOM uint32_t  IC_CON;                       /*!< I2C Control Register. This register can be written only when
                                                     the DW_apb_i2c is disabled, which corresponds to the IC_ENABLE[0]
                                                     register being set to 0. Writes at other times have no
                                                     effect. Read/Write Access: - bit 10 is read only. - bit
                                                     11 is read only - bit 16 is read only - bit 17 is read
                                                     only - bits 18 and 19 are read only.                                      */
  __IOM uint32_t  IC_TAR;                       /*!< I2C Target Address Register This register is 12 bits wide, and
                                                     bits 31:12 are reserved. This register can be written to
                                                     only when IC_ENABLE[0] is set to 0. Note: If the software
                                                     or application is aware that the DW_apb_i2c is not using
                                                     the TAR address for the pending commands in the Tx FIFO,
                                                     then it is possible to update the TAR address even while
                                                     the Tx FIFO has entries (IC_STATUS[2]= 0). - It is not
                                                     necessary to perform any write to this register if DW_apb_i2c
                                                     is enabled as an I2C slave only.                                          */
  __IOM uint32_t  IC_SAR;                       /*!< I2C Slave Address Register                                                */
  __IM  uint32_t  RESERVED;
  __IOM uint32_t  IC_DATA_CMD;                  /*!< I2C Rx/Tx Data Buffer and Command Register; this is the register
                                                     the CPU writes to when filling the TX FIFO and the CPU
                                                     reads from when retrieving bytes from RX FIFO. The size
                                                     of the register changes as follows: Write: - 11 bits when
                                                     IC_EMPTYFIFO_HOLD_MASTER_EN=1 - 9 bits when IC_EMPTYFIFO_HOLD_MASTER_EN=0
                                                     Read: - 12 bits when IC_FIRST_DATA_BYTE_STATUS = 1 - 8
                                                     bits when IC_FIRST_DATA_BYTE_STATUS = 0 Note: In order
                                                     for the DW_apb_i2c to continue acknowledging reads, a read
                                                     command should be written for every byte that is to be
                                                     received; otherwise the DW_apb_i2c will stop acknowledging.               */
  __IOM uint32_t  IC_SS_SCL_HCNT;               /*!< Standard Speed I2C Clock SCL High Count Register                          */
  __IOM uint32_t  IC_SS_SCL_LCNT;               /*!< Standard Speed I2C Clock SCL Low Count Register                           */
  __IOM uint32_t  IC_FS_SCL_HCNT;               /*!< Fast Mode or Fast Mode Plus I2C Clock SCL High Count Register             */
  __IOM uint32_t  IC_FS_SCL_LCNT;               /*!< Fast Mode or Fast Mode Plus I2C Clock SCL Low Count Register              */
  __IM  uint32_t  RESERVED1[2];
  __IOM uint32_t  IC_INTR_STAT;                 /*!< I2C Interrupt Status Register Each bit in this register has
                                                     a corresponding mask bit in the IC_INTR_MASK register.
                                                     These bits are cleared by reading the matching interrupt
                                                     clear register. The unmasked raw versions of these bits
                                                     are available in the IC_RAW_INTR_STAT register.                           */
  __IOM uint32_t  IC_INTR_MASK;                 /*!< I2C Interrupt Mask Register. These bits mask their corresponding
                                                     interrupt status bits. This register is active low; a value
                                                     of 0 masks the interrupt, whereas a value of 1 unmasks
                                                     the interrupt.                                                            */
  __IOM uint32_t  IC_RAW_INTR_STAT;             /*!< I2C Raw Interrupt Status Register Unlike the IC_INTR_STAT register,
                                                     these bits are not masked so they always show the true
                                                     status of the DW_apb_i2c.                                                 */
  __IOM uint32_t  IC_RX_TL;                     /*!< I2C Receive FIFO Threshold Register                                       */
  __IOM uint32_t  IC_TX_TL;                     /*!< I2C Transmit FIFO Threshold Register                                      */
  __IOM uint32_t  IC_CLR_INTR;                  /*!< Clear Combined and Individual Interrupt Register                          */
  __IOM uint32_t  IC_CLR_RX_UNDER;              /*!< Clear RX_UNDER Interrupt Register                                         */
  __IOM uint32_t  IC_CLR_RX_OVER;               /*!< Clear RX_OVER Interrupt Register                                          */
  __IOM uint32_t  IC_CLR_TX_OVER;               /*!< Clear TX_OVER Interrupt Register                                          */
  __IOM uint32_t  IC_CLR_RD_REQ;                /*!< Clear RD_REQ Interrupt Register                                           */
  __IOM uint32_t  IC_CLR_TX_ABRT;               /*!< Clear TX_ABRT Interrupt Register                                          */
  __IOM uint32_t  IC_CLR_RX_DONE;               /*!< Clear RX_DONE Interrupt Register                                          */
  __IOM uint32_t  IC_CLR_ACTIVITY;              /*!< Clear ACTIVITY Interrupt Register                                         */
  __IOM uint32_t  IC_CLR_STOP_DET;              /*!< Clear STOP_DET Interrupt Register                                         */
  __IOM uint32_t  IC_CLR_START_DET;             /*!< Clear START_DET Interrupt Register                                        */
  __IOM uint32_t  IC_CLR_GEN_CALL;              /*!< Clear GEN_CALL Interrupt Register                                         */
  __IOM uint32_t  IC_ENABLE;                    /*!< I2C Enable Register                                                       */
  __IOM uint32_t  IC_STATUS;                    /*!< I2C Status Register This is a read-only register used to indicate
                                                     the current transfer status and FIFO status. The status
                                                     register may be read at any time. None of the bits in this
                                                     register request an interrupt. When the I2C is disabled
                                                     by writing 0 in bit 0 of the IC_ENABLE register: - Bits
                                                     1 and 2 are set to 1 - Bits 3 and 10 are set to 0 When
                                                     the master or slave state machines goes to idle and ic_en=0:
                                                     - Bits 5 and 6 are set to 0                                               */
  __IOM uint32_t  IC_TXFLR;                     /*!< I2C Transmit FIFO Level Register This register contains the
                                                     number of valid data entries in the transmit FIFO buffer.
                                                     It is cleared whenever: - The I2C is disabled - There is
                                                     a transmit abort - that is, TX_ABRT bit is set in the IC_RAW_INTR_STAT
                                                     register - The slave bulk transmit mode is aborted The
                                                     register increments whenever data is placed into the transmit
                                                     FIFO and decrements when data is taken from the transmit
                                                     FIFO.                                                                     */
  __IOM uint32_t  IC_RXFLR;                     /*!< I2C Receive FIFO Level Register This register contains the number
                                                     of valid data entries in the receive FIFO buffer. It is
                                                     cleared whenever: - The I2C is disabled - Whenever there
                                                     is a transmit abort caused by any of the events tracked
                                                     in IC_TX_ABRT_SOURCE The register increments whenever data
                                                     is placed into the receive FIFO and decrements when data
                                                     is taken from the receive FIFO.                                           */
  __IOM uint32_t  IC_SDA_HOLD;                  /*!< I2C SDA Hold Time Length Register The bits [15:0] of this register
                                                     are used to control the hold time of SDA during transmit
                                                     in both slave and master mode (after SCL goes from HIGH
                                                     to LOW). The bits [23:16] of this register are used to
                                                     extend the SDA transition (if any) whenever SCL is HIGH
                                                     in the receiver in either master or slave mode. Writes
                                                     to this register succeed only when IC_ENABLE[0]=0. The
                                                     values in this register are in units of ic_clk period.
                                                     The value programmed in IC_SDA_TX_HOLD must be greater
                                                     than the minimum hold time in each mode (one cycle in master
                                                     mode, seven cycles in slave mode) for the value to be implemented.
                                                     The programmed SDA hold time during transmit (IC_SDA_TX_HOLD)
                                                     cannot exceed at any time the duration of the low part
                                                     of scl. Therefore the programmed value cannot be larger
                                                     than N_SCL_LOW-2, where N_SCL_LOW is the duration of the
                                                     low part of the scl period measured in ic_clk cycles.                     */
  __IOM uint32_t  IC_TX_ABRT_SOURCE;            /*!< I2C Transmit Abort Source Register This register has 32 bits
                                                     that indicate the source of the TX_ABRT bit. Except for
                                                     Bit 9, this register is cleared whenever the IC_CLR_TX_ABRT
                                                     register or the IC_CLR_INTR register is read. To clear
                                                     Bit 9, the source of the ABRT_SBYTE_NORSTRT must be fixed
                                                     first; RESTART must be enabled (IC_CON[5]=1), the SPECIAL
                                                     bit must be cleared (IC_TAR[11]), or the GC_OR_START bit
                                                     must be cleared (IC_TAR[10]). Once the source of the ABRT_SBYTE_NORSTRT
                                                     is fixed, then this bit can be cleared in the same manner
                                                     as other bits in this register. If the source of the ABRT_SBYTE_NORSTRT
                                                     is not fixed before attempting to clear this bit, Bit 9
                                                     clears for one cycle and is then re-asserted.                             */
  __IOM uint32_t  IC_SLV_DATA_NACK_ONLY;        /*!< Generate Slave Data NACK Register The register is used to generate
                                                     a NACK for the data part of a transfer when DW_apb_i2c
                                                     is acting as a slave-receiver. This register only exists
                                                     when the IC_SLV_DATA_NACK_ONLY parameter is set to 1. When
                                                     this parameter disabled, this register does not exist and
                                                     writing to the register's address has no effect. A write
                                                     can occur on this register if both of the following conditions
                                                     are met: - DW_apb_i2c is disabled (IC_ENABLE[0] = 0) -
                                                     Slave part is inactive (IC_STATUS[6] = 0) Note: The IC_STATUS[6]
                                                     is a register read-back location for the internal slv_activity
                                                     signal; the user should poll this before writing the ic_slv_data_nack_onl
                                                     bit.                                                                      */
  __IOM uint32_t  IC_DMA_CR;                    /*!< DMA Control Register The register is used to enable the DMA
                                                     Controller interface operation. There is a separate bit
                                                     for transmit and receive. This can be programmed regardless
                                                     of the state of IC_ENABLE.                                                */
  __IOM uint32_t  IC_DMA_TDLR;                  /*!< DMA Transmit Data Level Register                                          */
  __IOM uint32_t  IC_DMA_RDLR;                  /*!< I2C Receive Data Level Register                                           */
  __IOM uint32_t  IC_SDA_SETUP;                 /*!< I2C SDA Setup Register This register controls the amount of
                                                     time delay (in terms of number of ic_clk clock periods)
                                                     introduced in the rising edge of SCL - relative to SDA
                                                     changing - when DW_apb_i2c services a read request in a
                                                     slave-transmitter operation. The relevant I2C requirement
                                                     is tSU:DAT (note 4) as detailed in the I2C Bus Specification.
                                                     This register must be programmed with a value equal to
                                                     or greater than 2. Writes to this register succeed only
                                                     when IC_ENABLE[0] = 0. Note: The length of setup time is
                                                     calculated using [(IC_SDA_SETUP - 1) * (ic_clk_period)],
                                                     so if the user requires 10 ic_clk periods of setup time,
                                                     they should program a value of 11. The IC_SDA_SETUP register
                                                     is only used by the DW_apb_i2c when operating as a slave
                                                     transmitter.                                                              */
  __IOM uint32_t  IC_ACK_GENERAL_CALL;          /*!< I2C ACK General Call Register The register controls whether
                                                     DW_apb_i2c responds with a ACK or NACK when it receives
                                                     an I2C General Call address. This register is applicable
                                                     only when the DW_apb_i2c is in slave mode.                                */
  __IOM uint32_t  IC_ENABLE_STATUS;             /*!< I2C Enable Status Register The register is used to report the
                                                     DW_apb_i2c hardware status when the IC_ENABLE[0] register
                                                     is set from 1 to 0; that is, when DW_apb_i2c is disabled.
                                                     If IC_ENABLE[0] has been set to 1, bits 2:1 are forced
                                                     to 0, and bit 0 is forced to 1. If IC_ENABLE[0] has been
                                                     set to 0, bits 2:1 is only be valid as soon as bit 0 is
                                                     read as '0'. Note: When IC_ENABLE[0] has been set to 0,
                                                     a delay occurs for bit 0 to be read as 0 because disabling
                                                     the DW_apb_i2c depends on I2C bus activities.                             */
  __IOM uint32_t  IC_FS_SPKLEN;                 /*!< I2C SS, FS or FM+ spike suppression limit This register is used
                                                     to store the duration, measured in ic_clk cycles, of the
                                                     longest spike that is filtered out by the spike suppression
                                                     logic when the component is operating in SS, FS or FM+
                                                     modes. The relevant I2C requirement is tSP (table 4) as
                                                     detailed in the I2C Bus Specification. This register must
                                                     be programmed with a minimum value of 1.                                  */
  __IM  uint32_t  RESERVED2;
  __IOM uint32_t  IC_CLR_RESTART_DET;           /*!< Clear RESTART_DET Interrupt Register                                      */
  __IM  uint32_t  RESERVED3[18];
  __IOM uint32_t  IC_COMP_PARAM_1;              /*!< Component Parameter Register 1 Note This register is not implemented
                                                     and therefore reads as 0. If it was implemented it would
                                                     be a constant read-only register that contains encoded
                                                     information about the component's parameter settings. Fields
                                                     shown below are the settings for those parameters                         */
  __IOM uint32_t  IC_COMP_VERSION;              /*!< I2C Component Version Register                                            */
  __IOM uint32_t  IC_COMP_TYPE;                 /*!< I2C Component Type Register                                               */
} I2C0_Type;                                    /*!< Size = 256 (0x100)                                                        */



/* =========================================================================================================================== */
/* ================                                           SPI0                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief SPI0 (SPI0)
  */

typedef struct {                                /*!< SPI0 Structure                                                            */
  __IOM uint32_t  SSPCR0;                       /*!< Control register 0, SSPCR0 on page 3-4                                    */
  __IOM uint32_t  SSPCR1;                       /*!< Control register 1, SSPCR1 on page 3-5                                    */
  __IOM uint32_t  SSPDR;                        /*!< Data register, SSPDR on page 3-6                                          */
  __IOM uint32_t  SSPSR;                        /*!< Status register, SSPSR on page 3-7                                        */
  __IOM uint32_t  SSPCPSR;                      /*!< Clock prescale register, SSPCPSR on page 3-8                              */
  __IOM uint32_t  SSPIMSC;                      /*!< Interrupt mask set or clear register, SSPIMSC on page 3-9                 */
  __IOM uint32_t  SSPRIS;                       /*!< Raw interrupt status register, SSPRIS on page 3-10                        */
  __IOM uint32_t  SSPMIS;                       /*!< Masked interrupt status register, SSPMIS on page 3-11                     */
  __IOM uint32_t  SSPICR;                       /*!< Interrupt clear register, SSPICR on page 3-11                             */
  __IOM uint32_t  SSPDMACR;                     /*!< DMA control register, SSPDMACR on page 3-12                               */
  __IM  uint32_t  RESERVED[1006];
  __IOM uint32_t  SSPPERIPHID0;                 /*!< Peripheral identification registers, SSPPeriphID0-3 on page
                                                     3-13                                                                      */
  __IOM uint32_t  SSPPERIPHID1;                 /*!< Peripheral identification registers, SSPPeriphID0-3 on page
                                                     3-13                                                                      */
  __IOM uint32_t  SSPPERIPHID2;                 /*!< Peripheral identification registers, SSPPeriphID0-3 on page
                                                     3-13                                                                      */
  __IOM uint32_t  SSPPERIPHID3;                 /*!< Peripheral identification registers, SSPPeriphID0-3 on page
                                                     3-13                                                                      */
  __IOM uint32_t  SSPPCELLID0;                  /*!< PrimeCell identification registers, SSPPCellID0-3 on page 3-16            */
  __IOM uint32_t  SSPPCELLID1;                  /*!< PrimeCell identification registers, SSPPCellID0-3 on page 3-16            */
  __IOM uint32_t  SSPPCELLID2;                  /*!< PrimeCell identification registers, SSPPCellID0-3 on page 3-16            */
  __IOM uint32_t  SSPPCELLID3;                  /*!< PrimeCell identification registers, SSPPCellID0-3 on page 3-16            */
} SPI0_Type;                                    /*!< Size = 4096 (0x1000)                                                      */



/* =========================================================================================================================== */
/* ================                                           PIO0                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Programmable IO block (PIO0)
  */

typedef struct {                                /*!< PIO0 Structure                                                            */
  __IOM uint32_t  CTRL;                         /*!< PIO control register                                                      */
  __IOM uint32_t  FSTAT;                        /*!< FIFO status register                                                      */
  __IOM uint32_t  FDEBUG;                       /*!< FIFO debug register                                                       */
  __IOM uint32_t  FLEVEL;                       /*!< FIFO levels                                                               */
  __IOM uint32_t  TXF0;                         /*!< Direct write access to the TX FIFO for this state machine. Each
                                                     write pushes one word to the FIFO. Attempting to write
                                                     to a full FIFO has no effect on the FIFO state or contents,
                                                     and sets the sticky FDEBUG_TXOVER error flag for this FIFO.               */
  __IOM uint32_t  TXF1;                         /*!< Direct write access to the TX FIFO for this state machine. Each
                                                     write pushes one word to the FIFO. Attempting to write
                                                     to a full FIFO has no effect on the FIFO state or contents,
                                                     and sets the sticky FDEBUG_TXOVER error flag for this FIFO.               */
  __IOM uint32_t  TXF2;                         /*!< Direct write access to the TX FIFO for this state machine. Each
                                                     write pushes one word to the FIFO. Attempting to write
                                                     to a full FIFO has no effect on the FIFO state or contents,
                                                     and sets the sticky FDEBUG_TXOVER error flag for this FIFO.               */
  __IOM uint32_t  TXF3;                         /*!< Direct write access to the TX FIFO for this state machine. Each
                                                     write pushes one word to the FIFO. Attempting to write
                                                     to a full FIFO has no effect on the FIFO state or contents,
                                                     and sets the sticky FDEBUG_TXOVER error flag for this FIFO.               */
  __IOM uint32_t  RXF0;                         /*!< Direct read access to the RX FIFO for this state machine. Each
                                                     read pops one word from the FIFO. Attempting to read from
                                                     an empty FIFO has no effect on the FIFO state, and sets
                                                     the sticky FDEBUG_RXUNDER error flag for this FIFO. The
                                                     data returned to the system on a read from an empty FIFO
                                                     is undefined.                                                             */
  __IOM uint32_t  RXF1;                         /*!< Direct read access to the RX FIFO for this state machine. Each
                                                     read pops one word from the FIFO. Attempting to read from
                                                     an empty FIFO has no effect on the FIFO state, and sets
                                                     the sticky FDEBUG_RXUNDER error flag for this FIFO. The
                                                     data returned to the system on a read from an empty FIFO
                                                     is undefined.                                                             */
  __IOM uint32_t  RXF2;                         /*!< Direct read access to the RX FIFO for this state machine. Each
                                                     read pops one word from the FIFO. Attempting to read from
                                                     an empty FIFO has no effect on the FIFO state, and sets
                                                     the sticky FDEBUG_RXUNDER error flag for this FIFO. The
                                                     data returned to the system on a read from an empty FIFO
                                                     is undefined.                                                             */
  __IOM uint32_t  RXF3;                         /*!< Direct read access to the RX FIFO for this state machine. Each
                                                     read pops one word from the FIFO. Attempting to read from
                                                     an empty FIFO has no effect on the FIFO state, and sets
                                                     the sticky FDEBUG_RXUNDER error flag for this FIFO. The
                                                     data returned to the system on a read from an empty FIFO
                                                     is undefined.                                                             */
  __IOM uint32_t  IRQ;                          /*!< State machine IRQ flags register. Write 1 to clear. There are
                                                     8 state machine IRQ flags, which can be set, cleared, and
                                                     waited on by the state machines. There's no fixed association
                                                     between flags and state machines -- any state machine can
                                                     use any flag. Any of the 8 flags can be used for timing
                                                     synchronisation between state machines, using IRQ and WAIT
                                                     instructions. The lower four of these flags are also routed
                                                     out to system-level interrupt requests, alongside FIFO
                                                     status interrupts -- see e.g. IRQ0_INTE.                                  */
  __IOM uint32_t  IRQ_FORCE;                    /*!< Writing a 1 to each of these bits will forcibly assert the corresponding
                                                     IRQ. Note this is different to the INTF register: writing
                                                     here affects PIO internal state. INTF just asserts the
                                                     processor-facing IRQ signal for testing ISRs, and is not
                                                     visible to the state machines.                                            */
  __IOM uint32_t  INPUT_SYNC_BYPASS;            /*!< There is a 2-flipflop synchronizer on each GPIO input, which
                                                     protects PIO logic from metastabilities. This increases
                                                     input delay, and for fast synchronous IO (e.g. SPI) these
                                                     synchronizers may need to be bypassed. Each bit in this
                                                     register corresponds to one GPIO. 0 -> input is synchronized
                                                     (default) 1 -> synchronizer is bypassed If in doubt, leave
                                                     this register as all zeroes.                                              */
  __IOM uint32_t  DBG_PADOUT;                   /*!< Read to sample the pad output values PIO is currently driving
                                                     to the GPIOs. On RP2040 there are 30 GPIOs, so the two
                                                     most significant bits are hardwired to 0.                                 */
  __IOM uint32_t  DBG_PADOE;                    /*!< Read to sample the pad output enables (direction) PIO is currently
                                                     driving to the GPIOs. On RP2040 there are 30 GPIOs, so
                                                     the two most significant bits are hardwired to 0.                         */
  __IOM uint32_t  DBG_CFGINFO;                  /*!< The PIO hardware has some free parameters that may vary between
                                                     chip products. These should be provided in the chip datasheet,
                                                     but are also exposed here.                                                */
  __IOM uint32_t  INSTR_MEM0;                   /*!< Write-only access to instruction memory location 0                        */
  __IOM uint32_t  INSTR_MEM1;                   /*!< Write-only access to instruction memory location 1                        */
  __IOM uint32_t  INSTR_MEM2;                   /*!< Write-only access to instruction memory location 2                        */
  __IOM uint32_t  INSTR_MEM3;                   /*!< Write-only access to instruction memory location 3                        */
  __IOM uint32_t  INSTR_MEM4;                   /*!< Write-only access to instruction memory location 4                        */
  __IOM uint32_t  INSTR_MEM5;                   /*!< Write-only access to instruction memory location 5                        */
  __IOM uint32_t  INSTR_MEM6;                   /*!< Write-only access to instruction memory location 6                        */
  __IOM uint32_t  INSTR_MEM7;                   /*!< Write-only access to instruction memory location 7                        */
  __IOM uint32_t  INSTR_MEM8;                   /*!< Write-only access to instruction memory location 8                        */
  __IOM uint32_t  INSTR_MEM9;                   /*!< Write-only access to instruction memory location 9                        */
  __IOM uint32_t  INSTR_MEM10;                  /*!< Write-only access to instruction memory location 10                       */
  __IOM uint32_t  INSTR_MEM11;                  /*!< Write-only access to instruction memory location 11                       */
  __IOM uint32_t  INSTR_MEM12;                  /*!< Write-only access to instruction memory location 12                       */
  __IOM uint32_t  INSTR_MEM13;                  /*!< Write-only access to instruction memory location 13                       */
  __IOM uint32_t  INSTR_MEM14;                  /*!< Write-only access to instruction memory location 14                       */
  __IOM uint32_t  INSTR_MEM15;                  /*!< Write-only access to instruction memory location 15                       */
  __IOM uint32_t  INSTR_MEM16;                  /*!< Write-only access to instruction memory location 16                       */
  __IOM uint32_t  INSTR_MEM17;                  /*!< Write-only access to instruction memory location 17                       */
  __IOM uint32_t  INSTR_MEM18;                  /*!< Write-only access to instruction memory location 18                       */
  __IOM uint32_t  INSTR_MEM19;                  /*!< Write-only access to instruction memory location 19                       */
  __IOM uint32_t  INSTR_MEM20;                  /*!< Write-only access to instruction memory location 20                       */
  __IOM uint32_t  INSTR_MEM21;                  /*!< Write-only access to instruction memory location 21                       */
  __IOM uint32_t  INSTR_MEM22;                  /*!< Write-only access to instruction memory location 22                       */
  __IOM uint32_t  INSTR_MEM23;                  /*!< Write-only access to instruction memory location 23                       */
  __IOM uint32_t  INSTR_MEM24;                  /*!< Write-only access to instruction memory location 24                       */
  __IOM uint32_t  INSTR_MEM25;                  /*!< Write-only access to instruction memory location 25                       */
  __IOM uint32_t  INSTR_MEM26;                  /*!< Write-only access to instruction memory location 26                       */
  __IOM uint32_t  INSTR_MEM27;                  /*!< Write-only access to instruction memory location 27                       */
  __IOM uint32_t  INSTR_MEM28;                  /*!< Write-only access to instruction memory location 28                       */
  __IOM uint32_t  INSTR_MEM29;                  /*!< Write-only access to instruction memory location 29                       */
  __IOM uint32_t  INSTR_MEM30;                  /*!< Write-only access to instruction memory location 30                       */
  __IOM uint32_t  INSTR_MEM31;                  /*!< Write-only access to instruction memory location 31                       */
  __IOM uint32_t  SM0_CLKDIV;                   /*!< Clock divisor register for state machine 0 Frequency = clock
                                                     freq / (CLKDIV_INT + CLKDIV_FRAC / 256)                                   */
  __IOM uint32_t  SM0_EXECCTRL;                 /*!< Execution/behavioural settings for state machine 0                        */
  __IOM uint32_t  SM0_SHIFTCTRL;                /*!< Control behaviour of the input/output shift registers for state
                                                     machine 0                                                                 */
  __IOM uint32_t  SM0_ADDR;                     /*!< Current instruction address of state machine 0                            */
  __IOM uint32_t  SM0_INSTR;                    /*!< Read to see the instruction currently addressed by state machine
                                                     0's program counter Write to execute an instruction immediately
                                                     (including jumps) and then resume execution.                              */
  __IOM uint32_t  SM0_PINCTRL;                  /*!< State machine pin control                                                 */
  __IOM uint32_t  SM1_CLKDIV;                   /*!< Clock divisor register for state machine 1 Frequency = clock
                                                     freq / (CLKDIV_INT + CLKDIV_FRAC / 256)                                   */
  __IOM uint32_t  SM1_EXECCTRL;                 /*!< Execution/behavioural settings for state machine 1                        */
  __IOM uint32_t  SM1_SHIFTCTRL;                /*!< Control behaviour of the input/output shift registers for state
                                                     machine 1                                                                 */
  __IOM uint32_t  SM1_ADDR;                     /*!< Current instruction address of state machine 1                            */
  __IOM uint32_t  SM1_INSTR;                    /*!< Read to see the instruction currently addressed by state machine
                                                     1's program counter Write to execute an instruction immediately
                                                     (including jumps) and then resume execution.                              */
  __IOM uint32_t  SM1_PINCTRL;                  /*!< State machine pin control                                                 */
  __IOM uint32_t  SM2_CLKDIV;                   /*!< Clock divisor register for state machine 2 Frequency = clock
                                                     freq / (CLKDIV_INT + CLKDIV_FRAC / 256)                                   */
  __IOM uint32_t  SM2_EXECCTRL;                 /*!< Execution/behavioural settings for state machine 2                        */
  __IOM uint32_t  SM2_SHIFTCTRL;                /*!< Control behaviour of the input/output shift registers for state
                                                     machine 2                                                                 */
  __IOM uint32_t  SM2_ADDR;                     /*!< Current instruction address of state machine 2                            */
  __IOM uint32_t  SM2_INSTR;                    /*!< Read to see the instruction currently addressed by state machine
                                                     2's program counter Write to execute an instruction immediately
                                                     (including jumps) and then resume execution.                              */
  __IOM uint32_t  SM2_PINCTRL;                  /*!< State machine pin control                                                 */
  __IOM uint32_t  SM3_CLKDIV;                   /*!< Clock divisor register for state machine 3 Frequency = clock
                                                     freq / (CLKDIV_INT + CLKDIV_FRAC / 256)                                   */
  __IOM uint32_t  SM3_EXECCTRL;                 /*!< Execution/behavioural settings for state machine 3                        */
  __IOM uint32_t  SM3_SHIFTCTRL;                /*!< Control behaviour of the input/output shift registers for state
                                                     machine 3                                                                 */
  __IOM uint32_t  SM3_ADDR;                     /*!< Current instruction address of state machine 3                            */
  __IOM uint32_t  SM3_INSTR;                    /*!< Read to see the instruction currently addressed by state machine
                                                     3's program counter Write to execute an instruction immediately
                                                     (including jumps) and then resume execution.                              */
  __IOM uint32_t  SM3_PINCTRL;                  /*!< State machine pin control                                                 */
  __IOM uint32_t  INTR;                         /*!< Raw Interrupts                                                            */
  __IOM uint32_t  IRQ0_INTE;                    /*!< Interrupt Enable for irq0                                                 */
  __IOM uint32_t  IRQ0_INTF;                    /*!< Interrupt Force for irq0                                                  */
  __IOM uint32_t  IRQ0_INTS;                    /*!< Interrupt status after masking & forcing for irq0                         */
  __IOM uint32_t  IRQ1_INTE;                    /*!< Interrupt Enable for irq1                                                 */
  __IOM uint32_t  IRQ1_INTF;                    /*!< Interrupt Force for irq1                                                  */
  __IOM uint32_t  IRQ1_INTS;                    /*!< Interrupt status after masking & forcing for irq1                         */
} PIO0_Type;                                    /*!< Size = 324 (0x144)                                                        */



/* =========================================================================================================================== */
/* ================                                          BUSCTRL                                          ================ */
/* =========================================================================================================================== */


/**
  * @brief Register block for busfabric control signals and performance counters (BUSCTRL)
  */

typedef struct {                                /*!< BUSCTRL Structure                                                         */
  __IOM uint32_t  BUS_PRIORITY;                 /*!< Set the priority of each master for bus arbitration.                      */
  __IOM uint32_t  BUS_PRIORITY_ACK;             /*!< Bus priority acknowledge                                                  */
  __IOM uint32_t  PERFCTR0;                     /*!< Bus fabric performance counter 0                                          */
  __IOM uint32_t  PERFSEL0;                     /*!< Bus fabric performance event select for PERFCTR0                          */
  __IOM uint32_t  PERFCTR1;                     /*!< Bus fabric performance counter 1                                          */
  __IOM uint32_t  PERFSEL1;                     /*!< Bus fabric performance event select for PERFCTR1                          */
  __IOM uint32_t  PERFCTR2;                     /*!< Bus fabric performance counter 2                                          */
  __IOM uint32_t  PERFSEL2;                     /*!< Bus fabric performance event select for PERFCTR2                          */
  __IOM uint32_t  PERFCTR3;                     /*!< Bus fabric performance counter 3                                          */
  __IOM uint32_t  PERFSEL3;                     /*!< Bus fabric performance event select for PERFCTR3                          */
} BUSCTRL_Type;                                 /*!< Size = 40 (0x28)                                                          */



/* =========================================================================================================================== */
/* ================                                            SIO                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Single-cycle IO block 
            Provides core-local and inter-core hardware for the two processors, with single-cycle access. (SIO)
  */

typedef struct {                                /*!< SIO Structure                                                             */
  __IOM uint32_t  CPUID;                        /*!< Processor core identifier                                                 */
  __IOM uint32_t  GPIO_IN;                      /*!< Input value for GPIO pins                                                 */
  __IOM uint32_t  GPIO_HI_IN;                   /*!< Input value for QSPI pins                                                 */
  __IM  uint32_t  RESERVED;
  __IOM uint32_t  GPIO_OUT;                     /*!< GPIO output value                                                         */
  __IOM uint32_t  GPIO_OUT_SET;                 /*!< GPIO output value set                                                     */
  __IOM uint32_t  GPIO_OUT_CLR;                 /*!< GPIO output value clear                                                   */
  __IOM uint32_t  GPIO_OUT_XOR;                 /*!< GPIO output value XOR                                                     */
  __IOM uint32_t  GPIO_OE;                      /*!< GPIO output enable                                                        */
  __IOM uint32_t  GPIO_OE_SET;                  /*!< GPIO output enable set                                                    */
  __IOM uint32_t  GPIO_OE_CLR;                  /*!< GPIO output enable clear                                                  */
  __IOM uint32_t  GPIO_OE_XOR;                  /*!< GPIO output enable XOR                                                    */
  __IOM uint32_t  GPIO_HI_OUT;                  /*!< QSPI output value                                                         */
  __IOM uint32_t  GPIO_HI_OUT_SET;              /*!< QSPI output value set                                                     */
  __IOM uint32_t  GPIO_HI_OUT_CLR;              /*!< QSPI output value clear                                                   */
  __IOM uint32_t  GPIO_HI_OUT_XOR;              /*!< QSPI output value XOR                                                     */
  __IOM uint32_t  GPIO_HI_OE;                   /*!< QSPI output enable                                                        */
  __IOM uint32_t  GPIO_HI_OE_SET;               /*!< QSPI output enable set                                                    */
  __IOM uint32_t  GPIO_HI_OE_CLR;               /*!< QSPI output enable clear                                                  */
  __IOM uint32_t  GPIO_HI_OE_XOR;               /*!< QSPI output enable XOR                                                    */
  __IOM uint32_t  FIFO_ST;                      /*!< Status register for inter-core FIFOs (mailboxes). There is one
                                                     FIFO in the core 0 -> core 1 direction, and one core 1
                                                     -> core 0. Both are 32 bits wide and 8 words deep. Core
                                                     0 can see the read side of the 1->0 FIFO (RX), and the
                                                     write side of 0->1 FIFO (TX). Core 1 can see the read side
                                                     of the 0->1 FIFO (RX), and the write side of 1->0 FIFO
                                                     (TX). The SIO IRQ for each core is the logical OR of the
                                                     VLD, WOF and ROE fields of its FIFO_ST register.                          */
  __IOM uint32_t  FIFO_WR;                      /*!< Write access to this core's TX FIFO                                       */
  __IOM uint32_t  FIFO_RD;                      /*!< Read access to this core's RX FIFO                                        */
  __IOM uint32_t  SPINLOCK_ST;                  /*!< Spinlock state A bitmap containing the state of all 32 spinlocks
                                                     (1=locked). Mainly intended for debugging.                                */
  __IOM uint32_t  DIV_UDIVIDEND;                /*!< Divider unsigned dividend Write to the DIVIDEND operand of the
                                                     divider, i.e. the p in `p / q`. Any operand write starts
                                                     a new calculation. The results appear in QUOTIENT, REMAINDER.
                                                     UDIVIDEND/SDIVIDEND are aliases of the same internal register.
                                                     The U alias starts an unsigned calculation, and the S alias
                                                     starts a signed calculation.                                              */
  __IOM uint32_t  DIV_UDIVISOR;                 /*!< Divider unsigned divisor Write to the DIVISOR operand of the
                                                     divider, i.e. the q in `p / q`. Any operand write starts
                                                     a new calculation. The results appear in QUOTIENT, REMAINDER.
                                                     UDIVISOR/SDIVISOR are aliases of the same internal register.
                                                     The U alias starts an unsigned calculation, and the S alias
                                                     starts a signed calculation.                                              */
  __IOM uint32_t  DIV_SDIVIDEND;                /*!< Divider signed dividend The same as UDIVIDEND, but starts a
                                                     signed calculation, rather than unsigned.                                 */
  __IOM uint32_t  DIV_SDIVISOR;                 /*!< Divider signed divisor The same as UDIVISOR, but starts a signed
                                                     calculation, rather than unsigned.                                        */
  __IOM uint32_t  DIV_QUOTIENT;                 /*!< Divider result quotient The result of `DIVIDEND / DIVISOR` (division).
                                                     Contents undefined while CSR_READY is low. For signed calculations,
                                                     QUOTIENT is negative when the signs of DIVIDEND and DIVISOR
                                                     differ. This register can be written to directly, for context
                                                     save/restore purposes. This halts any in-progress calculation
                                                     and sets the CSR_READY and CSR_DIRTY flags. Reading from
                                                     QUOTIENT clears the CSR_DIRTY flag, so should read results
                                                     in the order REMAINDER, QUOTIENT if CSR_DIRTY is used.                    */
  __IOM uint32_t  DIV_REMAINDER;                /*!< Divider result remainder The result of `DIVIDEND % DIVISOR`
                                                     (modulo). Contents undefined while CSR_READY is low. For
                                                     signed calculations, REMAINDER is negative only when DIVIDEND
                                                     is negative. This register can be written to directly,
                                                     for context save/restore purposes. This halts any in-progress
                                                     calculation and sets the CSR_READY and CSR_DIRTY flags.                   */
  __IOM uint32_t  DIV_CSR;                      /*!< Control and status register for divider.                                  */
  __IM  uint32_t  RESERVED1;
  __IOM uint32_t  INTERP0_ACCUM0;               /*!< Read/write access to accumulator 0                                        */
  __IOM uint32_t  INTERP0_ACCUM1;               /*!< Read/write access to accumulator 1                                        */
  __IOM uint32_t  INTERP0_BASE0;                /*!< Read/write access to BASE0 register.                                      */
  __IOM uint32_t  INTERP0_BASE1;                /*!< Read/write access to BASE1 register.                                      */
  __IOM uint32_t  INTERP0_BASE2;                /*!< Read/write access to BASE2 register.                                      */
  __IOM uint32_t  INTERP0_POP_LANE0;            /*!< Read LANE0 result, and simultaneously write lane results to
                                                     both accumulators (POP).                                                  */
  __IOM uint32_t  INTERP0_POP_LANE1;            /*!< Read LANE1 result, and simultaneously write lane results to
                                                     both accumulators (POP).                                                  */
  __IOM uint32_t  INTERP0_POP_FULL;             /*!< Read FULL result, and simultaneously write lane results to both
                                                     accumulators (POP).                                                       */
  __IOM uint32_t  INTERP0_PEEK_LANE0;           /*!< Read LANE0 result, without altering any internal state (PEEK).            */
  __IOM uint32_t  INTERP0_PEEK_LANE1;           /*!< Read LANE1 result, without altering any internal state (PEEK).            */
  __IOM uint32_t  INTERP0_PEEK_FULL;            /*!< Read FULL result, without altering any internal state (PEEK).             */
  __IOM uint32_t  INTERP0_CTRL_LANE0;           /*!< Control register for lane 0                                               */
  __IOM uint32_t  INTERP0_CTRL_LANE1;           /*!< Control register for lane 1                                               */
  __IOM uint32_t  INTERP0_ACCUM0_ADD;           /*!< Values written here are atomically added to ACCUM0 Reading yields
                                                     lane 0's raw shift and mask value (BASE0 not added).                      */
  __IOM uint32_t  INTERP0_ACCUM1_ADD;           /*!< Values written here are atomically added to ACCUM1 Reading yields
                                                     lane 1's raw shift and mask value (BASE1 not added).                      */
  __IOM uint32_t  INTERP0_BASE_1AND0;           /*!< On write, the lower 16 bits go to BASE0, upper bits to BASE1
                                                     simultaneously. Each half is sign-extended to 32 bits if
                                                     that lane's SIGNED flag is set.                                           */
  __IOM uint32_t  INTERP1_ACCUM0;               /*!< Read/write access to accumulator 0                                        */
  __IOM uint32_t  INTERP1_ACCUM1;               /*!< Read/write access to accumulator 1                                        */
  __IOM uint32_t  INTERP1_BASE0;                /*!< Read/write access to BASE0 register.                                      */
  __IOM uint32_t  INTERP1_BASE1;                /*!< Read/write access to BASE1 register.                                      */
  __IOM uint32_t  INTERP1_BASE2;                /*!< Read/write access to BASE2 register.                                      */
  __IOM uint32_t  INTERP1_POP_LANE0;            /*!< Read LANE0 result, and simultaneously write lane results to
                                                     both accumulators (POP).                                                  */
  __IOM uint32_t  INTERP1_POP_LANE1;            /*!< Read LANE1 result, and simultaneously write lane results to
                                                     both accumulators (POP).                                                  */
  __IOM uint32_t  INTERP1_POP_FULL;             /*!< Read FULL result, and simultaneously write lane results to both
                                                     accumulators (POP).                                                       */
  __IOM uint32_t  INTERP1_PEEK_LANE0;           /*!< Read LANE0 result, without altering any internal state (PEEK).            */
  __IOM uint32_t  INTERP1_PEEK_LANE1;           /*!< Read LANE1 result, without altering any internal state (PEEK).            */
  __IOM uint32_t  INTERP1_PEEK_FULL;            /*!< Read FULL result, without altering any internal state (PEEK).             */
  __IOM uint32_t  INTERP1_CTRL_LANE0;           /*!< Control register for lane 0                                               */
  __IOM uint32_t  INTERP1_CTRL_LANE1;           /*!< Control register for lane 1                                               */
  __IOM uint32_t  INTERP1_ACCUM0_ADD;           /*!< Values written here are atomically added to ACCUM0 Reading yields
                                                     lane 0's raw shift and mask value (BASE0 not added).                      */
  __IOM uint32_t  INTERP1_ACCUM1_ADD;           /*!< Values written here are atomically added to ACCUM1 Reading yields
                                                     lane 1's raw shift and mask value (BASE1 not added).                      */
  __IOM uint32_t  INTERP1_BASE_1AND0;           /*!< On write, the lower 16 bits go to BASE0, upper bits to BASE1
                                                     simultaneously. Each half is sign-extended to 32 bits if
                                                     that lane's SIGNED flag is set.                                           */
  __IOM uint32_t  SPINLOCK0;                    /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK1;                    /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK2;                    /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK3;                    /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK4;                    /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK5;                    /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK6;                    /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK7;                    /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK8;                    /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK9;                    /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK10;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK11;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK12;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK13;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK14;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK15;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK16;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK17;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK18;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK19;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK20;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK21;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK22;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK23;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK24;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK25;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK26;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK27;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK28;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK29;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK30;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
  __IOM uint32_t  SPINLOCK31;                   /*!< Reading from a spinlock address will: - Return 0 if lock is
                                                     already locked - Otherwise return nonzero, and simultaneously
                                                     claim the lock Writing (any value) releases the lock. If
                                                     core 0 and core 1 attempt to claim the same lock simultaneously,
                                                     core 0 wins. The value returned on success is 0x1 << lock
                                                     number.                                                                   */
} SIO_Type;                                     /*!< Size = 384 (0x180)                                                        */



/* =========================================================================================================================== */
/* ================                                            USB                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief USB FS/LS controller device registers (USB)
  */

typedef struct {                                /*!< USB Structure                                                             */
  __IOM uint32_t  ADDR_ENDP;                    /*!< Device address and endpoint control                                       */
  __IOM uint32_t  ADDR_ENDP1;                   /*!< Interrupt endpoint 1. Only valid for HOST mode.                           */
  __IOM uint32_t  ADDR_ENDP2;                   /*!< Interrupt endpoint 2. Only valid for HOST mode.                           */
  __IOM uint32_t  ADDR_ENDP3;                   /*!< Interrupt endpoint 3. Only valid for HOST mode.                           */
  __IOM uint32_t  ADDR_ENDP4;                   /*!< Interrupt endpoint 4. Only valid for HOST mode.                           */
  __IOM uint32_t  ADDR_ENDP5;                   /*!< Interrupt endpoint 5. Only valid for HOST mode.                           */
  __IOM uint32_t  ADDR_ENDP6;                   /*!< Interrupt endpoint 6. Only valid for HOST mode.                           */
  __IOM uint32_t  ADDR_ENDP7;                   /*!< Interrupt endpoint 7. Only valid for HOST mode.                           */
  __IOM uint32_t  ADDR_ENDP8;                   /*!< Interrupt endpoint 8. Only valid for HOST mode.                           */
  __IOM uint32_t  ADDR_ENDP9;                   /*!< Interrupt endpoint 9. Only valid for HOST mode.                           */
  __IOM uint32_t  ADDR_ENDP10;                  /*!< Interrupt endpoint 10. Only valid for HOST mode.                          */
  __IOM uint32_t  ADDR_ENDP11;                  /*!< Interrupt endpoint 11. Only valid for HOST mode.                          */
  __IOM uint32_t  ADDR_ENDP12;                  /*!< Interrupt endpoint 12. Only valid for HOST mode.                          */
  __IOM uint32_t  ADDR_ENDP13;                  /*!< Interrupt endpoint 13. Only valid for HOST mode.                          */
  __IOM uint32_t  ADDR_ENDP14;                  /*!< Interrupt endpoint 14. Only valid for HOST mode.                          */
  __IOM uint32_t  ADDR_ENDP15;                  /*!< Interrupt endpoint 15. Only valid for HOST mode.                          */
  __IOM uint32_t  MAIN_CTRL;                    /*!< Main control register                                                     */
  __IOM uint32_t  SOF_WR;                       /*!< Set the SOF (Start of Frame) frame number in the host controller.
                                                     The SOF packet is sent every 1ms and the host will increment
                                                     the frame number by 1 each time.                                          */
  __IOM uint32_t  SOF_RD;                       /*!< Read the last SOF (Start of Frame) frame number seen. In device
                                                     mode the last SOF received from the host. In host mode
                                                     the last SOF sent by the host.                                            */
  __IOM uint32_t  SIE_CTRL;                     /*!< SIE control register                                                      */
  __IOM uint32_t  SIE_STATUS;                   /*!< SIE status register                                                       */
  __IOM uint32_t  INT_EP_CTRL;                  /*!< interrupt endpoint control register                                       */
  __IOM uint32_t  BUFF_STATUS;                  /*!< Buffer status register. A bit set here indicates that a buffer
                                                     has completed on the endpoint (if the buffer interrupt
                                                     is enabled). It is possible for 2 buffers to be completed,
                                                     so clearing the buffer status bit may instantly re set
                                                     it on the next clock cycle.                                               */
  __IOM uint32_t  BUFF_CPU_SHOULD_HANDLE;       /*!< Which of the double buffers should be handled. Only valid if
                                                     using an interrupt per buffer (i.e. not per 2 buffers).
                                                     Not valid for host interrupt endpoint polling because they
                                                     are only single buffered.                                                 */
  __IOM uint32_t  EP_ABORT;                     /*!< Device only: Can be set to ignore the buffer control register
                                                     for this endpoint in case you would like to revoke a buffer.
                                                     A NAK will be sent for every access to the endpoint until
                                                     this bit is cleared. A corresponding bit in `EP_ABORT_DONE`
                                                     is set when it is safe to modify the buffer control register.             */
  __IOM uint32_t  EP_ABORT_DONE;                /*!< Device only: Used in conjunction with `EP_ABORT`. Set once an
                                                     endpoint is idle so the programmer knows it is safe to
                                                     modify the buffer control register.                                       */
  __IOM uint32_t  EP_STALL_ARM;                 /*!< Device: this bit must be set in conjunction with the `STALL`
                                                     bit in the buffer control register to send a STALL on EP0.
                                                     The device controller clears these bits when a SETUP packet
                                                     is received because the USB spec requires that a STALL
                                                     condition is cleared when a SETUP packet is received.                     */
  __IOM uint32_t  NAK_POLL;                     /*!< Used by the host controller. Sets the wait time in microseconds
                                                     before trying again if the device replies with a NAK.                     */
  __IOM uint32_t  EP_STATUS_STALL_NAK;          /*!< Device: bits are set when the `IRQ_ON_NAK` or `IRQ_ON_STALL`
                                                     bits are set. For EP0 this comes from `SIE_CTRL`. For all
                                                     other endpoints it comes from the endpoint control register.              */
  __IOM uint32_t  USB_MUXING;                   /*!< Where to connect the USB controller. Should be to_phy by default.         */
  __IOM uint32_t  USB_PWR;                      /*!< Overrides for the power signals in the event that the VBUS signals
                                                     are not hooked up to GPIO. Set the value of the override
                                                     and then the override enable so switch over to the override
                                                     value.                                                                    */
  __IOM uint32_t  USBPHY_DIRECT;                /*!< Note that most functions are driven directly from usb_fsls controller.
                                                     This register allows more detailed control/status from
                                                     the USB PHY. Useful for debug but not expected to be used
                                                     in normal operation Use in conjunction with usbphy_direct_override
                                                     register                                                                  */
  __IOM uint32_t  USBPHY_DIRECT_OVERRIDE;       /*!< USBPHY_DIRECT_OVERRIDE                                                    */
  __IOM uint32_t  USBPHY_TRIM;                  /*!< Note that most functions are driven directly from usb_fsls controller.
                                                     This register allows more detailed control/status from
                                                     the USB PHY. Useful for debug but not expected to be used
                                                     in normal operation                                                       */
  __IM  uint32_t  RESERVED;
  __IOM uint32_t  INTR;                         /*!< Raw Interrupts                                                            */
  __IOM uint32_t  INTE;                         /*!< Interrupt Enable                                                          */
  __IOM uint32_t  INTF;                         /*!< Interrupt Force                                                           */
  __IOM uint32_t  INTS;                         /*!< Interrupt status after masking & forcing                                  */
} USB_Type;                                     /*!< Size = 156 (0x9c)                                                         */



/* =========================================================================================================================== */
/* ================                                         USB_DPRAM                                         ================ */
/* =========================================================================================================================== */


/**
  * @brief DPRAM layout for USB device. (USB_DPRAM)
  */

typedef struct {                                /*!< USB_DPRAM Structure                                                       */
  __IOM uint32_t  SETUP_PACKET_LOW;             /*!< Bytes 0-3 of the SETUP packet from the host.                              */
  __IOM uint32_t  SETUP_PACKET_HIGH;            /*!< Bytes 4-7 of the setup packet from the host.                              */
  __IOM uint32_t  EP1_IN_CONTROL;               /*!< EP1_IN_CONTROL                                                            */
  __IOM uint32_t  EP1_OUT_CONTROL;              /*!< EP1_OUT_CONTROL                                                           */
  __IOM uint32_t  EP2_IN_CONTROL;               /*!< EP2_IN_CONTROL                                                            */
  __IOM uint32_t  EP2_OUT_CONTROL;              /*!< EP2_OUT_CONTROL                                                           */
  __IOM uint32_t  EP3_IN_CONTROL;               /*!< EP3_IN_CONTROL                                                            */
  __IOM uint32_t  EP3_OUT_CONTROL;              /*!< EP3_OUT_CONTROL                                                           */
  __IOM uint32_t  EP4_IN_CONTROL;               /*!< EP4_IN_CONTROL                                                            */
  __IOM uint32_t  EP4_OUT_CONTROL;              /*!< EP4_OUT_CONTROL                                                           */
  __IOM uint32_t  EP5_IN_CONTROL;               /*!< EP5_IN_CONTROL                                                            */
  __IOM uint32_t  EP5_OUT_CONTROL;              /*!< EP5_OUT_CONTROL                                                           */
  __IOM uint32_t  EP6_IN_CONTROL;               /*!< EP6_IN_CONTROL                                                            */
  __IOM uint32_t  EP6_OUT_CONTROL;              /*!< EP6_OUT_CONTROL                                                           */
  __IOM uint32_t  EP7_IN_CONTROL;               /*!< EP7_IN_CONTROL                                                            */
  __IOM uint32_t  EP7_OUT_CONTROL;              /*!< EP7_OUT_CONTROL                                                           */
  __IOM uint32_t  EP8_IN_CONTROL;               /*!< EP8_IN_CONTROL                                                            */
  __IOM uint32_t  EP8_OUT_CONTROL;              /*!< EP8_OUT_CONTROL                                                           */
  __IOM uint32_t  EP9_IN_CONTROL;               /*!< EP9_IN_CONTROL                                                            */
  __IOM uint32_t  EP9_OUT_CONTROL;              /*!< EP9_OUT_CONTROL                                                           */
  __IOM uint32_t  EP10_IN_CONTROL;              /*!< EP10_IN_CONTROL                                                           */
  __IOM uint32_t  EP10_OUT_CONTROL;             /*!< EP10_OUT_CONTROL                                                          */
  __IOM uint32_t  EP11_IN_CONTROL;              /*!< EP11_IN_CONTROL                                                           */
  __IOM uint32_t  EP11_OUT_CONTROL;             /*!< EP11_OUT_CONTROL                                                          */
  __IOM uint32_t  EP12_IN_CONTROL;              /*!< EP12_IN_CONTROL                                                           */
  __IOM uint32_t  EP12_OUT_CONTROL;             /*!< EP12_OUT_CONTROL                                                          */
  __IOM uint32_t  EP13_IN_CONTROL;              /*!< EP13_IN_CONTROL                                                           */
  __IOM uint32_t  EP13_OUT_CONTROL;             /*!< EP13_OUT_CONTROL                                                          */
  __IOM uint32_t  EP14_IN_CONTROL;              /*!< EP14_IN_CONTROL                                                           */
  __IOM uint32_t  EP14_OUT_CONTROL;             /*!< EP14_OUT_CONTROL                                                          */
  __IOM uint32_t  EP15_IN_CONTROL;              /*!< EP15_IN_CONTROL                                                           */
  __IOM uint32_t  EP15_OUT_CONTROL;             /*!< EP15_OUT_CONTROL                                                          */
  __IOM uint32_t  EP0_IN_BUFFER_CONTROL;        /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP0_OUT_BUFFER_CONTROL;       /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP1_IN_BUFFER_CONTROL;        /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP1_OUT_BUFFER_CONTROL;       /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP2_IN_BUFFER_CONTROL;        /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP2_OUT_BUFFER_CONTROL;       /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP3_IN_BUFFER_CONTROL;        /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP3_OUT_BUFFER_CONTROL;       /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP4_IN_BUFFER_CONTROL;        /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP4_OUT_BUFFER_CONTROL;       /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP5_IN_BUFFER_CONTROL;        /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP5_OUT_BUFFER_CONTROL;       /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP6_IN_BUFFER_CONTROL;        /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP6_OUT_BUFFER_CONTROL;       /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP7_IN_BUFFER_CONTROL;        /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP7_OUT_BUFFER_CONTROL;       /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP8_IN_BUFFER_CONTROL;        /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP8_OUT_BUFFER_CONTROL;       /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP9_IN_BUFFER_CONTROL;        /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP9_OUT_BUFFER_CONTROL;       /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP10_IN_BUFFER_CONTROL;       /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP10_OUT_BUFFER_CONTROL;      /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP11_IN_BUFFER_CONTROL;       /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP11_OUT_BUFFER_CONTROL;      /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP12_IN_BUFFER_CONTROL;       /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP12_OUT_BUFFER_CONTROL;      /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP13_IN_BUFFER_CONTROL;       /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP13_OUT_BUFFER_CONTROL;      /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP14_IN_BUFFER_CONTROL;       /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP14_OUT_BUFFER_CONTROL;      /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP15_IN_BUFFER_CONTROL;       /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
  __IOM uint32_t  EP15_OUT_BUFFER_CONTROL;      /*!< Buffer control for both buffers of an endpoint. Fields ending
                                                     in a _1 are for buffer 1. Fields ending in a _0 are for
                                                     buffer 0. Buffer 1 controls are only valid if the endpoint
                                                     is in double buffered mode.                                               */
} USB_DPRAM_Type;                               /*!< Size = 256 (0x100)                                                        */



/* =========================================================================================================================== */
/* ================                                           TBMAN                                           ================ */
/* =========================================================================================================================== */


/**
  * @brief Testbench manager. Allows the programmer to know what platform their software is running on. (TBMAN)
  */

typedef struct {                                /*!< TBMAN Structure                                                           */
  __IOM uint32_t  PLATFORM;                     /*!< Indicates the type of platform in use                                     */
} TBMAN_Type;                                   /*!< Size = 4 (0x4)                                                            */



/* =========================================================================================================================== */
/* ================                                    VREG_AND_CHIP_RESET                                    ================ */
/* =========================================================================================================================== */


/**
  * @brief control and status for on-chip voltage regulator and chip level reset subsystem (VREG_AND_CHIP_RESET)
  */

typedef struct {                                /*!< VREG_AND_CHIP_RESET Structure                                             */
  __IOM uint32_t  VREG;                         /*!< Voltage regulator control and status                                      */
  __IOM uint32_t  BOD;                          /*!< brown-out detection control                                               */
  __IOM uint32_t  CHIP_RESET;                   /*!< Chip reset control and status                                             */
} VREG_AND_CHIP_RESET_Type;                     /*!< Size = 12 (0xc)                                                           */



/* =========================================================================================================================== */
/* ================                                            RTC                                            ================ */
/* =========================================================================================================================== */


/**
  * @brief Register block to control RTC (RTC)
  */

typedef struct {                                /*!< RTC Structure                                                             */
  __IOM uint32_t  CLKDIV_M1;                    /*!< Divider minus 1 for the 1 second counter. Safe to change the
                                                     value when RTC is not enabled.                                            */
  __IOM uint32_t  SETUP_0;                      /*!< RTC setup register 0                                                      */
  __IOM uint32_t  SETUP_1;                      /*!< RTC setup register 1                                                      */
  __IOM uint32_t  CTRL;                         /*!< RTC Control and status                                                    */
  __IOM uint32_t  IRQ_SETUP_0;                  /*!< Interrupt setup register 0                                                */
  __IOM uint32_t  IRQ_SETUP_1;                  /*!< Interrupt setup register 1                                                */
  __IOM uint32_t  RTC_1;                        /*!< RTC register 1.                                                           */
  __IOM uint32_t  RTC_0;                        /*!< RTC register 0 Read this before RTC 1!                                    */
  __IOM uint32_t  INTR;                         /*!< Raw Interrupts                                                            */
  __IOM uint32_t  INTE;                         /*!< Interrupt Enable                                                          */
  __IOM uint32_t  INTF;                         /*!< Interrupt Force                                                           */
  __IOM uint32_t  INTS;                         /*!< Interrupt status after masking & forcing                                  */
} RTC_Type;                                     /*!< Size = 48 (0x30)                                                          */


/** @} */ /* End of group Device_Peripheral_peripherals */


/* =========================================================================================================================== */
/* ================                          Device Specific Peripheral Address Map                           ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_peripheralAddr
  * @{
  */

#if 0
#define RESETS_BASE                 0x4000C000UL
#define PSM_BASE                    0x40010000UL
#define CLOCKS_BASE                 0x40008000UL
#define PADS_BANK0_BASE             0x4001C000UL
#define PADS_QSPI_BASE              0x40020000UL
#define IO_QSPI_BASE                0x40018000UL
#define IO_BANK0_BASE               0x40014000UL
#define SYSINFO_BASE                0x40000000UL
#define PPB_BASE                    0xE0000000UL
#define SSI_BASE                    0x18000000UL
#define XIP_CTRL_BASE               0x14000000UL
#define SYSCFG_BASE                 0x40004000UL
#define XOSC_BASE                   0x40024000UL
#define PLL_SYS_BASE                0x40028000UL
#define PLL_USB_BASE                0x4002C000UL
#define UART0_BASE                  0x40034000UL
#define UART1_BASE                  0x40038000UL
#define ROSC_BASE                   0x40060000UL
#define WATCHDOG_BASE               0x40058000UL
#define DMA_BASE                    0x50000000UL
#define TIMER_BASE                  0x40054000UL
#define PWM_BASE                    0x40050000UL
#define ADC_BASE                    0x4004C000UL
#define I2C0_BASE                   0x40044000UL
#define I2C1_BASE                   0x40048000UL
#define SPI0_BASE                   0x4003C000UL
#define SPI1_BASE                   0x40040000UL
#define PIO0_BASE                   0x50200000UL
#define PIO1_BASE                   0x50300000UL
#define BUSCTRL_BASE                0x40030000UL
#define SIO_BASE                    0xD0000000UL
#define USB_BASE                    0x50110000UL
#define USB_DPRAM_BASE              0x50100000UL
#define TBMAN_BASE                  0x4006C000UL
#define VREG_AND_CHIP_RESET_BASE    0x40064000UL
#define RTC_BASE                    0x4005C000UL
#endif

/** @} */ /* End of group Device_Peripheral_peripheralAddr */


/* =========================================================================================================================== */
/* ================                                  Peripheral declaration                                   ================ */
/* =========================================================================================================================== */


/** @addtogroup Device_Peripheral_declaration
  * @{
  */

#define RESETS                      ((RESETS_Type*)            RESETS_BASE)
#define PSM                         ((PSM_Type*)               PSM_BASE)
#define CLOCKS                      ((CLOCKS_Type*)            CLOCKS_BASE)
#define PADS_BANK0                  ((PADS_BANK0_Type*)        PADS_BANK0_BASE)
#define PADS_QSPI                   ((PADS_QSPI_Type*)         PADS_QSPI_BASE)
#define IO_QSPI                     ((IO_QSPI_Type*)           IO_QSPI_BASE)
#define IO_BANK0                    ((IO_BANK0_Type*)          IO_BANK0_BASE)
#define SYSINFO                     ((SYSINFO_Type*)           SYSINFO_BASE)
#define PPB                         ((PPB_Type*)               PPB_BASE)
#define SSI                         ((SSI_Type*)               SSI_BASE)
#define XIP_CTRL                    ((XIP_CTRL_Type*)          XIP_CTRL_BASE)
#define SYSCFG                      ((SYSCFG_Type*)            SYSCFG_BASE)
#define XOSC                        ((XOSC_Type*)              XOSC_BASE)
#define PLL_SYS                     ((PLL_SYS_Type*)           PLL_SYS_BASE)
#define PLL_USB                     ((PLL_SYS_Type*)           PLL_USB_BASE)
#define UART0                       ((UART0_Type*)             UART0_BASE)
#define UART1                       ((UART0_Type*)             UART1_BASE)
#define ROSC                        ((ROSC_Type*)              ROSC_BASE)
#define WATCHDOG                    ((WATCHDOG_Type*)          WATCHDOG_BASE)
#define DMA                         ((DMA_Type*)               DMA_BASE)
#define TIMER                       ((TIMER_Type*)             TIMER_BASE)
#define PWM                         ((PWM_Type*)               PWM_BASE)
#define ADC                         ((ADC_Type*)               ADC_BASE)
#define I2C0                        ((I2C0_Type*)              I2C0_BASE)
#define I2C1                        ((I2C0_Type*)              I2C1_BASE)
#define SPI0                        ((SPI0_Type*)              SPI0_BASE)
#define SPI1                        ((SPI0_Type*)              SPI1_BASE)
#define PIO0                        ((PIO0_Type*)              PIO0_BASE)
#define PIO1                        ((PIO0_Type*)              PIO1_BASE)
#define BUSCTRL                     ((BUSCTRL_Type*)           BUSCTRL_BASE)
#define SIO                         ((SIO_Type*)               SIO_BASE)
#define USB                         ((USB_Type*)               USB_BASE)
#define USB_DPRAM                   ((USB_DPRAM_Type*)         USB_DPRAM_BASE)
#define TBMAN                       ((TBMAN_Type*)             TBMAN_BASE)
#define VREG_AND_CHIP_RESET         ((VREG_AND_CHIP_RESET_Type*)  VREG_AND_CHIP_RESET_BASE)
#define RTC                         ((RTC_Type*)               RTC_BASE)

/** @} */ /* End of group Device_Peripheral_declaration */


#ifdef __cplusplus
}
#endif

#endif /* RP2040_H */


/** @} */ /* End of group RP2040 */

/** @} */ /* End of group Raspberry Pi */
