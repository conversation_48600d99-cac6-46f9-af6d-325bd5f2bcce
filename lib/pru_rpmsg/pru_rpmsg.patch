diff --git a/lib/pru_rpmsg/include/am335x/pru_iep.h b/lib/pru_rpmsg/include/am335x/pru_iep.h
index d877ddd..064fb42 100644
--- a/lib/pru_rpmsg/include/am335x/pru_iep.h
+++ b/lib/pru_rpmsg/include/am335x/pru_iep.h
@@ -251,6 +251,11 @@ typedef struct {
 
 } pruIep;
 
+#ifdef __GNUC__
+static volatile pruIntc *__CT_IEP = (void *)0x0002e000;
+#define CT_IEP	(*__CT_INTC)
+#else
 volatile __far pruIep CT_IEP __attribute__((cregister("PRU_IEP", far), peripheral));
+#endif
 
 #endif /* _PRU_IEP_H_ */
