/**
 * \file
 *
 * \brief Peripheral I/O description for SAME51N19A
 *
 * Copyright (c) 2019 Microchip Technology Inc.
 *
 * \asf_license_start
 *
 * \page License
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the Licence at
 * 
 * http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * \asf_license_stop
 *
 */

#ifndef _SAME51N19A_PIO_
#define _SAME51N19A_PIO_

#define PIN_PA00                            0  /**< \brief Pin Number for PA00 */
#define PORT_PA00              (_UL_(1) <<  0) /**< \brief PORT Mask  for PA00 */
#define PIN_PA01                            1  /**< \brief Pin Number for PA01 */
#define PORT_PA01              (_UL_(1) <<  1) /**< \brief PORT Mask  for PA01 */
#define PIN_PA02                            2  /**< \brief Pin Number for PA02 */
#define PORT_PA02              (_UL_(1) <<  2) /**< \brief PORT Mask  for PA02 */
#define PIN_PA03                            3  /**< \brief Pin Number for PA03 */
#define PORT_PA03              (_UL_(1) <<  3) /**< \brief PORT Mask  for PA03 */
#define PIN_PA04                            4  /**< \brief Pin Number for PA04 */
#define PORT_PA04              (_UL_(1) <<  4) /**< \brief PORT Mask  for PA04 */
#define PIN_PA05                            5  /**< \brief Pin Number for PA05 */
#define PORT_PA05              (_UL_(1) <<  5) /**< \brief PORT Mask  for PA05 */
#define PIN_PA06                            6  /**< \brief Pin Number for PA06 */
#define PORT_PA06              (_UL_(1) <<  6) /**< \brief PORT Mask  for PA06 */
#define PIN_PA07                            7  /**< \brief Pin Number for PA07 */
#define PORT_PA07              (_UL_(1) <<  7) /**< \brief PORT Mask  for PA07 */
#define PIN_PA08                            8  /**< \brief Pin Number for PA08 */
#define PORT_PA08              (_UL_(1) <<  8) /**< \brief PORT Mask  for PA08 */
#define PIN_PA09                            9  /**< \brief Pin Number for PA09 */
#define PORT_PA09              (_UL_(1) <<  9) /**< \brief PORT Mask  for PA09 */
#define PIN_PA10                           10  /**< \brief Pin Number for PA10 */
#define PORT_PA10              (_UL_(1) << 10) /**< \brief PORT Mask  for PA10 */
#define PIN_PA11                           11  /**< \brief Pin Number for PA11 */
#define PORT_PA11              (_UL_(1) << 11) /**< \brief PORT Mask  for PA11 */
#define PIN_PA12                           12  /**< \brief Pin Number for PA12 */
#define PORT_PA12              (_UL_(1) << 12) /**< \brief PORT Mask  for PA12 */
#define PIN_PA13                           13  /**< \brief Pin Number for PA13 */
#define PORT_PA13              (_UL_(1) << 13) /**< \brief PORT Mask  for PA13 */
#define PIN_PA14                           14  /**< \brief Pin Number for PA14 */
#define PORT_PA14              (_UL_(1) << 14) /**< \brief PORT Mask  for PA14 */
#define PIN_PA15                           15  /**< \brief Pin Number for PA15 */
#define PORT_PA15              (_UL_(1) << 15) /**< \brief PORT Mask  for PA15 */
#define PIN_PA16                           16  /**< \brief Pin Number for PA16 */
#define PORT_PA16              (_UL_(1) << 16) /**< \brief PORT Mask  for PA16 */
#define PIN_PA17                           17  /**< \brief Pin Number for PA17 */
#define PORT_PA17              (_UL_(1) << 17) /**< \brief PORT Mask  for PA17 */
#define PIN_PA18                           18  /**< \brief Pin Number for PA18 */
#define PORT_PA18              (_UL_(1) << 18) /**< \brief PORT Mask  for PA18 */
#define PIN_PA19                           19  /**< \brief Pin Number for PA19 */
#define PORT_PA19              (_UL_(1) << 19) /**< \brief PORT Mask  for PA19 */
#define PIN_PA20                           20  /**< \brief Pin Number for PA20 */
#define PORT_PA20              (_UL_(1) << 20) /**< \brief PORT Mask  for PA20 */
#define PIN_PA21                           21  /**< \brief Pin Number for PA21 */
#define PORT_PA21              (_UL_(1) << 21) /**< \brief PORT Mask  for PA21 */
#define PIN_PA22                           22  /**< \brief Pin Number for PA22 */
#define PORT_PA22              (_UL_(1) << 22) /**< \brief PORT Mask  for PA22 */
#define PIN_PA23                           23  /**< \brief Pin Number for PA23 */
#define PORT_PA23              (_UL_(1) << 23) /**< \brief PORT Mask  for PA23 */
#define PIN_PA24                           24  /**< \brief Pin Number for PA24 */
#define PORT_PA24              (_UL_(1) << 24) /**< \brief PORT Mask  for PA24 */
#define PIN_PA25                           25  /**< \brief Pin Number for PA25 */
#define PORT_PA25              (_UL_(1) << 25) /**< \brief PORT Mask  for PA25 */
#define PIN_PA27                           27  /**< \brief Pin Number for PA27 */
#define PORT_PA27              (_UL_(1) << 27) /**< \brief PORT Mask  for PA27 */
#define PIN_PA30                           30  /**< \brief Pin Number for PA30 */
#define PORT_PA30              (_UL_(1) << 30) /**< \brief PORT Mask  for PA30 */
#define PIN_PA31                           31  /**< \brief Pin Number for PA31 */
#define PORT_PA31              (_UL_(1) << 31) /**< \brief PORT Mask  for PA31 */
#define PIN_PB00                           32  /**< \brief Pin Number for PB00 */
#define PORT_PB00              (_UL_(1) <<  0) /**< \brief PORT Mask  for PB00 */
#define PIN_PB01                           33  /**< \brief Pin Number for PB01 */
#define PORT_PB01              (_UL_(1) <<  1) /**< \brief PORT Mask  for PB01 */
#define PIN_PB02                           34  /**< \brief Pin Number for PB02 */
#define PORT_PB02              (_UL_(1) <<  2) /**< \brief PORT Mask  for PB02 */
#define PIN_PB03                           35  /**< \brief Pin Number for PB03 */
#define PORT_PB03              (_UL_(1) <<  3) /**< \brief PORT Mask  for PB03 */
#define PIN_PB04                           36  /**< \brief Pin Number for PB04 */
#define PORT_PB04              (_UL_(1) <<  4) /**< \brief PORT Mask  for PB04 */
#define PIN_PB05                           37  /**< \brief Pin Number for PB05 */
#define PORT_PB05              (_UL_(1) <<  5) /**< \brief PORT Mask  for PB05 */
#define PIN_PB06                           38  /**< \brief Pin Number for PB06 */
#define PORT_PB06              (_UL_(1) <<  6) /**< \brief PORT Mask  for PB06 */
#define PIN_PB07                           39  /**< \brief Pin Number for PB07 */
#define PORT_PB07              (_UL_(1) <<  7) /**< \brief PORT Mask  for PB07 */
#define PIN_PB08                           40  /**< \brief Pin Number for PB08 */
#define PORT_PB08              (_UL_(1) <<  8) /**< \brief PORT Mask  for PB08 */
#define PIN_PB09                           41  /**< \brief Pin Number for PB09 */
#define PORT_PB09              (_UL_(1) <<  9) /**< \brief PORT Mask  for PB09 */
#define PIN_PB10                           42  /**< \brief Pin Number for PB10 */
#define PORT_PB10              (_UL_(1) << 10) /**< \brief PORT Mask  for PB10 */
#define PIN_PB11                           43  /**< \brief Pin Number for PB11 */
#define PORT_PB11              (_UL_(1) << 11) /**< \brief PORT Mask  for PB11 */
#define PIN_PB12                           44  /**< \brief Pin Number for PB12 */
#define PORT_PB12              (_UL_(1) << 12) /**< \brief PORT Mask  for PB12 */
#define PIN_PB13                           45  /**< \brief Pin Number for PB13 */
#define PORT_PB13              (_UL_(1) << 13) /**< \brief PORT Mask  for PB13 */
#define PIN_PB14                           46  /**< \brief Pin Number for PB14 */
#define PORT_PB14              (_UL_(1) << 14) /**< \brief PORT Mask  for PB14 */
#define PIN_PB15                           47  /**< \brief Pin Number for PB15 */
#define PORT_PB15              (_UL_(1) << 15) /**< \brief PORT Mask  for PB15 */
#define PIN_PB16                           48  /**< \brief Pin Number for PB16 */
#define PORT_PB16              (_UL_(1) << 16) /**< \brief PORT Mask  for PB16 */
#define PIN_PB17                           49  /**< \brief Pin Number for PB17 */
#define PORT_PB17              (_UL_(1) << 17) /**< \brief PORT Mask  for PB17 */
#define PIN_PB18                           50  /**< \brief Pin Number for PB18 */
#define PORT_PB18              (_UL_(1) << 18) /**< \brief PORT Mask  for PB18 */
#define PIN_PB19                           51  /**< \brief Pin Number for PB19 */
#define PORT_PB19              (_UL_(1) << 19) /**< \brief PORT Mask  for PB19 */
#define PIN_PB20                           52  /**< \brief Pin Number for PB20 */
#define PORT_PB20              (_UL_(1) << 20) /**< \brief PORT Mask  for PB20 */
#define PIN_PB21                           53  /**< \brief Pin Number for PB21 */
#define PORT_PB21              (_UL_(1) << 21) /**< \brief PORT Mask  for PB21 */
#define PIN_PB22                           54  /**< \brief Pin Number for PB22 */
#define PORT_PB22              (_UL_(1) << 22) /**< \brief PORT Mask  for PB22 */
#define PIN_PB23                           55  /**< \brief Pin Number for PB23 */
#define PORT_PB23              (_UL_(1) << 23) /**< \brief PORT Mask  for PB23 */
#define PIN_PB24                           56  /**< \brief Pin Number for PB24 */
#define PORT_PB24              (_UL_(1) << 24) /**< \brief PORT Mask  for PB24 */
#define PIN_PB25                           57  /**< \brief Pin Number for PB25 */
#define PORT_PB25              (_UL_(1) << 25) /**< \brief PORT Mask  for PB25 */
#define PIN_PB30                           62  /**< \brief Pin Number for PB30 */
#define PORT_PB30              (_UL_(1) << 30) /**< \brief PORT Mask  for PB30 */
#define PIN_PB31                           63  /**< \brief Pin Number for PB31 */
#define PORT_PB31              (_UL_(1) << 31) /**< \brief PORT Mask  for PB31 */
#define PIN_PC00                           64  /**< \brief Pin Number for PC00 */
#define PORT_PC00              (_UL_(1) <<  0) /**< \brief PORT Mask  for PC00 */
#define PIN_PC01                           65  /**< \brief Pin Number for PC01 */
#define PORT_PC01              (_UL_(1) <<  1) /**< \brief PORT Mask  for PC01 */
#define PIN_PC02                           66  /**< \brief Pin Number for PC02 */
#define PORT_PC02              (_UL_(1) <<  2) /**< \brief PORT Mask  for PC02 */
#define PIN_PC03                           67  /**< \brief Pin Number for PC03 */
#define PORT_PC03              (_UL_(1) <<  3) /**< \brief PORT Mask  for PC03 */
#define PIN_PC05                           69  /**< \brief Pin Number for PC05 */
#define PORT_PC05              (_UL_(1) <<  5) /**< \brief PORT Mask  for PC05 */
#define PIN_PC06                           70  /**< \brief Pin Number for PC06 */
#define PORT_PC06              (_UL_(1) <<  6) /**< \brief PORT Mask  for PC06 */
#define PIN_PC07                           71  /**< \brief Pin Number for PC07 */
#define PORT_PC07              (_UL_(1) <<  7) /**< \brief PORT Mask  for PC07 */
#define PIN_PC10                           74  /**< \brief Pin Number for PC10 */
#define PORT_PC10              (_UL_(1) << 10) /**< \brief PORT Mask  for PC10 */
#define PIN_PC11                           75  /**< \brief Pin Number for PC11 */
#define PORT_PC11              (_UL_(1) << 11) /**< \brief PORT Mask  for PC11 */
#define PIN_PC12                           76  /**< \brief Pin Number for PC12 */
#define PORT_PC12              (_UL_(1) << 12) /**< \brief PORT Mask  for PC12 */
#define PIN_PC13                           77  /**< \brief Pin Number for PC13 */
#define PORT_PC13              (_UL_(1) << 13) /**< \brief PORT Mask  for PC13 */
#define PIN_PC14                           78  /**< \brief Pin Number for PC14 */
#define PORT_PC14              (_UL_(1) << 14) /**< \brief PORT Mask  for PC14 */
#define PIN_PC15                           79  /**< \brief Pin Number for PC15 */
#define PORT_PC15              (_UL_(1) << 15) /**< \brief PORT Mask  for PC15 */
#define PIN_PC16                           80  /**< \brief Pin Number for PC16 */
#define PORT_PC16              (_UL_(1) << 16) /**< \brief PORT Mask  for PC16 */
#define PIN_PC17                           81  /**< \brief Pin Number for PC17 */
#define PORT_PC17              (_UL_(1) << 17) /**< \brief PORT Mask  for PC17 */
#define PIN_PC18                           82  /**< \brief Pin Number for PC18 */
#define PORT_PC18              (_UL_(1) << 18) /**< \brief PORT Mask  for PC18 */
#define PIN_PC19                           83  /**< \brief Pin Number for PC19 */
#define PORT_PC19              (_UL_(1) << 19) /**< \brief PORT Mask  for PC19 */
#define PIN_PC20                           84  /**< \brief Pin Number for PC20 */
#define PORT_PC20              (_UL_(1) << 20) /**< \brief PORT Mask  for PC20 */
#define PIN_PC21                           85  /**< \brief Pin Number for PC21 */
#define PORT_PC21              (_UL_(1) << 21) /**< \brief PORT Mask  for PC21 */
#define PIN_PC24                           88  /**< \brief Pin Number for PC24 */
#define PORT_PC24              (_UL_(1) << 24) /**< \brief PORT Mask  for PC24 */
#define PIN_PC25                           89  /**< \brief Pin Number for PC25 */
#define PORT_PC25              (_UL_(1) << 25) /**< \brief PORT Mask  for PC25 */
#define PIN_PC26                           90  /**< \brief Pin Number for PC26 */
#define PORT_PC26              (_UL_(1) << 26) /**< \brief PORT Mask  for PC26 */
#define PIN_PC27                           91  /**< \brief Pin Number for PC27 */
#define PORT_PC27              (_UL_(1) << 27) /**< \brief PORT Mask  for PC27 */
#define PIN_PC28                           92  /**< \brief Pin Number for PC28 */
#define PORT_PC28              (_UL_(1) << 28) /**< \brief PORT Mask  for PC28 */
/* ========== PORT definition for CM4 peripheral ========== */
#define PIN_PA30H_CM4_SWCLK            _L_(30) /**< \brief CM4 signal: SWCLK on PA30 mux H */
#define MUX_PA30H_CM4_SWCLK             _L_(7)
#define PINMUX_PA30H_CM4_SWCLK     ((PIN_PA30H_CM4_SWCLK << 16) | MUX_PA30H_CM4_SWCLK)
#define PORT_PA30H_CM4_SWCLK   (_UL_(1) << 30)
#define PIN_PC27M_CM4_SWO              _L_(91) /**< \brief CM4 signal: SWO on PC27 mux M */
#define MUX_PC27M_CM4_SWO              _L_(12)
#define PINMUX_PC27M_CM4_SWO       ((PIN_PC27M_CM4_SWO << 16) | MUX_PC27M_CM4_SWO)
#define PORT_PC27M_CM4_SWO     (_UL_(1) << 27)
#define PIN_PB30H_CM4_SWO              _L_(62) /**< \brief CM4 signal: SWO on PB30 mux H */
#define MUX_PB30H_CM4_SWO               _L_(7)
#define PINMUX_PB30H_CM4_SWO       ((PIN_PB30H_CM4_SWO << 16) | MUX_PB30H_CM4_SWO)
#define PORT_PB30H_CM4_SWO     (_UL_(1) << 30)
#define PIN_PC27H_CM4_TRACECLK         _L_(91) /**< \brief CM4 signal: TRACECLK on PC27 mux H */
#define MUX_PC27H_CM4_TRACECLK          _L_(7)
#define PINMUX_PC27H_CM4_TRACECLK  ((PIN_PC27H_CM4_TRACECLK << 16) | MUX_PC27H_CM4_TRACECLK)
#define PORT_PC27H_CM4_TRACECLK  (_UL_(1) << 27)
#define PIN_PC28H_CM4_TRACEDATA0       _L_(92) /**< \brief CM4 signal: TRACEDATA0 on PC28 mux H */
#define MUX_PC28H_CM4_TRACEDATA0        _L_(7)
#define PINMUX_PC28H_CM4_TRACEDATA0  ((PIN_PC28H_CM4_TRACEDATA0 << 16) | MUX_PC28H_CM4_TRACEDATA0)
#define PORT_PC28H_CM4_TRACEDATA0  (_UL_(1) << 28)
#define PIN_PC26H_CM4_TRACEDATA1       _L_(90) /**< \brief CM4 signal: TRACEDATA1 on PC26 mux H */
#define MUX_PC26H_CM4_TRACEDATA1        _L_(7)
#define PINMUX_PC26H_CM4_TRACEDATA1  ((PIN_PC26H_CM4_TRACEDATA1 << 16) | MUX_PC26H_CM4_TRACEDATA1)
#define PORT_PC26H_CM4_TRACEDATA1  (_UL_(1) << 26)
#define PIN_PC25H_CM4_TRACEDATA2       _L_(89) /**< \brief CM4 signal: TRACEDATA2 on PC25 mux H */
#define MUX_PC25H_CM4_TRACEDATA2        _L_(7)
#define PINMUX_PC25H_CM4_TRACEDATA2  ((PIN_PC25H_CM4_TRACEDATA2 << 16) | MUX_PC25H_CM4_TRACEDATA2)
#define PORT_PC25H_CM4_TRACEDATA2  (_UL_(1) << 25)
#define PIN_PC24H_CM4_TRACEDATA3       _L_(88) /**< \brief CM4 signal: TRACEDATA3 on PC24 mux H */
#define MUX_PC24H_CM4_TRACEDATA3        _L_(7)
#define PINMUX_PC24H_CM4_TRACEDATA3  ((PIN_PC24H_CM4_TRACEDATA3 << 16) | MUX_PC24H_CM4_TRACEDATA3)
#define PORT_PC24H_CM4_TRACEDATA3  (_UL_(1) << 24)
/* ========== PORT definition for ANAREF peripheral ========== */
#define PIN_PA03B_ANAREF_VREF0          _L_(3) /**< \brief ANAREF signal: VREF0 on PA03 mux B */
#define MUX_PA03B_ANAREF_VREF0          _L_(1)
#define PINMUX_PA03B_ANAREF_VREF0  ((PIN_PA03B_ANAREF_VREF0 << 16) | MUX_PA03B_ANAREF_VREF0)
#define PORT_PA03B_ANAREF_VREF0  (_UL_(1) <<  3)
#define PIN_PA04B_ANAREF_VREF1          _L_(4) /**< \brief ANAREF signal: VREF1 on PA04 mux B */
#define MUX_PA04B_ANAREF_VREF1          _L_(1)
#define PINMUX_PA04B_ANAREF_VREF1  ((PIN_PA04B_ANAREF_VREF1 << 16) | MUX_PA04B_ANAREF_VREF1)
#define PORT_PA04B_ANAREF_VREF1  (_UL_(1) <<  4)
#define PIN_PA06B_ANAREF_VREF2          _L_(6) /**< \brief ANAREF signal: VREF2 on PA06 mux B */
#define MUX_PA06B_ANAREF_VREF2          _L_(1)
#define PINMUX_PA06B_ANAREF_VREF2  ((PIN_PA06B_ANAREF_VREF2 << 16) | MUX_PA06B_ANAREF_VREF2)
#define PORT_PA06B_ANAREF_VREF2  (_UL_(1) <<  6)
/* ========== PORT definition for GCLK peripheral ========== */
#define PIN_PA30M_GCLK_IO0             _L_(30) /**< \brief GCLK signal: IO0 on PA30 mux M */
#define MUX_PA30M_GCLK_IO0             _L_(12)
#define PINMUX_PA30M_GCLK_IO0      ((PIN_PA30M_GCLK_IO0 << 16) | MUX_PA30M_GCLK_IO0)
#define PORT_PA30M_GCLK_IO0    (_UL_(1) << 30)
#define PIN_PB14M_GCLK_IO0             _L_(46) /**< \brief GCLK signal: IO0 on PB14 mux M */
#define MUX_PB14M_GCLK_IO0             _L_(12)
#define PINMUX_PB14M_GCLK_IO0      ((PIN_PB14M_GCLK_IO0 << 16) | MUX_PB14M_GCLK_IO0)
#define PORT_PB14M_GCLK_IO0    (_UL_(1) << 14)
#define PIN_PA14M_GCLK_IO0             _L_(14) /**< \brief GCLK signal: IO0 on PA14 mux M */
#define MUX_PA14M_GCLK_IO0             _L_(12)
#define PINMUX_PA14M_GCLK_IO0      ((PIN_PA14M_GCLK_IO0 << 16) | MUX_PA14M_GCLK_IO0)
#define PORT_PA14M_GCLK_IO0    (_UL_(1) << 14)
#define PIN_PB22M_GCLK_IO0             _L_(54) /**< \brief GCLK signal: IO0 on PB22 mux M */
#define MUX_PB22M_GCLK_IO0             _L_(12)
#define PINMUX_PB22M_GCLK_IO0      ((PIN_PB22M_GCLK_IO0 << 16) | MUX_PB22M_GCLK_IO0)
#define PORT_PB22M_GCLK_IO0    (_UL_(1) << 22)
#define PIN_PB15M_GCLK_IO1             _L_(47) /**< \brief GCLK signal: IO1 on PB15 mux M */
#define MUX_PB15M_GCLK_IO1             _L_(12)
#define PINMUX_PB15M_GCLK_IO1      ((PIN_PB15M_GCLK_IO1 << 16) | MUX_PB15M_GCLK_IO1)
#define PORT_PB15M_GCLK_IO1    (_UL_(1) << 15)
#define PIN_PA15M_GCLK_IO1             _L_(15) /**< \brief GCLK signal: IO1 on PA15 mux M */
#define MUX_PA15M_GCLK_IO1             _L_(12)
#define PINMUX_PA15M_GCLK_IO1      ((PIN_PA15M_GCLK_IO1 << 16) | MUX_PA15M_GCLK_IO1)
#define PORT_PA15M_GCLK_IO1    (_UL_(1) << 15)
#define PIN_PB23M_GCLK_IO1             _L_(55) /**< \brief GCLK signal: IO1 on PB23 mux M */
#define MUX_PB23M_GCLK_IO1             _L_(12)
#define PINMUX_PB23M_GCLK_IO1      ((PIN_PB23M_GCLK_IO1 << 16) | MUX_PB23M_GCLK_IO1)
#define PORT_PB23M_GCLK_IO1    (_UL_(1) << 23)
#define PIN_PA27M_GCLK_IO1             _L_(27) /**< \brief GCLK signal: IO1 on PA27 mux M */
#define MUX_PA27M_GCLK_IO1             _L_(12)
#define PINMUX_PA27M_GCLK_IO1      ((PIN_PA27M_GCLK_IO1 << 16) | MUX_PA27M_GCLK_IO1)
#define PORT_PA27M_GCLK_IO1    (_UL_(1) << 27)
#define PIN_PA16M_GCLK_IO2             _L_(16) /**< \brief GCLK signal: IO2 on PA16 mux M */
#define MUX_PA16M_GCLK_IO2             _L_(12)
#define PINMUX_PA16M_GCLK_IO2      ((PIN_PA16M_GCLK_IO2 << 16) | MUX_PA16M_GCLK_IO2)
#define PORT_PA16M_GCLK_IO2    (_UL_(1) << 16)
#define PIN_PB16M_GCLK_IO2             _L_(48) /**< \brief GCLK signal: IO2 on PB16 mux M */
#define MUX_PB16M_GCLK_IO2             _L_(12)
#define PINMUX_PB16M_GCLK_IO2      ((PIN_PB16M_GCLK_IO2 << 16) | MUX_PB16M_GCLK_IO2)
#define PORT_PB16M_GCLK_IO2    (_UL_(1) << 16)
#define PIN_PA17M_GCLK_IO3             _L_(17) /**< \brief GCLK signal: IO3 on PA17 mux M */
#define MUX_PA17M_GCLK_IO3             _L_(12)
#define PINMUX_PA17M_GCLK_IO3      ((PIN_PA17M_GCLK_IO3 << 16) | MUX_PA17M_GCLK_IO3)
#define PORT_PA17M_GCLK_IO3    (_UL_(1) << 17)
#define PIN_PB17M_GCLK_IO3             _L_(49) /**< \brief GCLK signal: IO3 on PB17 mux M */
#define MUX_PB17M_GCLK_IO3             _L_(12)
#define PINMUX_PB17M_GCLK_IO3      ((PIN_PB17M_GCLK_IO3 << 16) | MUX_PB17M_GCLK_IO3)
#define PORT_PB17M_GCLK_IO3    (_UL_(1) << 17)
#define PIN_PA10M_GCLK_IO4             _L_(10) /**< \brief GCLK signal: IO4 on PA10 mux M */
#define MUX_PA10M_GCLK_IO4             _L_(12)
#define PINMUX_PA10M_GCLK_IO4      ((PIN_PA10M_GCLK_IO4 << 16) | MUX_PA10M_GCLK_IO4)
#define PORT_PA10M_GCLK_IO4    (_UL_(1) << 10)
#define PIN_PB10M_GCLK_IO4             _L_(42) /**< \brief GCLK signal: IO4 on PB10 mux M */
#define MUX_PB10M_GCLK_IO4             _L_(12)
#define PINMUX_PB10M_GCLK_IO4      ((PIN_PB10M_GCLK_IO4 << 16) | MUX_PB10M_GCLK_IO4)
#define PORT_PB10M_GCLK_IO4    (_UL_(1) << 10)
#define PIN_PB18M_GCLK_IO4             _L_(50) /**< \brief GCLK signal: IO4 on PB18 mux M */
#define MUX_PB18M_GCLK_IO4             _L_(12)
#define PINMUX_PB18M_GCLK_IO4      ((PIN_PB18M_GCLK_IO4 << 16) | MUX_PB18M_GCLK_IO4)
#define PORT_PB18M_GCLK_IO4    (_UL_(1) << 18)
#define PIN_PA11M_GCLK_IO5             _L_(11) /**< \brief GCLK signal: IO5 on PA11 mux M */
#define MUX_PA11M_GCLK_IO5             _L_(12)
#define PINMUX_PA11M_GCLK_IO5      ((PIN_PA11M_GCLK_IO5 << 16) | MUX_PA11M_GCLK_IO5)
#define PORT_PA11M_GCLK_IO5    (_UL_(1) << 11)
#define PIN_PB11M_GCLK_IO5             _L_(43) /**< \brief GCLK signal: IO5 on PB11 mux M */
#define MUX_PB11M_GCLK_IO5             _L_(12)
#define PINMUX_PB11M_GCLK_IO5      ((PIN_PB11M_GCLK_IO5 << 16) | MUX_PB11M_GCLK_IO5)
#define PORT_PB11M_GCLK_IO5    (_UL_(1) << 11)
#define PIN_PB19M_GCLK_IO5             _L_(51) /**< \brief GCLK signal: IO5 on PB19 mux M */
#define MUX_PB19M_GCLK_IO5             _L_(12)
#define PINMUX_PB19M_GCLK_IO5      ((PIN_PB19M_GCLK_IO5 << 16) | MUX_PB19M_GCLK_IO5)
#define PORT_PB19M_GCLK_IO5    (_UL_(1) << 19)
#define PIN_PB12M_GCLK_IO6             _L_(44) /**< \brief GCLK signal: IO6 on PB12 mux M */
#define MUX_PB12M_GCLK_IO6             _L_(12)
#define PINMUX_PB12M_GCLK_IO6      ((PIN_PB12M_GCLK_IO6 << 16) | MUX_PB12M_GCLK_IO6)
#define PORT_PB12M_GCLK_IO6    (_UL_(1) << 12)
#define PIN_PB20M_GCLK_IO6             _L_(52) /**< \brief GCLK signal: IO6 on PB20 mux M */
#define MUX_PB20M_GCLK_IO6             _L_(12)
#define PINMUX_PB20M_GCLK_IO6      ((PIN_PB20M_GCLK_IO6 << 16) | MUX_PB20M_GCLK_IO6)
#define PORT_PB20M_GCLK_IO6    (_UL_(1) << 20)
#define PIN_PB13M_GCLK_IO7             _L_(45) /**< \brief GCLK signal: IO7 on PB13 mux M */
#define MUX_PB13M_GCLK_IO7             _L_(12)
#define PINMUX_PB13M_GCLK_IO7      ((PIN_PB13M_GCLK_IO7 << 16) | MUX_PB13M_GCLK_IO7)
#define PORT_PB13M_GCLK_IO7    (_UL_(1) << 13)
#define PIN_PB21M_GCLK_IO7             _L_(53) /**< \brief GCLK signal: IO7 on PB21 mux M */
#define MUX_PB21M_GCLK_IO7             _L_(12)
#define PINMUX_PB21M_GCLK_IO7      ((PIN_PB21M_GCLK_IO7 << 16) | MUX_PB21M_GCLK_IO7)
#define PORT_PB21M_GCLK_IO7    (_UL_(1) << 21)
/* ========== PORT definition for EIC peripheral ========== */
#define PIN_PA00A_EIC_EXTINT0           _L_(0) /**< \brief EIC signal: EXTINT0 on PA00 mux A */
#define MUX_PA00A_EIC_EXTINT0           _L_(0)
#define PINMUX_PA00A_EIC_EXTINT0   ((PIN_PA00A_EIC_EXTINT0 << 16) | MUX_PA00A_EIC_EXTINT0)
#define PORT_PA00A_EIC_EXTINT0  (_UL_(1) <<  0)
#define PIN_PA00A_EIC_EXTINT_NUM        _L_(0) /**< \brief EIC signal: PIN_PA00 External Interrupt Line */
#define PIN_PA16A_EIC_EXTINT0          _L_(16) /**< \brief EIC signal: EXTINT0 on PA16 mux A */
#define MUX_PA16A_EIC_EXTINT0           _L_(0)
#define PINMUX_PA16A_EIC_EXTINT0   ((PIN_PA16A_EIC_EXTINT0 << 16) | MUX_PA16A_EIC_EXTINT0)
#define PORT_PA16A_EIC_EXTINT0  (_UL_(1) << 16)
#define PIN_PA16A_EIC_EXTINT_NUM        _L_(0) /**< \brief EIC signal: PIN_PA16 External Interrupt Line */
#define PIN_PB00A_EIC_EXTINT0          _L_(32) /**< \brief EIC signal: EXTINT0 on PB00 mux A */
#define MUX_PB00A_EIC_EXTINT0           _L_(0)
#define PINMUX_PB00A_EIC_EXTINT0   ((PIN_PB00A_EIC_EXTINT0 << 16) | MUX_PB00A_EIC_EXTINT0)
#define PORT_PB00A_EIC_EXTINT0  (_UL_(1) <<  0)
#define PIN_PB00A_EIC_EXTINT_NUM        _L_(0) /**< \brief EIC signal: PIN_PB00 External Interrupt Line */
#define PIN_PB16A_EIC_EXTINT0          _L_(48) /**< \brief EIC signal: EXTINT0 on PB16 mux A */
#define MUX_PB16A_EIC_EXTINT0           _L_(0)
#define PINMUX_PB16A_EIC_EXTINT0   ((PIN_PB16A_EIC_EXTINT0 << 16) | MUX_PB16A_EIC_EXTINT0)
#define PORT_PB16A_EIC_EXTINT0  (_UL_(1) << 16)
#define PIN_PB16A_EIC_EXTINT_NUM        _L_(0) /**< \brief EIC signal: PIN_PB16 External Interrupt Line */
#define PIN_PC00A_EIC_EXTINT0          _L_(64) /**< \brief EIC signal: EXTINT0 on PC00 mux A */
#define MUX_PC00A_EIC_EXTINT0           _L_(0)
#define PINMUX_PC00A_EIC_EXTINT0   ((PIN_PC00A_EIC_EXTINT0 << 16) | MUX_PC00A_EIC_EXTINT0)
#define PORT_PC00A_EIC_EXTINT0  (_UL_(1) <<  0)
#define PIN_PC00A_EIC_EXTINT_NUM        _L_(0) /**< \brief EIC signal: PIN_PC00 External Interrupt Line */
#define PIN_PC16A_EIC_EXTINT0          _L_(80) /**< \brief EIC signal: EXTINT0 on PC16 mux A */
#define MUX_PC16A_EIC_EXTINT0           _L_(0)
#define PINMUX_PC16A_EIC_EXTINT0   ((PIN_PC16A_EIC_EXTINT0 << 16) | MUX_PC16A_EIC_EXTINT0)
#define PORT_PC16A_EIC_EXTINT0  (_UL_(1) << 16)
#define PIN_PC16A_EIC_EXTINT_NUM        _L_(0) /**< \brief EIC signal: PIN_PC16 External Interrupt Line */
#define PIN_PA01A_EIC_EXTINT1           _L_(1) /**< \brief EIC signal: EXTINT1 on PA01 mux A */
#define MUX_PA01A_EIC_EXTINT1           _L_(0)
#define PINMUX_PA01A_EIC_EXTINT1   ((PIN_PA01A_EIC_EXTINT1 << 16) | MUX_PA01A_EIC_EXTINT1)
#define PORT_PA01A_EIC_EXTINT1  (_UL_(1) <<  1)
#define PIN_PA01A_EIC_EXTINT_NUM        _L_(1) /**< \brief EIC signal: PIN_PA01 External Interrupt Line */
#define PIN_PA17A_EIC_EXTINT1          _L_(17) /**< \brief EIC signal: EXTINT1 on PA17 mux A */
#define MUX_PA17A_EIC_EXTINT1           _L_(0)
#define PINMUX_PA17A_EIC_EXTINT1   ((PIN_PA17A_EIC_EXTINT1 << 16) | MUX_PA17A_EIC_EXTINT1)
#define PORT_PA17A_EIC_EXTINT1  (_UL_(1) << 17)
#define PIN_PA17A_EIC_EXTINT_NUM        _L_(1) /**< \brief EIC signal: PIN_PA17 External Interrupt Line */
#define PIN_PB01A_EIC_EXTINT1          _L_(33) /**< \brief EIC signal: EXTINT1 on PB01 mux A */
#define MUX_PB01A_EIC_EXTINT1           _L_(0)
#define PINMUX_PB01A_EIC_EXTINT1   ((PIN_PB01A_EIC_EXTINT1 << 16) | MUX_PB01A_EIC_EXTINT1)
#define PORT_PB01A_EIC_EXTINT1  (_UL_(1) <<  1)
#define PIN_PB01A_EIC_EXTINT_NUM        _L_(1) /**< \brief EIC signal: PIN_PB01 External Interrupt Line */
#define PIN_PB17A_EIC_EXTINT1          _L_(49) /**< \brief EIC signal: EXTINT1 on PB17 mux A */
#define MUX_PB17A_EIC_EXTINT1           _L_(0)
#define PINMUX_PB17A_EIC_EXTINT1   ((PIN_PB17A_EIC_EXTINT1 << 16) | MUX_PB17A_EIC_EXTINT1)
#define PORT_PB17A_EIC_EXTINT1  (_UL_(1) << 17)
#define PIN_PB17A_EIC_EXTINT_NUM        _L_(1) /**< \brief EIC signal: PIN_PB17 External Interrupt Line */
#define PIN_PC01A_EIC_EXTINT1          _L_(65) /**< \brief EIC signal: EXTINT1 on PC01 mux A */
#define MUX_PC01A_EIC_EXTINT1           _L_(0)
#define PINMUX_PC01A_EIC_EXTINT1   ((PIN_PC01A_EIC_EXTINT1 << 16) | MUX_PC01A_EIC_EXTINT1)
#define PORT_PC01A_EIC_EXTINT1  (_UL_(1) <<  1)
#define PIN_PC01A_EIC_EXTINT_NUM        _L_(1) /**< \brief EIC signal: PIN_PC01 External Interrupt Line */
#define PIN_PC17A_EIC_EXTINT1          _L_(81) /**< \brief EIC signal: EXTINT1 on PC17 mux A */
#define MUX_PC17A_EIC_EXTINT1           _L_(0)
#define PINMUX_PC17A_EIC_EXTINT1   ((PIN_PC17A_EIC_EXTINT1 << 16) | MUX_PC17A_EIC_EXTINT1)
#define PORT_PC17A_EIC_EXTINT1  (_UL_(1) << 17)
#define PIN_PC17A_EIC_EXTINT_NUM        _L_(1) /**< \brief EIC signal: PIN_PC17 External Interrupt Line */
#define PIN_PA02A_EIC_EXTINT2           _L_(2) /**< \brief EIC signal: EXTINT2 on PA02 mux A */
#define MUX_PA02A_EIC_EXTINT2           _L_(0)
#define PINMUX_PA02A_EIC_EXTINT2   ((PIN_PA02A_EIC_EXTINT2 << 16) | MUX_PA02A_EIC_EXTINT2)
#define PORT_PA02A_EIC_EXTINT2  (_UL_(1) <<  2)
#define PIN_PA02A_EIC_EXTINT_NUM        _L_(2) /**< \brief EIC signal: PIN_PA02 External Interrupt Line */
#define PIN_PA18A_EIC_EXTINT2          _L_(18) /**< \brief EIC signal: EXTINT2 on PA18 mux A */
#define MUX_PA18A_EIC_EXTINT2           _L_(0)
#define PINMUX_PA18A_EIC_EXTINT2   ((PIN_PA18A_EIC_EXTINT2 << 16) | MUX_PA18A_EIC_EXTINT2)
#define PORT_PA18A_EIC_EXTINT2  (_UL_(1) << 18)
#define PIN_PA18A_EIC_EXTINT_NUM        _L_(2) /**< \brief EIC signal: PIN_PA18 External Interrupt Line */
#define PIN_PB02A_EIC_EXTINT2          _L_(34) /**< \brief EIC signal: EXTINT2 on PB02 mux A */
#define MUX_PB02A_EIC_EXTINT2           _L_(0)
#define PINMUX_PB02A_EIC_EXTINT2   ((PIN_PB02A_EIC_EXTINT2 << 16) | MUX_PB02A_EIC_EXTINT2)
#define PORT_PB02A_EIC_EXTINT2  (_UL_(1) <<  2)
#define PIN_PB02A_EIC_EXTINT_NUM        _L_(2) /**< \brief EIC signal: PIN_PB02 External Interrupt Line */
#define PIN_PB18A_EIC_EXTINT2          _L_(50) /**< \brief EIC signal: EXTINT2 on PB18 mux A */
#define MUX_PB18A_EIC_EXTINT2           _L_(0)
#define PINMUX_PB18A_EIC_EXTINT2   ((PIN_PB18A_EIC_EXTINT2 << 16) | MUX_PB18A_EIC_EXTINT2)
#define PORT_PB18A_EIC_EXTINT2  (_UL_(1) << 18)
#define PIN_PB18A_EIC_EXTINT_NUM        _L_(2) /**< \brief EIC signal: PIN_PB18 External Interrupt Line */
#define PIN_PC02A_EIC_EXTINT2          _L_(66) /**< \brief EIC signal: EXTINT2 on PC02 mux A */
#define MUX_PC02A_EIC_EXTINT2           _L_(0)
#define PINMUX_PC02A_EIC_EXTINT2   ((PIN_PC02A_EIC_EXTINT2 << 16) | MUX_PC02A_EIC_EXTINT2)
#define PORT_PC02A_EIC_EXTINT2  (_UL_(1) <<  2)
#define PIN_PC02A_EIC_EXTINT_NUM        _L_(2) /**< \brief EIC signal: PIN_PC02 External Interrupt Line */
#define PIN_PC18A_EIC_EXTINT2          _L_(82) /**< \brief EIC signal: EXTINT2 on PC18 mux A */
#define MUX_PC18A_EIC_EXTINT2           _L_(0)
#define PINMUX_PC18A_EIC_EXTINT2   ((PIN_PC18A_EIC_EXTINT2 << 16) | MUX_PC18A_EIC_EXTINT2)
#define PORT_PC18A_EIC_EXTINT2  (_UL_(1) << 18)
#define PIN_PC18A_EIC_EXTINT_NUM        _L_(2) /**< \brief EIC signal: PIN_PC18 External Interrupt Line */
#define PIN_PA03A_EIC_EXTINT3           _L_(3) /**< \brief EIC signal: EXTINT3 on PA03 mux A */
#define MUX_PA03A_EIC_EXTINT3           _L_(0)
#define PINMUX_PA03A_EIC_EXTINT3   ((PIN_PA03A_EIC_EXTINT3 << 16) | MUX_PA03A_EIC_EXTINT3)
#define PORT_PA03A_EIC_EXTINT3  (_UL_(1) <<  3)
#define PIN_PA03A_EIC_EXTINT_NUM        _L_(3) /**< \brief EIC signal: PIN_PA03 External Interrupt Line */
#define PIN_PA19A_EIC_EXTINT3          _L_(19) /**< \brief EIC signal: EXTINT3 on PA19 mux A */
#define MUX_PA19A_EIC_EXTINT3           _L_(0)
#define PINMUX_PA19A_EIC_EXTINT3   ((PIN_PA19A_EIC_EXTINT3 << 16) | MUX_PA19A_EIC_EXTINT3)
#define PORT_PA19A_EIC_EXTINT3  (_UL_(1) << 19)
#define PIN_PA19A_EIC_EXTINT_NUM        _L_(3) /**< \brief EIC signal: PIN_PA19 External Interrupt Line */
#define PIN_PB03A_EIC_EXTINT3          _L_(35) /**< \brief EIC signal: EXTINT3 on PB03 mux A */
#define MUX_PB03A_EIC_EXTINT3           _L_(0)
#define PINMUX_PB03A_EIC_EXTINT3   ((PIN_PB03A_EIC_EXTINT3 << 16) | MUX_PB03A_EIC_EXTINT3)
#define PORT_PB03A_EIC_EXTINT3  (_UL_(1) <<  3)
#define PIN_PB03A_EIC_EXTINT_NUM        _L_(3) /**< \brief EIC signal: PIN_PB03 External Interrupt Line */
#define PIN_PB19A_EIC_EXTINT3          _L_(51) /**< \brief EIC signal: EXTINT3 on PB19 mux A */
#define MUX_PB19A_EIC_EXTINT3           _L_(0)
#define PINMUX_PB19A_EIC_EXTINT3   ((PIN_PB19A_EIC_EXTINT3 << 16) | MUX_PB19A_EIC_EXTINT3)
#define PORT_PB19A_EIC_EXTINT3  (_UL_(1) << 19)
#define PIN_PB19A_EIC_EXTINT_NUM        _L_(3) /**< \brief EIC signal: PIN_PB19 External Interrupt Line */
#define PIN_PC03A_EIC_EXTINT3          _L_(67) /**< \brief EIC signal: EXTINT3 on PC03 mux A */
#define MUX_PC03A_EIC_EXTINT3           _L_(0)
#define PINMUX_PC03A_EIC_EXTINT3   ((PIN_PC03A_EIC_EXTINT3 << 16) | MUX_PC03A_EIC_EXTINT3)
#define PORT_PC03A_EIC_EXTINT3  (_UL_(1) <<  3)
#define PIN_PC03A_EIC_EXTINT_NUM        _L_(3) /**< \brief EIC signal: PIN_PC03 External Interrupt Line */
#define PIN_PC19A_EIC_EXTINT3          _L_(83) /**< \brief EIC signal: EXTINT3 on PC19 mux A */
#define MUX_PC19A_EIC_EXTINT3           _L_(0)
#define PINMUX_PC19A_EIC_EXTINT3   ((PIN_PC19A_EIC_EXTINT3 << 16) | MUX_PC19A_EIC_EXTINT3)
#define PORT_PC19A_EIC_EXTINT3  (_UL_(1) << 19)
#define PIN_PC19A_EIC_EXTINT_NUM        _L_(3) /**< \brief EIC signal: PIN_PC19 External Interrupt Line */
#define PIN_PA04A_EIC_EXTINT4           _L_(4) /**< \brief EIC signal: EXTINT4 on PA04 mux A */
#define MUX_PA04A_EIC_EXTINT4           _L_(0)
#define PINMUX_PA04A_EIC_EXTINT4   ((PIN_PA04A_EIC_EXTINT4 << 16) | MUX_PA04A_EIC_EXTINT4)
#define PORT_PA04A_EIC_EXTINT4  (_UL_(1) <<  4)
#define PIN_PA04A_EIC_EXTINT_NUM        _L_(4) /**< \brief EIC signal: PIN_PA04 External Interrupt Line */
#define PIN_PA20A_EIC_EXTINT4          _L_(20) /**< \brief EIC signal: EXTINT4 on PA20 mux A */
#define MUX_PA20A_EIC_EXTINT4           _L_(0)
#define PINMUX_PA20A_EIC_EXTINT4   ((PIN_PA20A_EIC_EXTINT4 << 16) | MUX_PA20A_EIC_EXTINT4)
#define PORT_PA20A_EIC_EXTINT4  (_UL_(1) << 20)
#define PIN_PA20A_EIC_EXTINT_NUM        _L_(4) /**< \brief EIC signal: PIN_PA20 External Interrupt Line */
#define PIN_PB04A_EIC_EXTINT4          _L_(36) /**< \brief EIC signal: EXTINT4 on PB04 mux A */
#define MUX_PB04A_EIC_EXTINT4           _L_(0)
#define PINMUX_PB04A_EIC_EXTINT4   ((PIN_PB04A_EIC_EXTINT4 << 16) | MUX_PB04A_EIC_EXTINT4)
#define PORT_PB04A_EIC_EXTINT4  (_UL_(1) <<  4)
#define PIN_PB04A_EIC_EXTINT_NUM        _L_(4) /**< \brief EIC signal: PIN_PB04 External Interrupt Line */
#define PIN_PB20A_EIC_EXTINT4          _L_(52) /**< \brief EIC signal: EXTINT4 on PB20 mux A */
#define MUX_PB20A_EIC_EXTINT4           _L_(0)
#define PINMUX_PB20A_EIC_EXTINT4   ((PIN_PB20A_EIC_EXTINT4 << 16) | MUX_PB20A_EIC_EXTINT4)
#define PORT_PB20A_EIC_EXTINT4  (_UL_(1) << 20)
#define PIN_PB20A_EIC_EXTINT_NUM        _L_(4) /**< \brief EIC signal: PIN_PB20 External Interrupt Line */
#define PIN_PC20A_EIC_EXTINT4          _L_(84) /**< \brief EIC signal: EXTINT4 on PC20 mux A */
#define MUX_PC20A_EIC_EXTINT4           _L_(0)
#define PINMUX_PC20A_EIC_EXTINT4   ((PIN_PC20A_EIC_EXTINT4 << 16) | MUX_PC20A_EIC_EXTINT4)
#define PORT_PC20A_EIC_EXTINT4  (_UL_(1) << 20)
#define PIN_PC20A_EIC_EXTINT_NUM        _L_(4) /**< \brief EIC signal: PIN_PC20 External Interrupt Line */
#define PIN_PA05A_EIC_EXTINT5           _L_(5) /**< \brief EIC signal: EXTINT5 on PA05 mux A */
#define MUX_PA05A_EIC_EXTINT5           _L_(0)
#define PINMUX_PA05A_EIC_EXTINT5   ((PIN_PA05A_EIC_EXTINT5 << 16) | MUX_PA05A_EIC_EXTINT5)
#define PORT_PA05A_EIC_EXTINT5  (_UL_(1) <<  5)
#define PIN_PA05A_EIC_EXTINT_NUM        _L_(5) /**< \brief EIC signal: PIN_PA05 External Interrupt Line */
#define PIN_PA21A_EIC_EXTINT5          _L_(21) /**< \brief EIC signal: EXTINT5 on PA21 mux A */
#define MUX_PA21A_EIC_EXTINT5           _L_(0)
#define PINMUX_PA21A_EIC_EXTINT5   ((PIN_PA21A_EIC_EXTINT5 << 16) | MUX_PA21A_EIC_EXTINT5)
#define PORT_PA21A_EIC_EXTINT5  (_UL_(1) << 21)
#define PIN_PA21A_EIC_EXTINT_NUM        _L_(5) /**< \brief EIC signal: PIN_PA21 External Interrupt Line */
#define PIN_PB05A_EIC_EXTINT5          _L_(37) /**< \brief EIC signal: EXTINT5 on PB05 mux A */
#define MUX_PB05A_EIC_EXTINT5           _L_(0)
#define PINMUX_PB05A_EIC_EXTINT5   ((PIN_PB05A_EIC_EXTINT5 << 16) | MUX_PB05A_EIC_EXTINT5)
#define PORT_PB05A_EIC_EXTINT5  (_UL_(1) <<  5)
#define PIN_PB05A_EIC_EXTINT_NUM        _L_(5) /**< \brief EIC signal: PIN_PB05 External Interrupt Line */
#define PIN_PB21A_EIC_EXTINT5          _L_(53) /**< \brief EIC signal: EXTINT5 on PB21 mux A */
#define MUX_PB21A_EIC_EXTINT5           _L_(0)
#define PINMUX_PB21A_EIC_EXTINT5   ((PIN_PB21A_EIC_EXTINT5 << 16) | MUX_PB21A_EIC_EXTINT5)
#define PORT_PB21A_EIC_EXTINT5  (_UL_(1) << 21)
#define PIN_PB21A_EIC_EXTINT_NUM        _L_(5) /**< \brief EIC signal: PIN_PB21 External Interrupt Line */
#define PIN_PC05A_EIC_EXTINT5          _L_(69) /**< \brief EIC signal: EXTINT5 on PC05 mux A */
#define MUX_PC05A_EIC_EXTINT5           _L_(0)
#define PINMUX_PC05A_EIC_EXTINT5   ((PIN_PC05A_EIC_EXTINT5 << 16) | MUX_PC05A_EIC_EXTINT5)
#define PORT_PC05A_EIC_EXTINT5  (_UL_(1) <<  5)
#define PIN_PC05A_EIC_EXTINT_NUM        _L_(5) /**< \brief EIC signal: PIN_PC05 External Interrupt Line */
#define PIN_PC21A_EIC_EXTINT5          _L_(85) /**< \brief EIC signal: EXTINT5 on PC21 mux A */
#define MUX_PC21A_EIC_EXTINT5           _L_(0)
#define PINMUX_PC21A_EIC_EXTINT5   ((PIN_PC21A_EIC_EXTINT5 << 16) | MUX_PC21A_EIC_EXTINT5)
#define PORT_PC21A_EIC_EXTINT5  (_UL_(1) << 21)
#define PIN_PC21A_EIC_EXTINT_NUM        _L_(5) /**< \brief EIC signal: PIN_PC21 External Interrupt Line */
#define PIN_PA06A_EIC_EXTINT6           _L_(6) /**< \brief EIC signal: EXTINT6 on PA06 mux A */
#define MUX_PA06A_EIC_EXTINT6           _L_(0)
#define PINMUX_PA06A_EIC_EXTINT6   ((PIN_PA06A_EIC_EXTINT6 << 16) | MUX_PA06A_EIC_EXTINT6)
#define PORT_PA06A_EIC_EXTINT6  (_UL_(1) <<  6)
#define PIN_PA06A_EIC_EXTINT_NUM        _L_(6) /**< \brief EIC signal: PIN_PA06 External Interrupt Line */
#define PIN_PA22A_EIC_EXTINT6          _L_(22) /**< \brief EIC signal: EXTINT6 on PA22 mux A */
#define MUX_PA22A_EIC_EXTINT6           _L_(0)
#define PINMUX_PA22A_EIC_EXTINT6   ((PIN_PA22A_EIC_EXTINT6 << 16) | MUX_PA22A_EIC_EXTINT6)
#define PORT_PA22A_EIC_EXTINT6  (_UL_(1) << 22)
#define PIN_PA22A_EIC_EXTINT_NUM        _L_(6) /**< \brief EIC signal: PIN_PA22 External Interrupt Line */
#define PIN_PB06A_EIC_EXTINT6          _L_(38) /**< \brief EIC signal: EXTINT6 on PB06 mux A */
#define MUX_PB06A_EIC_EXTINT6           _L_(0)
#define PINMUX_PB06A_EIC_EXTINT6   ((PIN_PB06A_EIC_EXTINT6 << 16) | MUX_PB06A_EIC_EXTINT6)
#define PORT_PB06A_EIC_EXTINT6  (_UL_(1) <<  6)
#define PIN_PB06A_EIC_EXTINT_NUM        _L_(6) /**< \brief EIC signal: PIN_PB06 External Interrupt Line */
#define PIN_PB22A_EIC_EXTINT6          _L_(54) /**< \brief EIC signal: EXTINT6 on PB22 mux A */
#define MUX_PB22A_EIC_EXTINT6           _L_(0)
#define PINMUX_PB22A_EIC_EXTINT6   ((PIN_PB22A_EIC_EXTINT6 << 16) | MUX_PB22A_EIC_EXTINT6)
#define PORT_PB22A_EIC_EXTINT6  (_UL_(1) << 22)
#define PIN_PB22A_EIC_EXTINT_NUM        _L_(6) /**< \brief EIC signal: PIN_PB22 External Interrupt Line */
#define PIN_PC06A_EIC_EXTINT6          _L_(70) /**< \brief EIC signal: EXTINT6 on PC06 mux A */
#define MUX_PC06A_EIC_EXTINT6           _L_(0)
#define PINMUX_PC06A_EIC_EXTINT6   ((PIN_PC06A_EIC_EXTINT6 << 16) | MUX_PC06A_EIC_EXTINT6)
#define PORT_PC06A_EIC_EXTINT6  (_UL_(1) <<  6)
#define PIN_PC06A_EIC_EXTINT_NUM        _L_(6) /**< \brief EIC signal: PIN_PC06 External Interrupt Line */
#define PIN_PA07A_EIC_EXTINT7           _L_(7) /**< \brief EIC signal: EXTINT7 on PA07 mux A */
#define MUX_PA07A_EIC_EXTINT7           _L_(0)
#define PINMUX_PA07A_EIC_EXTINT7   ((PIN_PA07A_EIC_EXTINT7 << 16) | MUX_PA07A_EIC_EXTINT7)
#define PORT_PA07A_EIC_EXTINT7  (_UL_(1) <<  7)
#define PIN_PA07A_EIC_EXTINT_NUM        _L_(7) /**< \brief EIC signal: PIN_PA07 External Interrupt Line */
#define PIN_PA23A_EIC_EXTINT7          _L_(23) /**< \brief EIC signal: EXTINT7 on PA23 mux A */
#define MUX_PA23A_EIC_EXTINT7           _L_(0)
#define PINMUX_PA23A_EIC_EXTINT7   ((PIN_PA23A_EIC_EXTINT7 << 16) | MUX_PA23A_EIC_EXTINT7)
#define PORT_PA23A_EIC_EXTINT7  (_UL_(1) << 23)
#define PIN_PA23A_EIC_EXTINT_NUM        _L_(7) /**< \brief EIC signal: PIN_PA23 External Interrupt Line */
#define PIN_PB07A_EIC_EXTINT7          _L_(39) /**< \brief EIC signal: EXTINT7 on PB07 mux A */
#define MUX_PB07A_EIC_EXTINT7           _L_(0)
#define PINMUX_PB07A_EIC_EXTINT7   ((PIN_PB07A_EIC_EXTINT7 << 16) | MUX_PB07A_EIC_EXTINT7)
#define PORT_PB07A_EIC_EXTINT7  (_UL_(1) <<  7)
#define PIN_PB07A_EIC_EXTINT_NUM        _L_(7) /**< \brief EIC signal: PIN_PB07 External Interrupt Line */
#define PIN_PB23A_EIC_EXTINT7          _L_(55) /**< \brief EIC signal: EXTINT7 on PB23 mux A */
#define MUX_PB23A_EIC_EXTINT7           _L_(0)
#define PINMUX_PB23A_EIC_EXTINT7   ((PIN_PB23A_EIC_EXTINT7 << 16) | MUX_PB23A_EIC_EXTINT7)
#define PORT_PB23A_EIC_EXTINT7  (_UL_(1) << 23)
#define PIN_PB23A_EIC_EXTINT_NUM        _L_(7) /**< \brief EIC signal: PIN_PB23 External Interrupt Line */
#define PIN_PA24A_EIC_EXTINT8          _L_(24) /**< \brief EIC signal: EXTINT8 on PA24 mux A */
#define MUX_PA24A_EIC_EXTINT8           _L_(0)
#define PINMUX_PA24A_EIC_EXTINT8   ((PIN_PA24A_EIC_EXTINT8 << 16) | MUX_PA24A_EIC_EXTINT8)
#define PORT_PA24A_EIC_EXTINT8  (_UL_(1) << 24)
#define PIN_PA24A_EIC_EXTINT_NUM        _L_(8) /**< \brief EIC signal: PIN_PA24 External Interrupt Line */
#define PIN_PB08A_EIC_EXTINT8          _L_(40) /**< \brief EIC signal: EXTINT8 on PB08 mux A */
#define MUX_PB08A_EIC_EXTINT8           _L_(0)
#define PINMUX_PB08A_EIC_EXTINT8   ((PIN_PB08A_EIC_EXTINT8 << 16) | MUX_PB08A_EIC_EXTINT8)
#define PORT_PB08A_EIC_EXTINT8  (_UL_(1) <<  8)
#define PIN_PB08A_EIC_EXTINT_NUM        _L_(8) /**< \brief EIC signal: PIN_PB08 External Interrupt Line */
#define PIN_PB24A_EIC_EXTINT8          _L_(56) /**< \brief EIC signal: EXTINT8 on PB24 mux A */
#define MUX_PB24A_EIC_EXTINT8           _L_(0)
#define PINMUX_PB24A_EIC_EXTINT8   ((PIN_PB24A_EIC_EXTINT8 << 16) | MUX_PB24A_EIC_EXTINT8)
#define PORT_PB24A_EIC_EXTINT8  (_UL_(1) << 24)
#define PIN_PB24A_EIC_EXTINT_NUM        _L_(8) /**< \brief EIC signal: PIN_PB24 External Interrupt Line */
#define PIN_PC24A_EIC_EXTINT8          _L_(88) /**< \brief EIC signal: EXTINT8 on PC24 mux A */
#define MUX_PC24A_EIC_EXTINT8           _L_(0)
#define PINMUX_PC24A_EIC_EXTINT8   ((PIN_PC24A_EIC_EXTINT8 << 16) | MUX_PC24A_EIC_EXTINT8)
#define PORT_PC24A_EIC_EXTINT8  (_UL_(1) << 24)
#define PIN_PC24A_EIC_EXTINT_NUM        _L_(8) /**< \brief EIC signal: PIN_PC24 External Interrupt Line */
#define PIN_PA09A_EIC_EXTINT9           _L_(9) /**< \brief EIC signal: EXTINT9 on PA09 mux A */
#define MUX_PA09A_EIC_EXTINT9           _L_(0)
#define PINMUX_PA09A_EIC_EXTINT9   ((PIN_PA09A_EIC_EXTINT9 << 16) | MUX_PA09A_EIC_EXTINT9)
#define PORT_PA09A_EIC_EXTINT9  (_UL_(1) <<  9)
#define PIN_PA09A_EIC_EXTINT_NUM        _L_(9) /**< \brief EIC signal: PIN_PA09 External Interrupt Line */
#define PIN_PA25A_EIC_EXTINT9          _L_(25) /**< \brief EIC signal: EXTINT9 on PA25 mux A */
#define MUX_PA25A_EIC_EXTINT9           _L_(0)
#define PINMUX_PA25A_EIC_EXTINT9   ((PIN_PA25A_EIC_EXTINT9 << 16) | MUX_PA25A_EIC_EXTINT9)
#define PORT_PA25A_EIC_EXTINT9  (_UL_(1) << 25)
#define PIN_PA25A_EIC_EXTINT_NUM        _L_(9) /**< \brief EIC signal: PIN_PA25 External Interrupt Line */
#define PIN_PB09A_EIC_EXTINT9          _L_(41) /**< \brief EIC signal: EXTINT9 on PB09 mux A */
#define MUX_PB09A_EIC_EXTINT9           _L_(0)
#define PINMUX_PB09A_EIC_EXTINT9   ((PIN_PB09A_EIC_EXTINT9 << 16) | MUX_PB09A_EIC_EXTINT9)
#define PORT_PB09A_EIC_EXTINT9  (_UL_(1) <<  9)
#define PIN_PB09A_EIC_EXTINT_NUM        _L_(9) /**< \brief EIC signal: PIN_PB09 External Interrupt Line */
#define PIN_PB25A_EIC_EXTINT9          _L_(57) /**< \brief EIC signal: EXTINT9 on PB25 mux A */
#define MUX_PB25A_EIC_EXTINT9           _L_(0)
#define PINMUX_PB25A_EIC_EXTINT9   ((PIN_PB25A_EIC_EXTINT9 << 16) | MUX_PB25A_EIC_EXTINT9)
#define PORT_PB25A_EIC_EXTINT9  (_UL_(1) << 25)
#define PIN_PB25A_EIC_EXTINT_NUM        _L_(9) /**< \brief EIC signal: PIN_PB25 External Interrupt Line */
#define PIN_PC07A_EIC_EXTINT9          _L_(71) /**< \brief EIC signal: EXTINT9 on PC07 mux A */
#define MUX_PC07A_EIC_EXTINT9           _L_(0)
#define PINMUX_PC07A_EIC_EXTINT9   ((PIN_PC07A_EIC_EXTINT9 << 16) | MUX_PC07A_EIC_EXTINT9)
#define PORT_PC07A_EIC_EXTINT9  (_UL_(1) <<  7)
#define PIN_PC07A_EIC_EXTINT_NUM        _L_(9) /**< \brief EIC signal: PIN_PC07 External Interrupt Line */
#define PIN_PC25A_EIC_EXTINT9          _L_(89) /**< \brief EIC signal: EXTINT9 on PC25 mux A */
#define MUX_PC25A_EIC_EXTINT9           _L_(0)
#define PINMUX_PC25A_EIC_EXTINT9   ((PIN_PC25A_EIC_EXTINT9 << 16) | MUX_PC25A_EIC_EXTINT9)
#define PORT_PC25A_EIC_EXTINT9  (_UL_(1) << 25)
#define PIN_PC25A_EIC_EXTINT_NUM        _L_(9) /**< \brief EIC signal: PIN_PC25 External Interrupt Line */
#define PIN_PA10A_EIC_EXTINT10         _L_(10) /**< \brief EIC signal: EXTINT10 on PA10 mux A */
#define MUX_PA10A_EIC_EXTINT10          _L_(0)
#define PINMUX_PA10A_EIC_EXTINT10  ((PIN_PA10A_EIC_EXTINT10 << 16) | MUX_PA10A_EIC_EXTINT10)
#define PORT_PA10A_EIC_EXTINT10  (_UL_(1) << 10)
#define PIN_PA10A_EIC_EXTINT_NUM       _L_(10) /**< \brief EIC signal: PIN_PA10 External Interrupt Line */
#define PIN_PB10A_EIC_EXTINT10         _L_(42) /**< \brief EIC signal: EXTINT10 on PB10 mux A */
#define MUX_PB10A_EIC_EXTINT10          _L_(0)
#define PINMUX_PB10A_EIC_EXTINT10  ((PIN_PB10A_EIC_EXTINT10 << 16) | MUX_PB10A_EIC_EXTINT10)
#define PORT_PB10A_EIC_EXTINT10  (_UL_(1) << 10)
#define PIN_PB10A_EIC_EXTINT_NUM       _L_(10) /**< \brief EIC signal: PIN_PB10 External Interrupt Line */
#define PIN_PC10A_EIC_EXTINT10         _L_(74) /**< \brief EIC signal: EXTINT10 on PC10 mux A */
#define MUX_PC10A_EIC_EXTINT10          _L_(0)
#define PINMUX_PC10A_EIC_EXTINT10  ((PIN_PC10A_EIC_EXTINT10 << 16) | MUX_PC10A_EIC_EXTINT10)
#define PORT_PC10A_EIC_EXTINT10  (_UL_(1) << 10)
#define PIN_PC10A_EIC_EXTINT_NUM       _L_(10) /**< \brief EIC signal: PIN_PC10 External Interrupt Line */
#define PIN_PC26A_EIC_EXTINT10         _L_(90) /**< \brief EIC signal: EXTINT10 on PC26 mux A */
#define MUX_PC26A_EIC_EXTINT10          _L_(0)
#define PINMUX_PC26A_EIC_EXTINT10  ((PIN_PC26A_EIC_EXTINT10 << 16) | MUX_PC26A_EIC_EXTINT10)
#define PORT_PC26A_EIC_EXTINT10  (_UL_(1) << 26)
#define PIN_PC26A_EIC_EXTINT_NUM       _L_(10) /**< \brief EIC signal: PIN_PC26 External Interrupt Line */
#define PIN_PA11A_EIC_EXTINT11         _L_(11) /**< \brief EIC signal: EXTINT11 on PA11 mux A */
#define MUX_PA11A_EIC_EXTINT11          _L_(0)
#define PINMUX_PA11A_EIC_EXTINT11  ((PIN_PA11A_EIC_EXTINT11 << 16) | MUX_PA11A_EIC_EXTINT11)
#define PORT_PA11A_EIC_EXTINT11  (_UL_(1) << 11)
#define PIN_PA11A_EIC_EXTINT_NUM       _L_(11) /**< \brief EIC signal: PIN_PA11 External Interrupt Line */
#define PIN_PA27A_EIC_EXTINT11         _L_(27) /**< \brief EIC signal: EXTINT11 on PA27 mux A */
#define MUX_PA27A_EIC_EXTINT11          _L_(0)
#define PINMUX_PA27A_EIC_EXTINT11  ((PIN_PA27A_EIC_EXTINT11 << 16) | MUX_PA27A_EIC_EXTINT11)
#define PORT_PA27A_EIC_EXTINT11  (_UL_(1) << 27)
#define PIN_PA27A_EIC_EXTINT_NUM       _L_(11) /**< \brief EIC signal: PIN_PA27 External Interrupt Line */
#define PIN_PB11A_EIC_EXTINT11         _L_(43) /**< \brief EIC signal: EXTINT11 on PB11 mux A */
#define MUX_PB11A_EIC_EXTINT11          _L_(0)
#define PINMUX_PB11A_EIC_EXTINT11  ((PIN_PB11A_EIC_EXTINT11 << 16) | MUX_PB11A_EIC_EXTINT11)
#define PORT_PB11A_EIC_EXTINT11  (_UL_(1) << 11)
#define PIN_PB11A_EIC_EXTINT_NUM       _L_(11) /**< \brief EIC signal: PIN_PB11 External Interrupt Line */
#define PIN_PC11A_EIC_EXTINT11         _L_(75) /**< \brief EIC signal: EXTINT11 on PC11 mux A */
#define MUX_PC11A_EIC_EXTINT11          _L_(0)
#define PINMUX_PC11A_EIC_EXTINT11  ((PIN_PC11A_EIC_EXTINT11 << 16) | MUX_PC11A_EIC_EXTINT11)
#define PORT_PC11A_EIC_EXTINT11  (_UL_(1) << 11)
#define PIN_PC11A_EIC_EXTINT_NUM       _L_(11) /**< \brief EIC signal: PIN_PC11 External Interrupt Line */
#define PIN_PC27A_EIC_EXTINT11         _L_(91) /**< \brief EIC signal: EXTINT11 on PC27 mux A */
#define MUX_PC27A_EIC_EXTINT11          _L_(0)
#define PINMUX_PC27A_EIC_EXTINT11  ((PIN_PC27A_EIC_EXTINT11 << 16) | MUX_PC27A_EIC_EXTINT11)
#define PORT_PC27A_EIC_EXTINT11  (_UL_(1) << 27)
#define PIN_PC27A_EIC_EXTINT_NUM       _L_(11) /**< \brief EIC signal: PIN_PC27 External Interrupt Line */
#define PIN_PA12A_EIC_EXTINT12         _L_(12) /**< \brief EIC signal: EXTINT12 on PA12 mux A */
#define MUX_PA12A_EIC_EXTINT12          _L_(0)
#define PINMUX_PA12A_EIC_EXTINT12  ((PIN_PA12A_EIC_EXTINT12 << 16) | MUX_PA12A_EIC_EXTINT12)
#define PORT_PA12A_EIC_EXTINT12  (_UL_(1) << 12)
#define PIN_PA12A_EIC_EXTINT_NUM       _L_(12) /**< \brief EIC signal: PIN_PA12 External Interrupt Line */
#define PIN_PB12A_EIC_EXTINT12         _L_(44) /**< \brief EIC signal: EXTINT12 on PB12 mux A */
#define MUX_PB12A_EIC_EXTINT12          _L_(0)
#define PINMUX_PB12A_EIC_EXTINT12  ((PIN_PB12A_EIC_EXTINT12 << 16) | MUX_PB12A_EIC_EXTINT12)
#define PORT_PB12A_EIC_EXTINT12  (_UL_(1) << 12)
#define PIN_PB12A_EIC_EXTINT_NUM       _L_(12) /**< \brief EIC signal: PIN_PB12 External Interrupt Line */
#define PIN_PC12A_EIC_EXTINT12         _L_(76) /**< \brief EIC signal: EXTINT12 on PC12 mux A */
#define MUX_PC12A_EIC_EXTINT12          _L_(0)
#define PINMUX_PC12A_EIC_EXTINT12  ((PIN_PC12A_EIC_EXTINT12 << 16) | MUX_PC12A_EIC_EXTINT12)
#define PORT_PC12A_EIC_EXTINT12  (_UL_(1) << 12)
#define PIN_PC12A_EIC_EXTINT_NUM       _L_(12) /**< \brief EIC signal: PIN_PC12 External Interrupt Line */
#define PIN_PC28A_EIC_EXTINT12         _L_(92) /**< \brief EIC signal: EXTINT12 on PC28 mux A */
#define MUX_PC28A_EIC_EXTINT12          _L_(0)
#define PINMUX_PC28A_EIC_EXTINT12  ((PIN_PC28A_EIC_EXTINT12 << 16) | MUX_PC28A_EIC_EXTINT12)
#define PORT_PC28A_EIC_EXTINT12  (_UL_(1) << 28)
#define PIN_PC28A_EIC_EXTINT_NUM       _L_(12) /**< \brief EIC signal: PIN_PC28 External Interrupt Line */
#define PIN_PA13A_EIC_EXTINT13         _L_(13) /**< \brief EIC signal: EXTINT13 on PA13 mux A */
#define MUX_PA13A_EIC_EXTINT13          _L_(0)
#define PINMUX_PA13A_EIC_EXTINT13  ((PIN_PA13A_EIC_EXTINT13 << 16) | MUX_PA13A_EIC_EXTINT13)
#define PORT_PA13A_EIC_EXTINT13  (_UL_(1) << 13)
#define PIN_PA13A_EIC_EXTINT_NUM       _L_(13) /**< \brief EIC signal: PIN_PA13 External Interrupt Line */
#define PIN_PB13A_EIC_EXTINT13         _L_(45) /**< \brief EIC signal: EXTINT13 on PB13 mux A */
#define MUX_PB13A_EIC_EXTINT13          _L_(0)
#define PINMUX_PB13A_EIC_EXTINT13  ((PIN_PB13A_EIC_EXTINT13 << 16) | MUX_PB13A_EIC_EXTINT13)
#define PORT_PB13A_EIC_EXTINT13  (_UL_(1) << 13)
#define PIN_PB13A_EIC_EXTINT_NUM       _L_(13) /**< \brief EIC signal: PIN_PB13 External Interrupt Line */
#define PIN_PC13A_EIC_EXTINT13         _L_(77) /**< \brief EIC signal: EXTINT13 on PC13 mux A */
#define MUX_PC13A_EIC_EXTINT13          _L_(0)
#define PINMUX_PC13A_EIC_EXTINT13  ((PIN_PC13A_EIC_EXTINT13 << 16) | MUX_PC13A_EIC_EXTINT13)
#define PORT_PC13A_EIC_EXTINT13  (_UL_(1) << 13)
#define PIN_PC13A_EIC_EXTINT_NUM       _L_(13) /**< \brief EIC signal: PIN_PC13 External Interrupt Line */
#define PIN_PA30A_EIC_EXTINT14         _L_(30) /**< \brief EIC signal: EXTINT14 on PA30 mux A */
#define MUX_PA30A_EIC_EXTINT14          _L_(0)
#define PINMUX_PA30A_EIC_EXTINT14  ((PIN_PA30A_EIC_EXTINT14 << 16) | MUX_PA30A_EIC_EXTINT14)
#define PORT_PA30A_EIC_EXTINT14  (_UL_(1) << 30)
#define PIN_PA30A_EIC_EXTINT_NUM       _L_(14) /**< \brief EIC signal: PIN_PA30 External Interrupt Line */
#define PIN_PB14A_EIC_EXTINT14         _L_(46) /**< \brief EIC signal: EXTINT14 on PB14 mux A */
#define MUX_PB14A_EIC_EXTINT14          _L_(0)
#define PINMUX_PB14A_EIC_EXTINT14  ((PIN_PB14A_EIC_EXTINT14 << 16) | MUX_PB14A_EIC_EXTINT14)
#define PORT_PB14A_EIC_EXTINT14  (_UL_(1) << 14)
#define PIN_PB14A_EIC_EXTINT_NUM       _L_(14) /**< \brief EIC signal: PIN_PB14 External Interrupt Line */
#define PIN_PB30A_EIC_EXTINT14         _L_(62) /**< \brief EIC signal: EXTINT14 on PB30 mux A */
#define MUX_PB30A_EIC_EXTINT14          _L_(0)
#define PINMUX_PB30A_EIC_EXTINT14  ((PIN_PB30A_EIC_EXTINT14 << 16) | MUX_PB30A_EIC_EXTINT14)
#define PORT_PB30A_EIC_EXTINT14  (_UL_(1) << 30)
#define PIN_PB30A_EIC_EXTINT_NUM       _L_(14) /**< \brief EIC signal: PIN_PB30 External Interrupt Line */
#define PIN_PC14A_EIC_EXTINT14         _L_(78) /**< \brief EIC signal: EXTINT14 on PC14 mux A */
#define MUX_PC14A_EIC_EXTINT14          _L_(0)
#define PINMUX_PC14A_EIC_EXTINT14  ((PIN_PC14A_EIC_EXTINT14 << 16) | MUX_PC14A_EIC_EXTINT14)
#define PORT_PC14A_EIC_EXTINT14  (_UL_(1) << 14)
#define PIN_PC14A_EIC_EXTINT_NUM       _L_(14) /**< \brief EIC signal: PIN_PC14 External Interrupt Line */
#define PIN_PA14A_EIC_EXTINT14         _L_(14) /**< \brief EIC signal: EXTINT14 on PA14 mux A */
#define MUX_PA14A_EIC_EXTINT14          _L_(0)
#define PINMUX_PA14A_EIC_EXTINT14  ((PIN_PA14A_EIC_EXTINT14 << 16) | MUX_PA14A_EIC_EXTINT14)
#define PORT_PA14A_EIC_EXTINT14  (_UL_(1) << 14)
#define PIN_PA14A_EIC_EXTINT_NUM       _L_(14) /**< \brief EIC signal: PIN_PA14 External Interrupt Line */
#define PIN_PA15A_EIC_EXTINT15         _L_(15) /**< \brief EIC signal: EXTINT15 on PA15 mux A */
#define MUX_PA15A_EIC_EXTINT15          _L_(0)
#define PINMUX_PA15A_EIC_EXTINT15  ((PIN_PA15A_EIC_EXTINT15 << 16) | MUX_PA15A_EIC_EXTINT15)
#define PORT_PA15A_EIC_EXTINT15  (_UL_(1) << 15)
#define PIN_PA15A_EIC_EXTINT_NUM       _L_(15) /**< \brief EIC signal: PIN_PA15 External Interrupt Line */
#define PIN_PA31A_EIC_EXTINT15         _L_(31) /**< \brief EIC signal: EXTINT15 on PA31 mux A */
#define MUX_PA31A_EIC_EXTINT15          _L_(0)
#define PINMUX_PA31A_EIC_EXTINT15  ((PIN_PA31A_EIC_EXTINT15 << 16) | MUX_PA31A_EIC_EXTINT15)
#define PORT_PA31A_EIC_EXTINT15  (_UL_(1) << 31)
#define PIN_PA31A_EIC_EXTINT_NUM       _L_(15) /**< \brief EIC signal: PIN_PA31 External Interrupt Line */
#define PIN_PB15A_EIC_EXTINT15         _L_(47) /**< \brief EIC signal: EXTINT15 on PB15 mux A */
#define MUX_PB15A_EIC_EXTINT15          _L_(0)
#define PINMUX_PB15A_EIC_EXTINT15  ((PIN_PB15A_EIC_EXTINT15 << 16) | MUX_PB15A_EIC_EXTINT15)
#define PORT_PB15A_EIC_EXTINT15  (_UL_(1) << 15)
#define PIN_PB15A_EIC_EXTINT_NUM       _L_(15) /**< \brief EIC signal: PIN_PB15 External Interrupt Line */
#define PIN_PB31A_EIC_EXTINT15         _L_(63) /**< \brief EIC signal: EXTINT15 on PB31 mux A */
#define MUX_PB31A_EIC_EXTINT15          _L_(0)
#define PINMUX_PB31A_EIC_EXTINT15  ((PIN_PB31A_EIC_EXTINT15 << 16) | MUX_PB31A_EIC_EXTINT15)
#define PORT_PB31A_EIC_EXTINT15  (_UL_(1) << 31)
#define PIN_PB31A_EIC_EXTINT_NUM       _L_(15) /**< \brief EIC signal: PIN_PB31 External Interrupt Line */
#define PIN_PC15A_EIC_EXTINT15         _L_(79) /**< \brief EIC signal: EXTINT15 on PC15 mux A */
#define MUX_PC15A_EIC_EXTINT15          _L_(0)
#define PINMUX_PC15A_EIC_EXTINT15  ((PIN_PC15A_EIC_EXTINT15 << 16) | MUX_PC15A_EIC_EXTINT15)
#define PORT_PC15A_EIC_EXTINT15  (_UL_(1) << 15)
#define PIN_PC15A_EIC_EXTINT_NUM       _L_(15) /**< \brief EIC signal: PIN_PC15 External Interrupt Line */
#define PIN_PA08A_EIC_NMI               _L_(8) /**< \brief EIC signal: NMI on PA08 mux A */
#define MUX_PA08A_EIC_NMI               _L_(0)
#define PINMUX_PA08A_EIC_NMI       ((PIN_PA08A_EIC_NMI << 16) | MUX_PA08A_EIC_NMI)
#define PORT_PA08A_EIC_NMI     (_UL_(1) <<  8)
/* ========== PORT definition for SERCOM0 peripheral ========== */
#define PIN_PA04D_SERCOM0_PAD0          _L_(4) /**< \brief SERCOM0 signal: PAD0 on PA04 mux D */
#define MUX_PA04D_SERCOM0_PAD0          _L_(3)
#define PINMUX_PA04D_SERCOM0_PAD0  ((PIN_PA04D_SERCOM0_PAD0 << 16) | MUX_PA04D_SERCOM0_PAD0)
#define PORT_PA04D_SERCOM0_PAD0  (_UL_(1) <<  4)
#define PIN_PC17D_SERCOM0_PAD0         _L_(81) /**< \brief SERCOM0 signal: PAD0 on PC17 mux D */
#define MUX_PC17D_SERCOM0_PAD0          _L_(3)
#define PINMUX_PC17D_SERCOM0_PAD0  ((PIN_PC17D_SERCOM0_PAD0 << 16) | MUX_PC17D_SERCOM0_PAD0)
#define PORT_PC17D_SERCOM0_PAD0  (_UL_(1) << 17)
#define PIN_PA08C_SERCOM0_PAD0          _L_(8) /**< \brief SERCOM0 signal: PAD0 on PA08 mux C */
#define MUX_PA08C_SERCOM0_PAD0          _L_(2)
#define PINMUX_PA08C_SERCOM0_PAD0  ((PIN_PA08C_SERCOM0_PAD0 << 16) | MUX_PA08C_SERCOM0_PAD0)
#define PORT_PA08C_SERCOM0_PAD0  (_UL_(1) <<  8)
#define PIN_PB24C_SERCOM0_PAD0         _L_(56) /**< \brief SERCOM0 signal: PAD0 on PB24 mux C */
#define MUX_PB24C_SERCOM0_PAD0          _L_(2)
#define PINMUX_PB24C_SERCOM0_PAD0  ((PIN_PB24C_SERCOM0_PAD0 << 16) | MUX_PB24C_SERCOM0_PAD0)
#define PORT_PB24C_SERCOM0_PAD0  (_UL_(1) << 24)
#define PIN_PA05D_SERCOM0_PAD1          _L_(5) /**< \brief SERCOM0 signal: PAD1 on PA05 mux D */
#define MUX_PA05D_SERCOM0_PAD1          _L_(3)
#define PINMUX_PA05D_SERCOM0_PAD1  ((PIN_PA05D_SERCOM0_PAD1 << 16) | MUX_PA05D_SERCOM0_PAD1)
#define PORT_PA05D_SERCOM0_PAD1  (_UL_(1) <<  5)
#define PIN_PC16D_SERCOM0_PAD1         _L_(80) /**< \brief SERCOM0 signal: PAD1 on PC16 mux D */
#define MUX_PC16D_SERCOM0_PAD1          _L_(3)
#define PINMUX_PC16D_SERCOM0_PAD1  ((PIN_PC16D_SERCOM0_PAD1 << 16) | MUX_PC16D_SERCOM0_PAD1)
#define PORT_PC16D_SERCOM0_PAD1  (_UL_(1) << 16)
#define PIN_PA09C_SERCOM0_PAD1          _L_(9) /**< \brief SERCOM0 signal: PAD1 on PA09 mux C */
#define MUX_PA09C_SERCOM0_PAD1          _L_(2)
#define PINMUX_PA09C_SERCOM0_PAD1  ((PIN_PA09C_SERCOM0_PAD1 << 16) | MUX_PA09C_SERCOM0_PAD1)
#define PORT_PA09C_SERCOM0_PAD1  (_UL_(1) <<  9)
#define PIN_PB25C_SERCOM0_PAD1         _L_(57) /**< \brief SERCOM0 signal: PAD1 on PB25 mux C */
#define MUX_PB25C_SERCOM0_PAD1          _L_(2)
#define PINMUX_PB25C_SERCOM0_PAD1  ((PIN_PB25C_SERCOM0_PAD1 << 16) | MUX_PB25C_SERCOM0_PAD1)
#define PORT_PB25C_SERCOM0_PAD1  (_UL_(1) << 25)
#define PIN_PA06D_SERCOM0_PAD2          _L_(6) /**< \brief SERCOM0 signal: PAD2 on PA06 mux D */
#define MUX_PA06D_SERCOM0_PAD2          _L_(3)
#define PINMUX_PA06D_SERCOM0_PAD2  ((PIN_PA06D_SERCOM0_PAD2 << 16) | MUX_PA06D_SERCOM0_PAD2)
#define PORT_PA06D_SERCOM0_PAD2  (_UL_(1) <<  6)
#define PIN_PC18D_SERCOM0_PAD2         _L_(82) /**< \brief SERCOM0 signal: PAD2 on PC18 mux D */
#define MUX_PC18D_SERCOM0_PAD2          _L_(3)
#define PINMUX_PC18D_SERCOM0_PAD2  ((PIN_PC18D_SERCOM0_PAD2 << 16) | MUX_PC18D_SERCOM0_PAD2)
#define PORT_PC18D_SERCOM0_PAD2  (_UL_(1) << 18)
#define PIN_PA10C_SERCOM0_PAD2         _L_(10) /**< \brief SERCOM0 signal: PAD2 on PA10 mux C */
#define MUX_PA10C_SERCOM0_PAD2          _L_(2)
#define PINMUX_PA10C_SERCOM0_PAD2  ((PIN_PA10C_SERCOM0_PAD2 << 16) | MUX_PA10C_SERCOM0_PAD2)
#define PORT_PA10C_SERCOM0_PAD2  (_UL_(1) << 10)
#define PIN_PC24C_SERCOM0_PAD2         _L_(88) /**< \brief SERCOM0 signal: PAD2 on PC24 mux C */
#define MUX_PC24C_SERCOM0_PAD2          _L_(2)
#define PINMUX_PC24C_SERCOM0_PAD2  ((PIN_PC24C_SERCOM0_PAD2 << 16) | MUX_PC24C_SERCOM0_PAD2)
#define PORT_PC24C_SERCOM0_PAD2  (_UL_(1) << 24)
#define PIN_PA07D_SERCOM0_PAD3          _L_(7) /**< \brief SERCOM0 signal: PAD3 on PA07 mux D */
#define MUX_PA07D_SERCOM0_PAD3          _L_(3)
#define PINMUX_PA07D_SERCOM0_PAD3  ((PIN_PA07D_SERCOM0_PAD3 << 16) | MUX_PA07D_SERCOM0_PAD3)
#define PORT_PA07D_SERCOM0_PAD3  (_UL_(1) <<  7)
#define PIN_PC19D_SERCOM0_PAD3         _L_(83) /**< \brief SERCOM0 signal: PAD3 on PC19 mux D */
#define MUX_PC19D_SERCOM0_PAD3          _L_(3)
#define PINMUX_PC19D_SERCOM0_PAD3  ((PIN_PC19D_SERCOM0_PAD3 << 16) | MUX_PC19D_SERCOM0_PAD3)
#define PORT_PC19D_SERCOM0_PAD3  (_UL_(1) << 19)
#define PIN_PA11C_SERCOM0_PAD3         _L_(11) /**< \brief SERCOM0 signal: PAD3 on PA11 mux C */
#define MUX_PA11C_SERCOM0_PAD3          _L_(2)
#define PINMUX_PA11C_SERCOM0_PAD3  ((PIN_PA11C_SERCOM0_PAD3 << 16) | MUX_PA11C_SERCOM0_PAD3)
#define PORT_PA11C_SERCOM0_PAD3  (_UL_(1) << 11)
#define PIN_PC25C_SERCOM0_PAD3         _L_(89) /**< \brief SERCOM0 signal: PAD3 on PC25 mux C */
#define MUX_PC25C_SERCOM0_PAD3          _L_(2)
#define PINMUX_PC25C_SERCOM0_PAD3  ((PIN_PC25C_SERCOM0_PAD3 << 16) | MUX_PC25C_SERCOM0_PAD3)
#define PORT_PC25C_SERCOM0_PAD3  (_UL_(1) << 25)
/* ========== PORT definition for SERCOM1 peripheral ========== */
#define PIN_PA00D_SERCOM1_PAD0          _L_(0) /**< \brief SERCOM1 signal: PAD0 on PA00 mux D */
#define MUX_PA00D_SERCOM1_PAD0          _L_(3)
#define PINMUX_PA00D_SERCOM1_PAD0  ((PIN_PA00D_SERCOM1_PAD0 << 16) | MUX_PA00D_SERCOM1_PAD0)
#define PORT_PA00D_SERCOM1_PAD0  (_UL_(1) <<  0)
#define PIN_PA16C_SERCOM1_PAD0         _L_(16) /**< \brief SERCOM1 signal: PAD0 on PA16 mux C */
#define MUX_PA16C_SERCOM1_PAD0          _L_(2)
#define PINMUX_PA16C_SERCOM1_PAD0  ((PIN_PA16C_SERCOM1_PAD0 << 16) | MUX_PA16C_SERCOM1_PAD0)
#define PORT_PA16C_SERCOM1_PAD0  (_UL_(1) << 16)
#define PIN_PC27C_SERCOM1_PAD0         _L_(91) /**< \brief SERCOM1 signal: PAD0 on PC27 mux C */
#define MUX_PC27C_SERCOM1_PAD0          _L_(2)
#define PINMUX_PC27C_SERCOM1_PAD0  ((PIN_PC27C_SERCOM1_PAD0 << 16) | MUX_PC27C_SERCOM1_PAD0)
#define PORT_PC27C_SERCOM1_PAD0  (_UL_(1) << 27)
#define PIN_PA01D_SERCOM1_PAD1          _L_(1) /**< \brief SERCOM1 signal: PAD1 on PA01 mux D */
#define MUX_PA01D_SERCOM1_PAD1          _L_(3)
#define PINMUX_PA01D_SERCOM1_PAD1  ((PIN_PA01D_SERCOM1_PAD1 << 16) | MUX_PA01D_SERCOM1_PAD1)
#define PORT_PA01D_SERCOM1_PAD1  (_UL_(1) <<  1)
#define PIN_PA17C_SERCOM1_PAD1         _L_(17) /**< \brief SERCOM1 signal: PAD1 on PA17 mux C */
#define MUX_PA17C_SERCOM1_PAD1          _L_(2)
#define PINMUX_PA17C_SERCOM1_PAD1  ((PIN_PA17C_SERCOM1_PAD1 << 16) | MUX_PA17C_SERCOM1_PAD1)
#define PORT_PA17C_SERCOM1_PAD1  (_UL_(1) << 17)
#define PIN_PC28C_SERCOM1_PAD1         _L_(92) /**< \brief SERCOM1 signal: PAD1 on PC28 mux C */
#define MUX_PC28C_SERCOM1_PAD1          _L_(2)
#define PINMUX_PC28C_SERCOM1_PAD1  ((PIN_PC28C_SERCOM1_PAD1 << 16) | MUX_PC28C_SERCOM1_PAD1)
#define PORT_PC28C_SERCOM1_PAD1  (_UL_(1) << 28)
#define PIN_PA30D_SERCOM1_PAD2         _L_(30) /**< \brief SERCOM1 signal: PAD2 on PA30 mux D */
#define MUX_PA30D_SERCOM1_PAD2          _L_(3)
#define PINMUX_PA30D_SERCOM1_PAD2  ((PIN_PA30D_SERCOM1_PAD2 << 16) | MUX_PA30D_SERCOM1_PAD2)
#define PORT_PA30D_SERCOM1_PAD2  (_UL_(1) << 30)
#define PIN_PA18C_SERCOM1_PAD2         _L_(18) /**< \brief SERCOM1 signal: PAD2 on PA18 mux C */
#define MUX_PA18C_SERCOM1_PAD2          _L_(2)
#define PINMUX_PA18C_SERCOM1_PAD2  ((PIN_PA18C_SERCOM1_PAD2 << 16) | MUX_PA18C_SERCOM1_PAD2)
#define PORT_PA18C_SERCOM1_PAD2  (_UL_(1) << 18)
#define PIN_PB22C_SERCOM1_PAD2         _L_(54) /**< \brief SERCOM1 signal: PAD2 on PB22 mux C */
#define MUX_PB22C_SERCOM1_PAD2          _L_(2)
#define PINMUX_PB22C_SERCOM1_PAD2  ((PIN_PB22C_SERCOM1_PAD2 << 16) | MUX_PB22C_SERCOM1_PAD2)
#define PORT_PB22C_SERCOM1_PAD2  (_UL_(1) << 22)
#define PIN_PA31D_SERCOM1_PAD3         _L_(31) /**< \brief SERCOM1 signal: PAD3 on PA31 mux D */
#define MUX_PA31D_SERCOM1_PAD3          _L_(3)
#define PINMUX_PA31D_SERCOM1_PAD3  ((PIN_PA31D_SERCOM1_PAD3 << 16) | MUX_PA31D_SERCOM1_PAD3)
#define PORT_PA31D_SERCOM1_PAD3  (_UL_(1) << 31)
#define PIN_PA19C_SERCOM1_PAD3         _L_(19) /**< \brief SERCOM1 signal: PAD3 on PA19 mux C */
#define MUX_PA19C_SERCOM1_PAD3          _L_(2)
#define PINMUX_PA19C_SERCOM1_PAD3  ((PIN_PA19C_SERCOM1_PAD3 << 16) | MUX_PA19C_SERCOM1_PAD3)
#define PORT_PA19C_SERCOM1_PAD3  (_UL_(1) << 19)
#define PIN_PB23C_SERCOM1_PAD3         _L_(55) /**< \brief SERCOM1 signal: PAD3 on PB23 mux C */
#define MUX_PB23C_SERCOM1_PAD3          _L_(2)
#define PINMUX_PB23C_SERCOM1_PAD3  ((PIN_PB23C_SERCOM1_PAD3 << 16) | MUX_PB23C_SERCOM1_PAD3)
#define PORT_PB23C_SERCOM1_PAD3  (_UL_(1) << 23)
/* ========== PORT definition for TC0 peripheral ========== */
#define PIN_PA04E_TC0_WO0               _L_(4) /**< \brief TC0 signal: WO0 on PA04 mux E */
#define MUX_PA04E_TC0_WO0               _L_(4)
#define PINMUX_PA04E_TC0_WO0       ((PIN_PA04E_TC0_WO0 << 16) | MUX_PA04E_TC0_WO0)
#define PORT_PA04E_TC0_WO0     (_UL_(1) <<  4)
#define PIN_PA08E_TC0_WO0               _L_(8) /**< \brief TC0 signal: WO0 on PA08 mux E */
#define MUX_PA08E_TC0_WO0               _L_(4)
#define PINMUX_PA08E_TC0_WO0       ((PIN_PA08E_TC0_WO0 << 16) | MUX_PA08E_TC0_WO0)
#define PORT_PA08E_TC0_WO0     (_UL_(1) <<  8)
#define PIN_PB30E_TC0_WO0              _L_(62) /**< \brief TC0 signal: WO0 on PB30 mux E */
#define MUX_PB30E_TC0_WO0               _L_(4)
#define PINMUX_PB30E_TC0_WO0       ((PIN_PB30E_TC0_WO0 << 16) | MUX_PB30E_TC0_WO0)
#define PORT_PB30E_TC0_WO0     (_UL_(1) << 30)
#define PIN_PA05E_TC0_WO1               _L_(5) /**< \brief TC0 signal: WO1 on PA05 mux E */
#define MUX_PA05E_TC0_WO1               _L_(4)
#define PINMUX_PA05E_TC0_WO1       ((PIN_PA05E_TC0_WO1 << 16) | MUX_PA05E_TC0_WO1)
#define PORT_PA05E_TC0_WO1     (_UL_(1) <<  5)
#define PIN_PA09E_TC0_WO1               _L_(9) /**< \brief TC0 signal: WO1 on PA09 mux E */
#define MUX_PA09E_TC0_WO1               _L_(4)
#define PINMUX_PA09E_TC0_WO1       ((PIN_PA09E_TC0_WO1 << 16) | MUX_PA09E_TC0_WO1)
#define PORT_PA09E_TC0_WO1     (_UL_(1) <<  9)
#define PIN_PB31E_TC0_WO1              _L_(63) /**< \brief TC0 signal: WO1 on PB31 mux E */
#define MUX_PB31E_TC0_WO1               _L_(4)
#define PINMUX_PB31E_TC0_WO1       ((PIN_PB31E_TC0_WO1 << 16) | MUX_PB31E_TC0_WO1)
#define PORT_PB31E_TC0_WO1     (_UL_(1) << 31)
/* ========== PORT definition for TC1 peripheral ========== */
#define PIN_PA06E_TC1_WO0               _L_(6) /**< \brief TC1 signal: WO0 on PA06 mux E */
#define MUX_PA06E_TC1_WO0               _L_(4)
#define PINMUX_PA06E_TC1_WO0       ((PIN_PA06E_TC1_WO0 << 16) | MUX_PA06E_TC1_WO0)
#define PORT_PA06E_TC1_WO0     (_UL_(1) <<  6)
#define PIN_PA10E_TC1_WO0              _L_(10) /**< \brief TC1 signal: WO0 on PA10 mux E */
#define MUX_PA10E_TC1_WO0               _L_(4)
#define PINMUX_PA10E_TC1_WO0       ((PIN_PA10E_TC1_WO0 << 16) | MUX_PA10E_TC1_WO0)
#define PORT_PA10E_TC1_WO0     (_UL_(1) << 10)
#define PIN_PA07E_TC1_WO1               _L_(7) /**< \brief TC1 signal: WO1 on PA07 mux E */
#define MUX_PA07E_TC1_WO1               _L_(4)
#define PINMUX_PA07E_TC1_WO1       ((PIN_PA07E_TC1_WO1 << 16) | MUX_PA07E_TC1_WO1)
#define PORT_PA07E_TC1_WO1     (_UL_(1) <<  7)
#define PIN_PA11E_TC1_WO1              _L_(11) /**< \brief TC1 signal: WO1 on PA11 mux E */
#define MUX_PA11E_TC1_WO1               _L_(4)
#define PINMUX_PA11E_TC1_WO1       ((PIN_PA11E_TC1_WO1 << 16) | MUX_PA11E_TC1_WO1)
#define PORT_PA11E_TC1_WO1     (_UL_(1) << 11)
/* ========== PORT definition for USB peripheral ========== */
#define PIN_PA24H_USB_DM               _L_(24) /**< \brief USB signal: DM on PA24 mux H */
#define MUX_PA24H_USB_DM                _L_(7)
#define PINMUX_PA24H_USB_DM        ((PIN_PA24H_USB_DM << 16) | MUX_PA24H_USB_DM)
#define PORT_PA24H_USB_DM      (_UL_(1) << 24)
#define PIN_PA25H_USB_DP               _L_(25) /**< \brief USB signal: DP on PA25 mux H */
#define MUX_PA25H_USB_DP                _L_(7)
#define PINMUX_PA25H_USB_DP        ((PIN_PA25H_USB_DP << 16) | MUX_PA25H_USB_DP)
#define PORT_PA25H_USB_DP      (_UL_(1) << 25)
#define PIN_PA23H_USB_SOF_1KHZ         _L_(23) /**< \brief USB signal: SOF_1KHZ on PA23 mux H */
#define MUX_PA23H_USB_SOF_1KHZ          _L_(7)
#define PINMUX_PA23H_USB_SOF_1KHZ  ((PIN_PA23H_USB_SOF_1KHZ << 16) | MUX_PA23H_USB_SOF_1KHZ)
#define PORT_PA23H_USB_SOF_1KHZ  (_UL_(1) << 23)
#define PIN_PB22H_USB_SOF_1KHZ         _L_(54) /**< \brief USB signal: SOF_1KHZ on PB22 mux H */
#define MUX_PB22H_USB_SOF_1KHZ          _L_(7)
#define PINMUX_PB22H_USB_SOF_1KHZ  ((PIN_PB22H_USB_SOF_1KHZ << 16) | MUX_PB22H_USB_SOF_1KHZ)
#define PORT_PB22H_USB_SOF_1KHZ  (_UL_(1) << 22)
/* ========== PORT definition for SERCOM2 peripheral ========== */
#define PIN_PA09D_SERCOM2_PAD0          _L_(9) /**< \brief SERCOM2 signal: PAD0 on PA09 mux D */
#define MUX_PA09D_SERCOM2_PAD0          _L_(3)
#define PINMUX_PA09D_SERCOM2_PAD0  ((PIN_PA09D_SERCOM2_PAD0 << 16) | MUX_PA09D_SERCOM2_PAD0)
#define PORT_PA09D_SERCOM2_PAD0  (_UL_(1) <<  9)
#define PIN_PB25D_SERCOM2_PAD0         _L_(57) /**< \brief SERCOM2 signal: PAD0 on PB25 mux D */
#define MUX_PB25D_SERCOM2_PAD0          _L_(3)
#define PINMUX_PB25D_SERCOM2_PAD0  ((PIN_PB25D_SERCOM2_PAD0 << 16) | MUX_PB25D_SERCOM2_PAD0)
#define PORT_PB25D_SERCOM2_PAD0  (_UL_(1) << 25)
#define PIN_PA12C_SERCOM2_PAD0         _L_(12) /**< \brief SERCOM2 signal: PAD0 on PA12 mux C */
#define MUX_PA12C_SERCOM2_PAD0          _L_(2)
#define PINMUX_PA12C_SERCOM2_PAD0  ((PIN_PA12C_SERCOM2_PAD0 << 16) | MUX_PA12C_SERCOM2_PAD0)
#define PORT_PA12C_SERCOM2_PAD0  (_UL_(1) << 12)
#define PIN_PA08D_SERCOM2_PAD1          _L_(8) /**< \brief SERCOM2 signal: PAD1 on PA08 mux D */
#define MUX_PA08D_SERCOM2_PAD1          _L_(3)
#define PINMUX_PA08D_SERCOM2_PAD1  ((PIN_PA08D_SERCOM2_PAD1 << 16) | MUX_PA08D_SERCOM2_PAD1)
#define PORT_PA08D_SERCOM2_PAD1  (_UL_(1) <<  8)
#define PIN_PB24D_SERCOM2_PAD1         _L_(56) /**< \brief SERCOM2 signal: PAD1 on PB24 mux D */
#define MUX_PB24D_SERCOM2_PAD1          _L_(3)
#define PINMUX_PB24D_SERCOM2_PAD1  ((PIN_PB24D_SERCOM2_PAD1 << 16) | MUX_PB24D_SERCOM2_PAD1)
#define PORT_PB24D_SERCOM2_PAD1  (_UL_(1) << 24)
#define PIN_PA13C_SERCOM2_PAD1         _L_(13) /**< \brief SERCOM2 signal: PAD1 on PA13 mux C */
#define MUX_PA13C_SERCOM2_PAD1          _L_(2)
#define PINMUX_PA13C_SERCOM2_PAD1  ((PIN_PA13C_SERCOM2_PAD1 << 16) | MUX_PA13C_SERCOM2_PAD1)
#define PORT_PA13C_SERCOM2_PAD1  (_UL_(1) << 13)
#define PIN_PA10D_SERCOM2_PAD2         _L_(10) /**< \brief SERCOM2 signal: PAD2 on PA10 mux D */
#define MUX_PA10D_SERCOM2_PAD2          _L_(3)
#define PINMUX_PA10D_SERCOM2_PAD2  ((PIN_PA10D_SERCOM2_PAD2 << 16) | MUX_PA10D_SERCOM2_PAD2)
#define PORT_PA10D_SERCOM2_PAD2  (_UL_(1) << 10)
#define PIN_PC24D_SERCOM2_PAD2         _L_(88) /**< \brief SERCOM2 signal: PAD2 on PC24 mux D */
#define MUX_PC24D_SERCOM2_PAD2          _L_(3)
#define PINMUX_PC24D_SERCOM2_PAD2  ((PIN_PC24D_SERCOM2_PAD2 << 16) | MUX_PC24D_SERCOM2_PAD2)
#define PORT_PC24D_SERCOM2_PAD2  (_UL_(1) << 24)
#define PIN_PA14C_SERCOM2_PAD2         _L_(14) /**< \brief SERCOM2 signal: PAD2 on PA14 mux C */
#define MUX_PA14C_SERCOM2_PAD2          _L_(2)
#define PINMUX_PA14C_SERCOM2_PAD2  ((PIN_PA14C_SERCOM2_PAD2 << 16) | MUX_PA14C_SERCOM2_PAD2)
#define PORT_PA14C_SERCOM2_PAD2  (_UL_(1) << 14)
#define PIN_PA11D_SERCOM2_PAD3         _L_(11) /**< \brief SERCOM2 signal: PAD3 on PA11 mux D */
#define MUX_PA11D_SERCOM2_PAD3          _L_(3)
#define PINMUX_PA11D_SERCOM2_PAD3  ((PIN_PA11D_SERCOM2_PAD3 << 16) | MUX_PA11D_SERCOM2_PAD3)
#define PORT_PA11D_SERCOM2_PAD3  (_UL_(1) << 11)
#define PIN_PC25D_SERCOM2_PAD3         _L_(89) /**< \brief SERCOM2 signal: PAD3 on PC25 mux D */
#define MUX_PC25D_SERCOM2_PAD3          _L_(3)
#define PINMUX_PC25D_SERCOM2_PAD3  ((PIN_PC25D_SERCOM2_PAD3 << 16) | MUX_PC25D_SERCOM2_PAD3)
#define PORT_PC25D_SERCOM2_PAD3  (_UL_(1) << 25)
#define PIN_PA15C_SERCOM2_PAD3         _L_(15) /**< \brief SERCOM2 signal: PAD3 on PA15 mux C */
#define MUX_PA15C_SERCOM2_PAD3          _L_(2)
#define PINMUX_PA15C_SERCOM2_PAD3  ((PIN_PA15C_SERCOM2_PAD3 << 16) | MUX_PA15C_SERCOM2_PAD3)
#define PORT_PA15C_SERCOM2_PAD3  (_UL_(1) << 15)
/* ========== PORT definition for SERCOM3 peripheral ========== */
#define PIN_PA17D_SERCOM3_PAD0         _L_(17) /**< \brief SERCOM3 signal: PAD0 on PA17 mux D */
#define MUX_PA17D_SERCOM3_PAD0          _L_(3)
#define PINMUX_PA17D_SERCOM3_PAD0  ((PIN_PA17D_SERCOM3_PAD0 << 16) | MUX_PA17D_SERCOM3_PAD0)
#define PORT_PA17D_SERCOM3_PAD0  (_UL_(1) << 17)
#define PIN_PA22C_SERCOM3_PAD0         _L_(22) /**< \brief SERCOM3 signal: PAD0 on PA22 mux C */
#define MUX_PA22C_SERCOM3_PAD0          _L_(2)
#define PINMUX_PA22C_SERCOM3_PAD0  ((PIN_PA22C_SERCOM3_PAD0 << 16) | MUX_PA22C_SERCOM3_PAD0)
#define PORT_PA22C_SERCOM3_PAD0  (_UL_(1) << 22)
#define PIN_PB20C_SERCOM3_PAD0         _L_(52) /**< \brief SERCOM3 signal: PAD0 on PB20 mux C */
#define MUX_PB20C_SERCOM3_PAD0          _L_(2)
#define PINMUX_PB20C_SERCOM3_PAD0  ((PIN_PB20C_SERCOM3_PAD0 << 16) | MUX_PB20C_SERCOM3_PAD0)
#define PORT_PB20C_SERCOM3_PAD0  (_UL_(1) << 20)
#define PIN_PA16D_SERCOM3_PAD1         _L_(16) /**< \brief SERCOM3 signal: PAD1 on PA16 mux D */
#define MUX_PA16D_SERCOM3_PAD1          _L_(3)
#define PINMUX_PA16D_SERCOM3_PAD1  ((PIN_PA16D_SERCOM3_PAD1 << 16) | MUX_PA16D_SERCOM3_PAD1)
#define PORT_PA16D_SERCOM3_PAD1  (_UL_(1) << 16)
#define PIN_PA23C_SERCOM3_PAD1         _L_(23) /**< \brief SERCOM3 signal: PAD1 on PA23 mux C */
#define MUX_PA23C_SERCOM3_PAD1          _L_(2)
#define PINMUX_PA23C_SERCOM3_PAD1  ((PIN_PA23C_SERCOM3_PAD1 << 16) | MUX_PA23C_SERCOM3_PAD1)
#define PORT_PA23C_SERCOM3_PAD1  (_UL_(1) << 23)
#define PIN_PB21C_SERCOM3_PAD1         _L_(53) /**< \brief SERCOM3 signal: PAD1 on PB21 mux C */
#define MUX_PB21C_SERCOM3_PAD1          _L_(2)
#define PINMUX_PB21C_SERCOM3_PAD1  ((PIN_PB21C_SERCOM3_PAD1 << 16) | MUX_PB21C_SERCOM3_PAD1)
#define PORT_PB21C_SERCOM3_PAD1  (_UL_(1) << 21)
#define PIN_PA18D_SERCOM3_PAD2         _L_(18) /**< \brief SERCOM3 signal: PAD2 on PA18 mux D */
#define MUX_PA18D_SERCOM3_PAD2          _L_(3)
#define PINMUX_PA18D_SERCOM3_PAD2  ((PIN_PA18D_SERCOM3_PAD2 << 16) | MUX_PA18D_SERCOM3_PAD2)
#define PORT_PA18D_SERCOM3_PAD2  (_UL_(1) << 18)
#define PIN_PA20D_SERCOM3_PAD2         _L_(20) /**< \brief SERCOM3 signal: PAD2 on PA20 mux D */
#define MUX_PA20D_SERCOM3_PAD2          _L_(3)
#define PINMUX_PA20D_SERCOM3_PAD2  ((PIN_PA20D_SERCOM3_PAD2 << 16) | MUX_PA20D_SERCOM3_PAD2)
#define PORT_PA20D_SERCOM3_PAD2  (_UL_(1) << 20)
#define PIN_PA24C_SERCOM3_PAD2         _L_(24) /**< \brief SERCOM3 signal: PAD2 on PA24 mux C */
#define MUX_PA24C_SERCOM3_PAD2          _L_(2)
#define PINMUX_PA24C_SERCOM3_PAD2  ((PIN_PA24C_SERCOM3_PAD2 << 16) | MUX_PA24C_SERCOM3_PAD2)
#define PORT_PA24C_SERCOM3_PAD2  (_UL_(1) << 24)
#define PIN_PA19D_SERCOM3_PAD3         _L_(19) /**< \brief SERCOM3 signal: PAD3 on PA19 mux D */
#define MUX_PA19D_SERCOM3_PAD3          _L_(3)
#define PINMUX_PA19D_SERCOM3_PAD3  ((PIN_PA19D_SERCOM3_PAD3 << 16) | MUX_PA19D_SERCOM3_PAD3)
#define PORT_PA19D_SERCOM3_PAD3  (_UL_(1) << 19)
#define PIN_PA21D_SERCOM3_PAD3         _L_(21) /**< \brief SERCOM3 signal: PAD3 on PA21 mux D */
#define MUX_PA21D_SERCOM3_PAD3          _L_(3)
#define PINMUX_PA21D_SERCOM3_PAD3  ((PIN_PA21D_SERCOM3_PAD3 << 16) | MUX_PA21D_SERCOM3_PAD3)
#define PORT_PA21D_SERCOM3_PAD3  (_UL_(1) << 21)
#define PIN_PA25C_SERCOM3_PAD3         _L_(25) /**< \brief SERCOM3 signal: PAD3 on PA25 mux C */
#define MUX_PA25C_SERCOM3_PAD3          _L_(2)
#define PINMUX_PA25C_SERCOM3_PAD3  ((PIN_PA25C_SERCOM3_PAD3 << 16) | MUX_PA25C_SERCOM3_PAD3)
#define PORT_PA25C_SERCOM3_PAD3  (_UL_(1) << 25)
/* ========== PORT definition for TCC0 peripheral ========== */
#define PIN_PA20G_TCC0_WO0             _L_(20) /**< \brief TCC0 signal: WO0 on PA20 mux G */
#define MUX_PA20G_TCC0_WO0              _L_(6)
#define PINMUX_PA20G_TCC0_WO0      ((PIN_PA20G_TCC0_WO0 << 16) | MUX_PA20G_TCC0_WO0)
#define PORT_PA20G_TCC0_WO0    (_UL_(1) << 20)
#define PIN_PB12G_TCC0_WO0             _L_(44) /**< \brief TCC0 signal: WO0 on PB12 mux G */
#define MUX_PB12G_TCC0_WO0              _L_(6)
#define PINMUX_PB12G_TCC0_WO0      ((PIN_PB12G_TCC0_WO0 << 16) | MUX_PB12G_TCC0_WO0)
#define PORT_PB12G_TCC0_WO0    (_UL_(1) << 12)
#define PIN_PA08F_TCC0_WO0              _L_(8) /**< \brief TCC0 signal: WO0 on PA08 mux F */
#define MUX_PA08F_TCC0_WO0              _L_(5)
#define PINMUX_PA08F_TCC0_WO0      ((PIN_PA08F_TCC0_WO0 << 16) | MUX_PA08F_TCC0_WO0)
#define PORT_PA08F_TCC0_WO0    (_UL_(1) <<  8)
#define PIN_PC10F_TCC0_WO0             _L_(74) /**< \brief TCC0 signal: WO0 on PC10 mux F */
#define MUX_PC10F_TCC0_WO0              _L_(5)
#define PINMUX_PC10F_TCC0_WO0      ((PIN_PC10F_TCC0_WO0 << 16) | MUX_PC10F_TCC0_WO0)
#define PORT_PC10F_TCC0_WO0    (_UL_(1) << 10)
#define PIN_PC16F_TCC0_WO0             _L_(80) /**< \brief TCC0 signal: WO0 on PC16 mux F */
#define MUX_PC16F_TCC0_WO0              _L_(5)
#define PINMUX_PC16F_TCC0_WO0      ((PIN_PC16F_TCC0_WO0 << 16) | MUX_PC16F_TCC0_WO0)
#define PORT_PC16F_TCC0_WO0    (_UL_(1) << 16)
#define PIN_PA21G_TCC0_WO1             _L_(21) /**< \brief TCC0 signal: WO1 on PA21 mux G */
#define MUX_PA21G_TCC0_WO1              _L_(6)
#define PINMUX_PA21G_TCC0_WO1      ((PIN_PA21G_TCC0_WO1 << 16) | MUX_PA21G_TCC0_WO1)
#define PORT_PA21G_TCC0_WO1    (_UL_(1) << 21)
#define PIN_PB13G_TCC0_WO1             _L_(45) /**< \brief TCC0 signal: WO1 on PB13 mux G */
#define MUX_PB13G_TCC0_WO1              _L_(6)
#define PINMUX_PB13G_TCC0_WO1      ((PIN_PB13G_TCC0_WO1 << 16) | MUX_PB13G_TCC0_WO1)
#define PORT_PB13G_TCC0_WO1    (_UL_(1) << 13)
#define PIN_PA09F_TCC0_WO1              _L_(9) /**< \brief TCC0 signal: WO1 on PA09 mux F */
#define MUX_PA09F_TCC0_WO1              _L_(5)
#define PINMUX_PA09F_TCC0_WO1      ((PIN_PA09F_TCC0_WO1 << 16) | MUX_PA09F_TCC0_WO1)
#define PORT_PA09F_TCC0_WO1    (_UL_(1) <<  9)
#define PIN_PC11F_TCC0_WO1             _L_(75) /**< \brief TCC0 signal: WO1 on PC11 mux F */
#define MUX_PC11F_TCC0_WO1              _L_(5)
#define PINMUX_PC11F_TCC0_WO1      ((PIN_PC11F_TCC0_WO1 << 16) | MUX_PC11F_TCC0_WO1)
#define PORT_PC11F_TCC0_WO1    (_UL_(1) << 11)
#define PIN_PC17F_TCC0_WO1             _L_(81) /**< \brief TCC0 signal: WO1 on PC17 mux F */
#define MUX_PC17F_TCC0_WO1              _L_(5)
#define PINMUX_PC17F_TCC0_WO1      ((PIN_PC17F_TCC0_WO1 << 16) | MUX_PC17F_TCC0_WO1)
#define PORT_PC17F_TCC0_WO1    (_UL_(1) << 17)
#define PIN_PA22G_TCC0_WO2             _L_(22) /**< \brief TCC0 signal: WO2 on PA22 mux G */
#define MUX_PA22G_TCC0_WO2              _L_(6)
#define PINMUX_PA22G_TCC0_WO2      ((PIN_PA22G_TCC0_WO2 << 16) | MUX_PA22G_TCC0_WO2)
#define PORT_PA22G_TCC0_WO2    (_UL_(1) << 22)
#define PIN_PB14G_TCC0_WO2             _L_(46) /**< \brief TCC0 signal: WO2 on PB14 mux G */
#define MUX_PB14G_TCC0_WO2              _L_(6)
#define PINMUX_PB14G_TCC0_WO2      ((PIN_PB14G_TCC0_WO2 << 16) | MUX_PB14G_TCC0_WO2)
#define PORT_PB14G_TCC0_WO2    (_UL_(1) << 14)
#define PIN_PA10F_TCC0_WO2             _L_(10) /**< \brief TCC0 signal: WO2 on PA10 mux F */
#define MUX_PA10F_TCC0_WO2              _L_(5)
#define PINMUX_PA10F_TCC0_WO2      ((PIN_PA10F_TCC0_WO2 << 16) | MUX_PA10F_TCC0_WO2)
#define PORT_PA10F_TCC0_WO2    (_UL_(1) << 10)
#define PIN_PC12F_TCC0_WO2             _L_(76) /**< \brief TCC0 signal: WO2 on PC12 mux F */
#define MUX_PC12F_TCC0_WO2              _L_(5)
#define PINMUX_PC12F_TCC0_WO2      ((PIN_PC12F_TCC0_WO2 << 16) | MUX_PC12F_TCC0_WO2)
#define PORT_PC12F_TCC0_WO2    (_UL_(1) << 12)
#define PIN_PC18F_TCC0_WO2             _L_(82) /**< \brief TCC0 signal: WO2 on PC18 mux F */
#define MUX_PC18F_TCC0_WO2              _L_(5)
#define PINMUX_PC18F_TCC0_WO2      ((PIN_PC18F_TCC0_WO2 << 16) | MUX_PC18F_TCC0_WO2)
#define PORT_PC18F_TCC0_WO2    (_UL_(1) << 18)
#define PIN_PA23G_TCC0_WO3             _L_(23) /**< \brief TCC0 signal: WO3 on PA23 mux G */
#define MUX_PA23G_TCC0_WO3              _L_(6)
#define PINMUX_PA23G_TCC0_WO3      ((PIN_PA23G_TCC0_WO3 << 16) | MUX_PA23G_TCC0_WO3)
#define PORT_PA23G_TCC0_WO3    (_UL_(1) << 23)
#define PIN_PB15G_TCC0_WO3             _L_(47) /**< \brief TCC0 signal: WO3 on PB15 mux G */
#define MUX_PB15G_TCC0_WO3              _L_(6)
#define PINMUX_PB15G_TCC0_WO3      ((PIN_PB15G_TCC0_WO3 << 16) | MUX_PB15G_TCC0_WO3)
#define PORT_PB15G_TCC0_WO3    (_UL_(1) << 15)
#define PIN_PA11F_TCC0_WO3             _L_(11) /**< \brief TCC0 signal: WO3 on PA11 mux F */
#define MUX_PA11F_TCC0_WO3              _L_(5)
#define PINMUX_PA11F_TCC0_WO3      ((PIN_PA11F_TCC0_WO3 << 16) | MUX_PA11F_TCC0_WO3)
#define PORT_PA11F_TCC0_WO3    (_UL_(1) << 11)
#define PIN_PC13F_TCC0_WO3             _L_(77) /**< \brief TCC0 signal: WO3 on PC13 mux F */
#define MUX_PC13F_TCC0_WO3              _L_(5)
#define PINMUX_PC13F_TCC0_WO3      ((PIN_PC13F_TCC0_WO3 << 16) | MUX_PC13F_TCC0_WO3)
#define PORT_PC13F_TCC0_WO3    (_UL_(1) << 13)
#define PIN_PC19F_TCC0_WO3             _L_(83) /**< \brief TCC0 signal: WO3 on PC19 mux F */
#define MUX_PC19F_TCC0_WO3              _L_(5)
#define PINMUX_PC19F_TCC0_WO3      ((PIN_PC19F_TCC0_WO3 << 16) | MUX_PC19F_TCC0_WO3)
#define PORT_PC19F_TCC0_WO3    (_UL_(1) << 19)
#define PIN_PA16G_TCC0_WO4             _L_(16) /**< \brief TCC0 signal: WO4 on PA16 mux G */
#define MUX_PA16G_TCC0_WO4              _L_(6)
#define PINMUX_PA16G_TCC0_WO4      ((PIN_PA16G_TCC0_WO4 << 16) | MUX_PA16G_TCC0_WO4)
#define PORT_PA16G_TCC0_WO4    (_UL_(1) << 16)
#define PIN_PB16G_TCC0_WO4             _L_(48) /**< \brief TCC0 signal: WO4 on PB16 mux G */
#define MUX_PB16G_TCC0_WO4              _L_(6)
#define PINMUX_PB16G_TCC0_WO4      ((PIN_PB16G_TCC0_WO4 << 16) | MUX_PB16G_TCC0_WO4)
#define PORT_PB16G_TCC0_WO4    (_UL_(1) << 16)
#define PIN_PB10F_TCC0_WO4             _L_(42) /**< \brief TCC0 signal: WO4 on PB10 mux F */
#define MUX_PB10F_TCC0_WO4              _L_(5)
#define PINMUX_PB10F_TCC0_WO4      ((PIN_PB10F_TCC0_WO4 << 16) | MUX_PB10F_TCC0_WO4)
#define PORT_PB10F_TCC0_WO4    (_UL_(1) << 10)
#define PIN_PC14F_TCC0_WO4             _L_(78) /**< \brief TCC0 signal: WO4 on PC14 mux F */
#define MUX_PC14F_TCC0_WO4              _L_(5)
#define PINMUX_PC14F_TCC0_WO4      ((PIN_PC14F_TCC0_WO4 << 16) | MUX_PC14F_TCC0_WO4)
#define PORT_PC14F_TCC0_WO4    (_UL_(1) << 14)
#define PIN_PC20F_TCC0_WO4             _L_(84) /**< \brief TCC0 signal: WO4 on PC20 mux F */
#define MUX_PC20F_TCC0_WO4              _L_(5)
#define PINMUX_PC20F_TCC0_WO4      ((PIN_PC20F_TCC0_WO4 << 16) | MUX_PC20F_TCC0_WO4)
#define PORT_PC20F_TCC0_WO4    (_UL_(1) << 20)
#define PIN_PA17G_TCC0_WO5             _L_(17) /**< \brief TCC0 signal: WO5 on PA17 mux G */
#define MUX_PA17G_TCC0_WO5              _L_(6)
#define PINMUX_PA17G_TCC0_WO5      ((PIN_PA17G_TCC0_WO5 << 16) | MUX_PA17G_TCC0_WO5)
#define PORT_PA17G_TCC0_WO5    (_UL_(1) << 17)
#define PIN_PB17G_TCC0_WO5             _L_(49) /**< \brief TCC0 signal: WO5 on PB17 mux G */
#define MUX_PB17G_TCC0_WO5              _L_(6)
#define PINMUX_PB17G_TCC0_WO5      ((PIN_PB17G_TCC0_WO5 << 16) | MUX_PB17G_TCC0_WO5)
#define PORT_PB17G_TCC0_WO5    (_UL_(1) << 17)
#define PIN_PB11F_TCC0_WO5             _L_(43) /**< \brief TCC0 signal: WO5 on PB11 mux F */
#define MUX_PB11F_TCC0_WO5              _L_(5)
#define PINMUX_PB11F_TCC0_WO5      ((PIN_PB11F_TCC0_WO5 << 16) | MUX_PB11F_TCC0_WO5)
#define PORT_PB11F_TCC0_WO5    (_UL_(1) << 11)
#define PIN_PC15F_TCC0_WO5             _L_(79) /**< \brief TCC0 signal: WO5 on PC15 mux F */
#define MUX_PC15F_TCC0_WO5              _L_(5)
#define PINMUX_PC15F_TCC0_WO5      ((PIN_PC15F_TCC0_WO5 << 16) | MUX_PC15F_TCC0_WO5)
#define PORT_PC15F_TCC0_WO5    (_UL_(1) << 15)
#define PIN_PC21F_TCC0_WO5             _L_(85) /**< \brief TCC0 signal: WO5 on PC21 mux F */
#define MUX_PC21F_TCC0_WO5              _L_(5)
#define PINMUX_PC21F_TCC0_WO5      ((PIN_PC21F_TCC0_WO5 << 16) | MUX_PC21F_TCC0_WO5)
#define PORT_PC21F_TCC0_WO5    (_UL_(1) << 21)
#define PIN_PA18G_TCC0_WO6             _L_(18) /**< \brief TCC0 signal: WO6 on PA18 mux G */
#define MUX_PA18G_TCC0_WO6              _L_(6)
#define PINMUX_PA18G_TCC0_WO6      ((PIN_PA18G_TCC0_WO6 << 16) | MUX_PA18G_TCC0_WO6)
#define PORT_PA18G_TCC0_WO6    (_UL_(1) << 18)
#define PIN_PB30G_TCC0_WO6             _L_(62) /**< \brief TCC0 signal: WO6 on PB30 mux G */
#define MUX_PB30G_TCC0_WO6              _L_(6)
#define PINMUX_PB30G_TCC0_WO6      ((PIN_PB30G_TCC0_WO6 << 16) | MUX_PB30G_TCC0_WO6)
#define PORT_PB30G_TCC0_WO6    (_UL_(1) << 30)
#define PIN_PA12F_TCC0_WO6             _L_(12) /**< \brief TCC0 signal: WO6 on PA12 mux F */
#define MUX_PA12F_TCC0_WO6              _L_(5)
#define PINMUX_PA12F_TCC0_WO6      ((PIN_PA12F_TCC0_WO6 << 16) | MUX_PA12F_TCC0_WO6)
#define PORT_PA12F_TCC0_WO6    (_UL_(1) << 12)
#define PIN_PA19G_TCC0_WO7             _L_(19) /**< \brief TCC0 signal: WO7 on PA19 mux G */
#define MUX_PA19G_TCC0_WO7              _L_(6)
#define PINMUX_PA19G_TCC0_WO7      ((PIN_PA19G_TCC0_WO7 << 16) | MUX_PA19G_TCC0_WO7)
#define PORT_PA19G_TCC0_WO7    (_UL_(1) << 19)
#define PIN_PB31G_TCC0_WO7             _L_(63) /**< \brief TCC0 signal: WO7 on PB31 mux G */
#define MUX_PB31G_TCC0_WO7              _L_(6)
#define PINMUX_PB31G_TCC0_WO7      ((PIN_PB31G_TCC0_WO7 << 16) | MUX_PB31G_TCC0_WO7)
#define PORT_PB31G_TCC0_WO7    (_UL_(1) << 31)
#define PIN_PA13F_TCC0_WO7             _L_(13) /**< \brief TCC0 signal: WO7 on PA13 mux F */
#define MUX_PA13F_TCC0_WO7              _L_(5)
#define PINMUX_PA13F_TCC0_WO7      ((PIN_PA13F_TCC0_WO7 << 16) | MUX_PA13F_TCC0_WO7)
#define PORT_PA13F_TCC0_WO7    (_UL_(1) << 13)
/* ========== PORT definition for TCC1 peripheral ========== */
#define PIN_PB10G_TCC1_WO0             _L_(42) /**< \brief TCC1 signal: WO0 on PB10 mux G */
#define MUX_PB10G_TCC1_WO0              _L_(6)
#define PINMUX_PB10G_TCC1_WO0      ((PIN_PB10G_TCC1_WO0 << 16) | MUX_PB10G_TCC1_WO0)
#define PORT_PB10G_TCC1_WO0    (_UL_(1) << 10)
#define PIN_PC14G_TCC1_WO0             _L_(78) /**< \brief TCC1 signal: WO0 on PC14 mux G */
#define MUX_PC14G_TCC1_WO0              _L_(6)
#define PINMUX_PC14G_TCC1_WO0      ((PIN_PC14G_TCC1_WO0 << 16) | MUX_PC14G_TCC1_WO0)
#define PORT_PC14G_TCC1_WO0    (_UL_(1) << 14)
#define PIN_PA16F_TCC1_WO0             _L_(16) /**< \brief TCC1 signal: WO0 on PA16 mux F */
#define MUX_PA16F_TCC1_WO0              _L_(5)
#define PINMUX_PA16F_TCC1_WO0      ((PIN_PA16F_TCC1_WO0 << 16) | MUX_PA16F_TCC1_WO0)
#define PORT_PA16F_TCC1_WO0    (_UL_(1) << 16)
#define PIN_PB18F_TCC1_WO0             _L_(50) /**< \brief TCC1 signal: WO0 on PB18 mux F */
#define MUX_PB18F_TCC1_WO0              _L_(5)
#define PINMUX_PB18F_TCC1_WO0      ((PIN_PB18F_TCC1_WO0 << 16) | MUX_PB18F_TCC1_WO0)
#define PORT_PB18F_TCC1_WO0    (_UL_(1) << 18)
#define PIN_PB11G_TCC1_WO1             _L_(43) /**< \brief TCC1 signal: WO1 on PB11 mux G */
#define MUX_PB11G_TCC1_WO1              _L_(6)
#define PINMUX_PB11G_TCC1_WO1      ((PIN_PB11G_TCC1_WO1 << 16) | MUX_PB11G_TCC1_WO1)
#define PORT_PB11G_TCC1_WO1    (_UL_(1) << 11)
#define PIN_PC15G_TCC1_WO1             _L_(79) /**< \brief TCC1 signal: WO1 on PC15 mux G */
#define MUX_PC15G_TCC1_WO1              _L_(6)
#define PINMUX_PC15G_TCC1_WO1      ((PIN_PC15G_TCC1_WO1 << 16) | MUX_PC15G_TCC1_WO1)
#define PORT_PC15G_TCC1_WO1    (_UL_(1) << 15)
#define PIN_PA17F_TCC1_WO1             _L_(17) /**< \brief TCC1 signal: WO1 on PA17 mux F */
#define MUX_PA17F_TCC1_WO1              _L_(5)
#define PINMUX_PA17F_TCC1_WO1      ((PIN_PA17F_TCC1_WO1 << 16) | MUX_PA17F_TCC1_WO1)
#define PORT_PA17F_TCC1_WO1    (_UL_(1) << 17)
#define PIN_PB19F_TCC1_WO1             _L_(51) /**< \brief TCC1 signal: WO1 on PB19 mux F */
#define MUX_PB19F_TCC1_WO1              _L_(5)
#define PINMUX_PB19F_TCC1_WO1      ((PIN_PB19F_TCC1_WO1 << 16) | MUX_PB19F_TCC1_WO1)
#define PORT_PB19F_TCC1_WO1    (_UL_(1) << 19)
#define PIN_PA12G_TCC1_WO2             _L_(12) /**< \brief TCC1 signal: WO2 on PA12 mux G */
#define MUX_PA12G_TCC1_WO2              _L_(6)
#define PINMUX_PA12G_TCC1_WO2      ((PIN_PA12G_TCC1_WO2 << 16) | MUX_PA12G_TCC1_WO2)
#define PORT_PA12G_TCC1_WO2    (_UL_(1) << 12)
#define PIN_PA14G_TCC1_WO2             _L_(14) /**< \brief TCC1 signal: WO2 on PA14 mux G */
#define MUX_PA14G_TCC1_WO2              _L_(6)
#define PINMUX_PA14G_TCC1_WO2      ((PIN_PA14G_TCC1_WO2 << 16) | MUX_PA14G_TCC1_WO2)
#define PORT_PA14G_TCC1_WO2    (_UL_(1) << 14)
#define PIN_PA18F_TCC1_WO2             _L_(18) /**< \brief TCC1 signal: WO2 on PA18 mux F */
#define MUX_PA18F_TCC1_WO2              _L_(5)
#define PINMUX_PA18F_TCC1_WO2      ((PIN_PA18F_TCC1_WO2 << 16) | MUX_PA18F_TCC1_WO2)
#define PORT_PA18F_TCC1_WO2    (_UL_(1) << 18)
#define PIN_PB20F_TCC1_WO2             _L_(52) /**< \brief TCC1 signal: WO2 on PB20 mux F */
#define MUX_PB20F_TCC1_WO2              _L_(5)
#define PINMUX_PB20F_TCC1_WO2      ((PIN_PB20F_TCC1_WO2 << 16) | MUX_PB20F_TCC1_WO2)
#define PORT_PB20F_TCC1_WO2    (_UL_(1) << 20)
#define PIN_PA13G_TCC1_WO3             _L_(13) /**< \brief TCC1 signal: WO3 on PA13 mux G */
#define MUX_PA13G_TCC1_WO3              _L_(6)
#define PINMUX_PA13G_TCC1_WO3      ((PIN_PA13G_TCC1_WO3 << 16) | MUX_PA13G_TCC1_WO3)
#define PORT_PA13G_TCC1_WO3    (_UL_(1) << 13)
#define PIN_PA15G_TCC1_WO3             _L_(15) /**< \brief TCC1 signal: WO3 on PA15 mux G */
#define MUX_PA15G_TCC1_WO3              _L_(6)
#define PINMUX_PA15G_TCC1_WO3      ((PIN_PA15G_TCC1_WO3 << 16) | MUX_PA15G_TCC1_WO3)
#define PORT_PA15G_TCC1_WO3    (_UL_(1) << 15)
#define PIN_PA19F_TCC1_WO3             _L_(19) /**< \brief TCC1 signal: WO3 on PA19 mux F */
#define MUX_PA19F_TCC1_WO3              _L_(5)
#define PINMUX_PA19F_TCC1_WO3      ((PIN_PA19F_TCC1_WO3 << 16) | MUX_PA19F_TCC1_WO3)
#define PORT_PA19F_TCC1_WO3    (_UL_(1) << 19)
#define PIN_PB21F_TCC1_WO3             _L_(53) /**< \brief TCC1 signal: WO3 on PB21 mux F */
#define MUX_PB21F_TCC1_WO3              _L_(5)
#define PINMUX_PB21F_TCC1_WO3      ((PIN_PB21F_TCC1_WO3 << 16) | MUX_PB21F_TCC1_WO3)
#define PORT_PB21F_TCC1_WO3    (_UL_(1) << 21)
#define PIN_PA08G_TCC1_WO4              _L_(8) /**< \brief TCC1 signal: WO4 on PA08 mux G */
#define MUX_PA08G_TCC1_WO4              _L_(6)
#define PINMUX_PA08G_TCC1_WO4      ((PIN_PA08G_TCC1_WO4 << 16) | MUX_PA08G_TCC1_WO4)
#define PORT_PA08G_TCC1_WO4    (_UL_(1) <<  8)
#define PIN_PC10G_TCC1_WO4             _L_(74) /**< \brief TCC1 signal: WO4 on PC10 mux G */
#define MUX_PC10G_TCC1_WO4              _L_(6)
#define PINMUX_PC10G_TCC1_WO4      ((PIN_PC10G_TCC1_WO4 << 16) | MUX_PC10G_TCC1_WO4)
#define PORT_PC10G_TCC1_WO4    (_UL_(1) << 10)
#define PIN_PA20F_TCC1_WO4             _L_(20) /**< \brief TCC1 signal: WO4 on PA20 mux F */
#define MUX_PA20F_TCC1_WO4              _L_(5)
#define PINMUX_PA20F_TCC1_WO4      ((PIN_PA20F_TCC1_WO4 << 16) | MUX_PA20F_TCC1_WO4)
#define PORT_PA20F_TCC1_WO4    (_UL_(1) << 20)
#define PIN_PA09G_TCC1_WO5              _L_(9) /**< \brief TCC1 signal: WO5 on PA09 mux G */
#define MUX_PA09G_TCC1_WO5              _L_(6)
#define PINMUX_PA09G_TCC1_WO5      ((PIN_PA09G_TCC1_WO5 << 16) | MUX_PA09G_TCC1_WO5)
#define PORT_PA09G_TCC1_WO5    (_UL_(1) <<  9)
#define PIN_PC11G_TCC1_WO5             _L_(75) /**< \brief TCC1 signal: WO5 on PC11 mux G */
#define MUX_PC11G_TCC1_WO5              _L_(6)
#define PINMUX_PC11G_TCC1_WO5      ((PIN_PC11G_TCC1_WO5 << 16) | MUX_PC11G_TCC1_WO5)
#define PORT_PC11G_TCC1_WO5    (_UL_(1) << 11)
#define PIN_PA21F_TCC1_WO5             _L_(21) /**< \brief TCC1 signal: WO5 on PA21 mux F */
#define MUX_PA21F_TCC1_WO5              _L_(5)
#define PINMUX_PA21F_TCC1_WO5      ((PIN_PA21F_TCC1_WO5 << 16) | MUX_PA21F_TCC1_WO5)
#define PORT_PA21F_TCC1_WO5    (_UL_(1) << 21)
#define PIN_PA10G_TCC1_WO6             _L_(10) /**< \brief TCC1 signal: WO6 on PA10 mux G */
#define MUX_PA10G_TCC1_WO6              _L_(6)
#define PINMUX_PA10G_TCC1_WO6      ((PIN_PA10G_TCC1_WO6 << 16) | MUX_PA10G_TCC1_WO6)
#define PORT_PA10G_TCC1_WO6    (_UL_(1) << 10)
#define PIN_PC12G_TCC1_WO6             _L_(76) /**< \brief TCC1 signal: WO6 on PC12 mux G */
#define MUX_PC12G_TCC1_WO6              _L_(6)
#define PINMUX_PC12G_TCC1_WO6      ((PIN_PC12G_TCC1_WO6 << 16) | MUX_PC12G_TCC1_WO6)
#define PORT_PC12G_TCC1_WO6    (_UL_(1) << 12)
#define PIN_PA22F_TCC1_WO6             _L_(22) /**< \brief TCC1 signal: WO6 on PA22 mux F */
#define MUX_PA22F_TCC1_WO6              _L_(5)
#define PINMUX_PA22F_TCC1_WO6      ((PIN_PA22F_TCC1_WO6 << 16) | MUX_PA22F_TCC1_WO6)
#define PORT_PA22F_TCC1_WO6    (_UL_(1) << 22)
#define PIN_PA11G_TCC1_WO7             _L_(11) /**< \brief TCC1 signal: WO7 on PA11 mux G */
#define MUX_PA11G_TCC1_WO7              _L_(6)
#define PINMUX_PA11G_TCC1_WO7      ((PIN_PA11G_TCC1_WO7 << 16) | MUX_PA11G_TCC1_WO7)
#define PORT_PA11G_TCC1_WO7    (_UL_(1) << 11)
#define PIN_PC13G_TCC1_WO7             _L_(77) /**< \brief TCC1 signal: WO7 on PC13 mux G */
#define MUX_PC13G_TCC1_WO7              _L_(6)
#define PINMUX_PC13G_TCC1_WO7      ((PIN_PC13G_TCC1_WO7 << 16) | MUX_PC13G_TCC1_WO7)
#define PORT_PC13G_TCC1_WO7    (_UL_(1) << 13)
#define PIN_PA23F_TCC1_WO7             _L_(23) /**< \brief TCC1 signal: WO7 on PA23 mux F */
#define MUX_PA23F_TCC1_WO7              _L_(5)
#define PINMUX_PA23F_TCC1_WO7      ((PIN_PA23F_TCC1_WO7 << 16) | MUX_PA23F_TCC1_WO7)
#define PORT_PA23F_TCC1_WO7    (_UL_(1) << 23)
/* ========== PORT definition for TC2 peripheral ========== */
#define PIN_PA12E_TC2_WO0              _L_(12) /**< \brief TC2 signal: WO0 on PA12 mux E */
#define MUX_PA12E_TC2_WO0               _L_(4)
#define PINMUX_PA12E_TC2_WO0       ((PIN_PA12E_TC2_WO0 << 16) | MUX_PA12E_TC2_WO0)
#define PORT_PA12E_TC2_WO0     (_UL_(1) << 12)
#define PIN_PA16E_TC2_WO0              _L_(16) /**< \brief TC2 signal: WO0 on PA16 mux E */
#define MUX_PA16E_TC2_WO0               _L_(4)
#define PINMUX_PA16E_TC2_WO0       ((PIN_PA16E_TC2_WO0 << 16) | MUX_PA16E_TC2_WO0)
#define PORT_PA16E_TC2_WO0     (_UL_(1) << 16)
#define PIN_PA00E_TC2_WO0               _L_(0) /**< \brief TC2 signal: WO0 on PA00 mux E */
#define MUX_PA00E_TC2_WO0               _L_(4)
#define PINMUX_PA00E_TC2_WO0       ((PIN_PA00E_TC2_WO0 << 16) | MUX_PA00E_TC2_WO0)
#define PORT_PA00E_TC2_WO0     (_UL_(1) <<  0)
#define PIN_PA01E_TC2_WO1               _L_(1) /**< \brief TC2 signal: WO1 on PA01 mux E */
#define MUX_PA01E_TC2_WO1               _L_(4)
#define PINMUX_PA01E_TC2_WO1       ((PIN_PA01E_TC2_WO1 << 16) | MUX_PA01E_TC2_WO1)
#define PORT_PA01E_TC2_WO1     (_UL_(1) <<  1)
#define PIN_PA13E_TC2_WO1              _L_(13) /**< \brief TC2 signal: WO1 on PA13 mux E */
#define MUX_PA13E_TC2_WO1               _L_(4)
#define PINMUX_PA13E_TC2_WO1       ((PIN_PA13E_TC2_WO1 << 16) | MUX_PA13E_TC2_WO1)
#define PORT_PA13E_TC2_WO1     (_UL_(1) << 13)
#define PIN_PA17E_TC2_WO1              _L_(17) /**< \brief TC2 signal: WO1 on PA17 mux E */
#define MUX_PA17E_TC2_WO1               _L_(4)
#define PINMUX_PA17E_TC2_WO1       ((PIN_PA17E_TC2_WO1 << 16) | MUX_PA17E_TC2_WO1)
#define PORT_PA17E_TC2_WO1     (_UL_(1) << 17)
/* ========== PORT definition for TC3 peripheral ========== */
#define PIN_PA18E_TC3_WO0              _L_(18) /**< \brief TC3 signal: WO0 on PA18 mux E */
#define MUX_PA18E_TC3_WO0               _L_(4)
#define PINMUX_PA18E_TC3_WO0       ((PIN_PA18E_TC3_WO0 << 16) | MUX_PA18E_TC3_WO0)
#define PORT_PA18E_TC3_WO0     (_UL_(1) << 18)
#define PIN_PA14E_TC3_WO0              _L_(14) /**< \brief TC3 signal: WO0 on PA14 mux E */
#define MUX_PA14E_TC3_WO0               _L_(4)
#define PINMUX_PA14E_TC3_WO0       ((PIN_PA14E_TC3_WO0 << 16) | MUX_PA14E_TC3_WO0)
#define PORT_PA14E_TC3_WO0     (_UL_(1) << 14)
#define PIN_PA15E_TC3_WO1              _L_(15) /**< \brief TC3 signal: WO1 on PA15 mux E */
#define MUX_PA15E_TC3_WO1               _L_(4)
#define PINMUX_PA15E_TC3_WO1       ((PIN_PA15E_TC3_WO1 << 16) | MUX_PA15E_TC3_WO1)
#define PORT_PA15E_TC3_WO1     (_UL_(1) << 15)
#define PIN_PA19E_TC3_WO1              _L_(19) /**< \brief TC3 signal: WO1 on PA19 mux E */
#define MUX_PA19E_TC3_WO1               _L_(4)
#define PINMUX_PA19E_TC3_WO1       ((PIN_PA19E_TC3_WO1 << 16) | MUX_PA19E_TC3_WO1)
#define PORT_PA19E_TC3_WO1     (_UL_(1) << 19)
/* ========== PORT definition for CAN0 peripheral ========== */
#define PIN_PA23I_CAN0_RX              _L_(23) /**< \brief CAN0 signal: RX on PA23 mux I */
#define MUX_PA23I_CAN0_RX               _L_(8)
#define PINMUX_PA23I_CAN0_RX       ((PIN_PA23I_CAN0_RX << 16) | MUX_PA23I_CAN0_RX)
#define PORT_PA23I_CAN0_RX     (_UL_(1) << 23)
#define PIN_PA25I_CAN0_RX              _L_(25) /**< \brief CAN0 signal: RX on PA25 mux I */
#define MUX_PA25I_CAN0_RX               _L_(8)
#define PINMUX_PA25I_CAN0_RX       ((PIN_PA25I_CAN0_RX << 16) | MUX_PA25I_CAN0_RX)
#define PORT_PA25I_CAN0_RX     (_UL_(1) << 25)
#define PIN_PA22I_CAN0_TX              _L_(22) /**< \brief CAN0 signal: TX on PA22 mux I */
#define MUX_PA22I_CAN0_TX               _L_(8)
#define PINMUX_PA22I_CAN0_TX       ((PIN_PA22I_CAN0_TX << 16) | MUX_PA22I_CAN0_TX)
#define PORT_PA22I_CAN0_TX     (_UL_(1) << 22)
#define PIN_PA24I_CAN0_TX              _L_(24) /**< \brief CAN0 signal: TX on PA24 mux I */
#define MUX_PA24I_CAN0_TX               _L_(8)
#define PINMUX_PA24I_CAN0_TX       ((PIN_PA24I_CAN0_TX << 16) | MUX_PA24I_CAN0_TX)
#define PORT_PA24I_CAN0_TX     (_UL_(1) << 24)
/* ========== PORT definition for CAN1 peripheral ========== */
#define PIN_PB13H_CAN1_RX              _L_(45) /**< \brief CAN1 signal: RX on PB13 mux H */
#define MUX_PB13H_CAN1_RX               _L_(7)
#define PINMUX_PB13H_CAN1_RX       ((PIN_PB13H_CAN1_RX << 16) | MUX_PB13H_CAN1_RX)
#define PORT_PB13H_CAN1_RX     (_UL_(1) << 13)
#define PIN_PB15H_CAN1_RX              _L_(47) /**< \brief CAN1 signal: RX on PB15 mux H */
#define MUX_PB15H_CAN1_RX               _L_(7)
#define PINMUX_PB15H_CAN1_RX       ((PIN_PB15H_CAN1_RX << 16) | MUX_PB15H_CAN1_RX)
#define PORT_PB15H_CAN1_RX     (_UL_(1) << 15)
#define PIN_PB12H_CAN1_TX              _L_(44) /**< \brief CAN1 signal: TX on PB12 mux H */
#define MUX_PB12H_CAN1_TX               _L_(7)
#define PINMUX_PB12H_CAN1_TX       ((PIN_PB12H_CAN1_TX << 16) | MUX_PB12H_CAN1_TX)
#define PORT_PB12H_CAN1_TX     (_UL_(1) << 12)
#define PIN_PB14H_CAN1_TX              _L_(46) /**< \brief CAN1 signal: TX on PB14 mux H */
#define MUX_PB14H_CAN1_TX               _L_(7)
#define PINMUX_PB14H_CAN1_TX       ((PIN_PB14H_CAN1_TX << 16) | MUX_PB14H_CAN1_TX)
#define PORT_PB14H_CAN1_TX     (_UL_(1) << 14)
/* ========== PORT definition for TCC2 peripheral ========== */
#define PIN_PA14F_TCC2_WO0             _L_(14) /**< \brief TCC2 signal: WO0 on PA14 mux F */
#define MUX_PA14F_TCC2_WO0              _L_(5)
#define PINMUX_PA14F_TCC2_WO0      ((PIN_PA14F_TCC2_WO0 << 16) | MUX_PA14F_TCC2_WO0)
#define PORT_PA14F_TCC2_WO0    (_UL_(1) << 14)
#define PIN_PA30F_TCC2_WO0             _L_(30) /**< \brief TCC2 signal: WO0 on PA30 mux F */
#define MUX_PA30F_TCC2_WO0              _L_(5)
#define PINMUX_PA30F_TCC2_WO0      ((PIN_PA30F_TCC2_WO0 << 16) | MUX_PA30F_TCC2_WO0)
#define PORT_PA30F_TCC2_WO0    (_UL_(1) << 30)
#define PIN_PA15F_TCC2_WO1             _L_(15) /**< \brief TCC2 signal: WO1 on PA15 mux F */
#define MUX_PA15F_TCC2_WO1              _L_(5)
#define PINMUX_PA15F_TCC2_WO1      ((PIN_PA15F_TCC2_WO1 << 16) | MUX_PA15F_TCC2_WO1)
#define PORT_PA15F_TCC2_WO1    (_UL_(1) << 15)
#define PIN_PA31F_TCC2_WO1             _L_(31) /**< \brief TCC2 signal: WO1 on PA31 mux F */
#define MUX_PA31F_TCC2_WO1              _L_(5)
#define PINMUX_PA31F_TCC2_WO1      ((PIN_PA31F_TCC2_WO1 << 16) | MUX_PA31F_TCC2_WO1)
#define PORT_PA31F_TCC2_WO1    (_UL_(1) << 31)
#define PIN_PA24F_TCC2_WO2             _L_(24) /**< \brief TCC2 signal: WO2 on PA24 mux F */
#define MUX_PA24F_TCC2_WO2              _L_(5)
#define PINMUX_PA24F_TCC2_WO2      ((PIN_PA24F_TCC2_WO2 << 16) | MUX_PA24F_TCC2_WO2)
#define PORT_PA24F_TCC2_WO2    (_UL_(1) << 24)
#define PIN_PB02F_TCC2_WO2             _L_(34) /**< \brief TCC2 signal: WO2 on PB02 mux F */
#define MUX_PB02F_TCC2_WO2              _L_(5)
#define PINMUX_PB02F_TCC2_WO2      ((PIN_PB02F_TCC2_WO2 << 16) | MUX_PB02F_TCC2_WO2)
#define PORT_PB02F_TCC2_WO2    (_UL_(1) <<  2)
/* ========== PORT definition for TCC3 peripheral ========== */
#define PIN_PB12F_TCC3_WO0             _L_(44) /**< \brief TCC3 signal: WO0 on PB12 mux F */
#define MUX_PB12F_TCC3_WO0              _L_(5)
#define PINMUX_PB12F_TCC3_WO0      ((PIN_PB12F_TCC3_WO0 << 16) | MUX_PB12F_TCC3_WO0)
#define PORT_PB12F_TCC3_WO0    (_UL_(1) << 12)
#define PIN_PB16F_TCC3_WO0             _L_(48) /**< \brief TCC3 signal: WO0 on PB16 mux F */
#define MUX_PB16F_TCC3_WO0              _L_(5)
#define PINMUX_PB16F_TCC3_WO0      ((PIN_PB16F_TCC3_WO0 << 16) | MUX_PB16F_TCC3_WO0)
#define PORT_PB16F_TCC3_WO0    (_UL_(1) << 16)
#define PIN_PB13F_TCC3_WO1             _L_(45) /**< \brief TCC3 signal: WO1 on PB13 mux F */
#define MUX_PB13F_TCC3_WO1              _L_(5)
#define PINMUX_PB13F_TCC3_WO1      ((PIN_PB13F_TCC3_WO1 << 16) | MUX_PB13F_TCC3_WO1)
#define PORT_PB13F_TCC3_WO1    (_UL_(1) << 13)
#define PIN_PB17F_TCC3_WO1             _L_(49) /**< \brief TCC3 signal: WO1 on PB17 mux F */
#define MUX_PB17F_TCC3_WO1              _L_(5)
#define PINMUX_PB17F_TCC3_WO1      ((PIN_PB17F_TCC3_WO1 << 16) | MUX_PB17F_TCC3_WO1)
#define PORT_PB17F_TCC3_WO1    (_UL_(1) << 17)
/* ========== PORT definition for TC4 peripheral ========== */
#define PIN_PA22E_TC4_WO0              _L_(22) /**< \brief TC4 signal: WO0 on PA22 mux E */
#define MUX_PA22E_TC4_WO0               _L_(4)
#define PINMUX_PA22E_TC4_WO0       ((PIN_PA22E_TC4_WO0 << 16) | MUX_PA22E_TC4_WO0)
#define PORT_PA22E_TC4_WO0     (_UL_(1) << 22)
#define PIN_PB08E_TC4_WO0              _L_(40) /**< \brief TC4 signal: WO0 on PB08 mux E */
#define MUX_PB08E_TC4_WO0               _L_(4)
#define PINMUX_PB08E_TC4_WO0       ((PIN_PB08E_TC4_WO0 << 16) | MUX_PB08E_TC4_WO0)
#define PORT_PB08E_TC4_WO0     (_UL_(1) <<  8)
#define PIN_PB12E_TC4_WO0              _L_(44) /**< \brief TC4 signal: WO0 on PB12 mux E */
#define MUX_PB12E_TC4_WO0               _L_(4)
#define PINMUX_PB12E_TC4_WO0       ((PIN_PB12E_TC4_WO0 << 16) | MUX_PB12E_TC4_WO0)
#define PORT_PB12E_TC4_WO0     (_UL_(1) << 12)
#define PIN_PA23E_TC4_WO1              _L_(23) /**< \brief TC4 signal: WO1 on PA23 mux E */
#define MUX_PA23E_TC4_WO1               _L_(4)
#define PINMUX_PA23E_TC4_WO1       ((PIN_PA23E_TC4_WO1 << 16) | MUX_PA23E_TC4_WO1)
#define PORT_PA23E_TC4_WO1     (_UL_(1) << 23)
#define PIN_PB09E_TC4_WO1              _L_(41) /**< \brief TC4 signal: WO1 on PB09 mux E */
#define MUX_PB09E_TC4_WO1               _L_(4)
#define PINMUX_PB09E_TC4_WO1       ((PIN_PB09E_TC4_WO1 << 16) | MUX_PB09E_TC4_WO1)
#define PORT_PB09E_TC4_WO1     (_UL_(1) <<  9)
#define PIN_PB13E_TC4_WO1              _L_(45) /**< \brief TC4 signal: WO1 on PB13 mux E */
#define MUX_PB13E_TC4_WO1               _L_(4)
#define PINMUX_PB13E_TC4_WO1       ((PIN_PB13E_TC4_WO1 << 16) | MUX_PB13E_TC4_WO1)
#define PORT_PB13E_TC4_WO1     (_UL_(1) << 13)
/* ========== PORT definition for TC5 peripheral ========== */
#define PIN_PA24E_TC5_WO0              _L_(24) /**< \brief TC5 signal: WO0 on PA24 mux E */
#define MUX_PA24E_TC5_WO0               _L_(4)
#define PINMUX_PA24E_TC5_WO0       ((PIN_PA24E_TC5_WO0 << 16) | MUX_PA24E_TC5_WO0)
#define PORT_PA24E_TC5_WO0     (_UL_(1) << 24)
#define PIN_PB10E_TC5_WO0              _L_(42) /**< \brief TC5 signal: WO0 on PB10 mux E */
#define MUX_PB10E_TC5_WO0               _L_(4)
#define PINMUX_PB10E_TC5_WO0       ((PIN_PB10E_TC5_WO0 << 16) | MUX_PB10E_TC5_WO0)
#define PORT_PB10E_TC5_WO0     (_UL_(1) << 10)
#define PIN_PB14E_TC5_WO0              _L_(46) /**< \brief TC5 signal: WO0 on PB14 mux E */
#define MUX_PB14E_TC5_WO0               _L_(4)
#define PINMUX_PB14E_TC5_WO0       ((PIN_PB14E_TC5_WO0 << 16) | MUX_PB14E_TC5_WO0)
#define PORT_PB14E_TC5_WO0     (_UL_(1) << 14)
#define PIN_PA25E_TC5_WO1              _L_(25) /**< \brief TC5 signal: WO1 on PA25 mux E */
#define MUX_PA25E_TC5_WO1               _L_(4)
#define PINMUX_PA25E_TC5_WO1       ((PIN_PA25E_TC5_WO1 << 16) | MUX_PA25E_TC5_WO1)
#define PORT_PA25E_TC5_WO1     (_UL_(1) << 25)
#define PIN_PB11E_TC5_WO1              _L_(43) /**< \brief TC5 signal: WO1 on PB11 mux E */
#define MUX_PB11E_TC5_WO1               _L_(4)
#define PINMUX_PB11E_TC5_WO1       ((PIN_PB11E_TC5_WO1 << 16) | MUX_PB11E_TC5_WO1)
#define PORT_PB11E_TC5_WO1     (_UL_(1) << 11)
#define PIN_PB15E_TC5_WO1              _L_(47) /**< \brief TC5 signal: WO1 on PB15 mux E */
#define MUX_PB15E_TC5_WO1               _L_(4)
#define PINMUX_PB15E_TC5_WO1       ((PIN_PB15E_TC5_WO1 << 16) | MUX_PB15E_TC5_WO1)
#define PORT_PB15E_TC5_WO1     (_UL_(1) << 15)
/* ========== PORT definition for PDEC peripheral ========== */
#define PIN_PB18G_PDEC_QDI0            _L_(50) /**< \brief PDEC signal: QDI0 on PB18 mux G */
#define MUX_PB18G_PDEC_QDI0             _L_(6)
#define PINMUX_PB18G_PDEC_QDI0     ((PIN_PB18G_PDEC_QDI0 << 16) | MUX_PB18G_PDEC_QDI0)
#define PORT_PB18G_PDEC_QDI0   (_UL_(1) << 18)
#define PIN_PB23G_PDEC_QDI0            _L_(55) /**< \brief PDEC signal: QDI0 on PB23 mux G */
#define MUX_PB23G_PDEC_QDI0             _L_(6)
#define PINMUX_PB23G_PDEC_QDI0     ((PIN_PB23G_PDEC_QDI0 << 16) | MUX_PB23G_PDEC_QDI0)
#define PORT_PB23G_PDEC_QDI0   (_UL_(1) << 23)
#define PIN_PC16G_PDEC_QDI0            _L_(80) /**< \brief PDEC signal: QDI0 on PC16 mux G */
#define MUX_PC16G_PDEC_QDI0             _L_(6)
#define PINMUX_PC16G_PDEC_QDI0     ((PIN_PC16G_PDEC_QDI0 << 16) | MUX_PC16G_PDEC_QDI0)
#define PORT_PC16G_PDEC_QDI0   (_UL_(1) << 16)
#define PIN_PA24G_PDEC_QDI0            _L_(24) /**< \brief PDEC signal: QDI0 on PA24 mux G */
#define MUX_PA24G_PDEC_QDI0             _L_(6)
#define PINMUX_PA24G_PDEC_QDI0     ((PIN_PA24G_PDEC_QDI0 << 16) | MUX_PA24G_PDEC_QDI0)
#define PORT_PA24G_PDEC_QDI0   (_UL_(1) << 24)
#define PIN_PB19G_PDEC_QDI1            _L_(51) /**< \brief PDEC signal: QDI1 on PB19 mux G */
#define MUX_PB19G_PDEC_QDI1             _L_(6)
#define PINMUX_PB19G_PDEC_QDI1     ((PIN_PB19G_PDEC_QDI1 << 16) | MUX_PB19G_PDEC_QDI1)
#define PORT_PB19G_PDEC_QDI1   (_UL_(1) << 19)
#define PIN_PB24G_PDEC_QDI1            _L_(56) /**< \brief PDEC signal: QDI1 on PB24 mux G */
#define MUX_PB24G_PDEC_QDI1             _L_(6)
#define PINMUX_PB24G_PDEC_QDI1     ((PIN_PB24G_PDEC_QDI1 << 16) | MUX_PB24G_PDEC_QDI1)
#define PORT_PB24G_PDEC_QDI1   (_UL_(1) << 24)
#define PIN_PC17G_PDEC_QDI1            _L_(81) /**< \brief PDEC signal: QDI1 on PC17 mux G */
#define MUX_PC17G_PDEC_QDI1             _L_(6)
#define PINMUX_PC17G_PDEC_QDI1     ((PIN_PC17G_PDEC_QDI1 << 16) | MUX_PC17G_PDEC_QDI1)
#define PORT_PC17G_PDEC_QDI1   (_UL_(1) << 17)
#define PIN_PA25G_PDEC_QDI1            _L_(25) /**< \brief PDEC signal: QDI1 on PA25 mux G */
#define MUX_PA25G_PDEC_QDI1             _L_(6)
#define PINMUX_PA25G_PDEC_QDI1     ((PIN_PA25G_PDEC_QDI1 << 16) | MUX_PA25G_PDEC_QDI1)
#define PORT_PA25G_PDEC_QDI1   (_UL_(1) << 25)
#define PIN_PB20G_PDEC_QDI2            _L_(52) /**< \brief PDEC signal: QDI2 on PB20 mux G */
#define MUX_PB20G_PDEC_QDI2             _L_(6)
#define PINMUX_PB20G_PDEC_QDI2     ((PIN_PB20G_PDEC_QDI2 << 16) | MUX_PB20G_PDEC_QDI2)
#define PORT_PB20G_PDEC_QDI2   (_UL_(1) << 20)
#define PIN_PB25G_PDEC_QDI2            _L_(57) /**< \brief PDEC signal: QDI2 on PB25 mux G */
#define MUX_PB25G_PDEC_QDI2             _L_(6)
#define PINMUX_PB25G_PDEC_QDI2     ((PIN_PB25G_PDEC_QDI2 << 16) | MUX_PB25G_PDEC_QDI2)
#define PORT_PB25G_PDEC_QDI2   (_UL_(1) << 25)
#define PIN_PC18G_PDEC_QDI2            _L_(82) /**< \brief PDEC signal: QDI2 on PC18 mux G */
#define MUX_PC18G_PDEC_QDI2             _L_(6)
#define PINMUX_PC18G_PDEC_QDI2     ((PIN_PC18G_PDEC_QDI2 << 16) | MUX_PC18G_PDEC_QDI2)
#define PORT_PC18G_PDEC_QDI2   (_UL_(1) << 18)
#define PIN_PB22G_PDEC_QDI2            _L_(54) /**< \brief PDEC signal: QDI2 on PB22 mux G */
#define MUX_PB22G_PDEC_QDI2             _L_(6)
#define PINMUX_PB22G_PDEC_QDI2     ((PIN_PB22G_PDEC_QDI2 << 16) | MUX_PB22G_PDEC_QDI2)
#define PORT_PB22G_PDEC_QDI2   (_UL_(1) << 22)
/* ========== PORT definition for AC peripheral ========== */
#define PIN_PA04B_AC_AIN0               _L_(4) /**< \brief AC signal: AIN0 on PA04 mux B */
#define MUX_PA04B_AC_AIN0               _L_(1)
#define PINMUX_PA04B_AC_AIN0       ((PIN_PA04B_AC_AIN0 << 16) | MUX_PA04B_AC_AIN0)
#define PORT_PA04B_AC_AIN0     (_UL_(1) <<  4)
#define PIN_PA05B_AC_AIN1               _L_(5) /**< \brief AC signal: AIN1 on PA05 mux B */
#define MUX_PA05B_AC_AIN1               _L_(1)
#define PINMUX_PA05B_AC_AIN1       ((PIN_PA05B_AC_AIN1 << 16) | MUX_PA05B_AC_AIN1)
#define PORT_PA05B_AC_AIN1     (_UL_(1) <<  5)
#define PIN_PA06B_AC_AIN2               _L_(6) /**< \brief AC signal: AIN2 on PA06 mux B */
#define MUX_PA06B_AC_AIN2               _L_(1)
#define PINMUX_PA06B_AC_AIN2       ((PIN_PA06B_AC_AIN2 << 16) | MUX_PA06B_AC_AIN2)
#define PORT_PA06B_AC_AIN2     (_UL_(1) <<  6)
#define PIN_PA07B_AC_AIN3               _L_(7) /**< \brief AC signal: AIN3 on PA07 mux B */
#define MUX_PA07B_AC_AIN3               _L_(1)
#define PINMUX_PA07B_AC_AIN3       ((PIN_PA07B_AC_AIN3 << 16) | MUX_PA07B_AC_AIN3)
#define PORT_PA07B_AC_AIN3     (_UL_(1) <<  7)
#define PIN_PA12M_AC_CMP0              _L_(12) /**< \brief AC signal: CMP0 on PA12 mux M */
#define MUX_PA12M_AC_CMP0              _L_(12)
#define PINMUX_PA12M_AC_CMP0       ((PIN_PA12M_AC_CMP0 << 16) | MUX_PA12M_AC_CMP0)
#define PORT_PA12M_AC_CMP0     (_UL_(1) << 12)
#define PIN_PA18M_AC_CMP0              _L_(18) /**< \brief AC signal: CMP0 on PA18 mux M */
#define MUX_PA18M_AC_CMP0              _L_(12)
#define PINMUX_PA18M_AC_CMP0       ((PIN_PA18M_AC_CMP0 << 16) | MUX_PA18M_AC_CMP0)
#define PORT_PA18M_AC_CMP0     (_UL_(1) << 18)
#define PIN_PB24M_AC_CMP0              _L_(56) /**< \brief AC signal: CMP0 on PB24 mux M */
#define MUX_PB24M_AC_CMP0              _L_(12)
#define PINMUX_PB24M_AC_CMP0       ((PIN_PB24M_AC_CMP0 << 16) | MUX_PB24M_AC_CMP0)
#define PORT_PB24M_AC_CMP0     (_UL_(1) << 24)
#define PIN_PA13M_AC_CMP1              _L_(13) /**< \brief AC signal: CMP1 on PA13 mux M */
#define MUX_PA13M_AC_CMP1              _L_(12)
#define PINMUX_PA13M_AC_CMP1       ((PIN_PA13M_AC_CMP1 << 16) | MUX_PA13M_AC_CMP1)
#define PORT_PA13M_AC_CMP1     (_UL_(1) << 13)
#define PIN_PA19M_AC_CMP1              _L_(19) /**< \brief AC signal: CMP1 on PA19 mux M */
#define MUX_PA19M_AC_CMP1              _L_(12)
#define PINMUX_PA19M_AC_CMP1       ((PIN_PA19M_AC_CMP1 << 16) | MUX_PA19M_AC_CMP1)
#define PORT_PA19M_AC_CMP1     (_UL_(1) << 19)
#define PIN_PB25M_AC_CMP1              _L_(57) /**< \brief AC signal: CMP1 on PB25 mux M */
#define MUX_PB25M_AC_CMP1              _L_(12)
#define PINMUX_PB25M_AC_CMP1       ((PIN_PB25M_AC_CMP1 << 16) | MUX_PB25M_AC_CMP1)
#define PORT_PB25M_AC_CMP1     (_UL_(1) << 25)
/* ========== PORT definition for QSPI peripheral ========== */
#define PIN_PB11H_QSPI_CS              _L_(43) /**< \brief QSPI signal: CS on PB11 mux H */
#define MUX_PB11H_QSPI_CS               _L_(7)
#define PINMUX_PB11H_QSPI_CS       ((PIN_PB11H_QSPI_CS << 16) | MUX_PB11H_QSPI_CS)
#define PORT_PB11H_QSPI_CS     (_UL_(1) << 11)
#define PIN_PA08H_QSPI_DATA0            _L_(8) /**< \brief QSPI signal: DATA0 on PA08 mux H */
#define MUX_PA08H_QSPI_DATA0            _L_(7)
#define PINMUX_PA08H_QSPI_DATA0    ((PIN_PA08H_QSPI_DATA0 << 16) | MUX_PA08H_QSPI_DATA0)
#define PORT_PA08H_QSPI_DATA0  (_UL_(1) <<  8)
#define PIN_PA09H_QSPI_DATA1            _L_(9) /**< \brief QSPI signal: DATA1 on PA09 mux H */
#define MUX_PA09H_QSPI_DATA1            _L_(7)
#define PINMUX_PA09H_QSPI_DATA1    ((PIN_PA09H_QSPI_DATA1 << 16) | MUX_PA09H_QSPI_DATA1)
#define PORT_PA09H_QSPI_DATA1  (_UL_(1) <<  9)
#define PIN_PA10H_QSPI_DATA2           _L_(10) /**< \brief QSPI signal: DATA2 on PA10 mux H */
#define MUX_PA10H_QSPI_DATA2            _L_(7)
#define PINMUX_PA10H_QSPI_DATA2    ((PIN_PA10H_QSPI_DATA2 << 16) | MUX_PA10H_QSPI_DATA2)
#define PORT_PA10H_QSPI_DATA2  (_UL_(1) << 10)
#define PIN_PA11H_QSPI_DATA3           _L_(11) /**< \brief QSPI signal: DATA3 on PA11 mux H */
#define MUX_PA11H_QSPI_DATA3            _L_(7)
#define PINMUX_PA11H_QSPI_DATA3    ((PIN_PA11H_QSPI_DATA3 << 16) | MUX_PA11H_QSPI_DATA3)
#define PORT_PA11H_QSPI_DATA3  (_UL_(1) << 11)
#define PIN_PB10H_QSPI_SCK             _L_(42) /**< \brief QSPI signal: SCK on PB10 mux H */
#define MUX_PB10H_QSPI_SCK              _L_(7)
#define PINMUX_PB10H_QSPI_SCK      ((PIN_PB10H_QSPI_SCK << 16) | MUX_PB10H_QSPI_SCK)
#define PORT_PB10H_QSPI_SCK    (_UL_(1) << 10)
/* ========== PORT definition for CCL peripheral ========== */
#define PIN_PA04N_CCL_IN0               _L_(4) /**< \brief CCL signal: IN0 on PA04 mux N */
#define MUX_PA04N_CCL_IN0              _L_(13)
#define PINMUX_PA04N_CCL_IN0       ((PIN_PA04N_CCL_IN0 << 16) | MUX_PA04N_CCL_IN0)
#define PORT_PA04N_CCL_IN0     (_UL_(1) <<  4)
#define PIN_PA16N_CCL_IN0              _L_(16) /**< \brief CCL signal: IN0 on PA16 mux N */
#define MUX_PA16N_CCL_IN0              _L_(13)
#define PINMUX_PA16N_CCL_IN0       ((PIN_PA16N_CCL_IN0 << 16) | MUX_PA16N_CCL_IN0)
#define PORT_PA16N_CCL_IN0     (_UL_(1) << 16)
#define PIN_PB22N_CCL_IN0              _L_(54) /**< \brief CCL signal: IN0 on PB22 mux N */
#define MUX_PB22N_CCL_IN0              _L_(13)
#define PINMUX_PB22N_CCL_IN0       ((PIN_PB22N_CCL_IN0 << 16) | MUX_PB22N_CCL_IN0)
#define PORT_PB22N_CCL_IN0     (_UL_(1) << 22)
#define PIN_PA05N_CCL_IN1               _L_(5) /**< \brief CCL signal: IN1 on PA05 mux N */
#define MUX_PA05N_CCL_IN1              _L_(13)
#define PINMUX_PA05N_CCL_IN1       ((PIN_PA05N_CCL_IN1 << 16) | MUX_PA05N_CCL_IN1)
#define PORT_PA05N_CCL_IN1     (_UL_(1) <<  5)
#define PIN_PA17N_CCL_IN1              _L_(17) /**< \brief CCL signal: IN1 on PA17 mux N */
#define MUX_PA17N_CCL_IN1              _L_(13)
#define PINMUX_PA17N_CCL_IN1       ((PIN_PA17N_CCL_IN1 << 16) | MUX_PA17N_CCL_IN1)
#define PORT_PA17N_CCL_IN1     (_UL_(1) << 17)
#define PIN_PB00N_CCL_IN1              _L_(32) /**< \brief CCL signal: IN1 on PB00 mux N */
#define MUX_PB00N_CCL_IN1              _L_(13)
#define PINMUX_PB00N_CCL_IN1       ((PIN_PB00N_CCL_IN1 << 16) | MUX_PB00N_CCL_IN1)
#define PORT_PB00N_CCL_IN1     (_UL_(1) <<  0)
#define PIN_PA06N_CCL_IN2               _L_(6) /**< \brief CCL signal: IN2 on PA06 mux N */
#define MUX_PA06N_CCL_IN2              _L_(13)
#define PINMUX_PA06N_CCL_IN2       ((PIN_PA06N_CCL_IN2 << 16) | MUX_PA06N_CCL_IN2)
#define PORT_PA06N_CCL_IN2     (_UL_(1) <<  6)
#define PIN_PA18N_CCL_IN2              _L_(18) /**< \brief CCL signal: IN2 on PA18 mux N */
#define MUX_PA18N_CCL_IN2              _L_(13)
#define PINMUX_PA18N_CCL_IN2       ((PIN_PA18N_CCL_IN2 << 16) | MUX_PA18N_CCL_IN2)
#define PORT_PA18N_CCL_IN2     (_UL_(1) << 18)
#define PIN_PB01N_CCL_IN2              _L_(33) /**< \brief CCL signal: IN2 on PB01 mux N */
#define MUX_PB01N_CCL_IN2              _L_(13)
#define PINMUX_PB01N_CCL_IN2       ((PIN_PB01N_CCL_IN2 << 16) | MUX_PB01N_CCL_IN2)
#define PORT_PB01N_CCL_IN2     (_UL_(1) <<  1)
#define PIN_PA08N_CCL_IN3               _L_(8) /**< \brief CCL signal: IN3 on PA08 mux N */
#define MUX_PA08N_CCL_IN3              _L_(13)
#define PINMUX_PA08N_CCL_IN3       ((PIN_PA08N_CCL_IN3 << 16) | MUX_PA08N_CCL_IN3)
#define PORT_PA08N_CCL_IN3     (_UL_(1) <<  8)
#define PIN_PA30N_CCL_IN3              _L_(30) /**< \brief CCL signal: IN3 on PA30 mux N */
#define MUX_PA30N_CCL_IN3              _L_(13)
#define PINMUX_PA30N_CCL_IN3       ((PIN_PA30N_CCL_IN3 << 16) | MUX_PA30N_CCL_IN3)
#define PORT_PA30N_CCL_IN3     (_UL_(1) << 30)
#define PIN_PA09N_CCL_IN4               _L_(9) /**< \brief CCL signal: IN4 on PA09 mux N */
#define MUX_PA09N_CCL_IN4              _L_(13)
#define PINMUX_PA09N_CCL_IN4       ((PIN_PA09N_CCL_IN4 << 16) | MUX_PA09N_CCL_IN4)
#define PORT_PA09N_CCL_IN4     (_UL_(1) <<  9)
#define PIN_PC27N_CCL_IN4              _L_(91) /**< \brief CCL signal: IN4 on PC27 mux N */
#define MUX_PC27N_CCL_IN4              _L_(13)
#define PINMUX_PC27N_CCL_IN4       ((PIN_PC27N_CCL_IN4 << 16) | MUX_PC27N_CCL_IN4)
#define PORT_PC27N_CCL_IN4     (_UL_(1) << 27)
#define PIN_PA10N_CCL_IN5              _L_(10) /**< \brief CCL signal: IN5 on PA10 mux N */
#define MUX_PA10N_CCL_IN5              _L_(13)
#define PINMUX_PA10N_CCL_IN5       ((PIN_PA10N_CCL_IN5 << 16) | MUX_PA10N_CCL_IN5)
#define PORT_PA10N_CCL_IN5     (_UL_(1) << 10)
#define PIN_PC28N_CCL_IN5              _L_(92) /**< \brief CCL signal: IN5 on PC28 mux N */
#define MUX_PC28N_CCL_IN5              _L_(13)
#define PINMUX_PC28N_CCL_IN5       ((PIN_PC28N_CCL_IN5 << 16) | MUX_PC28N_CCL_IN5)
#define PORT_PC28N_CCL_IN5     (_UL_(1) << 28)
#define PIN_PA22N_CCL_IN6              _L_(22) /**< \brief CCL signal: IN6 on PA22 mux N */
#define MUX_PA22N_CCL_IN6              _L_(13)
#define PINMUX_PA22N_CCL_IN6       ((PIN_PA22N_CCL_IN6 << 16) | MUX_PA22N_CCL_IN6)
#define PORT_PA22N_CCL_IN6     (_UL_(1) << 22)
#define PIN_PB06N_CCL_IN6              _L_(38) /**< \brief CCL signal: IN6 on PB06 mux N */
#define MUX_PB06N_CCL_IN6              _L_(13)
#define PINMUX_PB06N_CCL_IN6       ((PIN_PB06N_CCL_IN6 << 16) | MUX_PB06N_CCL_IN6)
#define PORT_PB06N_CCL_IN6     (_UL_(1) <<  6)
#define PIN_PA23N_CCL_IN7              _L_(23) /**< \brief CCL signal: IN7 on PA23 mux N */
#define MUX_PA23N_CCL_IN7              _L_(13)
#define PINMUX_PA23N_CCL_IN7       ((PIN_PA23N_CCL_IN7 << 16) | MUX_PA23N_CCL_IN7)
#define PORT_PA23N_CCL_IN7     (_UL_(1) << 23)
#define PIN_PB07N_CCL_IN7              _L_(39) /**< \brief CCL signal: IN7 on PB07 mux N */
#define MUX_PB07N_CCL_IN7              _L_(13)
#define PINMUX_PB07N_CCL_IN7       ((PIN_PB07N_CCL_IN7 << 16) | MUX_PB07N_CCL_IN7)
#define PORT_PB07N_CCL_IN7     (_UL_(1) <<  7)
#define PIN_PA24N_CCL_IN8              _L_(24) /**< \brief CCL signal: IN8 on PA24 mux N */
#define MUX_PA24N_CCL_IN8              _L_(13)
#define PINMUX_PA24N_CCL_IN8       ((PIN_PA24N_CCL_IN8 << 16) | MUX_PA24N_CCL_IN8)
#define PORT_PA24N_CCL_IN8     (_UL_(1) << 24)
#define PIN_PB08N_CCL_IN8              _L_(40) /**< \brief CCL signal: IN8 on PB08 mux N */
#define MUX_PB08N_CCL_IN8              _L_(13)
#define PINMUX_PB08N_CCL_IN8       ((PIN_PB08N_CCL_IN8 << 16) | MUX_PB08N_CCL_IN8)
#define PORT_PB08N_CCL_IN8     (_UL_(1) <<  8)
#define PIN_PB14N_CCL_IN9              _L_(46) /**< \brief CCL signal: IN9 on PB14 mux N */
#define MUX_PB14N_CCL_IN9              _L_(13)
#define PINMUX_PB14N_CCL_IN9       ((PIN_PB14N_CCL_IN9 << 16) | MUX_PB14N_CCL_IN9)
#define PORT_PB14N_CCL_IN9     (_UL_(1) << 14)
#define PIN_PC20N_CCL_IN9              _L_(84) /**< \brief CCL signal: IN9 on PC20 mux N */
#define MUX_PC20N_CCL_IN9              _L_(13)
#define PINMUX_PC20N_CCL_IN9       ((PIN_PC20N_CCL_IN9 << 16) | MUX_PC20N_CCL_IN9)
#define PORT_PC20N_CCL_IN9     (_UL_(1) << 20)
#define PIN_PB15N_CCL_IN10             _L_(47) /**< \brief CCL signal: IN10 on PB15 mux N */
#define MUX_PB15N_CCL_IN10             _L_(13)
#define PINMUX_PB15N_CCL_IN10      ((PIN_PB15N_CCL_IN10 << 16) | MUX_PB15N_CCL_IN10)
#define PORT_PB15N_CCL_IN10    (_UL_(1) << 15)
#define PIN_PC21N_CCL_IN10             _L_(85) /**< \brief CCL signal: IN10 on PC21 mux N */
#define MUX_PC21N_CCL_IN10             _L_(13)
#define PINMUX_PC21N_CCL_IN10      ((PIN_PC21N_CCL_IN10 << 16) | MUX_PC21N_CCL_IN10)
#define PORT_PC21N_CCL_IN10    (_UL_(1) << 21)
#define PIN_PB10N_CCL_IN11             _L_(42) /**< \brief CCL signal: IN11 on PB10 mux N */
#define MUX_PB10N_CCL_IN11             _L_(13)
#define PINMUX_PB10N_CCL_IN11      ((PIN_PB10N_CCL_IN11 << 16) | MUX_PB10N_CCL_IN11)
#define PORT_PB10N_CCL_IN11    (_UL_(1) << 10)
#define PIN_PB16N_CCL_IN11             _L_(48) /**< \brief CCL signal: IN11 on PB16 mux N */
#define MUX_PB16N_CCL_IN11             _L_(13)
#define PINMUX_PB16N_CCL_IN11      ((PIN_PB16N_CCL_IN11 << 16) | MUX_PB16N_CCL_IN11)
#define PORT_PB16N_CCL_IN11    (_UL_(1) << 16)
#define PIN_PA07N_CCL_OUT0              _L_(7) /**< \brief CCL signal: OUT0 on PA07 mux N */
#define MUX_PA07N_CCL_OUT0             _L_(13)
#define PINMUX_PA07N_CCL_OUT0      ((PIN_PA07N_CCL_OUT0 << 16) | MUX_PA07N_CCL_OUT0)
#define PORT_PA07N_CCL_OUT0    (_UL_(1) <<  7)
#define PIN_PA19N_CCL_OUT0             _L_(19) /**< \brief CCL signal: OUT0 on PA19 mux N */
#define MUX_PA19N_CCL_OUT0             _L_(13)
#define PINMUX_PA19N_CCL_OUT0      ((PIN_PA19N_CCL_OUT0 << 16) | MUX_PA19N_CCL_OUT0)
#define PORT_PA19N_CCL_OUT0    (_UL_(1) << 19)
#define PIN_PB02N_CCL_OUT0             _L_(34) /**< \brief CCL signal: OUT0 on PB02 mux N */
#define MUX_PB02N_CCL_OUT0             _L_(13)
#define PINMUX_PB02N_CCL_OUT0      ((PIN_PB02N_CCL_OUT0 << 16) | MUX_PB02N_CCL_OUT0)
#define PORT_PB02N_CCL_OUT0    (_UL_(1) <<  2)
#define PIN_PB23N_CCL_OUT0             _L_(55) /**< \brief CCL signal: OUT0 on PB23 mux N */
#define MUX_PB23N_CCL_OUT0             _L_(13)
#define PINMUX_PB23N_CCL_OUT0      ((PIN_PB23N_CCL_OUT0 << 16) | MUX_PB23N_CCL_OUT0)
#define PORT_PB23N_CCL_OUT0    (_UL_(1) << 23)
#define PIN_PA11N_CCL_OUT1             _L_(11) /**< \brief CCL signal: OUT1 on PA11 mux N */
#define MUX_PA11N_CCL_OUT1             _L_(13)
#define PINMUX_PA11N_CCL_OUT1      ((PIN_PA11N_CCL_OUT1 << 16) | MUX_PA11N_CCL_OUT1)
#define PORT_PA11N_CCL_OUT1    (_UL_(1) << 11)
#define PIN_PA31N_CCL_OUT1             _L_(31) /**< \brief CCL signal: OUT1 on PA31 mux N */
#define MUX_PA31N_CCL_OUT1             _L_(13)
#define PINMUX_PA31N_CCL_OUT1      ((PIN_PA31N_CCL_OUT1 << 16) | MUX_PA31N_CCL_OUT1)
#define PORT_PA31N_CCL_OUT1    (_UL_(1) << 31)
#define PIN_PB11N_CCL_OUT1             _L_(43) /**< \brief CCL signal: OUT1 on PB11 mux N */
#define MUX_PB11N_CCL_OUT1             _L_(13)
#define PINMUX_PB11N_CCL_OUT1      ((PIN_PB11N_CCL_OUT1 << 16) | MUX_PB11N_CCL_OUT1)
#define PORT_PB11N_CCL_OUT1    (_UL_(1) << 11)
#define PIN_PA25N_CCL_OUT2             _L_(25) /**< \brief CCL signal: OUT2 on PA25 mux N */
#define MUX_PA25N_CCL_OUT2             _L_(13)
#define PINMUX_PA25N_CCL_OUT2      ((PIN_PA25N_CCL_OUT2 << 16) | MUX_PA25N_CCL_OUT2)
#define PORT_PA25N_CCL_OUT2    (_UL_(1) << 25)
#define PIN_PB09N_CCL_OUT2             _L_(41) /**< \brief CCL signal: OUT2 on PB09 mux N */
#define MUX_PB09N_CCL_OUT2             _L_(13)
#define PINMUX_PB09N_CCL_OUT2      ((PIN_PB09N_CCL_OUT2 << 16) | MUX_PB09N_CCL_OUT2)
#define PORT_PB09N_CCL_OUT2    (_UL_(1) <<  9)
#define PIN_PB17N_CCL_OUT3             _L_(49) /**< \brief CCL signal: OUT3 on PB17 mux N */
#define MUX_PB17N_CCL_OUT3             _L_(13)
#define PINMUX_PB17N_CCL_OUT3      ((PIN_PB17N_CCL_OUT3 << 16) | MUX_PB17N_CCL_OUT3)
#define PORT_PB17N_CCL_OUT3    (_UL_(1) << 17)
/* ========== PORT definition for SERCOM4 peripheral ========== */
#define PIN_PA13D_SERCOM4_PAD0         _L_(13) /**< \brief SERCOM4 signal: PAD0 on PA13 mux D */
#define MUX_PA13D_SERCOM4_PAD0          _L_(3)
#define PINMUX_PA13D_SERCOM4_PAD0  ((PIN_PA13D_SERCOM4_PAD0 << 16) | MUX_PA13D_SERCOM4_PAD0)
#define PORT_PA13D_SERCOM4_PAD0  (_UL_(1) << 13)
#define PIN_PB08D_SERCOM4_PAD0         _L_(40) /**< \brief SERCOM4 signal: PAD0 on PB08 mux D */
#define MUX_PB08D_SERCOM4_PAD0          _L_(3)
#define PINMUX_PB08D_SERCOM4_PAD0  ((PIN_PB08D_SERCOM4_PAD0 << 16) | MUX_PB08D_SERCOM4_PAD0)
#define PORT_PB08D_SERCOM4_PAD0  (_UL_(1) <<  8)
#define PIN_PB12C_SERCOM4_PAD0         _L_(44) /**< \brief SERCOM4 signal: PAD0 on PB12 mux C */
#define MUX_PB12C_SERCOM4_PAD0          _L_(2)
#define PINMUX_PB12C_SERCOM4_PAD0  ((PIN_PB12C_SERCOM4_PAD0 << 16) | MUX_PB12C_SERCOM4_PAD0)
#define PORT_PB12C_SERCOM4_PAD0  (_UL_(1) << 12)
#define PIN_PA12D_SERCOM4_PAD1         _L_(12) /**< \brief SERCOM4 signal: PAD1 on PA12 mux D */
#define MUX_PA12D_SERCOM4_PAD1          _L_(3)
#define PINMUX_PA12D_SERCOM4_PAD1  ((PIN_PA12D_SERCOM4_PAD1 << 16) | MUX_PA12D_SERCOM4_PAD1)
#define PORT_PA12D_SERCOM4_PAD1  (_UL_(1) << 12)
#define PIN_PB09D_SERCOM4_PAD1         _L_(41) /**< \brief SERCOM4 signal: PAD1 on PB09 mux D */
#define MUX_PB09D_SERCOM4_PAD1          _L_(3)
#define PINMUX_PB09D_SERCOM4_PAD1  ((PIN_PB09D_SERCOM4_PAD1 << 16) | MUX_PB09D_SERCOM4_PAD1)
#define PORT_PB09D_SERCOM4_PAD1  (_UL_(1) <<  9)
#define PIN_PB13C_SERCOM4_PAD1         _L_(45) /**< \brief SERCOM4 signal: PAD1 on PB13 mux C */
#define MUX_PB13C_SERCOM4_PAD1          _L_(2)
#define PINMUX_PB13C_SERCOM4_PAD1  ((PIN_PB13C_SERCOM4_PAD1 << 16) | MUX_PB13C_SERCOM4_PAD1)
#define PORT_PB13C_SERCOM4_PAD1  (_UL_(1) << 13)
#define PIN_PA14D_SERCOM4_PAD2         _L_(14) /**< \brief SERCOM4 signal: PAD2 on PA14 mux D */
#define MUX_PA14D_SERCOM4_PAD2          _L_(3)
#define PINMUX_PA14D_SERCOM4_PAD2  ((PIN_PA14D_SERCOM4_PAD2 << 16) | MUX_PA14D_SERCOM4_PAD2)
#define PORT_PA14D_SERCOM4_PAD2  (_UL_(1) << 14)
#define PIN_PB10D_SERCOM4_PAD2         _L_(42) /**< \brief SERCOM4 signal: PAD2 on PB10 mux D */
#define MUX_PB10D_SERCOM4_PAD2          _L_(3)
#define PINMUX_PB10D_SERCOM4_PAD2  ((PIN_PB10D_SERCOM4_PAD2 << 16) | MUX_PB10D_SERCOM4_PAD2)
#define PORT_PB10D_SERCOM4_PAD2  (_UL_(1) << 10)
#define PIN_PB14C_SERCOM4_PAD2         _L_(46) /**< \brief SERCOM4 signal: PAD2 on PB14 mux C */
#define MUX_PB14C_SERCOM4_PAD2          _L_(2)
#define PINMUX_PB14C_SERCOM4_PAD2  ((PIN_PB14C_SERCOM4_PAD2 << 16) | MUX_PB14C_SERCOM4_PAD2)
#define PORT_PB14C_SERCOM4_PAD2  (_UL_(1) << 14)
#define PIN_PB11D_SERCOM4_PAD3         _L_(43) /**< \brief SERCOM4 signal: PAD3 on PB11 mux D */
#define MUX_PB11D_SERCOM4_PAD3          _L_(3)
#define PINMUX_PB11D_SERCOM4_PAD3  ((PIN_PB11D_SERCOM4_PAD3 << 16) | MUX_PB11D_SERCOM4_PAD3)
#define PORT_PB11D_SERCOM4_PAD3  (_UL_(1) << 11)
#define PIN_PA15D_SERCOM4_PAD3         _L_(15) /**< \brief SERCOM4 signal: PAD3 on PA15 mux D */
#define MUX_PA15D_SERCOM4_PAD3          _L_(3)
#define PINMUX_PA15D_SERCOM4_PAD3  ((PIN_PA15D_SERCOM4_PAD3 << 16) | MUX_PA15D_SERCOM4_PAD3)
#define PORT_PA15D_SERCOM4_PAD3  (_UL_(1) << 15)
#define PIN_PB15C_SERCOM4_PAD3         _L_(47) /**< \brief SERCOM4 signal: PAD3 on PB15 mux C */
#define MUX_PB15C_SERCOM4_PAD3          _L_(2)
#define PINMUX_PB15C_SERCOM4_PAD3  ((PIN_PB15C_SERCOM4_PAD3 << 16) | MUX_PB15C_SERCOM4_PAD3)
#define PORT_PB15C_SERCOM4_PAD3  (_UL_(1) << 15)
/* ========== PORT definition for SERCOM5 peripheral ========== */
#define PIN_PA23D_SERCOM5_PAD0         _L_(23) /**< \brief SERCOM5 signal: PAD0 on PA23 mux D */
#define MUX_PA23D_SERCOM5_PAD0          _L_(3)
#define PINMUX_PA23D_SERCOM5_PAD0  ((PIN_PA23D_SERCOM5_PAD0 << 16) | MUX_PA23D_SERCOM5_PAD0)
#define PORT_PA23D_SERCOM5_PAD0  (_UL_(1) << 23)
#define PIN_PB02D_SERCOM5_PAD0         _L_(34) /**< \brief SERCOM5 signal: PAD0 on PB02 mux D */
#define MUX_PB02D_SERCOM5_PAD0          _L_(3)
#define PINMUX_PB02D_SERCOM5_PAD0  ((PIN_PB02D_SERCOM5_PAD0 << 16) | MUX_PB02D_SERCOM5_PAD0)
#define PORT_PB02D_SERCOM5_PAD0  (_UL_(1) <<  2)
#define PIN_PB31D_SERCOM5_PAD0         _L_(63) /**< \brief SERCOM5 signal: PAD0 on PB31 mux D */
#define MUX_PB31D_SERCOM5_PAD0          _L_(3)
#define PINMUX_PB31D_SERCOM5_PAD0  ((PIN_PB31D_SERCOM5_PAD0 << 16) | MUX_PB31D_SERCOM5_PAD0)
#define PORT_PB31D_SERCOM5_PAD0  (_UL_(1) << 31)
#define PIN_PB16C_SERCOM5_PAD0         _L_(48) /**< \brief SERCOM5 signal: PAD0 on PB16 mux C */
#define MUX_PB16C_SERCOM5_PAD0          _L_(2)
#define PINMUX_PB16C_SERCOM5_PAD0  ((PIN_PB16C_SERCOM5_PAD0 << 16) | MUX_PB16C_SERCOM5_PAD0)
#define PORT_PB16C_SERCOM5_PAD0  (_UL_(1) << 16)
#define PIN_PA22D_SERCOM5_PAD1         _L_(22) /**< \brief SERCOM5 signal: PAD1 on PA22 mux D */
#define MUX_PA22D_SERCOM5_PAD1          _L_(3)
#define PINMUX_PA22D_SERCOM5_PAD1  ((PIN_PA22D_SERCOM5_PAD1 << 16) | MUX_PA22D_SERCOM5_PAD1)
#define PORT_PA22D_SERCOM5_PAD1  (_UL_(1) << 22)
#define PIN_PB03D_SERCOM5_PAD1         _L_(35) /**< \brief SERCOM5 signal: PAD1 on PB03 mux D */
#define MUX_PB03D_SERCOM5_PAD1          _L_(3)
#define PINMUX_PB03D_SERCOM5_PAD1  ((PIN_PB03D_SERCOM5_PAD1 << 16) | MUX_PB03D_SERCOM5_PAD1)
#define PORT_PB03D_SERCOM5_PAD1  (_UL_(1) <<  3)
#define PIN_PB30D_SERCOM5_PAD1         _L_(62) /**< \brief SERCOM5 signal: PAD1 on PB30 mux D */
#define MUX_PB30D_SERCOM5_PAD1          _L_(3)
#define PINMUX_PB30D_SERCOM5_PAD1  ((PIN_PB30D_SERCOM5_PAD1 << 16) | MUX_PB30D_SERCOM5_PAD1)
#define PORT_PB30D_SERCOM5_PAD1  (_UL_(1) << 30)
#define PIN_PB17C_SERCOM5_PAD1         _L_(49) /**< \brief SERCOM5 signal: PAD1 on PB17 mux C */
#define MUX_PB17C_SERCOM5_PAD1          _L_(2)
#define PINMUX_PB17C_SERCOM5_PAD1  ((PIN_PB17C_SERCOM5_PAD1 << 16) | MUX_PB17C_SERCOM5_PAD1)
#define PORT_PB17C_SERCOM5_PAD1  (_UL_(1) << 17)
#define PIN_PA24D_SERCOM5_PAD2         _L_(24) /**< \brief SERCOM5 signal: PAD2 on PA24 mux D */
#define MUX_PA24D_SERCOM5_PAD2          _L_(3)
#define PINMUX_PA24D_SERCOM5_PAD2  ((PIN_PA24D_SERCOM5_PAD2 << 16) | MUX_PA24D_SERCOM5_PAD2)
#define PORT_PA24D_SERCOM5_PAD2  (_UL_(1) << 24)
#define PIN_PB00D_SERCOM5_PAD2         _L_(32) /**< \brief SERCOM5 signal: PAD2 on PB00 mux D */
#define MUX_PB00D_SERCOM5_PAD2          _L_(3)
#define PINMUX_PB00D_SERCOM5_PAD2  ((PIN_PB00D_SERCOM5_PAD2 << 16) | MUX_PB00D_SERCOM5_PAD2)
#define PORT_PB00D_SERCOM5_PAD2  (_UL_(1) <<  0)
#define PIN_PB22D_SERCOM5_PAD2         _L_(54) /**< \brief SERCOM5 signal: PAD2 on PB22 mux D */
#define MUX_PB22D_SERCOM5_PAD2          _L_(3)
#define PINMUX_PB22D_SERCOM5_PAD2  ((PIN_PB22D_SERCOM5_PAD2 << 16) | MUX_PB22D_SERCOM5_PAD2)
#define PORT_PB22D_SERCOM5_PAD2  (_UL_(1) << 22)
#define PIN_PA20C_SERCOM5_PAD2         _L_(20) /**< \brief SERCOM5 signal: PAD2 on PA20 mux C */
#define MUX_PA20C_SERCOM5_PAD2          _L_(2)
#define PINMUX_PA20C_SERCOM5_PAD2  ((PIN_PA20C_SERCOM5_PAD2 << 16) | MUX_PA20C_SERCOM5_PAD2)
#define PORT_PA20C_SERCOM5_PAD2  (_UL_(1) << 20)
#define PIN_PB18C_SERCOM5_PAD2         _L_(50) /**< \brief SERCOM5 signal: PAD2 on PB18 mux C */
#define MUX_PB18C_SERCOM5_PAD2          _L_(2)
#define PINMUX_PB18C_SERCOM5_PAD2  ((PIN_PB18C_SERCOM5_PAD2 << 16) | MUX_PB18C_SERCOM5_PAD2)
#define PORT_PB18C_SERCOM5_PAD2  (_UL_(1) << 18)
#define PIN_PA25D_SERCOM5_PAD3         _L_(25) /**< \brief SERCOM5 signal: PAD3 on PA25 mux D */
#define MUX_PA25D_SERCOM5_PAD3          _L_(3)
#define PINMUX_PA25D_SERCOM5_PAD3  ((PIN_PA25D_SERCOM5_PAD3 << 16) | MUX_PA25D_SERCOM5_PAD3)
#define PORT_PA25D_SERCOM5_PAD3  (_UL_(1) << 25)
#define PIN_PB01D_SERCOM5_PAD3         _L_(33) /**< \brief SERCOM5 signal: PAD3 on PB01 mux D */
#define MUX_PB01D_SERCOM5_PAD3          _L_(3)
#define PINMUX_PB01D_SERCOM5_PAD3  ((PIN_PB01D_SERCOM5_PAD3 << 16) | MUX_PB01D_SERCOM5_PAD3)
#define PORT_PB01D_SERCOM5_PAD3  (_UL_(1) <<  1)
#define PIN_PB23D_SERCOM5_PAD3         _L_(55) /**< \brief SERCOM5 signal: PAD3 on PB23 mux D */
#define MUX_PB23D_SERCOM5_PAD3          _L_(3)
#define PINMUX_PB23D_SERCOM5_PAD3  ((PIN_PB23D_SERCOM5_PAD3 << 16) | MUX_PB23D_SERCOM5_PAD3)
#define PORT_PB23D_SERCOM5_PAD3  (_UL_(1) << 23)
#define PIN_PA21C_SERCOM5_PAD3         _L_(21) /**< \brief SERCOM5 signal: PAD3 on PA21 mux C */
#define MUX_PA21C_SERCOM5_PAD3          _L_(2)
#define PINMUX_PA21C_SERCOM5_PAD3  ((PIN_PA21C_SERCOM5_PAD3 << 16) | MUX_PA21C_SERCOM5_PAD3)
#define PORT_PA21C_SERCOM5_PAD3  (_UL_(1) << 21)
#define PIN_PB19C_SERCOM5_PAD3         _L_(51) /**< \brief SERCOM5 signal: PAD3 on PB19 mux C */
#define MUX_PB19C_SERCOM5_PAD3          _L_(2)
#define PINMUX_PB19C_SERCOM5_PAD3  ((PIN_PB19C_SERCOM5_PAD3 << 16) | MUX_PB19C_SERCOM5_PAD3)
#define PORT_PB19C_SERCOM5_PAD3  (_UL_(1) << 19)
/* ========== PORT definition for SERCOM6 peripheral ========== */
#define PIN_PC13D_SERCOM6_PAD0         _L_(77) /**< \brief SERCOM6 signal: PAD0 on PC13 mux D */
#define MUX_PC13D_SERCOM6_PAD0          _L_(3)
#define PINMUX_PC13D_SERCOM6_PAD0  ((PIN_PC13D_SERCOM6_PAD0 << 16) | MUX_PC13D_SERCOM6_PAD0)
#define PORT_PC13D_SERCOM6_PAD0  (_UL_(1) << 13)
#define PIN_PC16C_SERCOM6_PAD0         _L_(80) /**< \brief SERCOM6 signal: PAD0 on PC16 mux C */
#define MUX_PC16C_SERCOM6_PAD0          _L_(2)
#define PINMUX_PC16C_SERCOM6_PAD0  ((PIN_PC16C_SERCOM6_PAD0 << 16) | MUX_PC16C_SERCOM6_PAD0)
#define PORT_PC16C_SERCOM6_PAD0  (_UL_(1) << 16)
#define PIN_PC12D_SERCOM6_PAD1         _L_(76) /**< \brief SERCOM6 signal: PAD1 on PC12 mux D */
#define MUX_PC12D_SERCOM6_PAD1          _L_(3)
#define PINMUX_PC12D_SERCOM6_PAD1  ((PIN_PC12D_SERCOM6_PAD1 << 16) | MUX_PC12D_SERCOM6_PAD1)
#define PORT_PC12D_SERCOM6_PAD1  (_UL_(1) << 12)
#define PIN_PC05C_SERCOM6_PAD1         _L_(69) /**< \brief SERCOM6 signal: PAD1 on PC05 mux C */
#define MUX_PC05C_SERCOM6_PAD1          _L_(2)
#define PINMUX_PC05C_SERCOM6_PAD1  ((PIN_PC05C_SERCOM6_PAD1 << 16) | MUX_PC05C_SERCOM6_PAD1)
#define PORT_PC05C_SERCOM6_PAD1  (_UL_(1) <<  5)
#define PIN_PC17C_SERCOM6_PAD1         _L_(81) /**< \brief SERCOM6 signal: PAD1 on PC17 mux C */
#define MUX_PC17C_SERCOM6_PAD1          _L_(2)
#define PINMUX_PC17C_SERCOM6_PAD1  ((PIN_PC17C_SERCOM6_PAD1 << 16) | MUX_PC17C_SERCOM6_PAD1)
#define PORT_PC17C_SERCOM6_PAD1  (_UL_(1) << 17)
#define PIN_PC14D_SERCOM6_PAD2         _L_(78) /**< \brief SERCOM6 signal: PAD2 on PC14 mux D */
#define MUX_PC14D_SERCOM6_PAD2          _L_(3)
#define PINMUX_PC14D_SERCOM6_PAD2  ((PIN_PC14D_SERCOM6_PAD2 << 16) | MUX_PC14D_SERCOM6_PAD2)
#define PORT_PC14D_SERCOM6_PAD2  (_UL_(1) << 14)
#define PIN_PC06C_SERCOM6_PAD2         _L_(70) /**< \brief SERCOM6 signal: PAD2 on PC06 mux C */
#define MUX_PC06C_SERCOM6_PAD2          _L_(2)
#define PINMUX_PC06C_SERCOM6_PAD2  ((PIN_PC06C_SERCOM6_PAD2 << 16) | MUX_PC06C_SERCOM6_PAD2)
#define PORT_PC06C_SERCOM6_PAD2  (_UL_(1) <<  6)
#define PIN_PC10C_SERCOM6_PAD2         _L_(74) /**< \brief SERCOM6 signal: PAD2 on PC10 mux C */
#define MUX_PC10C_SERCOM6_PAD2          _L_(2)
#define PINMUX_PC10C_SERCOM6_PAD2  ((PIN_PC10C_SERCOM6_PAD2 << 16) | MUX_PC10C_SERCOM6_PAD2)
#define PORT_PC10C_SERCOM6_PAD2  (_UL_(1) << 10)
#define PIN_PC18C_SERCOM6_PAD2         _L_(82) /**< \brief SERCOM6 signal: PAD2 on PC18 mux C */
#define MUX_PC18C_SERCOM6_PAD2          _L_(2)
#define PINMUX_PC18C_SERCOM6_PAD2  ((PIN_PC18C_SERCOM6_PAD2 << 16) | MUX_PC18C_SERCOM6_PAD2)
#define PORT_PC18C_SERCOM6_PAD2  (_UL_(1) << 18)
#define PIN_PC15D_SERCOM6_PAD3         _L_(79) /**< \brief SERCOM6 signal: PAD3 on PC15 mux D */
#define MUX_PC15D_SERCOM6_PAD3          _L_(3)
#define PINMUX_PC15D_SERCOM6_PAD3  ((PIN_PC15D_SERCOM6_PAD3 << 16) | MUX_PC15D_SERCOM6_PAD3)
#define PORT_PC15D_SERCOM6_PAD3  (_UL_(1) << 15)
#define PIN_PC07C_SERCOM6_PAD3         _L_(71) /**< \brief SERCOM6 signal: PAD3 on PC07 mux C */
#define MUX_PC07C_SERCOM6_PAD3          _L_(2)
#define PINMUX_PC07C_SERCOM6_PAD3  ((PIN_PC07C_SERCOM6_PAD3 << 16) | MUX_PC07C_SERCOM6_PAD3)
#define PORT_PC07C_SERCOM6_PAD3  (_UL_(1) <<  7)
#define PIN_PC11C_SERCOM6_PAD3         _L_(75) /**< \brief SERCOM6 signal: PAD3 on PC11 mux C */
#define MUX_PC11C_SERCOM6_PAD3          _L_(2)
#define PINMUX_PC11C_SERCOM6_PAD3  ((PIN_PC11C_SERCOM6_PAD3 << 16) | MUX_PC11C_SERCOM6_PAD3)
#define PORT_PC11C_SERCOM6_PAD3  (_UL_(1) << 11)
#define PIN_PC19C_SERCOM6_PAD3         _L_(83) /**< \brief SERCOM6 signal: PAD3 on PC19 mux C */
#define MUX_PC19C_SERCOM6_PAD3          _L_(2)
#define PINMUX_PC19C_SERCOM6_PAD3  ((PIN_PC19C_SERCOM6_PAD3 << 16) | MUX_PC19C_SERCOM6_PAD3)
#define PORT_PC19C_SERCOM6_PAD3  (_UL_(1) << 19)
/* ========== PORT definition for SERCOM7 peripheral ========== */
#define PIN_PB21D_SERCOM7_PAD0         _L_(53) /**< \brief SERCOM7 signal: PAD0 on PB21 mux D */
#define MUX_PB21D_SERCOM7_PAD0          _L_(3)
#define PINMUX_PB21D_SERCOM7_PAD0  ((PIN_PB21D_SERCOM7_PAD0 << 16) | MUX_PB21D_SERCOM7_PAD0)
#define PORT_PB21D_SERCOM7_PAD0  (_UL_(1) << 21)
#define PIN_PB30C_SERCOM7_PAD0         _L_(62) /**< \brief SERCOM7 signal: PAD0 on PB30 mux C */
#define MUX_PB30C_SERCOM7_PAD0          _L_(2)
#define PINMUX_PB30C_SERCOM7_PAD0  ((PIN_PB30C_SERCOM7_PAD0 << 16) | MUX_PB30C_SERCOM7_PAD0)
#define PORT_PB30C_SERCOM7_PAD0  (_UL_(1) << 30)
#define PIN_PC12C_SERCOM7_PAD0         _L_(76) /**< \brief SERCOM7 signal: PAD0 on PC12 mux C */
#define MUX_PC12C_SERCOM7_PAD0          _L_(2)
#define PINMUX_PC12C_SERCOM7_PAD0  ((PIN_PC12C_SERCOM7_PAD0 << 16) | MUX_PC12C_SERCOM7_PAD0)
#define PORT_PC12C_SERCOM7_PAD0  (_UL_(1) << 12)
#define PIN_PB20D_SERCOM7_PAD1         _L_(52) /**< \brief SERCOM7 signal: PAD1 on PB20 mux D */
#define MUX_PB20D_SERCOM7_PAD1          _L_(3)
#define PINMUX_PB20D_SERCOM7_PAD1  ((PIN_PB20D_SERCOM7_PAD1 << 16) | MUX_PB20D_SERCOM7_PAD1)
#define PORT_PB20D_SERCOM7_PAD1  (_UL_(1) << 20)
#define PIN_PB31C_SERCOM7_PAD1         _L_(63) /**< \brief SERCOM7 signal: PAD1 on PB31 mux C */
#define MUX_PB31C_SERCOM7_PAD1          _L_(2)
#define PINMUX_PB31C_SERCOM7_PAD1  ((PIN_PB31C_SERCOM7_PAD1 << 16) | MUX_PB31C_SERCOM7_PAD1)
#define PORT_PB31C_SERCOM7_PAD1  (_UL_(1) << 31)
#define PIN_PC13C_SERCOM7_PAD1         _L_(77) /**< \brief SERCOM7 signal: PAD1 on PC13 mux C */
#define MUX_PC13C_SERCOM7_PAD1          _L_(2)
#define PINMUX_PC13C_SERCOM7_PAD1  ((PIN_PC13C_SERCOM7_PAD1 << 16) | MUX_PC13C_SERCOM7_PAD1)
#define PORT_PC13C_SERCOM7_PAD1  (_UL_(1) << 13)
#define PIN_PB18D_SERCOM7_PAD2         _L_(50) /**< \brief SERCOM7 signal: PAD2 on PB18 mux D */
#define MUX_PB18D_SERCOM7_PAD2          _L_(3)
#define PINMUX_PB18D_SERCOM7_PAD2  ((PIN_PB18D_SERCOM7_PAD2 << 16) | MUX_PB18D_SERCOM7_PAD2)
#define PORT_PB18D_SERCOM7_PAD2  (_UL_(1) << 18)
#define PIN_PC10D_SERCOM7_PAD2         _L_(74) /**< \brief SERCOM7 signal: PAD2 on PC10 mux D */
#define MUX_PC10D_SERCOM7_PAD2          _L_(3)
#define PINMUX_PC10D_SERCOM7_PAD2  ((PIN_PC10D_SERCOM7_PAD2 << 16) | MUX_PC10D_SERCOM7_PAD2)
#define PORT_PC10D_SERCOM7_PAD2  (_UL_(1) << 10)
#define PIN_PC14C_SERCOM7_PAD2         _L_(78) /**< \brief SERCOM7 signal: PAD2 on PC14 mux C */
#define MUX_PC14C_SERCOM7_PAD2          _L_(2)
#define PINMUX_PC14C_SERCOM7_PAD2  ((PIN_PC14C_SERCOM7_PAD2 << 16) | MUX_PC14C_SERCOM7_PAD2)
#define PORT_PC14C_SERCOM7_PAD2  (_UL_(1) << 14)
#define PIN_PA30C_SERCOM7_PAD2         _L_(30) /**< \brief SERCOM7 signal: PAD2 on PA30 mux C */
#define MUX_PA30C_SERCOM7_PAD2          _L_(2)
#define PINMUX_PA30C_SERCOM7_PAD2  ((PIN_PA30C_SERCOM7_PAD2 << 16) | MUX_PA30C_SERCOM7_PAD2)
#define PORT_PA30C_SERCOM7_PAD2  (_UL_(1) << 30)
#define PIN_PB19D_SERCOM7_PAD3         _L_(51) /**< \brief SERCOM7 signal: PAD3 on PB19 mux D */
#define MUX_PB19D_SERCOM7_PAD3          _L_(3)
#define PINMUX_PB19D_SERCOM7_PAD3  ((PIN_PB19D_SERCOM7_PAD3 << 16) | MUX_PB19D_SERCOM7_PAD3)
#define PORT_PB19D_SERCOM7_PAD3  (_UL_(1) << 19)
#define PIN_PC11D_SERCOM7_PAD3         _L_(75) /**< \brief SERCOM7 signal: PAD3 on PC11 mux D */
#define MUX_PC11D_SERCOM7_PAD3          _L_(3)
#define PINMUX_PC11D_SERCOM7_PAD3  ((PIN_PC11D_SERCOM7_PAD3 << 16) | MUX_PC11D_SERCOM7_PAD3)
#define PORT_PC11D_SERCOM7_PAD3  (_UL_(1) << 11)
#define PIN_PC15C_SERCOM7_PAD3         _L_(79) /**< \brief SERCOM7 signal: PAD3 on PC15 mux C */
#define MUX_PC15C_SERCOM7_PAD3          _L_(2)
#define PINMUX_PC15C_SERCOM7_PAD3  ((PIN_PC15C_SERCOM7_PAD3 << 16) | MUX_PC15C_SERCOM7_PAD3)
#define PORT_PC15C_SERCOM7_PAD3  (_UL_(1) << 15)
#define PIN_PA31C_SERCOM7_PAD3         _L_(31) /**< \brief SERCOM7 signal: PAD3 on PA31 mux C */
#define MUX_PA31C_SERCOM7_PAD3          _L_(2)
#define PINMUX_PA31C_SERCOM7_PAD3  ((PIN_PA31C_SERCOM7_PAD3 << 16) | MUX_PA31C_SERCOM7_PAD3)
#define PORT_PA31C_SERCOM7_PAD3  (_UL_(1) << 31)
/* ========== PORT definition for TCC4 peripheral ========== */
#define PIN_PB14F_TCC4_WO0             _L_(46) /**< \brief TCC4 signal: WO0 on PB14 mux F */
#define MUX_PB14F_TCC4_WO0              _L_(5)
#define PINMUX_PB14F_TCC4_WO0      ((PIN_PB14F_TCC4_WO0 << 16) | MUX_PB14F_TCC4_WO0)
#define PORT_PB14F_TCC4_WO0    (_UL_(1) << 14)
#define PIN_PB30F_TCC4_WO0             _L_(62) /**< \brief TCC4 signal: WO0 on PB30 mux F */
#define MUX_PB30F_TCC4_WO0              _L_(5)
#define PINMUX_PB30F_TCC4_WO0      ((PIN_PB30F_TCC4_WO0 << 16) | MUX_PB30F_TCC4_WO0)
#define PORT_PB30F_TCC4_WO0    (_UL_(1) << 30)
#define PIN_PB15F_TCC4_WO1             _L_(47) /**< \brief TCC4 signal: WO1 on PB15 mux F */
#define MUX_PB15F_TCC4_WO1              _L_(5)
#define PINMUX_PB15F_TCC4_WO1      ((PIN_PB15F_TCC4_WO1 << 16) | MUX_PB15F_TCC4_WO1)
#define PORT_PB15F_TCC4_WO1    (_UL_(1) << 15)
#define PIN_PB31F_TCC4_WO1             _L_(63) /**< \brief TCC4 signal: WO1 on PB31 mux F */
#define MUX_PB31F_TCC4_WO1              _L_(5)
#define PINMUX_PB31F_TCC4_WO1      ((PIN_PB31F_TCC4_WO1 << 16) | MUX_PB31F_TCC4_WO1)
#define PORT_PB31F_TCC4_WO1    (_UL_(1) << 31)
/* ========== PORT definition for TC6 peripheral ========== */
#define PIN_PA30E_TC6_WO0              _L_(30) /**< \brief TC6 signal: WO0 on PA30 mux E */
#define MUX_PA30E_TC6_WO0               _L_(4)
#define PINMUX_PA30E_TC6_WO0       ((PIN_PA30E_TC6_WO0 << 16) | MUX_PA30E_TC6_WO0)
#define PORT_PA30E_TC6_WO0     (_UL_(1) << 30)
#define PIN_PB02E_TC6_WO0              _L_(34) /**< \brief TC6 signal: WO0 on PB02 mux E */
#define MUX_PB02E_TC6_WO0               _L_(4)
#define PINMUX_PB02E_TC6_WO0       ((PIN_PB02E_TC6_WO0 << 16) | MUX_PB02E_TC6_WO0)
#define PORT_PB02E_TC6_WO0     (_UL_(1) <<  2)
#define PIN_PB16E_TC6_WO0              _L_(48) /**< \brief TC6 signal: WO0 on PB16 mux E */
#define MUX_PB16E_TC6_WO0               _L_(4)
#define PINMUX_PB16E_TC6_WO0       ((PIN_PB16E_TC6_WO0 << 16) | MUX_PB16E_TC6_WO0)
#define PORT_PB16E_TC6_WO0     (_UL_(1) << 16)
#define PIN_PA31E_TC6_WO1              _L_(31) /**< \brief TC6 signal: WO1 on PA31 mux E */
#define MUX_PA31E_TC6_WO1               _L_(4)
#define PINMUX_PA31E_TC6_WO1       ((PIN_PA31E_TC6_WO1 << 16) | MUX_PA31E_TC6_WO1)
#define PORT_PA31E_TC6_WO1     (_UL_(1) << 31)
#define PIN_PB03E_TC6_WO1              _L_(35) /**< \brief TC6 signal: WO1 on PB03 mux E */
#define MUX_PB03E_TC6_WO1               _L_(4)
#define PINMUX_PB03E_TC6_WO1       ((PIN_PB03E_TC6_WO1 << 16) | MUX_PB03E_TC6_WO1)
#define PORT_PB03E_TC6_WO1     (_UL_(1) <<  3)
#define PIN_PB17E_TC6_WO1              _L_(49) /**< \brief TC6 signal: WO1 on PB17 mux E */
#define MUX_PB17E_TC6_WO1               _L_(4)
#define PINMUX_PB17E_TC6_WO1       ((PIN_PB17E_TC6_WO1 << 16) | MUX_PB17E_TC6_WO1)
#define PORT_PB17E_TC6_WO1     (_UL_(1) << 17)
/* ========== PORT definition for TC7 peripheral ========== */
#define PIN_PA20E_TC7_WO0              _L_(20) /**< \brief TC7 signal: WO0 on PA20 mux E */
#define MUX_PA20E_TC7_WO0               _L_(4)
#define PINMUX_PA20E_TC7_WO0       ((PIN_PA20E_TC7_WO0 << 16) | MUX_PA20E_TC7_WO0)
#define PORT_PA20E_TC7_WO0     (_UL_(1) << 20)
#define PIN_PB00E_TC7_WO0              _L_(32) /**< \brief TC7 signal: WO0 on PB00 mux E */
#define MUX_PB00E_TC7_WO0               _L_(4)
#define PINMUX_PB00E_TC7_WO0       ((PIN_PB00E_TC7_WO0 << 16) | MUX_PB00E_TC7_WO0)
#define PORT_PB00E_TC7_WO0     (_UL_(1) <<  0)
#define PIN_PB22E_TC7_WO0              _L_(54) /**< \brief TC7 signal: WO0 on PB22 mux E */
#define MUX_PB22E_TC7_WO0               _L_(4)
#define PINMUX_PB22E_TC7_WO0       ((PIN_PB22E_TC7_WO0 << 16) | MUX_PB22E_TC7_WO0)
#define PORT_PB22E_TC7_WO0     (_UL_(1) << 22)
#define PIN_PA21E_TC7_WO1              _L_(21) /**< \brief TC7 signal: WO1 on PA21 mux E */
#define MUX_PA21E_TC7_WO1               _L_(4)
#define PINMUX_PA21E_TC7_WO1       ((PIN_PA21E_TC7_WO1 << 16) | MUX_PA21E_TC7_WO1)
#define PORT_PA21E_TC7_WO1     (_UL_(1) << 21)
#define PIN_PB01E_TC7_WO1              _L_(33) /**< \brief TC7 signal: WO1 on PB01 mux E */
#define MUX_PB01E_TC7_WO1               _L_(4)
#define PINMUX_PB01E_TC7_WO1       ((PIN_PB01E_TC7_WO1 << 16) | MUX_PB01E_TC7_WO1)
#define PORT_PB01E_TC7_WO1     (_UL_(1) <<  1)
#define PIN_PB23E_TC7_WO1              _L_(55) /**< \brief TC7 signal: WO1 on PB23 mux E */
#define MUX_PB23E_TC7_WO1               _L_(4)
#define PINMUX_PB23E_TC7_WO1       ((PIN_PB23E_TC7_WO1 << 16) | MUX_PB23E_TC7_WO1)
#define PORT_PB23E_TC7_WO1     (_UL_(1) << 23)
/* ========== PORT definition for ADC0 peripheral ========== */
#define PIN_PA02B_ADC0_AIN0             _L_(2) /**< \brief ADC0 signal: AIN0 on PA02 mux B */
#define MUX_PA02B_ADC0_AIN0             _L_(1)
#define PINMUX_PA02B_ADC0_AIN0     ((PIN_PA02B_ADC0_AIN0 << 16) | MUX_PA02B_ADC0_AIN0)
#define PORT_PA02B_ADC0_AIN0   (_UL_(1) <<  2)
#define PIN_PA03B_ADC0_AIN1             _L_(3) /**< \brief ADC0 signal: AIN1 on PA03 mux B */
#define MUX_PA03B_ADC0_AIN1             _L_(1)
#define PINMUX_PA03B_ADC0_AIN1     ((PIN_PA03B_ADC0_AIN1 << 16) | MUX_PA03B_ADC0_AIN1)
#define PORT_PA03B_ADC0_AIN1   (_UL_(1) <<  3)
#define PIN_PB08B_ADC0_AIN2            _L_(40) /**< \brief ADC0 signal: AIN2 on PB08 mux B */
#define MUX_PB08B_ADC0_AIN2             _L_(1)
#define PINMUX_PB08B_ADC0_AIN2     ((PIN_PB08B_ADC0_AIN2 << 16) | MUX_PB08B_ADC0_AIN2)
#define PORT_PB08B_ADC0_AIN2   (_UL_(1) <<  8)
#define PIN_PB09B_ADC0_AIN3            _L_(41) /**< \brief ADC0 signal: AIN3 on PB09 mux B */
#define MUX_PB09B_ADC0_AIN3             _L_(1)
#define PINMUX_PB09B_ADC0_AIN3     ((PIN_PB09B_ADC0_AIN3 << 16) | MUX_PB09B_ADC0_AIN3)
#define PORT_PB09B_ADC0_AIN3   (_UL_(1) <<  9)
#define PIN_PA04B_ADC0_AIN4             _L_(4) /**< \brief ADC0 signal: AIN4 on PA04 mux B */
#define MUX_PA04B_ADC0_AIN4             _L_(1)
#define PINMUX_PA04B_ADC0_AIN4     ((PIN_PA04B_ADC0_AIN4 << 16) | MUX_PA04B_ADC0_AIN4)
#define PORT_PA04B_ADC0_AIN4   (_UL_(1) <<  4)
#define PIN_PA05B_ADC0_AIN5             _L_(5) /**< \brief ADC0 signal: AIN5 on PA05 mux B */
#define MUX_PA05B_ADC0_AIN5             _L_(1)
#define PINMUX_PA05B_ADC0_AIN5     ((PIN_PA05B_ADC0_AIN5 << 16) | MUX_PA05B_ADC0_AIN5)
#define PORT_PA05B_ADC0_AIN5   (_UL_(1) <<  5)
#define PIN_PA06B_ADC0_AIN6             _L_(6) /**< \brief ADC0 signal: AIN6 on PA06 mux B */
#define MUX_PA06B_ADC0_AIN6             _L_(1)
#define PINMUX_PA06B_ADC0_AIN6     ((PIN_PA06B_ADC0_AIN6 << 16) | MUX_PA06B_ADC0_AIN6)
#define PORT_PA06B_ADC0_AIN6   (_UL_(1) <<  6)
#define PIN_PA07B_ADC0_AIN7             _L_(7) /**< \brief ADC0 signal: AIN7 on PA07 mux B */
#define MUX_PA07B_ADC0_AIN7             _L_(1)
#define PINMUX_PA07B_ADC0_AIN7     ((PIN_PA07B_ADC0_AIN7 << 16) | MUX_PA07B_ADC0_AIN7)
#define PORT_PA07B_ADC0_AIN7   (_UL_(1) <<  7)
#define PIN_PA08B_ADC0_AIN8             _L_(8) /**< \brief ADC0 signal: AIN8 on PA08 mux B */
#define MUX_PA08B_ADC0_AIN8             _L_(1)
#define PINMUX_PA08B_ADC0_AIN8     ((PIN_PA08B_ADC0_AIN8 << 16) | MUX_PA08B_ADC0_AIN8)
#define PORT_PA08B_ADC0_AIN8   (_UL_(1) <<  8)
#define PIN_PA09B_ADC0_AIN9             _L_(9) /**< \brief ADC0 signal: AIN9 on PA09 mux B */
#define MUX_PA09B_ADC0_AIN9             _L_(1)
#define PINMUX_PA09B_ADC0_AIN9     ((PIN_PA09B_ADC0_AIN9 << 16) | MUX_PA09B_ADC0_AIN9)
#define PORT_PA09B_ADC0_AIN9   (_UL_(1) <<  9)
#define PIN_PA10B_ADC0_AIN10           _L_(10) /**< \brief ADC0 signal: AIN10 on PA10 mux B */
#define MUX_PA10B_ADC0_AIN10            _L_(1)
#define PINMUX_PA10B_ADC0_AIN10    ((PIN_PA10B_ADC0_AIN10 << 16) | MUX_PA10B_ADC0_AIN10)
#define PORT_PA10B_ADC0_AIN10  (_UL_(1) << 10)
#define PIN_PA11B_ADC0_AIN11           _L_(11) /**< \brief ADC0 signal: AIN11 on PA11 mux B */
#define MUX_PA11B_ADC0_AIN11            _L_(1)
#define PINMUX_PA11B_ADC0_AIN11    ((PIN_PA11B_ADC0_AIN11 << 16) | MUX_PA11B_ADC0_AIN11)
#define PORT_PA11B_ADC0_AIN11  (_UL_(1) << 11)
#define PIN_PB00B_ADC0_AIN12           _L_(32) /**< \brief ADC0 signal: AIN12 on PB00 mux B */
#define MUX_PB00B_ADC0_AIN12            _L_(1)
#define PINMUX_PB00B_ADC0_AIN12    ((PIN_PB00B_ADC0_AIN12 << 16) | MUX_PB00B_ADC0_AIN12)
#define PORT_PB00B_ADC0_AIN12  (_UL_(1) <<  0)
#define PIN_PB01B_ADC0_AIN13           _L_(33) /**< \brief ADC0 signal: AIN13 on PB01 mux B */
#define MUX_PB01B_ADC0_AIN13            _L_(1)
#define PINMUX_PB01B_ADC0_AIN13    ((PIN_PB01B_ADC0_AIN13 << 16) | MUX_PB01B_ADC0_AIN13)
#define PORT_PB01B_ADC0_AIN13  (_UL_(1) <<  1)
#define PIN_PB02B_ADC0_AIN14           _L_(34) /**< \brief ADC0 signal: AIN14 on PB02 mux B */
#define MUX_PB02B_ADC0_AIN14            _L_(1)
#define PINMUX_PB02B_ADC0_AIN14    ((PIN_PB02B_ADC0_AIN14 << 16) | MUX_PB02B_ADC0_AIN14)
#define PORT_PB02B_ADC0_AIN14  (_UL_(1) <<  2)
#define PIN_PB03B_ADC0_AIN15           _L_(35) /**< \brief ADC0 signal: AIN15 on PB03 mux B */
#define MUX_PB03B_ADC0_AIN15            _L_(1)
#define PINMUX_PB03B_ADC0_AIN15    ((PIN_PB03B_ADC0_AIN15 << 16) | MUX_PB03B_ADC0_AIN15)
#define PORT_PB03B_ADC0_AIN15  (_UL_(1) <<  3)
#define PIN_PA03O_ADC0_DRV0             _L_(3) /**< \brief ADC0 signal: DRV0 on PA03 mux O */
#define MUX_PA03O_ADC0_DRV0            _L_(14)
#define PINMUX_PA03O_ADC0_DRV0     ((PIN_PA03O_ADC0_DRV0 << 16) | MUX_PA03O_ADC0_DRV0)
#define PORT_PA03O_ADC0_DRV0   (_UL_(1) <<  3)
#define PIN_PB08O_ADC0_DRV1            _L_(40) /**< \brief ADC0 signal: DRV1 on PB08 mux O */
#define MUX_PB08O_ADC0_DRV1            _L_(14)
#define PINMUX_PB08O_ADC0_DRV1     ((PIN_PB08O_ADC0_DRV1 << 16) | MUX_PB08O_ADC0_DRV1)
#define PORT_PB08O_ADC0_DRV1   (_UL_(1) <<  8)
#define PIN_PB09O_ADC0_DRV2            _L_(41) /**< \brief ADC0 signal: DRV2 on PB09 mux O */
#define MUX_PB09O_ADC0_DRV2            _L_(14)
#define PINMUX_PB09O_ADC0_DRV2     ((PIN_PB09O_ADC0_DRV2 << 16) | MUX_PB09O_ADC0_DRV2)
#define PORT_PB09O_ADC0_DRV2   (_UL_(1) <<  9)
#define PIN_PA04O_ADC0_DRV3             _L_(4) /**< \brief ADC0 signal: DRV3 on PA04 mux O */
#define MUX_PA04O_ADC0_DRV3            _L_(14)
#define PINMUX_PA04O_ADC0_DRV3     ((PIN_PA04O_ADC0_DRV3 << 16) | MUX_PA04O_ADC0_DRV3)
#define PORT_PA04O_ADC0_DRV3   (_UL_(1) <<  4)
#define PIN_PA06O_ADC0_DRV4             _L_(6) /**< \brief ADC0 signal: DRV4 on PA06 mux O */
#define MUX_PA06O_ADC0_DRV4            _L_(14)
#define PINMUX_PA06O_ADC0_DRV4     ((PIN_PA06O_ADC0_DRV4 << 16) | MUX_PA06O_ADC0_DRV4)
#define PORT_PA06O_ADC0_DRV4   (_UL_(1) <<  6)
#define PIN_PA07O_ADC0_DRV5             _L_(7) /**< \brief ADC0 signal: DRV5 on PA07 mux O */
#define MUX_PA07O_ADC0_DRV5            _L_(14)
#define PINMUX_PA07O_ADC0_DRV5     ((PIN_PA07O_ADC0_DRV5 << 16) | MUX_PA07O_ADC0_DRV5)
#define PORT_PA07O_ADC0_DRV5   (_UL_(1) <<  7)
#define PIN_PA08O_ADC0_DRV6             _L_(8) /**< \brief ADC0 signal: DRV6 on PA08 mux O */
#define MUX_PA08O_ADC0_DRV6            _L_(14)
#define PINMUX_PA08O_ADC0_DRV6     ((PIN_PA08O_ADC0_DRV6 << 16) | MUX_PA08O_ADC0_DRV6)
#define PORT_PA08O_ADC0_DRV6   (_UL_(1) <<  8)
#define PIN_PA09O_ADC0_DRV7             _L_(9) /**< \brief ADC0 signal: DRV7 on PA09 mux O */
#define MUX_PA09O_ADC0_DRV7            _L_(14)
#define PINMUX_PA09O_ADC0_DRV7     ((PIN_PA09O_ADC0_DRV7 << 16) | MUX_PA09O_ADC0_DRV7)
#define PORT_PA09O_ADC0_DRV7   (_UL_(1) <<  9)
#define PIN_PA10O_ADC0_DRV8            _L_(10) /**< \brief ADC0 signal: DRV8 on PA10 mux O */
#define MUX_PA10O_ADC0_DRV8            _L_(14)
#define PINMUX_PA10O_ADC0_DRV8     ((PIN_PA10O_ADC0_DRV8 << 16) | MUX_PA10O_ADC0_DRV8)
#define PORT_PA10O_ADC0_DRV8   (_UL_(1) << 10)
#define PIN_PA11O_ADC0_DRV9            _L_(11) /**< \brief ADC0 signal: DRV9 on PA11 mux O */
#define MUX_PA11O_ADC0_DRV9            _L_(14)
#define PINMUX_PA11O_ADC0_DRV9     ((PIN_PA11O_ADC0_DRV9 << 16) | MUX_PA11O_ADC0_DRV9)
#define PORT_PA11O_ADC0_DRV9   (_UL_(1) << 11)
#define PIN_PA16O_ADC0_DRV10           _L_(16) /**< \brief ADC0 signal: DRV10 on PA16 mux O */
#define MUX_PA16O_ADC0_DRV10           _L_(14)
#define PINMUX_PA16O_ADC0_DRV10    ((PIN_PA16O_ADC0_DRV10 << 16) | MUX_PA16O_ADC0_DRV10)
#define PORT_PA16O_ADC0_DRV10  (_UL_(1) << 16)
#define PIN_PA17O_ADC0_DRV11           _L_(17) /**< \brief ADC0 signal: DRV11 on PA17 mux O */
#define MUX_PA17O_ADC0_DRV11           _L_(14)
#define PINMUX_PA17O_ADC0_DRV11    ((PIN_PA17O_ADC0_DRV11 << 16) | MUX_PA17O_ADC0_DRV11)
#define PORT_PA17O_ADC0_DRV11  (_UL_(1) << 17)
#define PIN_PA18O_ADC0_DRV12           _L_(18) /**< \brief ADC0 signal: DRV12 on PA18 mux O */
#define MUX_PA18O_ADC0_DRV12           _L_(14)
#define PINMUX_PA18O_ADC0_DRV12    ((PIN_PA18O_ADC0_DRV12 << 16) | MUX_PA18O_ADC0_DRV12)
#define PORT_PA18O_ADC0_DRV12  (_UL_(1) << 18)
#define PIN_PA19O_ADC0_DRV13           _L_(19) /**< \brief ADC0 signal: DRV13 on PA19 mux O */
#define MUX_PA19O_ADC0_DRV13           _L_(14)
#define PINMUX_PA19O_ADC0_DRV13    ((PIN_PA19O_ADC0_DRV13 << 16) | MUX_PA19O_ADC0_DRV13)
#define PORT_PA19O_ADC0_DRV13  (_UL_(1) << 19)
#define PIN_PA20O_ADC0_DRV14           _L_(20) /**< \brief ADC0 signal: DRV14 on PA20 mux O */
#define MUX_PA20O_ADC0_DRV14           _L_(14)
#define PINMUX_PA20O_ADC0_DRV14    ((PIN_PA20O_ADC0_DRV14 << 16) | MUX_PA20O_ADC0_DRV14)
#define PORT_PA20O_ADC0_DRV14  (_UL_(1) << 20)
#define PIN_PA21O_ADC0_DRV15           _L_(21) /**< \brief ADC0 signal: DRV15 on PA21 mux O */
#define MUX_PA21O_ADC0_DRV15           _L_(14)
#define PINMUX_PA21O_ADC0_DRV15    ((PIN_PA21O_ADC0_DRV15 << 16) | MUX_PA21O_ADC0_DRV15)
#define PORT_PA21O_ADC0_DRV15  (_UL_(1) << 21)
#define PIN_PA22O_ADC0_DRV16           _L_(22) /**< \brief ADC0 signal: DRV16 on PA22 mux O */
#define MUX_PA22O_ADC0_DRV16           _L_(14)
#define PINMUX_PA22O_ADC0_DRV16    ((PIN_PA22O_ADC0_DRV16 << 16) | MUX_PA22O_ADC0_DRV16)
#define PORT_PA22O_ADC0_DRV16  (_UL_(1) << 22)
#define PIN_PA23O_ADC0_DRV17           _L_(23) /**< \brief ADC0 signal: DRV17 on PA23 mux O */
#define MUX_PA23O_ADC0_DRV17           _L_(14)
#define PINMUX_PA23O_ADC0_DRV17    ((PIN_PA23O_ADC0_DRV17 << 16) | MUX_PA23O_ADC0_DRV17)
#define PORT_PA23O_ADC0_DRV17  (_UL_(1) << 23)
#define PIN_PA27O_ADC0_DRV18           _L_(27) /**< \brief ADC0 signal: DRV18 on PA27 mux O */
#define MUX_PA27O_ADC0_DRV18           _L_(14)
#define PINMUX_PA27O_ADC0_DRV18    ((PIN_PA27O_ADC0_DRV18 << 16) | MUX_PA27O_ADC0_DRV18)
#define PORT_PA27O_ADC0_DRV18  (_UL_(1) << 27)
#define PIN_PA30O_ADC0_DRV19           _L_(30) /**< \brief ADC0 signal: DRV19 on PA30 mux O */
#define MUX_PA30O_ADC0_DRV19           _L_(14)
#define PINMUX_PA30O_ADC0_DRV19    ((PIN_PA30O_ADC0_DRV19 << 16) | MUX_PA30O_ADC0_DRV19)
#define PORT_PA30O_ADC0_DRV19  (_UL_(1) << 30)
#define PIN_PB02O_ADC0_DRV20           _L_(34) /**< \brief ADC0 signal: DRV20 on PB02 mux O */
#define MUX_PB02O_ADC0_DRV20           _L_(14)
#define PINMUX_PB02O_ADC0_DRV20    ((PIN_PB02O_ADC0_DRV20 << 16) | MUX_PB02O_ADC0_DRV20)
#define PORT_PB02O_ADC0_DRV20  (_UL_(1) <<  2)
#define PIN_PB03O_ADC0_DRV21           _L_(35) /**< \brief ADC0 signal: DRV21 on PB03 mux O */
#define MUX_PB03O_ADC0_DRV21           _L_(14)
#define PINMUX_PB03O_ADC0_DRV21    ((PIN_PB03O_ADC0_DRV21 << 16) | MUX_PB03O_ADC0_DRV21)
#define PORT_PB03O_ADC0_DRV21  (_UL_(1) <<  3)
#define PIN_PB04O_ADC0_DRV22           _L_(36) /**< \brief ADC0 signal: DRV22 on PB04 mux O */
#define MUX_PB04O_ADC0_DRV22           _L_(14)
#define PINMUX_PB04O_ADC0_DRV22    ((PIN_PB04O_ADC0_DRV22 << 16) | MUX_PB04O_ADC0_DRV22)
#define PORT_PB04O_ADC0_DRV22  (_UL_(1) <<  4)
#define PIN_PB05O_ADC0_DRV23           _L_(37) /**< \brief ADC0 signal: DRV23 on PB05 mux O */
#define MUX_PB05O_ADC0_DRV23           _L_(14)
#define PINMUX_PB05O_ADC0_DRV23    ((PIN_PB05O_ADC0_DRV23 << 16) | MUX_PB05O_ADC0_DRV23)
#define PORT_PB05O_ADC0_DRV23  (_UL_(1) <<  5)
#define PIN_PB06O_ADC0_DRV24           _L_(38) /**< \brief ADC0 signal: DRV24 on PB06 mux O */
#define MUX_PB06O_ADC0_DRV24           _L_(14)
#define PINMUX_PB06O_ADC0_DRV24    ((PIN_PB06O_ADC0_DRV24 << 16) | MUX_PB06O_ADC0_DRV24)
#define PORT_PB06O_ADC0_DRV24  (_UL_(1) <<  6)
#define PIN_PB07O_ADC0_DRV25           _L_(39) /**< \brief ADC0 signal: DRV25 on PB07 mux O */
#define MUX_PB07O_ADC0_DRV25           _L_(14)
#define PINMUX_PB07O_ADC0_DRV25    ((PIN_PB07O_ADC0_DRV25 << 16) | MUX_PB07O_ADC0_DRV25)
#define PORT_PB07O_ADC0_DRV25  (_UL_(1) <<  7)
#define PIN_PB12O_ADC0_DRV26           _L_(44) /**< \brief ADC0 signal: DRV26 on PB12 mux O */
#define MUX_PB12O_ADC0_DRV26           _L_(14)
#define PINMUX_PB12O_ADC0_DRV26    ((PIN_PB12O_ADC0_DRV26 << 16) | MUX_PB12O_ADC0_DRV26)
#define PORT_PB12O_ADC0_DRV26  (_UL_(1) << 12)
#define PIN_PB13O_ADC0_DRV27           _L_(45) /**< \brief ADC0 signal: DRV27 on PB13 mux O */
#define MUX_PB13O_ADC0_DRV27           _L_(14)
#define PINMUX_PB13O_ADC0_DRV27    ((PIN_PB13O_ADC0_DRV27 << 16) | MUX_PB13O_ADC0_DRV27)
#define PORT_PB13O_ADC0_DRV27  (_UL_(1) << 13)
#define PIN_PB14O_ADC0_DRV28           _L_(46) /**< \brief ADC0 signal: DRV28 on PB14 mux O */
#define MUX_PB14O_ADC0_DRV28           _L_(14)
#define PINMUX_PB14O_ADC0_DRV28    ((PIN_PB14O_ADC0_DRV28 << 16) | MUX_PB14O_ADC0_DRV28)
#define PORT_PB14O_ADC0_DRV28  (_UL_(1) << 14)
#define PIN_PB15O_ADC0_DRV29           _L_(47) /**< \brief ADC0 signal: DRV29 on PB15 mux O */
#define MUX_PB15O_ADC0_DRV29           _L_(14)
#define PINMUX_PB15O_ADC0_DRV29    ((PIN_PB15O_ADC0_DRV29 << 16) | MUX_PB15O_ADC0_DRV29)
#define PORT_PB15O_ADC0_DRV29  (_UL_(1) << 15)
#define PIN_PB00O_ADC0_DRV30           _L_(32) /**< \brief ADC0 signal: DRV30 on PB00 mux O */
#define MUX_PB00O_ADC0_DRV30           _L_(14)
#define PINMUX_PB00O_ADC0_DRV30    ((PIN_PB00O_ADC0_DRV30 << 16) | MUX_PB00O_ADC0_DRV30)
#define PORT_PB00O_ADC0_DRV30  (_UL_(1) <<  0)
#define PIN_PB01O_ADC0_DRV31           _L_(33) /**< \brief ADC0 signal: DRV31 on PB01 mux O */
#define MUX_PB01O_ADC0_DRV31           _L_(14)
#define PINMUX_PB01O_ADC0_DRV31    ((PIN_PB01O_ADC0_DRV31 << 16) | MUX_PB01O_ADC0_DRV31)
#define PORT_PB01O_ADC0_DRV31  (_UL_(1) <<  1)
#define PIN_PA03B_ADC0_PTCXY0           _L_(3) /**< \brief ADC0 signal: PTCXY0 on PA03 mux B */
#define MUX_PA03B_ADC0_PTCXY0           _L_(1)
#define PINMUX_PA03B_ADC0_PTCXY0   ((PIN_PA03B_ADC0_PTCXY0 << 16) | MUX_PA03B_ADC0_PTCXY0)
#define PORT_PA03B_ADC0_PTCXY0  (_UL_(1) <<  3)
#define PIN_PB08B_ADC0_PTCXY1          _L_(40) /**< \brief ADC0 signal: PTCXY1 on PB08 mux B */
#define MUX_PB08B_ADC0_PTCXY1           _L_(1)
#define PINMUX_PB08B_ADC0_PTCXY1   ((PIN_PB08B_ADC0_PTCXY1 << 16) | MUX_PB08B_ADC0_PTCXY1)
#define PORT_PB08B_ADC0_PTCXY1  (_UL_(1) <<  8)
#define PIN_PB09B_ADC0_PTCXY2          _L_(41) /**< \brief ADC0 signal: PTCXY2 on PB09 mux B */
#define MUX_PB09B_ADC0_PTCXY2           _L_(1)
#define PINMUX_PB09B_ADC0_PTCXY2   ((PIN_PB09B_ADC0_PTCXY2 << 16) | MUX_PB09B_ADC0_PTCXY2)
#define PORT_PB09B_ADC0_PTCXY2  (_UL_(1) <<  9)
#define PIN_PA04B_ADC0_PTCXY3           _L_(4) /**< \brief ADC0 signal: PTCXY3 on PA04 mux B */
#define MUX_PA04B_ADC0_PTCXY3           _L_(1)
#define PINMUX_PA04B_ADC0_PTCXY3   ((PIN_PA04B_ADC0_PTCXY3 << 16) | MUX_PA04B_ADC0_PTCXY3)
#define PORT_PA04B_ADC0_PTCXY3  (_UL_(1) <<  4)
#define PIN_PA06B_ADC0_PTCXY4           _L_(6) /**< \brief ADC0 signal: PTCXY4 on PA06 mux B */
#define MUX_PA06B_ADC0_PTCXY4           _L_(1)
#define PINMUX_PA06B_ADC0_PTCXY4   ((PIN_PA06B_ADC0_PTCXY4 << 16) | MUX_PA06B_ADC0_PTCXY4)
#define PORT_PA06B_ADC0_PTCXY4  (_UL_(1) <<  6)
#define PIN_PA07B_ADC0_PTCXY5           _L_(7) /**< \brief ADC0 signal: PTCXY5 on PA07 mux B */
#define MUX_PA07B_ADC0_PTCXY5           _L_(1)
#define PINMUX_PA07B_ADC0_PTCXY5   ((PIN_PA07B_ADC0_PTCXY5 << 16) | MUX_PA07B_ADC0_PTCXY5)
#define PORT_PA07B_ADC0_PTCXY5  (_UL_(1) <<  7)
#define PIN_PA08B_ADC0_PTCXY6           _L_(8) /**< \brief ADC0 signal: PTCXY6 on PA08 mux B */
#define MUX_PA08B_ADC0_PTCXY6           _L_(1)
#define PINMUX_PA08B_ADC0_PTCXY6   ((PIN_PA08B_ADC0_PTCXY6 << 16) | MUX_PA08B_ADC0_PTCXY6)
#define PORT_PA08B_ADC0_PTCXY6  (_UL_(1) <<  8)
#define PIN_PA09B_ADC0_PTCXY7           _L_(9) /**< \brief ADC0 signal: PTCXY7 on PA09 mux B */
#define MUX_PA09B_ADC0_PTCXY7           _L_(1)
#define PINMUX_PA09B_ADC0_PTCXY7   ((PIN_PA09B_ADC0_PTCXY7 << 16) | MUX_PA09B_ADC0_PTCXY7)
#define PORT_PA09B_ADC0_PTCXY7  (_UL_(1) <<  9)
#define PIN_PA10B_ADC0_PTCXY8          _L_(10) /**< \brief ADC0 signal: PTCXY8 on PA10 mux B */
#define MUX_PA10B_ADC0_PTCXY8           _L_(1)
#define PINMUX_PA10B_ADC0_PTCXY8   ((PIN_PA10B_ADC0_PTCXY8 << 16) | MUX_PA10B_ADC0_PTCXY8)
#define PORT_PA10B_ADC0_PTCXY8  (_UL_(1) << 10)
#define PIN_PA11B_ADC0_PTCXY9          _L_(11) /**< \brief ADC0 signal: PTCXY9 on PA11 mux B */
#define MUX_PA11B_ADC0_PTCXY9           _L_(1)
#define PINMUX_PA11B_ADC0_PTCXY9   ((PIN_PA11B_ADC0_PTCXY9 << 16) | MUX_PA11B_ADC0_PTCXY9)
#define PORT_PA11B_ADC0_PTCXY9  (_UL_(1) << 11)
#define PIN_PA16B_ADC0_PTCXY10         _L_(16) /**< \brief ADC0 signal: PTCXY10 on PA16 mux B */
#define MUX_PA16B_ADC0_PTCXY10          _L_(1)
#define PINMUX_PA16B_ADC0_PTCXY10  ((PIN_PA16B_ADC0_PTCXY10 << 16) | MUX_PA16B_ADC0_PTCXY10)
#define PORT_PA16B_ADC0_PTCXY10  (_UL_(1) << 16)
#define PIN_PA17B_ADC0_PTCXY11         _L_(17) /**< \brief ADC0 signal: PTCXY11 on PA17 mux B */
#define MUX_PA17B_ADC0_PTCXY11          _L_(1)
#define PINMUX_PA17B_ADC0_PTCXY11  ((PIN_PA17B_ADC0_PTCXY11 << 16) | MUX_PA17B_ADC0_PTCXY11)
#define PORT_PA17B_ADC0_PTCXY11  (_UL_(1) << 17)
#define PIN_PA18B_ADC0_PTCXY12         _L_(18) /**< \brief ADC0 signal: PTCXY12 on PA18 mux B */
#define MUX_PA18B_ADC0_PTCXY12          _L_(1)
#define PINMUX_PA18B_ADC0_PTCXY12  ((PIN_PA18B_ADC0_PTCXY12 << 16) | MUX_PA18B_ADC0_PTCXY12)
#define PORT_PA18B_ADC0_PTCXY12  (_UL_(1) << 18)
#define PIN_PA19B_ADC0_PTCXY13         _L_(19) /**< \brief ADC0 signal: PTCXY13 on PA19 mux B */
#define MUX_PA19B_ADC0_PTCXY13          _L_(1)
#define PINMUX_PA19B_ADC0_PTCXY13  ((PIN_PA19B_ADC0_PTCXY13 << 16) | MUX_PA19B_ADC0_PTCXY13)
#define PORT_PA19B_ADC0_PTCXY13  (_UL_(1) << 19)
#define PIN_PA20B_ADC0_PTCXY14         _L_(20) /**< \brief ADC0 signal: PTCXY14 on PA20 mux B */
#define MUX_PA20B_ADC0_PTCXY14          _L_(1)
#define PINMUX_PA20B_ADC0_PTCXY14  ((PIN_PA20B_ADC0_PTCXY14 << 16) | MUX_PA20B_ADC0_PTCXY14)
#define PORT_PA20B_ADC0_PTCXY14  (_UL_(1) << 20)
#define PIN_PA21B_ADC0_PTCXY15         _L_(21) /**< \brief ADC0 signal: PTCXY15 on PA21 mux B */
#define MUX_PA21B_ADC0_PTCXY15          _L_(1)
#define PINMUX_PA21B_ADC0_PTCXY15  ((PIN_PA21B_ADC0_PTCXY15 << 16) | MUX_PA21B_ADC0_PTCXY15)
#define PORT_PA21B_ADC0_PTCXY15  (_UL_(1) << 21)
#define PIN_PA22B_ADC0_PTCXY16         _L_(22) /**< \brief ADC0 signal: PTCXY16 on PA22 mux B */
#define MUX_PA22B_ADC0_PTCXY16          _L_(1)
#define PINMUX_PA22B_ADC0_PTCXY16  ((PIN_PA22B_ADC0_PTCXY16 << 16) | MUX_PA22B_ADC0_PTCXY16)
#define PORT_PA22B_ADC0_PTCXY16  (_UL_(1) << 22)
#define PIN_PA23B_ADC0_PTCXY17         _L_(23) /**< \brief ADC0 signal: PTCXY17 on PA23 mux B */
#define MUX_PA23B_ADC0_PTCXY17          _L_(1)
#define PINMUX_PA23B_ADC0_PTCXY17  ((PIN_PA23B_ADC0_PTCXY17 << 16) | MUX_PA23B_ADC0_PTCXY17)
#define PORT_PA23B_ADC0_PTCXY17  (_UL_(1) << 23)
#define PIN_PA27B_ADC0_PTCXY18         _L_(27) /**< \brief ADC0 signal: PTCXY18 on PA27 mux B */
#define MUX_PA27B_ADC0_PTCXY18          _L_(1)
#define PINMUX_PA27B_ADC0_PTCXY18  ((PIN_PA27B_ADC0_PTCXY18 << 16) | MUX_PA27B_ADC0_PTCXY18)
#define PORT_PA27B_ADC0_PTCXY18  (_UL_(1) << 27)
#define PIN_PA30B_ADC0_PTCXY19         _L_(30) /**< \brief ADC0 signal: PTCXY19 on PA30 mux B */
#define MUX_PA30B_ADC0_PTCXY19          _L_(1)
#define PINMUX_PA30B_ADC0_PTCXY19  ((PIN_PA30B_ADC0_PTCXY19 << 16) | MUX_PA30B_ADC0_PTCXY19)
#define PORT_PA30B_ADC0_PTCXY19  (_UL_(1) << 30)
#define PIN_PB02B_ADC0_PTCXY20         _L_(34) /**< \brief ADC0 signal: PTCXY20 on PB02 mux B */
#define MUX_PB02B_ADC0_PTCXY20          _L_(1)
#define PINMUX_PB02B_ADC0_PTCXY20  ((PIN_PB02B_ADC0_PTCXY20 << 16) | MUX_PB02B_ADC0_PTCXY20)
#define PORT_PB02B_ADC0_PTCXY20  (_UL_(1) <<  2)
#define PIN_PB03B_ADC0_PTCXY21         _L_(35) /**< \brief ADC0 signal: PTCXY21 on PB03 mux B */
#define MUX_PB03B_ADC0_PTCXY21          _L_(1)
#define PINMUX_PB03B_ADC0_PTCXY21  ((PIN_PB03B_ADC0_PTCXY21 << 16) | MUX_PB03B_ADC0_PTCXY21)
#define PORT_PB03B_ADC0_PTCXY21  (_UL_(1) <<  3)
#define PIN_PB04B_ADC0_PTCXY22         _L_(36) /**< \brief ADC0 signal: PTCXY22 on PB04 mux B */
#define MUX_PB04B_ADC0_PTCXY22          _L_(1)
#define PINMUX_PB04B_ADC0_PTCXY22  ((PIN_PB04B_ADC0_PTCXY22 << 16) | MUX_PB04B_ADC0_PTCXY22)
#define PORT_PB04B_ADC0_PTCXY22  (_UL_(1) <<  4)
#define PIN_PB05B_ADC0_PTCXY23         _L_(37) /**< \brief ADC0 signal: PTCXY23 on PB05 mux B */
#define MUX_PB05B_ADC0_PTCXY23          _L_(1)
#define PINMUX_PB05B_ADC0_PTCXY23  ((PIN_PB05B_ADC0_PTCXY23 << 16) | MUX_PB05B_ADC0_PTCXY23)
#define PORT_PB05B_ADC0_PTCXY23  (_UL_(1) <<  5)
#define PIN_PB06B_ADC0_PTCXY24         _L_(38) /**< \brief ADC0 signal: PTCXY24 on PB06 mux B */
#define MUX_PB06B_ADC0_PTCXY24          _L_(1)
#define PINMUX_PB06B_ADC0_PTCXY24  ((PIN_PB06B_ADC0_PTCXY24 << 16) | MUX_PB06B_ADC0_PTCXY24)
#define PORT_PB06B_ADC0_PTCXY24  (_UL_(1) <<  6)
#define PIN_PB07B_ADC0_PTCXY25         _L_(39) /**< \brief ADC0 signal: PTCXY25 on PB07 mux B */
#define MUX_PB07B_ADC0_PTCXY25          _L_(1)
#define PINMUX_PB07B_ADC0_PTCXY25  ((PIN_PB07B_ADC0_PTCXY25 << 16) | MUX_PB07B_ADC0_PTCXY25)
#define PORT_PB07B_ADC0_PTCXY25  (_UL_(1) <<  7)
#define PIN_PB12B_ADC0_PTCXY26         _L_(44) /**< \brief ADC0 signal: PTCXY26 on PB12 mux B */
#define MUX_PB12B_ADC0_PTCXY26          _L_(1)
#define PINMUX_PB12B_ADC0_PTCXY26  ((PIN_PB12B_ADC0_PTCXY26 << 16) | MUX_PB12B_ADC0_PTCXY26)
#define PORT_PB12B_ADC0_PTCXY26  (_UL_(1) << 12)
#define PIN_PB13B_ADC0_PTCXY27         _L_(45) /**< \brief ADC0 signal: PTCXY27 on PB13 mux B */
#define MUX_PB13B_ADC0_PTCXY27          _L_(1)
#define PINMUX_PB13B_ADC0_PTCXY27  ((PIN_PB13B_ADC0_PTCXY27 << 16) | MUX_PB13B_ADC0_PTCXY27)
#define PORT_PB13B_ADC0_PTCXY27  (_UL_(1) << 13)
#define PIN_PB14B_ADC0_PTCXY28         _L_(46) /**< \brief ADC0 signal: PTCXY28 on PB14 mux B */
#define MUX_PB14B_ADC0_PTCXY28          _L_(1)
#define PINMUX_PB14B_ADC0_PTCXY28  ((PIN_PB14B_ADC0_PTCXY28 << 16) | MUX_PB14B_ADC0_PTCXY28)
#define PORT_PB14B_ADC0_PTCXY28  (_UL_(1) << 14)
#define PIN_PB15B_ADC0_PTCXY29         _L_(47) /**< \brief ADC0 signal: PTCXY29 on PB15 mux B */
#define MUX_PB15B_ADC0_PTCXY29          _L_(1)
#define PINMUX_PB15B_ADC0_PTCXY29  ((PIN_PB15B_ADC0_PTCXY29 << 16) | MUX_PB15B_ADC0_PTCXY29)
#define PORT_PB15B_ADC0_PTCXY29  (_UL_(1) << 15)
#define PIN_PB00B_ADC0_PTCXY30         _L_(32) /**< \brief ADC0 signal: PTCXY30 on PB00 mux B */
#define MUX_PB00B_ADC0_PTCXY30          _L_(1)
#define PINMUX_PB00B_ADC0_PTCXY30  ((PIN_PB00B_ADC0_PTCXY30 << 16) | MUX_PB00B_ADC0_PTCXY30)
#define PORT_PB00B_ADC0_PTCXY30  (_UL_(1) <<  0)
#define PIN_PB01B_ADC0_PTCXY31         _L_(33) /**< \brief ADC0 signal: PTCXY31 on PB01 mux B */
#define MUX_PB01B_ADC0_PTCXY31          _L_(1)
#define PINMUX_PB01B_ADC0_PTCXY31  ((PIN_PB01B_ADC0_PTCXY31 << 16) | MUX_PB01B_ADC0_PTCXY31)
#define PORT_PB01B_ADC0_PTCXY31  (_UL_(1) <<  1)
/* ========== PORT definition for ADC1 peripheral ========== */
#define PIN_PB08B_ADC1_AIN0            _L_(40) /**< \brief ADC1 signal: AIN0 on PB08 mux B */
#define MUX_PB08B_ADC1_AIN0             _L_(1)
#define PINMUX_PB08B_ADC1_AIN0     ((PIN_PB08B_ADC1_AIN0 << 16) | MUX_PB08B_ADC1_AIN0)
#define PORT_PB08B_ADC1_AIN0   (_UL_(1) <<  8)
#define PIN_PB09B_ADC1_AIN1            _L_(41) /**< \brief ADC1 signal: AIN1 on PB09 mux B */
#define MUX_PB09B_ADC1_AIN1             _L_(1)
#define PINMUX_PB09B_ADC1_AIN1     ((PIN_PB09B_ADC1_AIN1 << 16) | MUX_PB09B_ADC1_AIN1)
#define PORT_PB09B_ADC1_AIN1   (_UL_(1) <<  9)
#define PIN_PA08B_ADC1_AIN2             _L_(8) /**< \brief ADC1 signal: AIN2 on PA08 mux B */
#define MUX_PA08B_ADC1_AIN2             _L_(1)
#define PINMUX_PA08B_ADC1_AIN2     ((PIN_PA08B_ADC1_AIN2 << 16) | MUX_PA08B_ADC1_AIN2)
#define PORT_PA08B_ADC1_AIN2   (_UL_(1) <<  8)
#define PIN_PA09B_ADC1_AIN3             _L_(9) /**< \brief ADC1 signal: AIN3 on PA09 mux B */
#define MUX_PA09B_ADC1_AIN3             _L_(1)
#define PINMUX_PA09B_ADC1_AIN3     ((PIN_PA09B_ADC1_AIN3 << 16) | MUX_PA09B_ADC1_AIN3)
#define PORT_PA09B_ADC1_AIN3   (_UL_(1) <<  9)
#define PIN_PC02B_ADC1_AIN4            _L_(66) /**< \brief ADC1 signal: AIN4 on PC02 mux B */
#define MUX_PC02B_ADC1_AIN4             _L_(1)
#define PINMUX_PC02B_ADC1_AIN4     ((PIN_PC02B_ADC1_AIN4 << 16) | MUX_PC02B_ADC1_AIN4)
#define PORT_PC02B_ADC1_AIN4   (_UL_(1) <<  2)
#define PIN_PC03B_ADC1_AIN5            _L_(67) /**< \brief ADC1 signal: AIN5 on PC03 mux B */
#define MUX_PC03B_ADC1_AIN5             _L_(1)
#define PINMUX_PC03B_ADC1_AIN5     ((PIN_PC03B_ADC1_AIN5 << 16) | MUX_PC03B_ADC1_AIN5)
#define PORT_PC03B_ADC1_AIN5   (_UL_(1) <<  3)
#define PIN_PB04B_ADC1_AIN6            _L_(36) /**< \brief ADC1 signal: AIN6 on PB04 mux B */
#define MUX_PB04B_ADC1_AIN6             _L_(1)
#define PINMUX_PB04B_ADC1_AIN6     ((PIN_PB04B_ADC1_AIN6 << 16) | MUX_PB04B_ADC1_AIN6)
#define PORT_PB04B_ADC1_AIN6   (_UL_(1) <<  4)
#define PIN_PB05B_ADC1_AIN7            _L_(37) /**< \brief ADC1 signal: AIN7 on PB05 mux B */
#define MUX_PB05B_ADC1_AIN7             _L_(1)
#define PINMUX_PB05B_ADC1_AIN7     ((PIN_PB05B_ADC1_AIN7 << 16) | MUX_PB05B_ADC1_AIN7)
#define PORT_PB05B_ADC1_AIN7   (_UL_(1) <<  5)
#define PIN_PB06B_ADC1_AIN8            _L_(38) /**< \brief ADC1 signal: AIN8 on PB06 mux B */
#define MUX_PB06B_ADC1_AIN8             _L_(1)
#define PINMUX_PB06B_ADC1_AIN8     ((PIN_PB06B_ADC1_AIN8 << 16) | MUX_PB06B_ADC1_AIN8)
#define PORT_PB06B_ADC1_AIN8   (_UL_(1) <<  6)
#define PIN_PB07B_ADC1_AIN9            _L_(39) /**< \brief ADC1 signal: AIN9 on PB07 mux B */
#define MUX_PB07B_ADC1_AIN9             _L_(1)
#define PINMUX_PB07B_ADC1_AIN9     ((PIN_PB07B_ADC1_AIN9 << 16) | MUX_PB07B_ADC1_AIN9)
#define PORT_PB07B_ADC1_AIN9   (_UL_(1) <<  7)
#define PIN_PC00B_ADC1_AIN10           _L_(64) /**< \brief ADC1 signal: AIN10 on PC00 mux B */
#define MUX_PC00B_ADC1_AIN10            _L_(1)
#define PINMUX_PC00B_ADC1_AIN10    ((PIN_PC00B_ADC1_AIN10 << 16) | MUX_PC00B_ADC1_AIN10)
#define PORT_PC00B_ADC1_AIN10  (_UL_(1) <<  0)
#define PIN_PC01B_ADC1_AIN11           _L_(65) /**< \brief ADC1 signal: AIN11 on PC01 mux B */
#define MUX_PC01B_ADC1_AIN11            _L_(1)
#define PINMUX_PC01B_ADC1_AIN11    ((PIN_PC01B_ADC1_AIN11 << 16) | MUX_PC01B_ADC1_AIN11)
#define PORT_PC01B_ADC1_AIN11  (_UL_(1) <<  1)
/* ========== PORT definition for DAC peripheral ========== */
#define PIN_PA02B_DAC_VOUT0             _L_(2) /**< \brief DAC signal: VOUT0 on PA02 mux B */
#define MUX_PA02B_DAC_VOUT0             _L_(1)
#define PINMUX_PA02B_DAC_VOUT0     ((PIN_PA02B_DAC_VOUT0 << 16) | MUX_PA02B_DAC_VOUT0)
#define PORT_PA02B_DAC_VOUT0   (_UL_(1) <<  2)
#define PIN_PA05B_DAC_VOUT1             _L_(5) /**< \brief DAC signal: VOUT1 on PA05 mux B */
#define MUX_PA05B_DAC_VOUT1             _L_(1)
#define PINMUX_PA05B_DAC_VOUT1     ((PIN_PA05B_DAC_VOUT1 << 16) | MUX_PA05B_DAC_VOUT1)
#define PORT_PA05B_DAC_VOUT1   (_UL_(1) <<  5)
/* ========== PORT definition for I2S peripheral ========== */
#define PIN_PA09J_I2S_FS0               _L_(9) /**< \brief I2S signal: FS0 on PA09 mux J */
#define MUX_PA09J_I2S_FS0               _L_(9)
#define PINMUX_PA09J_I2S_FS0       ((PIN_PA09J_I2S_FS0 << 16) | MUX_PA09J_I2S_FS0)
#define PORT_PA09J_I2S_FS0     (_UL_(1) <<  9)
#define PIN_PA20J_I2S_FS0              _L_(20) /**< \brief I2S signal: FS0 on PA20 mux J */
#define MUX_PA20J_I2S_FS0               _L_(9)
#define PINMUX_PA20J_I2S_FS0       ((PIN_PA20J_I2S_FS0 << 16) | MUX_PA20J_I2S_FS0)
#define PORT_PA20J_I2S_FS0     (_UL_(1) << 20)
#define PIN_PA23J_I2S_FS1              _L_(23) /**< \brief I2S signal: FS1 on PA23 mux J */
#define MUX_PA23J_I2S_FS1               _L_(9)
#define PINMUX_PA23J_I2S_FS1       ((PIN_PA23J_I2S_FS1 << 16) | MUX_PA23J_I2S_FS1)
#define PORT_PA23J_I2S_FS1     (_UL_(1) << 23)
#define PIN_PB11J_I2S_FS1              _L_(43) /**< \brief I2S signal: FS1 on PB11 mux J */
#define MUX_PB11J_I2S_FS1               _L_(9)
#define PINMUX_PB11J_I2S_FS1       ((PIN_PB11J_I2S_FS1 << 16) | MUX_PB11J_I2S_FS1)
#define PORT_PB11J_I2S_FS1     (_UL_(1) << 11)
#define PIN_PA08J_I2S_MCK0              _L_(8) /**< \brief I2S signal: MCK0 on PA08 mux J */
#define MUX_PA08J_I2S_MCK0              _L_(9)
#define PINMUX_PA08J_I2S_MCK0      ((PIN_PA08J_I2S_MCK0 << 16) | MUX_PA08J_I2S_MCK0)
#define PORT_PA08J_I2S_MCK0    (_UL_(1) <<  8)
#define PIN_PB17J_I2S_MCK0             _L_(49) /**< \brief I2S signal: MCK0 on PB17 mux J */
#define MUX_PB17J_I2S_MCK0              _L_(9)
#define PINMUX_PB17J_I2S_MCK0      ((PIN_PB17J_I2S_MCK0 << 16) | MUX_PB17J_I2S_MCK0)
#define PORT_PB17J_I2S_MCK0    (_UL_(1) << 17)
#define PIN_PB13J_I2S_MCK1             _L_(45) /**< \brief I2S signal: MCK1 on PB13 mux J */
#define MUX_PB13J_I2S_MCK1              _L_(9)
#define PINMUX_PB13J_I2S_MCK1      ((PIN_PB13J_I2S_MCK1 << 16) | MUX_PB13J_I2S_MCK1)
#define PORT_PB13J_I2S_MCK1    (_UL_(1) << 13)
#define PIN_PA10J_I2S_SCK0             _L_(10) /**< \brief I2S signal: SCK0 on PA10 mux J */
#define MUX_PA10J_I2S_SCK0              _L_(9)
#define PINMUX_PA10J_I2S_SCK0      ((PIN_PA10J_I2S_SCK0 << 16) | MUX_PA10J_I2S_SCK0)
#define PORT_PA10J_I2S_SCK0    (_UL_(1) << 10)
#define PIN_PB16J_I2S_SCK0             _L_(48) /**< \brief I2S signal: SCK0 on PB16 mux J */
#define MUX_PB16J_I2S_SCK0              _L_(9)
#define PINMUX_PB16J_I2S_SCK0      ((PIN_PB16J_I2S_SCK0 << 16) | MUX_PB16J_I2S_SCK0)
#define PORT_PB16J_I2S_SCK0    (_UL_(1) << 16)
#define PIN_PB12J_I2S_SCK1             _L_(44) /**< \brief I2S signal: SCK1 on PB12 mux J */
#define MUX_PB12J_I2S_SCK1              _L_(9)
#define PINMUX_PB12J_I2S_SCK1      ((PIN_PB12J_I2S_SCK1 << 16) | MUX_PB12J_I2S_SCK1)
#define PORT_PB12J_I2S_SCK1    (_UL_(1) << 12)
#define PIN_PA22J_I2S_SDI              _L_(22) /**< \brief I2S signal: SDI on PA22 mux J */
#define MUX_PA22J_I2S_SDI               _L_(9)
#define PINMUX_PA22J_I2S_SDI       ((PIN_PA22J_I2S_SDI << 16) | MUX_PA22J_I2S_SDI)
#define PORT_PA22J_I2S_SDI     (_UL_(1) << 22)
#define PIN_PB10J_I2S_SDI              _L_(42) /**< \brief I2S signal: SDI on PB10 mux J */
#define MUX_PB10J_I2S_SDI               _L_(9)
#define PINMUX_PB10J_I2S_SDI       ((PIN_PB10J_I2S_SDI << 16) | MUX_PB10J_I2S_SDI)
#define PORT_PB10J_I2S_SDI     (_UL_(1) << 10)
#define PIN_PA11J_I2S_SDO              _L_(11) /**< \brief I2S signal: SDO on PA11 mux J */
#define MUX_PA11J_I2S_SDO               _L_(9)
#define PINMUX_PA11J_I2S_SDO       ((PIN_PA11J_I2S_SDO << 16) | MUX_PA11J_I2S_SDO)
#define PORT_PA11J_I2S_SDO     (_UL_(1) << 11)
#define PIN_PA21J_I2S_SDO              _L_(21) /**< \brief I2S signal: SDO on PA21 mux J */
#define MUX_PA21J_I2S_SDO               _L_(9)
#define PINMUX_PA21J_I2S_SDO       ((PIN_PA21J_I2S_SDO << 16) | MUX_PA21J_I2S_SDO)
#define PORT_PA21J_I2S_SDO     (_UL_(1) << 21)
/* ========== PORT definition for PCC peripheral ========== */
#define PIN_PA14K_PCC_CLK              _L_(14) /**< \brief PCC signal: CLK on PA14 mux K */
#define MUX_PA14K_PCC_CLK              _L_(10)
#define PINMUX_PA14K_PCC_CLK       ((PIN_PA14K_PCC_CLK << 16) | MUX_PA14K_PCC_CLK)
#define PORT_PA14K_PCC_CLK     (_UL_(1) << 14)
#define PIN_PA16K_PCC_DATA0            _L_(16) /**< \brief PCC signal: DATA0 on PA16 mux K */
#define MUX_PA16K_PCC_DATA0            _L_(10)
#define PINMUX_PA16K_PCC_DATA0     ((PIN_PA16K_PCC_DATA0 << 16) | MUX_PA16K_PCC_DATA0)
#define PORT_PA16K_PCC_DATA0   (_UL_(1) << 16)
#define PIN_PA17K_PCC_DATA1            _L_(17) /**< \brief PCC signal: DATA1 on PA17 mux K */
#define MUX_PA17K_PCC_DATA1            _L_(10)
#define PINMUX_PA17K_PCC_DATA1     ((PIN_PA17K_PCC_DATA1 << 16) | MUX_PA17K_PCC_DATA1)
#define PORT_PA17K_PCC_DATA1   (_UL_(1) << 17)
#define PIN_PA18K_PCC_DATA2            _L_(18) /**< \brief PCC signal: DATA2 on PA18 mux K */
#define MUX_PA18K_PCC_DATA2            _L_(10)
#define PINMUX_PA18K_PCC_DATA2     ((PIN_PA18K_PCC_DATA2 << 16) | MUX_PA18K_PCC_DATA2)
#define PORT_PA18K_PCC_DATA2   (_UL_(1) << 18)
#define PIN_PA19K_PCC_DATA3            _L_(19) /**< \brief PCC signal: DATA3 on PA19 mux K */
#define MUX_PA19K_PCC_DATA3            _L_(10)
#define PINMUX_PA19K_PCC_DATA3     ((PIN_PA19K_PCC_DATA3 << 16) | MUX_PA19K_PCC_DATA3)
#define PORT_PA19K_PCC_DATA3   (_UL_(1) << 19)
#define PIN_PA20K_PCC_DATA4            _L_(20) /**< \brief PCC signal: DATA4 on PA20 mux K */
#define MUX_PA20K_PCC_DATA4            _L_(10)
#define PINMUX_PA20K_PCC_DATA4     ((PIN_PA20K_PCC_DATA4 << 16) | MUX_PA20K_PCC_DATA4)
#define PORT_PA20K_PCC_DATA4   (_UL_(1) << 20)
#define PIN_PA21K_PCC_DATA5            _L_(21) /**< \brief PCC signal: DATA5 on PA21 mux K */
#define MUX_PA21K_PCC_DATA5            _L_(10)
#define PINMUX_PA21K_PCC_DATA5     ((PIN_PA21K_PCC_DATA5 << 16) | MUX_PA21K_PCC_DATA5)
#define PORT_PA21K_PCC_DATA5   (_UL_(1) << 21)
#define PIN_PA22K_PCC_DATA6            _L_(22) /**< \brief PCC signal: DATA6 on PA22 mux K */
#define MUX_PA22K_PCC_DATA6            _L_(10)
#define PINMUX_PA22K_PCC_DATA6     ((PIN_PA22K_PCC_DATA6 << 16) | MUX_PA22K_PCC_DATA6)
#define PORT_PA22K_PCC_DATA6   (_UL_(1) << 22)
#define PIN_PA23K_PCC_DATA7            _L_(23) /**< \brief PCC signal: DATA7 on PA23 mux K */
#define MUX_PA23K_PCC_DATA7            _L_(10)
#define PINMUX_PA23K_PCC_DATA7     ((PIN_PA23K_PCC_DATA7 << 16) | MUX_PA23K_PCC_DATA7)
#define PORT_PA23K_PCC_DATA7   (_UL_(1) << 23)
#define PIN_PB14K_PCC_DATA8            _L_(46) /**< \brief PCC signal: DATA8 on PB14 mux K */
#define MUX_PB14K_PCC_DATA8            _L_(10)
#define PINMUX_PB14K_PCC_DATA8     ((PIN_PB14K_PCC_DATA8 << 16) | MUX_PB14K_PCC_DATA8)
#define PORT_PB14K_PCC_DATA8   (_UL_(1) << 14)
#define PIN_PB15K_PCC_DATA9            _L_(47) /**< \brief PCC signal: DATA9 on PB15 mux K */
#define MUX_PB15K_PCC_DATA9            _L_(10)
#define PINMUX_PB15K_PCC_DATA9     ((PIN_PB15K_PCC_DATA9 << 16) | MUX_PB15K_PCC_DATA9)
#define PORT_PB15K_PCC_DATA9   (_UL_(1) << 15)
#define PIN_PC12K_PCC_DATA10           _L_(76) /**< \brief PCC signal: DATA10 on PC12 mux K */
#define MUX_PC12K_PCC_DATA10           _L_(10)
#define PINMUX_PC12K_PCC_DATA10    ((PIN_PC12K_PCC_DATA10 << 16) | MUX_PC12K_PCC_DATA10)
#define PORT_PC12K_PCC_DATA10  (_UL_(1) << 12)
#define PIN_PC13K_PCC_DATA11           _L_(77) /**< \brief PCC signal: DATA11 on PC13 mux K */
#define MUX_PC13K_PCC_DATA11           _L_(10)
#define PINMUX_PC13K_PCC_DATA11    ((PIN_PC13K_PCC_DATA11 << 16) | MUX_PC13K_PCC_DATA11)
#define PORT_PC13K_PCC_DATA11  (_UL_(1) << 13)
#define PIN_PC14K_PCC_DATA12           _L_(78) /**< \brief PCC signal: DATA12 on PC14 mux K */
#define MUX_PC14K_PCC_DATA12           _L_(10)
#define PINMUX_PC14K_PCC_DATA12    ((PIN_PC14K_PCC_DATA12 << 16) | MUX_PC14K_PCC_DATA12)
#define PORT_PC14K_PCC_DATA12  (_UL_(1) << 14)
#define PIN_PC15K_PCC_DATA13           _L_(79) /**< \brief PCC signal: DATA13 on PC15 mux K */
#define MUX_PC15K_PCC_DATA13           _L_(10)
#define PINMUX_PC15K_PCC_DATA13    ((PIN_PC15K_PCC_DATA13 << 16) | MUX_PC15K_PCC_DATA13)
#define PORT_PC15K_PCC_DATA13  (_UL_(1) << 15)
#define PIN_PA12K_PCC_DEN1             _L_(12) /**< \brief PCC signal: DEN1 on PA12 mux K */
#define MUX_PA12K_PCC_DEN1             _L_(10)
#define PINMUX_PA12K_PCC_DEN1      ((PIN_PA12K_PCC_DEN1 << 16) | MUX_PA12K_PCC_DEN1)
#define PORT_PA12K_PCC_DEN1    (_UL_(1) << 12)
#define PIN_PA13K_PCC_DEN2             _L_(13) /**< \brief PCC signal: DEN2 on PA13 mux K */
#define MUX_PA13K_PCC_DEN2             _L_(10)
#define PINMUX_PA13K_PCC_DEN2      ((PIN_PA13K_PCC_DEN2 << 16) | MUX_PA13K_PCC_DEN2)
#define PORT_PA13K_PCC_DEN2    (_UL_(1) << 13)
/* ========== PORT definition for SDHC0 peripheral ========== */
#define PIN_PA06I_SDHC0_SDCD            _L_(6) /**< \brief SDHC0 signal: SDCD on PA06 mux I */
#define MUX_PA06I_SDHC0_SDCD            _L_(8)
#define PINMUX_PA06I_SDHC0_SDCD    ((PIN_PA06I_SDHC0_SDCD << 16) | MUX_PA06I_SDHC0_SDCD)
#define PORT_PA06I_SDHC0_SDCD  (_UL_(1) <<  6)
#define PIN_PA12I_SDHC0_SDCD           _L_(12) /**< \brief SDHC0 signal: SDCD on PA12 mux I */
#define MUX_PA12I_SDHC0_SDCD            _L_(8)
#define PINMUX_PA12I_SDHC0_SDCD    ((PIN_PA12I_SDHC0_SDCD << 16) | MUX_PA12I_SDHC0_SDCD)
#define PORT_PA12I_SDHC0_SDCD  (_UL_(1) << 12)
#define PIN_PB12I_SDHC0_SDCD           _L_(44) /**< \brief SDHC0 signal: SDCD on PB12 mux I */
#define MUX_PB12I_SDHC0_SDCD            _L_(8)
#define PINMUX_PB12I_SDHC0_SDCD    ((PIN_PB12I_SDHC0_SDCD << 16) | MUX_PB12I_SDHC0_SDCD)
#define PORT_PB12I_SDHC0_SDCD  (_UL_(1) << 12)
#define PIN_PC06I_SDHC0_SDCD           _L_(70) /**< \brief SDHC0 signal: SDCD on PC06 mux I */
#define MUX_PC06I_SDHC0_SDCD            _L_(8)
#define PINMUX_PC06I_SDHC0_SDCD    ((PIN_PC06I_SDHC0_SDCD << 16) | MUX_PC06I_SDHC0_SDCD)
#define PORT_PC06I_SDHC0_SDCD  (_UL_(1) <<  6)
#define PIN_PB11I_SDHC0_SDCK           _L_(43) /**< \brief SDHC0 signal: SDCK on PB11 mux I */
#define MUX_PB11I_SDHC0_SDCK            _L_(8)
#define PINMUX_PB11I_SDHC0_SDCK    ((PIN_PB11I_SDHC0_SDCK << 16) | MUX_PB11I_SDHC0_SDCK)
#define PORT_PB11I_SDHC0_SDCK  (_UL_(1) << 11)
#define PIN_PA08I_SDHC0_SDCMD           _L_(8) /**< \brief SDHC0 signal: SDCMD on PA08 mux I */
#define MUX_PA08I_SDHC0_SDCMD           _L_(8)
#define PINMUX_PA08I_SDHC0_SDCMD   ((PIN_PA08I_SDHC0_SDCMD << 16) | MUX_PA08I_SDHC0_SDCMD)
#define PORT_PA08I_SDHC0_SDCMD  (_UL_(1) <<  8)
#define PIN_PA09I_SDHC0_SDDAT0          _L_(9) /**< \brief SDHC0 signal: SDDAT0 on PA09 mux I */
#define MUX_PA09I_SDHC0_SDDAT0          _L_(8)
#define PINMUX_PA09I_SDHC0_SDDAT0  ((PIN_PA09I_SDHC0_SDDAT0 << 16) | MUX_PA09I_SDHC0_SDDAT0)
#define PORT_PA09I_SDHC0_SDDAT0  (_UL_(1) <<  9)
#define PIN_PA10I_SDHC0_SDDAT1         _L_(10) /**< \brief SDHC0 signal: SDDAT1 on PA10 mux I */
#define MUX_PA10I_SDHC0_SDDAT1          _L_(8)
#define PINMUX_PA10I_SDHC0_SDDAT1  ((PIN_PA10I_SDHC0_SDDAT1 << 16) | MUX_PA10I_SDHC0_SDDAT1)
#define PORT_PA10I_SDHC0_SDDAT1  (_UL_(1) << 10)
#define PIN_PA11I_SDHC0_SDDAT2         _L_(11) /**< \brief SDHC0 signal: SDDAT2 on PA11 mux I */
#define MUX_PA11I_SDHC0_SDDAT2          _L_(8)
#define PINMUX_PA11I_SDHC0_SDDAT2  ((PIN_PA11I_SDHC0_SDDAT2 << 16) | MUX_PA11I_SDHC0_SDDAT2)
#define PORT_PA11I_SDHC0_SDDAT2  (_UL_(1) << 11)
#define PIN_PB10I_SDHC0_SDDAT3         _L_(42) /**< \brief SDHC0 signal: SDDAT3 on PB10 mux I */
#define MUX_PB10I_SDHC0_SDDAT3          _L_(8)
#define PINMUX_PB10I_SDHC0_SDDAT3  ((PIN_PB10I_SDHC0_SDDAT3 << 16) | MUX_PB10I_SDHC0_SDDAT3)
#define PORT_PB10I_SDHC0_SDDAT3  (_UL_(1) << 10)
#define PIN_PA07I_SDHC0_SDWP            _L_(7) /**< \brief SDHC0 signal: SDWP on PA07 mux I */
#define MUX_PA07I_SDHC0_SDWP            _L_(8)
#define PINMUX_PA07I_SDHC0_SDWP    ((PIN_PA07I_SDHC0_SDWP << 16) | MUX_PA07I_SDHC0_SDWP)
#define PORT_PA07I_SDHC0_SDWP  (_UL_(1) <<  7)
#define PIN_PA13I_SDHC0_SDWP           _L_(13) /**< \brief SDHC0 signal: SDWP on PA13 mux I */
#define MUX_PA13I_SDHC0_SDWP            _L_(8)
#define PINMUX_PA13I_SDHC0_SDWP    ((PIN_PA13I_SDHC0_SDWP << 16) | MUX_PA13I_SDHC0_SDWP)
#define PORT_PA13I_SDHC0_SDWP  (_UL_(1) << 13)
#define PIN_PB13I_SDHC0_SDWP           _L_(45) /**< \brief SDHC0 signal: SDWP on PB13 mux I */
#define MUX_PB13I_SDHC0_SDWP            _L_(8)
#define PINMUX_PB13I_SDHC0_SDWP    ((PIN_PB13I_SDHC0_SDWP << 16) | MUX_PB13I_SDHC0_SDWP)
#define PORT_PB13I_SDHC0_SDWP  (_UL_(1) << 13)
#define PIN_PC07I_SDHC0_SDWP           _L_(71) /**< \brief SDHC0 signal: SDWP on PC07 mux I */
#define MUX_PC07I_SDHC0_SDWP            _L_(8)
#define PINMUX_PC07I_SDHC0_SDWP    ((PIN_PC07I_SDHC0_SDWP << 16) | MUX_PC07I_SDHC0_SDWP)
#define PORT_PC07I_SDHC0_SDWP  (_UL_(1) <<  7)

#endif /* _SAME51N19A_PIO_ */
