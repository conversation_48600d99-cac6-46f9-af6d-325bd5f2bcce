diff --git a/lib/samc21/samc21/include/instance/can0.h b/lib/samc21/samc21/include/instance/can0.h
index ffb7f796..4f04d555 100644
--- a/lib/samc21/samc21/include/instance/can0.h
+++ b/lib/samc21/samc21/include/instance/can0.h
@@ -133,7 +133,7 @@
 #define CAN0_CLK_AHB_ID             8        // Index of AHB clock
 #define CAN0_DMAC_ID_DEBUG          14       // DMA CAN Debug Req
 #define CAN0_GCLK_ID                26       // Index of Generic Clock
-#define CAN0_MSG_RAM_ADDR           0x200000000
+#define CAN0_MSG_RAM_ADDR           0x20000000
 #define CAN0_QOS_RESET_VAL          2        // QOS reset value
 
 #endif /* _SAMC21_CAN0_INSTANCE_ */
diff --git a/lib/samc21/samc21/include/instance/can1.h b/lib/samc21/samc21/include/instance/can1.h
index 484db284..eadd0b16 100644
--- a/lib/samc21/samc21/include/instance/can1.h
+++ b/lib/samc21/samc21/include/instance/can1.h
@@ -133,7 +133,7 @@
 #define CAN1_CLK_AHB_ID             9        // Index of AHB clock
 #define CAN1_DMAC_ID_DEBUG          15       // DMA CAN Debug Req
 #define CAN1_GCLK_ID                27       // Index of Generic Clock
-#define CAN1_MSG_RAM_ADDR           0x200000000
+#define CAN1_MSG_RAM_ADDR           0x20000000
 #define CAN1_QOS_RESET_VAL          2        // QOS reset value
 
 #endif /* _SAMC21_CAN1_INSTANCE_ */
