///////////////////////////////////////////////////////////////////////////////
// BOSSA
//
// Copyright (c) 2011-2018, ShumaTech
// All rights reserved.
// 
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the <organization> nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
///////////////////////////////////////////////////////////////////////////////
#ifndef _FILEERROR_H
#define _FILEERROR_H

#include <exception>
#include <errno.h>
#include <string.h>

#include "Flash.h"
#include "Samba.h"

class FileError : public std::exception
{
public:
    FileError() : std::exception() {}
};

class FileOpenError : public FileError
{
public:
    FileOpenError() : FileError(), _errnum(0) {};
    FileOpenError(int errnum) : FileError(), _errnum(errnum) {};
    const char* what() const throw()
    {
        if (_errnum == 0)
            return "Unable to open file";
        else
            return strerror(_errnum);
    }
private:
    int _errnum;
};

class FileIoError : public FileError
{
public:
    FileIoError() : FileError(), _errnum(0) {};
    FileIoError(int errnum) : FileError(), _errnum(errnum) {};
    const char* what() const throw()
    {
        if (_errnum == 0)
            return "File I/O operation failed";
        else
            return strerror(_errnum);
    }
private:
    int _errnum;
};

class FileShortError : public FileError
{
public:
    FileShortError() : FileError() {};
    const char* what() const throw()
    {
        return "Operation ended with a short write";
    }
};

class FileSizeError : public FileError
{
public:
    FileSizeError() {};
    const char* what() const throw() { return "File operation exceeds flash size"; }
};

#endif // _FILEERROR_H
