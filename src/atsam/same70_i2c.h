#ifndef __SAME70_I2C_H
#define __SAME70_I2C_H

// A series of redefinitions as upstream changed the name of the peripheral
#define Twi Twihs
#define TWI_CR TWIHS_CR
#define TWI_CR_MSDIS TWIHS_CR_MSDIS
#define TWI_CR_MSEN TWIHS_CR_MSEN
#define TWI_CR_START TWIHS_CR_START
#define TWI_CR_STOP TWIHS_CR_STOP
#define TWI_CR_SVDIS TWIHS_CR_SVDIS
#define TWI_CR_SWRST TWIHS_CR_SWRST
#define TWI_CWGR TWIHS_CWGR
#define TWI_CWGR_CHDIV TWIHS_CWGR_CHDIV
#define TWI_CWGR_CKDIV TWIHS_CWGR_CKDIV
#define TWI_CWGR_CLDIV TWIHS_CWGR_CLDIV
#define TWI_IADR TWIHS_IADR
#define TWI_IDR TWIHS_IDR
#define TWI_MMR TWIHS_MMR
#define TWI_MMR_DADR TWIHS_MMR_DADR
#define TWI_MMR_IADRSZ_Msk TWIHS_MMR_IADRSZ_Msk
#define TWI_MMR_IADRSZ_Pos TWIHS_MMR_IADRSZ_Pos
#define TWI_MMR_MREAD TWIHS_MMR_MREAD
#define TWI_RHR TWIHS_RHR
#define TWI_SR TWIHS_SR
#define TWI_SR_NAC TWIHS_SR_NACK
#define TWI_SR_NACK TWIHS_SR_NACK
#define TWI_SR_RXRDY TWIHS_SR_RXRDY
#define TWI_SR_TXCOMP TWIHS_SR_TXCOMP
#define TWI_SR_TXRDY TWIHS_SR_TXRDY
#define TWI_THR TWIHS_THR

#endif // same70_i2c.h
