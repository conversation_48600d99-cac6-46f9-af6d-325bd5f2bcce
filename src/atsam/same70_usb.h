#ifndef __SAME70_USB_H
#define __SAME70_USB_H

// Missing in upstream headers
#define USBHS_RAM_ADDR 0xa0100000u

// A series of redefinitions as upstream changed the name of the peripheral
#define UOTGHS USBHS
#define UOTGHS_RAM_ADDR USBHS_RAM_ADDR
#define ID_UOTGHS ID_USBHS
#define UOTGHS_IRQn USBHS_IRQn
#define UOTGHS_CTRL USBHS_CTRL
#define UOTGHS_CTRL_UIMOD USBHS_CTRL_UIMOD
#define UOTGHS_CTRL_USBE USBHS_CTRL_USBE
#define UOTGHS_DEVCTRL USBHS_DEVCTRL
#define UOTGHS_DEVCTRL_ADDEN USBHS_DEVCTRL_ADDEN
#define UOTGHS_DEVCTRL_SPDCONF_FORCED_FS USBHS_DEVCTRL_SPDCONF_FORCED_FS
#define UOTGHS_DEVEPT USBHS_DEVEPT
#define UOTGHS_DEVEPT_EPEN0 USBHS_DEVEPT_EPEN0
#define UOTGHS_DEVEPTCFG USBHS_DEVEPTCFG
#define UOTGHS_DEVEPTCFG_ALLOC USBHS_DEVEPTCFG_ALLOC
#define UOTGHS_DEVEPTCFG_EPBK_1_BANK USBHS_DEVEPTCFG_EPBK_1_BANK
#define UOTGHS_DEVEPTCFG_EPBK_2_BANK USBHS_DEVEPTCFG_EPBK_2_BANK
#define UOTGHS_DEVEPTCFG_EPDIR_IN USBHS_DEVEPTCFG_EPDIR_IN
#define UOTGHS_DEVEPTCFG_EPSIZE_8_BYTE USBHS_DEVEPTCFG_EPSIZE_8_BYTE
#define UOTGHS_DEVEPTCFG_EPSIZE_16_BYTE USBHS_DEVEPTCFG_EPSIZE_16_BYTE
#define UOTGHS_DEVEPTCFG_EPSIZE_32_BYTE USBHS_DEVEPTCFG_EPSIZE_32_BYTE
#define UOTGHS_DEVEPTCFG_EPSIZE_64_BYTE USBHS_DEVEPTCFG_EPSIZE_64_BYTE
#define UOTGHS_DEVEPTCFG_EPTYPE_BLK USBHS_DEVEPTCFG_EPTYPE_BLK
#define UOTGHS_DEVEPTCFG_EPTYPE_CTRL USBHS_DEVEPTCFG_EPTYPE_CTRL
#define UOTGHS_DEVEPTCFG_EPTYPE_INTRPT USBHS_DEVEPTCFG_EPTYPE_INTRPT
#define UOTGHS_DEVEPTICR USBHS_DEVEPTICR
#define UOTGHS_DEVEPTICR_RXOUTIC USBHS_DEVEPTICR_RXOUTIC
#define UOTGHS_DEVEPTICR_RXSTPIC USBHS_DEVEPTICR_RXSTPIC
#define UOTGHS_DEVEPTICR_TXINIC USBHS_DEVEPTICR_TXINIC
#define UOTGHS_DEVEPTIDR USBHS_DEVEPTIDR
#define UOTGHS_DEVEPTIDR_FIFOCONC USBHS_DEVEPTIDR_FIFOCONC
#define UOTGHS_DEVEPTIDR_RXOUTEC USBHS_DEVEPTIDR_RXOUTEC
#define UOTGHS_DEVEPTIDR_TXINEC USBHS_DEVEPTIDR_TXINEC
#define UOTGHS_DEVEPTIER USBHS_DEVEPTIER
#define UOTGHS_DEVEPTIER_RXOUTES USBHS_DEVEPTIER_RXOUTES
#define UOTGHS_DEVEPTIER_RXSTPES USBHS_DEVEPTIER_RXSTPES
#define UOTGHS_DEVEPTIER_STALLRQS USBHS_DEVEPTIER_STALLRQS
#define UOTGHS_DEVEPTIER_TXINES USBHS_DEVEPTIER_TXINES
#define UOTGHS_DEVEPTISR USBHS_DEVEPTISR
#define UOTGHS_DEVEPTISR_BYCT_Msk USBHS_DEVEPTISR_BYCT_Msk
#define UOTGHS_DEVEPTISR_BYCT_Pos USBHS_DEVEPTISR_BYCT_Pos
#define UOTGHS_DEVEPTISR_RXOUTI USBHS_DEVEPTISR_RXOUTI
#define UOTGHS_DEVEPTISR_RXSTPI USBHS_DEVEPTISR_RXSTPI
#define UOTGHS_DEVEPTISR_TXINI USBHS_DEVEPTISR_TXINI
#define UOTGHS_DEVICR USBHS_DEVICR
#define UOTGHS_DEVICR_EORSTC USBHS_DEVICR_EORSTC
#define UOTGHS_DEVIDR USBHS_DEVIDR
#define UOTGHS_DEVIER USBHS_DEVIER
#define UOTGHS_DEVIER_EORSTES USBHS_DEVIER_EORSTES
#define UOTGHS_DEVIER_PEP_0 USBHS_DEVIER_PEP_0
#define UOTGHS_DEVISR USBHS_DEVISR
#define UOTGHS_DEVISR_EORST USBHS_DEVISR_EORST
#define UOTGHS_DEVISR_PEP_0 USBHS_DEVISR_PEP_0

#endif // same70_usb.h
