#ifndef __SAME70_AFEC_H
#define __SAME70_AFEC_H

// A series of redefinitions as upstream changed the name of the peripheral
#define AFE_ACR AFEC_ACR
#define AFE_ACR_IBCTL AFEC_ACR_IBCTL
#define AFE_CDR AFEC_CDR
#define AFE_CGR AFEC_CGR
#define AFE_CHDR AFEC_CHDR
#define AFE_CHER AFEC_CHER
#define AFE_COCR AFEC_COCR
#define AFE_COCR_AOFF_Msk AFEC_COCR_AOFF_Msk
#define AFE_CR AFEC_CR
#define AFE_CR_START AFEC_CR_START
#define AFE_CR_SWRST AFEC_CR_SWRST
#define AFE_DIFFR AFEC_DIFFR
#define AFE_DUMMY AFEC_DUMMY
#define AFE_EMR AFEC_EMR
#define AFE_EMR_RES_NO_AVERAGE AFEC_EMR_RES_NO_AVERAGE
#define AFE_EMR_STM AFEC_EMR_STM
#define AFE_EMR_TAG AFEC_EMR_TAG
#define AFE_IDR AFEC_IDR
#define AFE_ISR AFEC_ISR
#define AFE_LCDR AFEC_LCDR
#define AFE_LCDR_LDATA_Msk AFEC_LCDR_LDATA_Msk
#define AFE_MR_FREERUN_ON AFEC_MR_FREERUN_ON
#define AFE_MR_PRESCAL AFEC_MR_PRESCAL
#define AFE_MR_STARTUP_SUT64 AFEC_MR_STARTUP_SUT64
#define AFE_MR_TRANSFER AFEC_MR_TRANSFER
#define AFE_MR_TRGEN AFEC_MR_TRGEN
#define AFE_MR_TRGEN_DIS AFEC_MR_TRGEN_DIS
#define AFE_MR_TRGSEL_Msk AFEC_MR_TRGSEL_Msk
#define AFE_ISR_DRDY AFEC_ISR_DRDY
#define AFE_MR AFEC_MR
#define AFE_CSELR AFEC_CSELR
#define AFE_CHSR AFEC_CHSR

#endif // same70_afec.h
