# Kconfig settings for Atmel SAM processors

if MACH_ATSAM

config ATSAM_SELECT
    bool
    default y
    select HAV<PERSON>_<PERSON><PERSON>
    select HAV<PERSON>_<PERSON><PERSON>_AD<PERSON>
    select HAVE_GPIO_I2C
    select HAVE_GPIO_SP<PERSON>
    select HAV<PERSON>_GPIO_HARD_PWM if !<PERSON><PERSON>_SAME70
    select HAVE_STRICT_TIMING
    select HAV<PERSON>_CHIPID
    select HAVE_STEPPER_OPTIMIZED_BOTH_EDGE
    select HAVE_BOOTLOADER_REQUEST

config BOARD_DIRECTORY
    string
    default "atsam"

choice
    prompt "Processor model"
    config MACH_SAM3X8E
        bool "SAM3x8e"
        select MACH_SAM3X
    config MACH_SAM3X8C
        bool "SAM3x8c"
        select MACH_SAM3X
    config MACH_SAM4S8C
        bool "SAM4s8c"
        select MACH_SAM4S
    config MACH_SAM4E8E
        bool "SAM4e8e"
        select MACH_SAM4E
    config MACH_SAME70Q20B
        bool "SAME70Q20B"
        select MACH_SAME70
endchoice

config MACH_SAM3X
    bool
config MACH_SAM4
    bool
config MACH_SAM4S
    bool
    select MACH_SAM4
config MACH_SAM4E
    bool
    select MACH_SAM4
config MACH_SAME70
    bool
config HAVE_SAM_CANBUS
    bool
    default y if MACH_SAME70

config MCU
    string
    default "sam3x8e" if MACH_SAM3X8E
    default "sam3x8c" if MACH_SAM3X8C
    default "sam4s8c" if MACH_SAM4S8C
    default "sam4e8e" if MACH_SAM4E8E
    default "same70q20b" if MACH_SAME70Q20B

config CLOCK_FREQ
    int
    default 84000000 if MACH_SAM3X
    default 120000000 if MACH_SAM4
    default 300000000 if MACH_SAME70

config FLASH_SIZE
    hex
    default 0x20000 if MACH_SAME70
    default 0x80000

config FLASH_BOOT_ADDRESS
    hex
    default 0x0

config RAM_START
    hex
    default 0x20400000 if MACH_SAME70
    default 0x20000000

config RAM_SIZE
    hex
    default 0x18000 if MACH_SAM3X
    default 0x20000 if MACH_SAM4 || MACH_SAME70

config STACK_SIZE
    int
    default 512

config FLASH_APPLICATION_ADDRESS
    hex
    default 0x0 if MACH_SAME70
    default 0x400000 if MACH_SAM4
    default 0x80000

config ARMCM_ITCM_FLASH_MIRROR_START
    depends on MACH_SAME70
    hex
    default 0x400000

config ARMCM_DTCM_START
    depends on MACH_SAME70
    hex
    default 0x20000000

config ARMCM_DTCM_SIZE
    depends on MACH_SAME70
    hex
    default 0x20000

choice
    prompt "Communication interface"
    config ATSAM_USB
        bool "USB"
        select USBSERIAL
    config ATSAM_SERIAL
        bool "Serial"
        select SERIAL
    config ATSAM_MMENU_CANBUS_PC12_PD12
        bool "CAN bus (on PC12/PD12)"
        depends on HAVE_SAM_CANBUS
        select CANSERIAL
    config ATSAM_MMENU_CANBUS_PB3_PB2
        bool "CAN bus (on PB3/PB2)"
        depends on HAVE_SAM_CANBUS
        select CANSERIAL
    config ATSAM_USBCANBUS
        bool "USB to CAN bus bridge"
        depends on HAVE_SAM_CANBUS
        select USBCANBUS
endchoice
choice
    prompt "CAN bus interface" if USBCANBUS
    config ATSAM_CMENU_CANBUS_PC12_PD12
        bool "CAN bus (on PC12/PD12)"
    config ATSAM_CMENU_CANBUS_PB3_PB2
        bool "CAN bus (on PB3/PB2)"
endchoice

config ATSAM_CANBUS_PC12_PD12
    bool
    default y if ATSAM_MMENU_CANBUS_PC12_PD12 || ATSAM_CMENU_CANBUS_PC12_PD12
config ATSAM_CANBUS_PB3_PB2
    bool
    default y if ATSAM_MMENU_CANBUS_PB3_PB2 || ATSAM_CMENU_CANBUS_PB3_PB2

endif
