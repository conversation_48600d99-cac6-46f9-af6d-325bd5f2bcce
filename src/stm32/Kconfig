# Kconfig settings for STM32 processors

if MACH_STM32

config STM32_SELECT
    bool
    default y
    select H<PERSON><PERSON>_<PERSON><PERSON>
    select HAVE_GPIO_ADC
    select HAVE_GPIO_I2C if !MACH_STM32F031
    select HAV<PERSON>_GPIO_SPI if !MACH_STM32F031
    select H<PERSON><PERSON>_GPIO_SDIO if MACH_STM32F4
    select HAVE_GPIO_HARD_PWM if MACH_STM32F070 || MACH_STM32F072 || MACH_STM32F1 || MACH_STM32F4 || MACH_STM32F7 || MACH_STM32G0 || MACH_STM32H7
    select HAVE_STRICT_TIMING
    select HAV<PERSON>_CHIPID
    select HAVE_STEPPER_OPTIMIZED_BOTH_EDGE if !MACH_STM32H7
    select HAVE_BOOTLOADER_REQUEST
    select HAVE_LIMITED_CODE_SIZE if FLASH_SIZE < 0x10000

config BOARD_DIRECTORY
    string
    default "stm32"


######################################################################
# Chip selection
######################################################################

choice
    prompt "Processor model"
    config MACH_STM32F103
        bool "STM32F103"
        select MACH_STM32F1
    config MACH_STM32F207
        bool "STM32F207"
        select MACH_STM32F2
    config MACH_STM32F401
        bool "STM32F401"
        select MACH_STM32F4
    config MACH_STM32F405
        bool "STM32F405"
        select MACH_STM32F4
        select MACH_STM32F4x5
    config MACH_STM32F407
        bool "STM32F407"
        select MACH_STM32F4
        select MACH_STM32F4x5
    config MACH_STM32F429
        bool "STM32F429"
        select MACH_STM32F4
        select MACH_STM32F4x5
    config MACH_STM32F446
        bool "STM32F446"
        select MACH_STM32F4
    config MACH_STM32F765
        bool "STM32F765"
        select MACH_STM32F7
    config MACH_STM32F031
        bool "STM32F031"
        select MACH_STM32F0
    config MACH_STM32F042
        bool "STM32F042"
        select MACH_STM32F0
        select MACH_STM32F0x2
    config MACH_STM32F070
        bool "STM32F070"
        select MACH_STM32F0
    config MACH_STM32F072
        bool "STM32F072"
        select MACH_STM32F0
        select MACH_STM32F0x2
    config MACH_STM32G070
        bool "STM32G070"
        select MACH_STM32G0
        select MACH_STM32G07x
    config MACH_STM32G071
        bool "STM32G071"
        select MACH_STM32G0
        select MACH_STM32G07x
    config MACH_STM32G0B0
        bool "STM32G0B0"
        select MACH_STM32G0
        select MACH_STM32G0Bx
    config MACH_STM32G0B1
        bool "STM32G0B1"
        select MACH_STM32G0
        select MACH_STM32G0Bx
    config MACH_STM32G431
        bool "STM32G431"
        select MACH_STM32G4
    config MACH_STM32G474
        bool "STM32G474"
        select MACH_STM32G4
    config MACH_STM32H723
        bool "STM32H723"
        select MACH_STM32H7
    config MACH_STM32H743
        bool "STM32H743"
        select MACH_STM32H7
    config MACH_STM32H750
        bool "STM32H750"
        select MACH_STM32H7
    config MACH_STM32L412
        bool "STM32L412"
        select MACH_STM32L4
    config MACH_N32G452
        bool "Nation N32G452"
        select MACH_N32G45x
        select MACH_STM32F1
    config MACH_N32G455
        bool "Nation N32G455"
        select MACH_N32G45x
        select MACH_STM32F1
endchoice

config MACH_STM32F103x6
    depends on LOW_LEVEL_OPTIONS && MACH_STM32F103
    bool "Only 10KiB of RAM (for rare stm32f103x6 variant)"

config MACH_STM32F070x6
    depends on LOW_LEVEL_OPTIONS && MACH_STM32F070
    bool "Only 6KiB of RAM (for rare stm32f070x6 variant)"

config MACH_STM32F0
    bool
config MACH_STM32F1
    bool
config MACH_STM32F2
    bool
config MACH_STM32F4
    bool
config MACH_STM32F7
    bool
config MACH_STM32G0
    bool
config MACH_STM32G07x
    bool
config MACH_STM32G0Bx
    bool
config MACH_STM32G4
    bool
config MACH_STM32H7
    bool
config MACH_STM32F0x2 # F042, F072 series
    bool
config MACH_STM32F4x5 # F405, F407, F429 series
    bool
config MACH_STM32L4
    bool
config MACH_N32G45x
    bool
config HAVE_STM32_USBFS
    bool
    default y if MACH_STM32F0x2 || MACH_STM32G0Bx || MACH_STM32L4 || MACH_STM32G4
    default y if (MACH_STM32F1 || MACH_STM32F070) && !STM32_CLOCK_REF_INTERNAL
config HAVE_STM32_USBOTG
    bool
    default y if MACH_STM32F2 || MACH_STM32F4 || MACH_STM32F7 || MACH_STM32H7
config HAVE_STM32_CANBUS
    bool
    default y if MACH_STM32F1 || MACH_STM32F2 || MACH_STM32F4x5 || MACH_STM32F446 || MACH_STM32F0x2
config HAVE_STM32_FDCANBUS
    bool
    default y if MACH_STM32G0B1 || MACH_STM32H7 || MACH_STM32G4
config HAVE_STM32_USBCANBUS
    bool
    depends on HAVE_STM32_USBFS || HAVE_STM32_USBOTG
    depends on HAVE_STM32_CANBUS || HAVE_STM32_FDCANBUS
    depends on !MACH_STM32F1
    default y

config MCU
    string
    default "stm32f031x6" if MACH_STM32F031
    default "stm32f042x6" if MACH_STM32F042
    default "stm32f070xb" if MACH_STM32F070
    default "stm32f072xb" if MACH_STM32F072
    default "stm32f103xe" if MACH_STM32F103
    default "stm32f207xx" if MACH_STM32F207
    default "stm32f401xc" if MACH_STM32F401
    default "stm32f405xx" if MACH_STM32F405
    default "stm32f407xx" if MACH_STM32F407
    default "stm32f429xx" if MACH_STM32F429
    default "stm32f446xx" if MACH_STM32F446
    default "stm32f765xx" if MACH_STM32F765
    default "stm32g070xx" if MACH_STM32G070
    default "stm32g071xx" if MACH_STM32G071
    default "stm32g0b0xx" if MACH_STM32G0B0
    default "stm32g0b1xx" if MACH_STM32G0B1
    default "stm32g431xx" if MACH_STM32G431
    default "stm32g474xx" if MACH_STM32G474
    default "stm32h723xx" if MACH_STM32H723
    default "stm32h743xx" if MACH_STM32H743
    default "stm32h750xx" if MACH_STM32H750
    default "stm32l412xx" if MACH_STM32L412
    default "stm32f103xe" if MACH_N32G45x

config CLOCK_FREQ
    int
    default 48000000 if MACH_STM32F0
    default 64000000 if MACH_STM32F103 && STM32_CLOCK_REF_INTERNAL
    default 72000000 if MACH_STM32F103
    default 120000000 if MACH_STM32F207
    default 84000000 if MACH_STM32F401
    default 168000000 if MACH_STM32F4x5
    default 180000000 if MACH_STM32F446
    default 216000000 if MACH_STM32F765
    default 64000000 if MACH_STM32G0
    default 170000000 if MACH_STM32G431
    default 170000000 if MACH_STM32G474
    default 520000000 if MACH_STM32H723
    default 400000000 if MACH_STM32H743 || MACH_STM32H750
    default 80000000 if MACH_STM32L412
    default 64000000 if MACH_N32G45x && STM32_CLOCK_REF_INTERNAL
    default 128000000 if MACH_N32G45x

config FLASH_SIZE
    hex
    default 0x8000 if MACH_STM32F031 || MACH_STM32F042 || MACH_STM32F070x6
    default 0x20000 if (MACH_STM32F070 || MACH_STM32F072) && !MACH_STM32F070x6
    default 0x10000 if MACH_STM32F103 || MACH_STM32L412 # Flash size of stm32f103x8 (64KiB)
    default 0x40000 if MACH_STM32F2 || MACH_STM32F401 || MACH_STM32H723
    default 0x80000 if MACH_STM32F4x5 || MACH_STM32F446
    default 0x20000 if MACH_STM32G0 || MACH_STM32G431
    default 0x40000 if MACH_STM32G474
    default 0x20000 if MACH_STM32H750
    default 0x200000 if MACH_STM32H743 || MACH_STM32F765
    default 0x20000 if MACH_N32G45x

config FLASH_BOOT_ADDRESS
    hex
    default 0x8000000

config RAM_START
    hex
    default 0x20000000

config RAM_SIZE
    hex
    default 0x1000 if MACH_STM32F031
    default 0x1800 if MACH_STM32F042
    default 0x1800 if MACH_STM32F070x6
    default 0x4000 if (MACH_STM32F070 || MACH_STM32F072) && !MACH_STM32F070x6
    default 0x2800 if MACH_STM32F103x6
    default 0x5000 if MACH_STM32F103 && !MACH_STM32F103x6 # Ram size of stm32f103x8
    default 0x8000 if MACH_STM32G431
    default 0x20000 if MACH_STM32G474
    default 0xa000 if MACH_STM32L412
    default 0x20000 if MACH_STM32F207
    default 0x10000 if MACH_STM32F401
    default 0x20000 if MACH_STM32F4x5 || MACH_STM32F446
    default 0x80000 if MACH_STM32F765
    default 0x9000 if MACH_STM32G07x
    default 0x24000 if MACH_STM32G0Bx
    default 0x20000 if MACH_STM32H7
    default 0x10000 if MACH_N32G45x

config STACK_SIZE
    int
    default 512

config STM32F103GD_DISABLE_SWD
    bool "Disable SWD at startup (for GigaDevice stm32f103 clones)"
    depends on MACH_STM32F103 && LOW_LEVEL_OPTIONS
    default n
    help
        The GigaDevice clone of the STM32F103 may not be able to
        reliably disable SWD at run-time. This can prevent the PA13
        and PA14 pins from being available. Selecting this option
        disables SWD at startup and thus makes these pins available.

config STM32_DFU_ROM_ADDRESS
    hex
    default 0 if !USB
    default 0x1fffc400 if MACH_STM32F042
    default 0x1fffc800 if MACH_STM32F072
    default 0x1fff0000 if MACH_STM32F4 || MACH_STM32F7 || MACH_STM32G0 || MACH_STM32G4 || MACH_STM32L4
    default 0x1ff09800 if MACH_STM32H7
    default 0


######################################################################
# Bootloader
######################################################################

choice
    prompt "Bootloader offset"
    config STM32_FLASH_START_2000
        bool "8KiB bootloader" if MACH_STM32F1 || MACH_STM32F070 || MACH_STM32G0 || MACH_STM32G4 || MACH_STM32F0x2
    config STM32_FLASH_START_5000
        bool "20KiB bootloader" if MACH_STM32F103
    config STM32_FLASH_START_7000
        bool "28KiB bootloader" if MACH_STM32F1
    config STM32_FLASH_START_8000
        bool "32KiB bootloader" if MACH_STM32F1 || MACH_STM32F2 || MACH_STM32F4 || MACH_STM32F7
    config STM32_FLASH_START_8800
        bool "34KiB bootloader" if MACH_STM32F103
    config STM32_FLASH_START_20200
        bool "128KiB bootloader with 512 byte offset" if MACH_STM32F4x5
    config STM32_FLASH_START_9000
        bool "36KiB bootloader" if MACH_STM32F1
    config STM32_FLASH_START_C000
        bool "48KiB bootloader" if MACH_STM32F4x5 || MACH_STM32F401
    config STM32_FLASH_START_10000
        bool "64KiB bootloader" if MACH_STM32F103 || MACH_STM32F4

    config STM32_FLASH_START_800
        bool "2KiB bootloader" if MACH_STM32F103
    config STM32_FLASH_START_1000
        bool "4KiB bootloader" if MACH_STM32F1 || MACH_STM32F0
    config STM32_FLASH_START_4000
        bool "16KiB bootloader" if MACH_STM32F207 || MACH_STM32F401 || MACH_STM32F4x5 || MACH_STM32F103 || MACH_STM32F072
    config STM32_FLASH_START_20000
        bool "128KiB bootloader" if MACH_STM32H743 || MACH_STM32H723 || MACH_STM32F7

    config STM32_FLASH_START_0000
        bool "No bootloader"
endchoice
config FLASH_APPLICATION_ADDRESS
    hex
    default 0x8000800 if STM32_FLASH_START_800
    default 0x8001000 if STM32_FLASH_START_1000
    default 0x8002000 if STM32_FLASH_START_2000
    default 0x8004000 if STM32_FLASH_START_4000
    default 0x8005000 if STM32_FLASH_START_5000
    default 0x8007000 if STM32_FLASH_START_7000
    default 0x8008000 if STM32_FLASH_START_8000
    default 0x8008800 if STM32_FLASH_START_8800
    default 0x8009000 if STM32_FLASH_START_9000
    default 0x800C000 if STM32_FLASH_START_C000
    default 0x8010000 if STM32_FLASH_START_10000
    default 0x8020000 if STM32_FLASH_START_20000
    default 0x8020200 if STM32_FLASH_START_20200
    default 0x8000000

config ARMCM_RAM_VECTORTABLE
    bool
    default y if MACH_STM32F0 && FLASH_APPLICATION_ADDRESS != 0x8000000
    default n


######################################################################
# Clock
######################################################################

choice
    prompt "Clock Reference" if LOW_LEVEL_OPTIONS
    config STM32_CLOCK_REF_8M
        bool "8 MHz crystal"
    config STM32_CLOCK_REF_12M
        bool "12 MHz crystal"
    config STM32_CLOCK_REF_16M
        bool "16 MHz crystal"
    config STM32_CLOCK_REF_20M
        bool "20 MHz crystal"
    config STM32_CLOCK_REF_24M
        bool "24 MHz crystal"
    config STM32_CLOCK_REF_25M
        bool "25 MHz crystal"
    config STM32_CLOCK_REF_INTERNAL
        bool "Internal clock"
endchoice
config CLOCK_REF_FREQ
    int
    default 25000000 if STM32_CLOCK_REF_25M
    default 24000000 if STM32_CLOCK_REF_24M
    default 20000000 if STM32_CLOCK_REF_20M
    default 16000000 if STM32_CLOCK_REF_16M
    default 12000000 if STM32_CLOCK_REF_12M
    default 1 if STM32_CLOCK_REF_INTERNAL
    default 8000000

config STM32F0_TRIM
    int "Internal clock trim override" if LOW_LEVEL_OPTIONS && MACH_STM32F0 && STM32_CLOCK_REF_INTERNAL && !USBSERIAL
    default 16
    help
        Specify the internal clock trim value. Setting this can be
        useful if the factory default internal clock is not accurate.
        Default is 16 (use factory default). Each increment increases
        the clock rate by ~240KHz.


######################################################################
# Communication inteface
######################################################################

choice
    prompt "Communication interface"
    config STM32_USB_PA11_PA12
        bool "USB (on PA11/PA12)" if HAVE_STM32_USBFS || HAVE_STM32_USBOTG
        select USBSERIAL
    config STM32_USB_PA11_PA12_REMAP
        bool "USB (on PA9/PA10)"
        depends on MACH_STM32F042 || MACH_STM32F070x6
        select USBSERIAL
    config STM32_USB_PB14_PB15
        bool "USB (on PB14/PB15)"
        depends on MACH_STM32H743 || MACH_STM32H750
        select USBSERIAL
    config STM32_SERIAL_USART1
        bool "Serial (on USART1 PA10/PA9)"
        select SERIAL
    config STM32_SERIAL_USART1_ALT_PB7_PB6
        bool "Serial (on USART1 PB7/PB6)" if LOW_LEVEL_OPTIONS
        select SERIAL
    config STM32_SERIAL_USART2
        bool "Serial (on USART2 PA3/PA2)" if LOW_LEVEL_OPTIONS
        select SERIAL
    config STM32_SERIAL_USART2_ALT_PA15_PA14
        bool "Serial (on USART2 PA15/PA14)" if LOW_LEVEL_OPTIONS && (MACH_STM32F0 || MACH_STM32G4)
        select SERIAL
    config STM32_SERIAL_USART2_ALT_PB4_PB3
        bool "Serial (on USART2 PB4/PB3)" if LOW_LEVEL_OPTIONS && MACH_STM32G4
        select SERIAL
    config STM32_SERIAL_USART2_ALT_PD6_PD5
        bool "Serial (on USART2 PD6/PD5)" if LOW_LEVEL_OPTIONS && !MACH_STM32F0
        select SERIAL
    config STM32_SERIAL_USART3
        bool "Serial (on USART3 PB11/PB10)" if LOW_LEVEL_OPTIONS
        depends on !MACH_STM32F0 && !MACH_STM32F401
        select SERIAL
    config STM32_SERIAL_USART3_ALT_PD9_PD8
        bool "Serial (on USART3 PD9/PD8)" if LOW_LEVEL_OPTIONS
        depends on !MACH_STM32F0 && !MACH_STM32F401
        select SERIAL
    config STM32_SERIAL_USART3_ALT_PC11_PC10
        bool "Serial (on USART3 PC11/PC10)" if LOW_LEVEL_OPTIONS
        depends on MACH_STM32G474
        select SERIAL
    config STM32_SERIAL_UART4
        bool "Serial (on UART4 PA0/PA1)"
        depends on MACH_STM32H7
        select SERIAL
    config STM32_SERIAL_USART5
        bool "Serial (on USART5 PD2/PD3)" if LOW_LEVEL_OPTIONS
        depends on MACH_STM32G0Bx
        select SERIAL
    config STM32_SERIAL_USART6
        bool "Serial (on USART6 PA12/PA11)" if LOW_LEVEL_OPTIONS && MACH_STM32F401
        select SERIAL
    config STM32_SERIAL_USART6_ALT_PC7_PC6
        bool "Serial (on USART6 PC7/PC6)" if LOW_LEVEL_OPTIONS && MACH_STM32F401
        select SERIAL
    config STM32_CANBUS_PA11_PA12
        bool "CAN bus (on PA11/PA12)"
        depends on HAVE_STM32_CANBUS || HAVE_STM32_FDCANBUS
        select CANSERIAL
    config STM32_CANBUS_PA11_PA12_REMAP
        bool "CAN bus (on PA9/PA10)" if LOW_LEVEL_OPTIONS
        depends on MACH_STM32F042
        select CANSERIAL
    config STM32_CANBUS_PA11_PB9
        bool "CAN bus (on PA11/PB9)"
        depends on HAVE_STM32_CANBUS || HAVE_STM32_FDCANBUS
        select CANSERIAL
    config STM32_MMENU_CANBUS_PB8_PB9
        bool "CAN bus (on PB8/PB9)" if LOW_LEVEL_OPTIONS
        depends on HAVE_STM32_CANBUS || HAVE_STM32_FDCANBUS
        select CANSERIAL
    config STM32_MMENU_CANBUS_PI9_PH13
        bool "CAN bus (on PI9/PH13)" if LOW_LEVEL_OPTIONS
        depends on (MACH_STM32F4 && HAVE_STM32_CANBUS)
        select CANSERIAL
    config STM32_MMENU_CANBUS_PB5_PB6
        bool "CAN bus (on PB5/PB6)" if LOW_LEVEL_OPTIONS
        depends on (MACH_STM32F4 && HAVE_STM32_CANBUS) || MACH_STM32H7
        select CANSERIAL
    config STM32_MMENU_CANBUS_PB12_PB13
        bool "CAN bus (on PB12/PB13)" if LOW_LEVEL_OPTIONS
        depends on (MACH_STM32F4 && HAVE_STM32_CANBUS) || HAVE_STM32_FDCANBUS
        select CANSERIAL
    config STM32_MMENU_CANBUS_PD0_PD1
        bool "CAN bus (on PD0/PD1)" if LOW_LEVEL_OPTIONS
        depends on HAVE_STM32_CANBUS || HAVE_STM32_FDCANBUS
        select CANSERIAL
    config STM32_MMENU_CANBUS_PB0_PB1
        bool "CAN bus (on PB0/PB1)"
        depends on HAVE_STM32_FDCANBUS
        select CANSERIAL
    config STM32_MMENU_CANBUS_PD12_PD13
        bool "CAN bus (on PD12/PD13)"
        depends on HAVE_STM32_FDCANBUS
        select CANSERIAL
    config STM32_MMENU_CANBUS_PC2_PC3
        bool "CAN bus (on PC2/PC3)"
        depends on HAVE_STM32_FDCANBUS
        select CANSERIAL
    config STM32_MMENU_CANBUS_PH13_PH14
        bool "CAN bus (on PH13/PH14)" if MACH_STM32H743
        depends on HAVE_STM32_FDCANBUS
        select CANSERIAL
    config STM32_USBCANBUS_PA11_PA12
        bool "USB to CAN bus bridge (USB on PA11/PA12)"
        depends on HAVE_STM32_USBCANBUS
        select USBCANBUS
endchoice
choice
    prompt "CAN bus interface" if USBCANBUS
    config STM32_CMENU_CANBUS_PB8_PB9
        bool "CAN bus (on PB8/PB9)"
    config STM32_CMENU_CANBUS_PI9_PH13
        bool "CAN bus (on PI9/PH13)"
        depends on (MACH_STM32F4 && HAVE_STM32_CANBUS)
    config STM32_CMENU_CANBUS_PB5_PB6
        bool "CAN bus (on PB5/PB6)"
        depends on (MACH_STM32F4 && HAVE_STM32_CANBUS) || MACH_STM32H7 || MACH_STM32G0B1
    config STM32_CMENU_CANBUS_PB12_PB13
        bool "CAN bus (on PB12/PB13)"
        depends on (MACH_STM32F4 && HAVE_STM32_CANBUS) || HAVE_STM32_FDCANBUS
    config STM32_CMENU_CANBUS_PD0_PD1
        bool "CAN bus (on PD0/PD1)"
        depends on HAVE_STM32_CANBUS || HAVE_STM32_FDCANBUS
    config STM32_CMENU_CANBUS_PB0_PB1
        bool "CAN bus (on PB0/PB1)"
        depends on HAVE_STM32_FDCANBUS
    config STM32_CMENU_CANBUS_PD12_PD13
        bool "CAN bus (on PD12/PD13)"
        depends on HAVE_STM32_FDCANBUS
    config STM32_CMENU_CANBUS_PC2_PC3
        bool "CAN bus (on PC2/PC3)"
        depends on HAVE_STM32_FDCANBUS
    config STM32_CMENU_CANBUS_PH13_PH14
        bool "CAN bus (on PH13/PH14)" if MACH_STM32H743
        depends on HAVE_STM32_FDCANBUS
endchoice


config STM32_CANBUS_PB8_PB9
    bool
    default y if STM32_MMENU_CANBUS_PB8_PB9 || STM32_CMENU_CANBUS_PB8_PB9
config STM32_CANBUS_PI9_PH13
    bool
    default y if STM32_MMENU_CANBUS_PI9_PH13 || STM32_CMENU_CANBUS_PI9_PH13
config STM32_CANBUS_PB5_PB6
    bool
    default y if STM32_MMENU_CANBUS_PB5_PB6 || STM32_CMENU_CANBUS_PB5_PB6
config STM32_CANBUS_PB12_PB13
    bool
    default y if STM32_MMENU_CANBUS_PB12_PB13 || STM32_CMENU_CANBUS_PB12_PB13
config STM32_CANBUS_PD0_PD1
    bool
    default y if STM32_MMENU_CANBUS_PD0_PD1 || STM32_CMENU_CANBUS_PD0_PD1
config STM32_CANBUS_PB0_PB1
    bool
    default y if STM32_MMENU_CANBUS_PB0_PB1 || STM32_CMENU_CANBUS_PB0_PB1
config STM32_CANBUS_PD12_PD13
    bool
    default y if STM32_MMENU_CANBUS_PD12_PD13 || STM32_CMENU_CANBUS_PD12_PD13
config STM32_CANBUS_PC2_PC3
    bool
    default y if STM32_MMENU_CANBUS_PC2_PC3 || STM32_CMENU_CANBUS_PC2_PC3
config STM32_CANBUS_PH13_PH14
    bool
    default y if STM32_MMENU_CANBUS_PH13_PH14 || STM32_CMENU_CANBUS_PH13_PH14

endif
