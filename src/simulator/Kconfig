# Kconfig settings for compiling and running the firmware on the host
# processor for simulation purposes.

if MACH_SIMU

config BOARD_DIRECTORY
    string
    default "simulator"

config SIMULATOR_SELECT
    bool
    default y
    select HAVE_GPIO
    select HAVE_GPIO_ADC
    select HAV<PERSON>_GPIO_SPI
    select HAV<PERSON>_GPIO_HARD_PWM

config SERIAL
    default y

config CLOCK_FREQ
    int
    default 20000000

endif
