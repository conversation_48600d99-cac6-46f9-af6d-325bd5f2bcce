# This file defines the default layout of the printer's menu.

# It is not necessary to edit this file to change the menu. Instead,
# one may override any of the sections defined here by defining a
# section with the same name in the main printer.cfg config file.

### DEFAULT MENU ###
# Main
#   + Tune
#       + Speed: 000%
#       + Flow: 000%
#       + Offset Z:00.00
#   + OctoPrint
#       + Pause printing
#       + Resume printing
#       + Abort printing
#   + SD Card
#       + Start printing
#       + Resume printing
#       + Pause printing
#       + Cancel printing
#       + ... (files)
#   + Control
#       + Home All
#       + Home Z
#       + Home X/Y
#       + Z Tilt
#       + Quad Gantry Lvl
#       + Bed Mesh
#       + Steppers off
#       + Fan: OFF
#       + Fan speed: 000%
#       + Lights: OFF
#       + Lights: 000%
#       + Move 10mm
#           + Move X:000.0
#           + Move Y:000.0
#           + Move Z:000.0
#           + Move E:+000.0
#       + Move 1mm
#           + Move X:000.0
#           + Move Y:000.0
#           + Move Z:000.0
#           + Move E:+000.0
#       + Move 0.1mm
#           + Move X:000.0
#           + Move Y:000.0
#           + Move Z:000.0
#           + Move E:+000.0
#   + Temperature
#       + Ex0:000 (0000)
#       + Ex1:000 (0000)
#       + Bed:000 (0000)
#       + Preheat PLA
#           + Preheat all
#           + Preheat hotend
#           + Preheat hotbed
#       + Preheat ABS
#           + Preheat all
#           + Preheat hotend
#           + Preheat hotbed
#       + Cooldown
#           + Cooldown all
#           + Cooldown hotend
#           + Cooldown hotbed
#   + Filament
#       + Ex0:000 (0000)
#       + Load Fil. fast
#       + Load Fil. slow
#       + Unload Fil.fast
#       + Unload Fil.slow
#       + Feed: 000.0
#   + Setup
#       + Save config
#       + Restart
#           + Restart host
#           + Restart FW
#       + PID tuning
#           + Tune Hotend PID
#           + Tune Hotbed PID
#       + Calibration
#           + Delta cal. auto
#           + Delta cal. man
#               + Start probing
#               + Move Z: 000.00
#               + Test Z: ++
#               + Accept
#               + Abort
#           + Bed probe
#       + Dump parameters

### menu main ###
[menu __main]
type: list
name: Main

### menu tune ###
[menu __main __tune]
type: list
enable: {printer.idle_timeout.state == "Printing"}
name: Tune

[menu __main __tune __speed]
type: input
name: Speed: {'%3d' % (menu.input*100)}%
input: {printer.gcode_move.speed_factor}
input_min: 0.01
input_max: 5
input_step: 0.01
realtime: True
gcode:
    M220 S{'%d' % (menu.input*100)}

[menu __main __tune __flow]
type: input
name: Flow: {'%3d' % (menu.input*100)}%
input: {printer.gcode_move.extrude_factor}
input_min: 0.01
input_max: 2
input_step: 0.01
realtime: True
gcode:
    M221 S{'%d' % (menu.input*100)}

[menu __main __tune __offsetz]
type: input
name: Offset Z:{'%05.3f' % menu.input}
input: {printer.gcode_move.homing_origin.z}
input_min: -5
input_max: 5
input_step: 0.005
realtime: True
gcode:
    SET_GCODE_OFFSET Z={'%.3f' % menu.input} MOVE=1


### menu octoprint ###
[menu __main __octoprint]
type: list
name: OctoPrint

[menu __main __octoprint __pause]
type: command
enable: {printer.idle_timeout.state == "Printing"}
name: Pause printing
gcode:
    {action_respond_info('action:pause')}

[menu __main __octoprint __resume]
type: command
enable: {not printer.idle_timeout.state == "Printing"}
name: Resume printing
gcode:
    {action_respond_info('action:resume')}

[menu __main __octoprint __abort]
type: command
enable: {printer.idle_timeout.state == "Printing"}
name: Abort printing
gcode:
    {action_respond_info('action:cancel')}

### menu virtual sdcard ###
[menu __main __sdcard]
type: vsdlist
enable: {('virtual_sdcard' in printer)}
name: SD Card

[menu __main __sdcard __start]
type: command
enable: {('virtual_sdcard' in printer) and printer.virtual_sdcard.file_path and not printer.virtual_sdcard.is_active}
name: Start printing
gcode: M24

[menu __main __sdcard __resume]
type: command
enable: {('virtual_sdcard' in printer) and printer.print_stats.state == "paused"}
name: Resume printing
gcode:
    {% if "pause_resume" in printer %}
        RESUME
    {% else %}
        M24
    {% endif %}

[menu __main __sdcard __pause]
type: command
enable: {('virtual_sdcard' in printer) and printer.print_stats.state == "printing"}
name: Pause printing
gcode:
    {% if "pause_resume" in printer %}
        PAUSE
    {% else %}
        M25
    {% endif %}

[menu __main __sdcard __cancel]
type: command
enable: {('virtual_sdcard' in printer) and (printer.print_stats.state == "printing" or printer.print_stats.state == "paused")}
name: Cancel printing
gcode:
    {% if 'pause_resume' in printer %}
        CANCEL_PRINT
    {% else %}
        M25
        M27
        M26 S0
        TURN_OFF_HEATERS
        {% if printer.toolhead.position.z <= printer.toolhead.axis_maximum.z - 5 %}
            G91
            G0 Z5 F1000
            G90
        {% endif %}
    {% endif %}

### menu control ###
[menu __main __control]
type: list
name: Control

[menu __main __control __home]
type: command
enable: {not printer.idle_timeout.state == "Printing"}
name: Home All
gcode: G28

[menu __main __control __homez]
type: command
enable: {not printer.idle_timeout.state == "Printing"}
name: Home Z
gcode: G28 Z

[menu __main __control __homexy]
type: command
enable: {not printer.idle_timeout.state == "Printing"}
name: Home X/Y
gcode: G28 X Y

[menu __main __control __z_tilt]
type: command
enable: {not printer.idle_timeout.state == "Printing" and ('z_tilt' in printer)}
name: Z Tilt
gcode: Z_TILT_ADJUST

[menu __main __control __quad_gantry_level]
type: command
enable: {not printer.idle_timeout.state == "Printing" and ('quad_gantry_level' in printer)}
name: Quad Gantry Lvl
gcode: QUAD_GANTRY_LEVEL

[menu __main __control __bed_mesh]
type: command
enable: {not printer.idle_timeout.state == "Printing" and ('bed_mesh' in printer)}
name: Bed Mesh
gcode: BED_MESH_CALIBRATE

[menu __main __control __disable]
type: command
name: Steppers off
gcode:
    M84
    M18

[menu __main __control __fanonoff]
type: input
enable: {'fan' in printer}
name: Fan: {'ON ' if menu.input else 'OFF'}
input: {printer.fan.speed}
input_min: 0
input_max: 1
input_step: 1
gcode:
    M106 S{255 if menu.input else 0}

[menu __main __control __fanspeed]
type: input
enable: {'fan' in printer}
name: Fan speed: {'%3d' % (menu.input*100)}%
input: {printer.fan.speed}
input_min: 0
input_max: 1
input_step: 0.01
gcode:
    M106 S{'%d' % (menu.input*255)}

[menu __main __control __caselightonoff]
type: input
enable: {'output_pin caselight' in printer}
name: Lights: {'ON ' if menu.input else 'OFF'}
input: {printer['output_pin caselight'].value}
input_min: 0
input_max: 1
input_step: 1
gcode:
    SET_PIN PIN=caselight VALUE={1 if menu.input else 0}

[menu __main __control __caselightpwm]
type: input
enable: {'output_pin caselight' in printer}
name: Lights: {'%3d' % (menu.input*100)}%
input: {printer['output_pin caselight'].value}
input_min: 0.0
input_max: 1.0
input_step: 0.01
gcode:
    SET_PIN PIN=caselight VALUE={menu.input}

### menu move 10mm ###
[menu __main __control __move_10mm]
type: list
enable: {not printer.idle_timeout.state == "Printing"}
name: Move 10mm

[menu __main __control __move_10mm __axis_x]
type: input
name: Move X:{'%05.1f' % menu.input}
input: {printer.gcode_move.gcode_position.x}
input_min: {printer.toolhead.axis_minimum.x}
input_max: {printer.toolhead.axis_maximum.x}
input_step: 10.0
gcode:
    SAVE_GCODE_STATE NAME=__move__axis
    G90
    G1 X{menu.input}
    RESTORE_GCODE_STATE NAME=__move__axis

[menu __main __control __move_10mm __axis_y]
type: input
name: Move Y:{'%05.1f' % menu.input}
input: {printer.gcode_move.gcode_position.y}
input_min: {printer.toolhead.axis_minimum.y}
input_max: {printer.toolhead.axis_maximum.y}
input_step: 10.0
gcode:
    SAVE_GCODE_STATE NAME=__move__axis
    G90
    G1 Y{menu.input}
    RESTORE_GCODE_STATE NAME=__move__axis

[menu __main __control __move_10mm __axis_z]
type: input
enable: {not printer.idle_timeout.state == "Printing"}
name: Move Z:{'%05.1f' % menu.input}
input: {printer.gcode_move.gcode_position.z}
input_min: 0
input_max: {printer.toolhead.axis_maximum.z}
input_step: 10.0
gcode:
    SAVE_GCODE_STATE NAME=__move__axis
    G90
    G1 Z{menu.input}
    RESTORE_GCODE_STATE NAME=__move__axis

[menu __main __control __move_10mm __axis_e]
type: input
enable: {not printer.idle_timeout.state == "Printing"}
name: Move E:{'%+06.1f' % menu.input}
input: 0
input_min: -{printer.configfile.config.extruder.max_extrude_only_distance|default(50)}
input_max: {printer.configfile.config.extruder.max_extrude_only_distance|default(50)}
input_step: 10.0
gcode:
    SAVE_GCODE_STATE NAME=__move__axis
    M83
    G1 E{menu.input} F240
    RESTORE_GCODE_STATE NAME=__move__axis

### menu move 1mm ###
[menu __main __control __move_1mm]
type: list
enable: {not printer.idle_timeout.state == "Printing"}
name: Move 1mm

[menu __main __control __move_1mm __axis_x]
type: input
name: Move X:{'%05.1f' % menu.input}
input: {printer.gcode_move.gcode_position.x}
input_min: {printer.toolhead.axis_minimum.x}
input_max: {printer.toolhead.axis_maximum.x}
input_step: 1.0
gcode:
    SAVE_GCODE_STATE NAME=__move__axis
    G90
    G1 X{menu.input}
    RESTORE_GCODE_STATE NAME=__move__axis

[menu __main __control __move_1mm __axis_y]
type: input
name: Move Y:{'%05.1f' % menu.input}
input: {printer.gcode_move.gcode_position.y}
input_min: {printer.toolhead.axis_minimum.y}
input_max: {printer.toolhead.axis_maximum.y}
input_step: 1.0
gcode:
    SAVE_GCODE_STATE NAME=__move__axis
    G90
    G1 Y{menu.input}
    RESTORE_GCODE_STATE NAME=__move__axis

[menu __main __control __move_1mm __axis_z]
type: input
enable: {not printer.idle_timeout.state == "Printing"}
name: Move Z:{'%05.1f' % menu.input}
input: {printer.gcode_move.gcode_position.z}
input_min: 0
input_max: {printer.toolhead.axis_maximum.z}
input_step: 1.0
gcode:
    SAVE_GCODE_STATE NAME=__move__axis
    G90
    G1 Z{menu.input}
    RESTORE_GCODE_STATE NAME=__move__axis

[menu __main __control __move_1mm __axis_e]
type: input
enable: {not printer.idle_timeout.state == "Printing"}
name: Move E:{'%+06.1f' % menu.input}
input: 0
input_min: -{printer.configfile.config.extruder.max_extrude_only_distance|default(50)}
input_max: {printer.configfile.config.extruder.max_extrude_only_distance|default(50)}
input_step: 1.0
gcode:
    SAVE_GCODE_STATE NAME=__move__axis
    M83
    G1 E{menu.input} F240
    RESTORE_GCODE_STATE NAME=__move__axis

### menu move 0.1mm ###
[menu __main __control __move_01mm]
type: list
enable: {not printer.idle_timeout.state == "Printing"}
name: Move 0.1mm

[menu __main __control __move_01mm __axis_x]
type: input
name: Move X:{'%05.1f' % menu.input}
input: {printer.gcode_move.gcode_position.x}
input_min: {printer.toolhead.axis_minimum.x}
input_max: {printer.toolhead.axis_maximum.x}
input_step: 0.1
gcode:
    SAVE_GCODE_STATE NAME=__move__axis
    G90
    G1 X{menu.input}
    RESTORE_GCODE_STATE NAME=__move__axis

[menu __main __control __move_01mm __axis_y]
type: input
name: Move Y:{'%05.1f' % menu.input}
input: {printer.gcode_move.gcode_position.y}
input_min: {printer.toolhead.axis_minimum.y}
input_max: {printer.toolhead.axis_maximum.y}
input_step: 0.1
gcode:
    SAVE_GCODE_STATE NAME=__move__axis
    G90
    G1 Y{menu.input}
    RESTORE_GCODE_STATE NAME=__move__axis

[menu __main __control __move_01mm __axis_z]
type: input
enable: {not printer.idle_timeout.state == "Printing"}
name: Move Z:{'%05.1f' % menu.input}
input: {printer.gcode_move.gcode_position.z}
input_min: 0
input_max: {printer.toolhead.axis_maximum.z}
input_step: 0.1
gcode:
    SAVE_GCODE_STATE NAME=__move__axis
    G90
    G1 Z{menu.input}
    RESTORE_GCODE_STATE NAME=__move__axis

[menu __main __control __move_01mm __axis_e]
type: input
enable: {not printer.idle_timeout.state == "Printing"}
name: Move E:{'%+06.1f' % menu.input}
input: 0
input_min: -{printer.configfile.config.extruder.max_extrude_only_distance|default(50)}
input_max: {printer.configfile.config.extruder.max_extrude_only_distance|default(50)}
input_step: 0.1
gcode:
    SAVE_GCODE_STATE NAME=__move__axis
    M83
    G1 E{menu.input} F240
    RESTORE_GCODE_STATE NAME=__move__axis

### menu temperature ###
[menu __main __temp]
type: list
name: Temperature

[menu __main __temp __hotend0_target]
type: input
enable: {('extruder' in printer) and ('extruder' in printer.heaters.available_heaters)}
name: {"Ex0:%3.0f (%4.0f)" % (menu.input, printer.extruder.temperature)}
input: {printer.extruder.target}
input_min: 0
input_max: {printer.configfile.config.extruder.max_temp}
input_step: 1
gcode: M104 T0 S{'%.0f' % menu.input}

[menu __main __temp __hotend1_target]
type: input
enable: {('extruder1' in printer) and ('extruder1' in printer.heaters.available_heaters)}
name: {"Ex1:%3.0f (%4.0f)" % (menu.input, printer.extruder1.temperature)}
input: {printer.extruder1.target}
input_min: 0
input_max: {printer.configfile.config.extruder1.max_temp}
input_step: 1
gcode: M104 T1 S{'%.0f' % menu.input}

[menu __main __temp __hotbed_target]
type: input
enable: {'heater_bed' in printer}
name: {"Bed:%3.0f (%4.0f)" % (menu.input, printer.heater_bed.temperature)}
input: {printer.heater_bed.target}
input_min: 0
input_max: {printer.configfile.config.heater_bed.max_temp}
input_step: 1
gcode: M140 S{'%.0f' % menu.input}

[menu __main __temp __preheat_pla]
type: list
name: Preheat PLA

[menu __main __temp __preheat_pla __all]
type: command
enable: {('extruder' in printer) and ('heater_bed' in printer)}
name: Preheat all
gcode:
    M140 S60
    M104 S200

[menu __main __temp __preheat_pla __hotend]
type: command
enable: {'extruder' in printer}
name: Preheat hotend
gcode: M104 S200

[menu __main __temp __preheat_pla __hotbed]
type: command
enable: {'heater_bed' in printer}
name: Preheat hotbed
gcode: M140 S60

[menu __main __temp __preheat_abs]
type: list
name: Preheat ABS

[menu __main __temp __preheat_abs __all]
type: command
enable: {('extruder' in printer) and ('heater_bed' in printer)}
name: Preheat all
gcode:
    M140 S110
    M104 S245

[menu __main __temp __preheat_abs __hotend]
type: command
enable: {'extruder' in printer}
name: Preheat hotend
gcode: M104 S245

[menu __main __temp __preheat_abs __hotbed]
type: command
enable: {'heater_bed' in printer}
name: Preheat hotbed
gcode: M140 S110

[menu __main __temp __cooldown]
type: list
name: Cooldown

[menu __main __temp __cooldown __all]
type: command
enable: {('extruder' in printer) and ('heater_bed' in printer)}
name: Cooldown all
gcode:
    M104 S0
    M140 S0

[menu __main __temp __cooldown __hotend]
type: command
enable: {'extruder' in printer}
name: Cooldown hotend
gcode: M104 S0

[menu __main __temp __cooldown __hotbed]
type: command
enable: {'heater_bed' in printer}
name: Cooldown hotbed
gcode: M140 S0

### menu filament ###

[menu __main __filament]
type: list
name: Filament

[menu __main __filament __hotend0_target]
type: input
enable: {'extruder' in printer}
name: {"Ex0:%3.0f (%4.0f)" % (menu.input, printer.extruder.temperature)}
input: {printer.extruder.target}
input_min: 0
input_max: {printer.configfile.config.extruder.max_temp}
input_step: 1
gcode: M104 T0 S{'%.0f' % menu.input}

[menu __main __filament __loadf]
type: command
name: Load Fil. fast
gcode:
    SAVE_GCODE_STATE NAME=__filament__load
    M83
    G1 E50 F960
    RESTORE_GCODE_STATE NAME=__filament__load

[menu __main __filament __loads]
type: command
name: Load Fil. slow
gcode:
    SAVE_GCODE_STATE NAME=__filament__load
    M83
    G1 E50 F240
    RESTORE_GCODE_STATE NAME=__filament__load

[menu __main __filament __unloadf]
type: command
name: Unload Fil.fast
gcode:
    SAVE_GCODE_STATE NAME=__filament__load
    M83
    G1 E-50 F960
    RESTORE_GCODE_STATE NAME=__filament__load

[menu __main __filament __unloads]
type: command
name: Unload Fil.slow
gcode:
    SAVE_GCODE_STATE NAME=__filament__load
    M83
    G1 E-50 F240
    RESTORE_GCODE_STATE NAME=__filament__load

[menu __main __filament __feed]
type: input
name: Feed: {'%.1f' % menu.input}
input: 5
input_step: 0.1
gcode:
    SAVE_GCODE_STATE NAME=__filament__load
    M83
    G1 E{'%.1f' % menu.input} F60
    RESTORE_GCODE_STATE NAME=__filament__load

### menu setup ###
[menu __main __setup]
type: list
enable: {not printer.idle_timeout.state == "Printing"}
name: Setup

[menu __main __setup __save_config]
type: command
name: Save config
gcode: SAVE_CONFIG

[menu __main __setup __restart]
type: list
name: Restart

[menu __main __setup __restart __host_restart]
type: command
enable: {not printer.idle_timeout.state == "Printing"}
name: Restart host
gcode: RESTART

[menu __main __setup __restart __firmware_restart]
type: command
enable: {not printer.idle_timeout.state == "Printing"}
name: Restart FW
gcode: FIRMWARE_RESTART

[menu __main __setup __tuning]
type: list
name: PID tuning

[menu __main __setup __tuning __hotend_pid_tuning]
type: command
enable: {(not printer.idle_timeout.state == "Printing") and ('extruder' in printer)}
name: Tune Hotend PID
gcode: PID_CALIBRATE HEATER=extruder TARGET=210 WRITE_FILE=1

[menu __main __setup __tuning __hotbed_pid_tuning]
type: command
enable: {(not printer.idle_timeout.state == "Printing") and ('heater_bed' in printer)}
name: Tune Hotbed PID
gcode: PID_CALIBRATE HEATER=heater_bed TARGET=60 WRITE_FILE=1

[menu __main __setup __calib]
type: list
name: Calibration

[menu __main __setup __calib __delta_calib_auto]
type: command
enable: {(not printer.idle_timeout.state == "Printing") and ('delta_calibrate' in printer)}
name: Delta cal. auto
gcode:
    G28
    DELTA_CALIBRATE

[menu __main __setup __calib __delta_calib_man]
type: list
enable: {(not printer.idle_timeout.state == "Printing") and ('delta_calibrate' in printer)}
name: Delta cal. man

[menu __main __setup __calib __bedprobe]
type: command
enable: {(not printer.idle_timeout.state == "Printing") and ('probe' in printer)}
name: Bed probe
gcode: PROBE

[menu __main __setup __calib __delta_calib_man __start]
type: command
name: Start probing
gcode:
    G28
    DELTA_CALIBRATE METHOD=manual

[menu __main __setup __calib __delta_calib_man __move_z]
type: input
name: Move Z: {'%03.2f' % menu.input}
input: {printer.gcode_move.gcode_position.z}
input_step: 1
realtime: True
gcode:
    {%- if menu.event == 'change' -%}
        G1 Z{'%.2f' % menu.input}
    {%- elif menu.event == 'long_click' -%}
        G1 Z{'%.2f' % menu.input}
        SAVE_GCODE_STATE NAME=__move__axis
        G91
        G1 Z2
        G1 Z-2
        RESTORE_GCODE_STATE NAME=__move__axis
    {%- endif -%}

[menu __main __setup __calib __delta_calib_man __test_z]
type: input
name: Test Z: {['++','+','+.01','+.05','+.1','+.5','-.5','-.1','-.05','-.01','-','--'][menu.input|int]}
input: 6
input_min: 0
input_max: 11
input_step: 1
gcode:
    {%- if menu.event == 'long_click' -%}
        TESTZ Z={['++','+','+.01','+.05','+.1','+.5','-.5','-.1','-.05','-.01','-','--'][menu.input|int]}
    {%- endif -%}

[menu __main __setup __calib __delta_calib_man __accept]
type: command
name: Accept
gcode: ACCEPT

[menu __main __setup __calib __delta_calib_man __abort]
type: command
name: Abort
gcode: ABORT

[menu __main __setup __dump]
type: command
name: Dump parameters
gcode:
   {% for name1 in printer %}
      {% for name2 in printer[name1] %}
         { action_respond_info("printer['%s'].%s = %s"
                               % (name1, name2, printer[name1][name2])) }
      {% else %}
         { action_respond_info("printer['%s'] = %s" % (name1, printer[name1])) }
      {% endfor %}
   {% endfor %}
