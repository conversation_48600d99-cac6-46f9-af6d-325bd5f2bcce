# This file defines the default layout of the printer's lcd display.

# It is not necessary to edit this file to change the display.
# Instead, one may override any of the sections defined here by
# defining a section with the same name in the main printer.cfg config
# file.


######################################################################
# Helper macros for showing common screen values
######################################################################

[display_template _heater_temperature]
param_heater_name: "extruder"
text:
  {% if param_heater_name in printer %}
    {% set heater = printer[param_heater_name] %}
    # Show glyph
    {% if param_heater_name == "heater_bed" %}
      {% if heater.target %}
        {% set frame = (printer.toolhead.estimated_print_time|int % 2) + 1 %}
        ~bed_heat{frame}~
      {% else %}
        ~bed~
      {% endif %}
    {% else %}
      ~extruder~
    {% endif %}
    # Show temperature
    { "%3.0f" % (heater.temperature,) }
    # Optionally show target
    {% if heater.target and (heater.temperature - heater.target)|abs > 2 %}
      ~right_arrow~
      { "%0.0f" % (heater.target,) }
    {% endif %}
    ~degrees~
  {% endif %}

[display_template _fan_speed]
text:
  {% if 'fan' in printer %}
    {% set speed = printer.fan.speed %}
    {% if speed %}
      {% set frame = (printer.toolhead.estimated_print_time|int % 2) + 1 %}
      ~fan{frame}~
    {% else %}
      ~fan1~
    {% endif %}
    { "{:>4.0%}".format(speed) }
  {% endif %}

[display_template _printing_time]
text:
  {% set ptime = printer.idle_timeout.printing_time %}
  { "%02d:%02d" % (ptime // (60 * 60), (ptime // 60) % 60) }

[display_template _print_status]
text:
  {% if printer.display_status.message %}
    { printer.display_status.message }
  {% elif printer.idle_timeout.printing_time %}
    {% set pos = printer.toolhead.position %}
    { "X%-4.0fY%-4.0fZ%-5.2f" % (pos.x, pos.y, pos.z) }
  {% else %}
    Ready
  {% endif %}


######################################################################
# Default 16x4 display
######################################################################

[display_data _default_16x4 extruder]
position: 0, 0
text:
  {% set active_extruder = printer.toolhead.extruder %}
  { render("_heater_temperature", param_heater_name=active_extruder) }

[display_data _default_16x4 fan]
position: 0, 10
text: { render("_fan_speed") }

[display_data _default_16x4 heater_bed]
position: 1, 0
text: { render("_heater_temperature", param_heater_name="heater_bed") }

[display_data _default_16x4 speed_factor]
position: 1, 10
text:
  ~feedrate~
  { "{:>4.0%}".format(printer.gcode_move.speed_factor) }

[display_data _default_16x4 print_progress]
position: 2, 0
text: { "{:^10.0%}".format(printer.display_status.progress) }
[display_data _default_16x4 progress_bar]
position: 2, 1 # Draw graphical progress bar after text is written
text: { draw_progress_bar(2, 0, 10, printer.display_status.progress) }

[display_data _default_16x4 printing_time]
position: 2, 10
text: { "%6s" % (render("_printing_time").strip(),) }

[display_data _default_16x4 print_status]
position: 3, 0
text: { render("_print_status") }


######################################################################
# Alternative 16x4 layout for multi-extruders
######################################################################

[display_data _multiextruder_16x4 extruder]
position: 0, 0
text: { render("_heater_temperature", param_heater_name="extruder") }

[display_data _multiextruder_16x4 fan]
position: 0, 10
text: { render("_fan_speed") }

[display_data _multiextruder_16x4 extruder1]
position: 1, 0
text: { render("_heater_temperature", param_heater_name="extruder1") }

[display_data _multiextruder_16x4 print_progress]
position: 1, 10
text: { "{:^6.0%}".format(printer.display_status.progress) }
[display_data _multiextruder_16x4 progress_bar]
position: 1, 11 # Draw graphical progress bar after text is written
text: { draw_progress_bar(1, 10, 6, printer.display_status.progress) }

[display_data _multiextruder_16x4 heater_bed]
position: 2, 0
text: { render("_heater_temperature", param_heater_name="heater_bed") }

[display_data _multiextruder_16x4 printing_time]
position: 2, 10
text: { "%6s" % (render("_printing_time").strip(),) }

[display_data _multiextruder_16x4 print_status]
position: 3, 0
text: { render("_print_status") }


######################################################################
# Default 20x4 display
######################################################################

[display_data _default_20x4 extruder]
position: 0, 0
text: { render("_heater_temperature", param_heater_name="extruder") }

[display_data _default_20x4 heater_bed]
position: 0, 10
text: { render("_heater_temperature", param_heater_name="heater_bed") }

[display_data _default_20x4 extruder1]
position: 1, 0
text: { render("_heater_temperature", param_heater_name="extruder1") }

[display_data _default_20x4 fan]
position: 1, 10
text:
  {% if 'fan' in printer %}
    { "Fan {:^4.0%}".format(printer.fan.speed) }
  {% endif %}

[display_data _default_20x4 speed_factor]
position: 2, 0
text:
  ~feedrate~
  { "{:^4.0%}".format(printer.gcode_move.speed_factor) }

[display_data _default_20x4 print_progress]
position: 2, 8
text:
  {% if 'virtual_sdcard' in printer and printer.virtual_sdcard.progress %}
    ~sd~
  {% else %}
    ~usb~
  {% endif %}
  { "{:^4.0%}".format(printer.display_status.progress) }

[display_data _default_20x4 printing_time]
position: 2, 14
text:
  ~clock~
  { render("_printing_time") }

[display_data _default_20x4 print_status]
position: 3, 0
text: { render("_print_status") }


######################################################################
# Default 16x4 glyphs
######################################################################

[display_glyph extruder]
data:
  ................
  ................
  ..************..
  .....******.....
  ..************..
  .....******.....
  ..************..
  ................
  ....********....
  ....******.*....
  ....********....
  ................
  ......****......
  .......**.......
  ................
  ................

[display_glyph bed]
data:
  ................
  ................
  ................
  ................
  ................
  ................
  ................
  ................
  ................
  ................
  ................
  ...*********....
  ..*.........*...
  .*************..
  ................
  ................

[display_glyph bed_heat1]
data:
  ................
  ................
  ..*....*....*...
  .*....*....*....
  ..*....*....*...
  ...*....*....*..
  ..*....*....*...
  .*....*....*....
  ..*....*....*...
  ................
  ................
  ...*********....
  ..*.........*...
  .*************..
  ................
  ................

[display_glyph bed_heat2]
data:
  ................
  ................
  ..*....*....*...
  ...*....*....*..
  ..*....*....*...
  .*....*....*....
  ..*....*....*...
  ...*....*....*..
  ..*....*....*...
  ................
  ................
  ...*********....
  ..*.........*...
  .*************..
  ................
  ................

[display_glyph fan1]
data:
  ................
  ................
  ....***.........
  ...****....**...
  ...****...****..
  ....***..*****..
  .....*....****..
  .......**.......
  .......**.......
  ..****....*.....
  ..*****..***....
  ..****...****...
  ...**....****...
  .........***....
  ................
  ................

[display_glyph fan2]
data:
  ................
  ................
  .......****.....
  .......****.....
  .......***......
  ..**...**.......
  ..***...........
  ..****.**.****..
  ..****.**.****..
  ...........***..
  .......**...**..
  ......***.......
  .....****.......
  .....****.......
  ................
  ................

[display_glyph feedrate]
data:
  ................
  ................
  ***.***.***.**..
  *...*...*...*.*.
  **..**..**..*.*.
  *...*...*...*.*.
  *...***.***.**..
  ................
  **...*..***.***.
  *.*.*.*..*..*...
  **..***..*..**..
  *.*.*.*..*..*...
  *.*.*.*..*..***.
  ................
  ................
  ................

# In addition to the above glyphs, 16x4 displays also have the
# following hard-coded single character glyphs: right_arrow, degrees.


######################################################################
# Default 20x4 glyphs
######################################################################

[display_glyph extruder]
hd44780_slot: 0
hd44780_data:
  ..*..
  .*.*.
  .*.*.
  .*.*.
  .*.*.
  *...*
  *...*
  .***.

[display_glyph bed]
hd44780_slot: 1
hd44780_data:
  .....
  *****
  *.*.*
  *...*
  *.*.*
  *****
  .....
  .....

[display_glyph bed_heat1]
hd44780_slot: 1
hd44780_data:
  .*..*
  *..*.
  .*..*
  *..*.
  .....
  *****
  .....
  .....

[display_glyph bed_heat2]
hd44780_slot: 1
hd44780_data:
  *..*.
  .*..*
  *..*.
  .*..*
  .....
  *****
  .....
  .....

[display_glyph fan]
hd44780_slot: 2
hd44780_data:
  .....
  *..**
  **.*.
  ..*..
  .*.**
  **..*
  .....
  .....

[display_glyph feedrate]
hd44780_slot: 3
hd44780_data:
  ***..
  *....
  **...
  *.***
  ..*.*
  ..**.
  ..*.*
  .....

[display_glyph clock]
hd44780_slot: 4
hd44780_data:
  .....
  .***.
  *..**
  *.*.*
  *...*
  .***.
  .....
  .....

[display_glyph degrees]
hd44780_slot: 5
hd44780_data:
  .**..
  *..*.
  *..*.
  .**..
  .....
  .....
  .....
  .....

[display_glyph usb]
hd44780_slot: 6
hd44780_data:
  .***.
  .***.
  .***.
  *****
  *****
  *****
  ..*..
  ..*..

[display_glyph sd]
hd44780_slot: 6
hd44780_data:
  .....
  ..***
  .****
  *****
  *****
  *****
  *****
  .....

# In addition to the above glyphs, 20x4 displays also have the
# following hard-coded glyphs: right_arrow.
