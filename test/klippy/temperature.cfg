# Test config with many different types of temperature sensors
[stepper_x]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PE5
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PF6
dir_pin: !PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: ^PJ1
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 8
endstop_pin: ^PD3
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 33.5
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4
sensor_type: AD595
sensor_pin: PK5
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PH5
sensor_type: PT100 INA826
sensor_pin: PK6
control: watermark
min_temp: 0
max_temp: 130

[temperature_fan test_max6675]
pin: PH6
min_temp: 0
max_temp: 100
control: watermark
sensor_type: MAX6675
sensor_pin: PE4

[temperature_fan test_max31855]
pin: PG5
min_temp: 0
max_temp: 100
control: watermark
sensor_type: MAX31855
sensor_pin: PE3

[temperature_fan test_max31856]
pin: PH3
min_temp: 0
max_temp: 100
control: watermark
sensor_type: MAX31856
sensor_pin: PH4

[temperature_fan test_max31865]
pin: PB6
min_temp: 0
max_temp: 100
control: watermark
sensor_type: MAX31865
sensor_pin: PB7

[thermistor my_custom_thermistor]
temperature1: 25
resistance1: 100000
temperature2: 100
resistance2: 1500
temperature3: 250
resistance3: 100

[temperature_fan test_custom_thermistor]
pin: PJ0
min_temp: 0
max_temp: 100
control: watermark
sensor_type: my_custom_thermistor
sensor_pin: PF3

[adc_temperature my_custom_adc]
temperature1: 25
voltage1: 1
temperature2: 100
voltage2: 2
temperature3: 250
voltage3: 3

[temperature_fan test_custom_adc]
pin: PH1
min_temp: 0
max_temp: 100
control: watermark
sensor_type: my_custom_adc
sensor_pin: PF4

[adc_temperature my_custom_resistance_adc]
temperature1: 25
resistance1: 1000
temperature2: 100
resistance2: 2000
temperature3: 250
resistance3: 3000

[temperature_sensor test_custom_resistance_adc]
sensor_type: my_custom_resistance_adc
sensor_pin: PF5

[temperature_sensor test_PT1000]
sensor_type: PT1000
sensor_pin: PK1

[temperature_sensor test_combined]
sensor_type: temperature_combined
sensor_list: temperature_sensor test_PT1000,extruder,heater_bed
combination_method: mean
maximum_deviation: 20.0

[controller_fan test_controller_fan]
pin: PH0

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100
