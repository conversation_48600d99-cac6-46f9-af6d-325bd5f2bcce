# Tests for miscellaneous g-code commands
DICTIONARY atmega2560.dict
CONFIG ../../config/example-cartesian.cfg

# Simple status commands
GET_POSITION
M114

STATUS

HELP

QUERY_ENDSTOPS

M115

M18

# G-code state commands
G28
SAVE_GCODE_STATE
G92 Z-5
G92 E5
SAVE_GCODE_STATE NAME=test
G1 Z-5
G91
G1 Z0
RESTORE_GCODE_STATE NAME=test
G1 Z-5
RESTORE_GCODE_STATE
G1 Z0 E0
RESTORE_GCODE_STATE MOVE=1

# Update commands
SET_GCODE_OFFSET Z=.1
M206 Z-.2
SET_GCODE_OFFSET Z_ADJUST=-.1

SET_VELOCITY_LIMIT ACCEL=100 VELOCITY=20 SQUARE_CORNER_VELOCITY=1 ACCEL_TO_DECEL=200
M204 S500

SET_PRESSURE_ADVANCE EXTRUDER=extruder ADVANCE=.001
SET_PRESSURE_ADVANCE ADVANCE=.002 SMOOTH_TIME=.001

# Restart command (must be last in test)
RESTART
