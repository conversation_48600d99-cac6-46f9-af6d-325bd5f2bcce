# Test case for basic movement on delta printers
CONFIG rotary_delta_calibrate.cfg
DICTIONARY atmega2560.dict

# Start by homing the printer.
G28

# Run basic delta calibration (in manual mode)
DELTA_ANALYZE MANUAL_HEIGHT=252
DELTA_CALIBRATE METHOD=manual
G1 Z0.1
ACCEPT
G1 Z0.1
ACCEPT
G1 Z0.1
ACCEPT
G1 Z0.1
ACCEPT
G1 Z0.1
ACCEPT
G1 Z0.1
ACCEPT
G1 Z0.1
ACCEPT

# Run extended delta calibration
DELTA_ANALYZE CENTER_DISTS=74,74,74,74,74,74
DELTA_ANALYZE OUTER_DISTS=74,74,74,74,74,74
DELTA_ANALYZE CENTER_PILLAR_WIDTHS=9,9,9
DELTA_ANALYZE OUTER_PILLAR_WIDTHS=9,9,9,9,9,9
DELTA_ANALYZE SCALE=1
DELTA_ANALYZE CALIBRATE=extended
