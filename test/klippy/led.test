# Test case for LEDs
CONFIG led.cfg
DICTIONARY atmega2560.dict

# SET_LED tests
SET_LED LED=lled RED=0.2
SET_LED LED=lled RED=0.3 TRANSMIT=0
SET_LED LED=lled RED=0.4
SET_LED LED=lled RED=0.5 SYNC=0

SET_LED LED=nled RED=0.2
SET_LED LED=nled RED=0.3 TRANSMIT=0
SET_LED LED=nled RED=0.4
SET_LED LED=nled RED=0.5 SYNC=0

SET_LED LED=dled RED=0.2
SET_LED LED=dled RED=0.3 TRANSMIT=0
SET_LED LED=dled RED=0.4
SET_LED LED=dled RED=0.5 SYNC=0

SET_LED LED=p5led RED=0.2
SET_LED LED=p5led RED=0.3 TRANSMIT=0
SET_LED LED=p5led RED=0.4
SET_LED LED=p5led RED=0.5 SYNC=0

SET_LED LED=p6led RED=0.2
SET_LED LED=p6led RED=0.3 TRANSMIT=0
SET_LED LED=p6led RED=0.4
SET_LED LED=p6led RED=0.5 SYNC=0

# SET_LED chain tests
SET_LED LED=nled INDEX=2 RED=0.2
SET_LED LED=nled INDEX=1 RED=0.3 TRANSMIT=0
SET_LED LED=nled INDEX=2 RED=0.4
SET_LED LED=nled INDEX=1 RED=0.5 SYNC=0

SET_LED LED=dled INDEX=2 RED=0.2
SET_LED LED=dled INDEX=1 RED=0.3 TRANSMIT=0
SET_LED LED=dled INDEX=2 RED=0.4
SET_LED LED=dled INDEX=1 RED=0.5 SYNC=0

# SET_LED_TEMPLATE tests
SET_LED_TEMPLATE LED=lled TEMPLATE=dtest
SET_LED_TEMPLATE LED=lled TEMPLATE=

SET_LED_TEMPLATE LED=nled TEMPLATE=dtest
SET_LED_TEMPLATE LED=nled TEMPLATE=
SET_LED_TEMPLATE LED=nled INDEX=2 TEMPLATE=dtest
SET_LED_TEMPLATE LED=nled TEMPLATE=

SET_LED_TEMPLATE LED=dled TEMPLATE=dtest
SET_LED_TEMPLATE LED=dled TEMPLATE=
SET_LED_TEMPLATE LED=dled INDEX=2 TEMPLATE=dtest
SET_LED_TEMPLATE LED=dled TEMPLATE=

SET_LED_TEMPLATE LED=p5led TEMPLATE=dtest

SET_LED_TEMPLATE LED=p6led TEMPLATE=dtest
