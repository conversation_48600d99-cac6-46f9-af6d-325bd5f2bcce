# Test config for the DELTA_CALIBRATE command (on rotary delta robots)
[stepper_a]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
gear_ratio: 107.000:16, 60:16
endstop_pin: ^PE4
homing_speed: 50
#position_endstop: 252
upper_arm_length: 170.000
lower_arm_length: 320.000

[stepper_b]
step_pin: PF6
dir_pin: PF7
enable_pin: !PF2
microsteps: 16
gear_ratio: 107.000:16, 60:16
endstop_pin: ^PJ0

[stepper_c]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
microsteps: 16
gear_ratio: 107.000:16, 60:16
endstop_pin: ^PD2

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: rotary_delta
max_velocity: 300
max_accel: 3000
max_z_velocity: 50
#shoulder_radius: 33.900
#shoulder_height: 412.900

[delta_calibrate]
radius: 50

#*# <---------------------- SAVE_CONFIG ---------------------->
#*# DO NOT EDIT THIS BLOCK OR BELOW. The contents are auto-generated.
#*#
#*# [printer]
#*# shoulder_radius = 33.900000
#*# shoulder_height = 412.900000
#*#
#*# [stepper_a]
#*# angle = 30.000000
#*# lower_arm = 320.000011
#*# position_endstop = 251.999999
#*#
#*# [stepper_b]
#*# angle = 150.000000
#*# lower_arm = 320.000000
#*# position_endstop = 251.999973
#*#
#*# [stepper_c]
#*# angle = 270.000000
#*# lower_arm = 319.999985
#*# position_endstop = 251.999902
#*#
#*# [delta_calibrate]
#*# height0 = 0.0
#*# height0_pos = 162606.000,162606.000,162605.000
#*# height1 = 0.0
#*# height1_pos = 157814.000,157814.000,177775.000
#*# height2 = 0.0
#*# height2_pos = 170956.000,151002.000,170956.000
#*# height3 = 0.0
#*# height3_pos = 176042.000,158122.000,158122.000
#*# height4 = 0.0
#*# height4_pos = 168774.000,168774.000,153337.000
#*# height5 = 0.0
#*# height5_pos = 158477.000,174341.000,158477.000
#*# height6 = 0.0
#*# height6_pos = 152152.000,169841.000,169841.000
