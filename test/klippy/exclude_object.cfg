[stepper_x]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PE5
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PF6
dir_pin: !PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: ^PJ1
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 8
endstop_pin: ^PD3
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 33.5
nozzle_diameter: 0.500
filament_diameter: 3.500
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 210

[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: watermark
min_temp: 0
max_temp: 110

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

# Test config for exclude_object
[exclude_object]

[gcode_macro M486]
gcode:
  # Parameters known to M486 are as follows:
  #   [C<flag>] Cancel the current object
  #   [P<index>] Cancel the object with the given index
  #   [S<index>] Set the index of the current object.
  #       If the object with the given index has been canceled, this will cause
  #       the firmware to skip to the next object. The value -1 is used to
  #       indicate something that isn’t an object and shouldn’t be skipped.
  #   [T<count>] Reset the state and set the number of objects
  #   [U<index>] Un-cancel the object with the given index. This command will be
  #       ignored if the object has already been skipped

  {% if 'exclude_object' not in printer %}
    {action_raise_error("[exclude_object] is not enabled")}
  {% endif %}

  {% if 'T' in params %}
    EXCLUDE_OBJECT RESET=1

    {% for i in range(params.T | int) %}
      EXCLUDE_OBJECT_DEFINE NAME={i}
    {% endfor %}
  {% endif %}

  {% if 'C' in params %}
    EXCLUDE_OBJECT CURRENT=1
  {% endif %}

  {% if 'P' in params %}
    EXCLUDE_OBJECT NAME={params.P}
  {% endif %}

  {% if 'S' in params %}
    {% if params.S == '-1' %}
      {% if printer.exclude_object.current_object %}
        EXCLUDE_OBJECT_END NAME={printer.exclude_object.current_object}
      {% endif %}
    {% else %}
      EXCLUDE_OBJECT_START NAME={params.S}
    {% endif %}
  {% endif %}

  {% if 'U' in params %}
    EXCLUDE_OBJECT RESET=1 NAME={params.U}
  {% endif %}
