# Test config for the DELTA_CALIBRATE command (on linear delta robots)
[stepper_a]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 0.32
endstop_pin: ^PE4
homing_speed: 50
#position_endstop: 297.05
#arm_length: 333.0

[stepper_b]
step_pin: PF6
dir_pin: PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 0.32
endstop_pin: ^PJ0

[stepper_c]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 0.32
endstop_pin: ^PD2

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: delta
max_velocity: 300
max_accel: 3000
#delta_radius: 174.75

[delta_calibrate]
radius: 50

#*# <---------------------- SAVE_CONFIG ---------------------->
#*# DO NOT EDIT THIS BLOCK OR BELOW. The contents are auto-generated.
#*#
#*# [printer]
#*# delta_radius = 174.750004
#*#
#*# [stepper_a]
#*# angle = 210.000032
#*# arm_length = 333.000032
#*# position_endstop = 297.049970
#*#
#*# [stepper_b]
#*# angle = 329.999997
#*# arm_length = 332.999811
#*# position_endstop = 297.050132
#*#
#*# [stepper_c]
#*# angle = 90.000000
#*# arm_length = 333.000181
#*# position_endstop = 297.049900
#*#
#*# [delta_calibrate]
#*# height0 = 0.0
#*# height0_pos = 2970499.999,2970499.999,2970499.999
#*# height1 = 0.0
#*# height1_pos = 3163266.999,3163266.999,2727853.999
#*# height2 = 0.0
#*# height2_pos = 2869315.999,3303154.999,2869315.999
#*# height3 = 0.0
#*# height3_pos = 2749008.999,3138330.999,3138330.999
#*# height4 = 0.0
#*# height4_pos = 2885497.999,2885497.999,3218746.999
#*# height5 = 0.0
#*# height5_pos = 3114555.999,2771134.999,3114555.999
#*# height6 = 0.0
#*# height6_pos = 3260109.999,2876968.999,2876968.999
