# Test config for tmc drivers

[stepper_x]
step_pin: PC0
dir_pin: PL0
enable_pin: !PA7
microsteps: 16
rotation_distance: 40
endstop_pin: tmc2130_stepper_x:virtual_endstop
position_endstop: 0
position_max: 250

[tmc2130 stepper_x]
cs_pin: PG0
run_current: .5
sense_resistor: 0.220
diag1_pin: !PK2

[stepper_x1]
step_pin: PC3
dir_pin: PL6
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB6

[tmc2130 stepper_x1]
cs_pin: PG1
run_current: .5
sense_resistor: 0.220
diag1_pin: !PK2

[stepper_y]
step_pin: PC1
dir_pin: !PL1
microsteps: 16
rotation_distance: 40
endstop_pin: tmc5160_stepper_y:virtual_endstop
position_endstop: 0
position_max: 210

[tmc5160 stepper_y]
cs_pin: PG2
run_current: .5
sense_resistor: 0.220
diag1_pin: !PK7

[stepper_y1]
step_pin: PA4
dir_pin: PA6
microsteps: 16
rotation_distance: 40
endstop_pin: tmc2209_stepper_y1:virtual_endstop

[tmc2209 stepper_y1]
uart_pin: PA5
diag_pin: PH6
run_current: .5
sense_resistor: 0.075

[stepper_z]
step_pin: PC2
dir_pin: PL2
microsteps: 16
rotation_distance: 8
endstop_pin: ^PB4
position_endstop: 0.5
position_max: 200

[tmc2208 stepper_z]
uart_pin: PK5
run_current: .5
sense_resistor: 0.220

[stepper_z1]
step_pin: PB5
dir_pin: PK6
enable_pin: !PH5
microsteps: 16
rotation_distance: 8
endstop_pin: ^PH3

[tmc2660 stepper_z1]
cs_pin: PK1
run_current: .5
sense_resistor: 0.220

[stepper_z2]
step_pin: PH1
dir_pin: PA2
enable_pin: !PA3
microsteps: 16
rotation_distance: 8

[tmc2240 stepper_z2]
cs_pin: PK3
run_current: .5

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[endstop_phase]
