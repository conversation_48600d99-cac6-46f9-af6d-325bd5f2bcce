# Test case for pwm
CONFIG pwm.cfg
DICTIONARY atmega2560.dict

# Hard PWM
# Basic test
SET_PIN PIN=hard_pwm_pin VALUE=0
SET_PIN PIN=hard_pwm_pin VALUE=0.5
SET_PIN PIN=hard_pwm_pin VALUE=0.5
SET_PIN PIN=hard_pwm_pin VALUE=0.25
SET_PIN PIN=hard_pwm_pin VALUE=1

# Soft PWM
# Test basic on off
SET_PIN PIN=soft_pwm_pin VALUE=0
SET_PIN PIN=soft_pwm_pin VALUE=0.5
SET_PIN PIN=soft_pwm_pin VALUE=1

# Soft PWM with dynamic cycle time
# Test basic on off
SET_PIN PIN=cycle_pwm_pin VALUE=0
SET_PIN PIN=cycle_pwm_pin VALUE=0.5
SET_PIN PIN=cycle_pwm_pin VALUE=1

# Test cycle time
SET_PIN PIN=cycle_pwm_pin VALUE=0 CYCLE_TIME=0.1
SET_PIN PIN=cycle_pwm_pin VALUE=1 CYCLE_TIME=0.5
SET_PIN PIN=cycle_pwm_pin VALUE=0.5 CYCLE_TIME=0.001
SET_PIN PIN=cycle_pwm_pin VALUE=0.75 CYCLE_TIME=0.01
SET_PIN PIN=cycle_pwm_pin VALUE=0.5 CYCLE_TIME=1

# Test duplicate values
SET_PIN PIN=cycle_pwm_pin VALUE=0.5 CYCLE_TIME=0.5
SET_PIN PIN=cycle_pwm_pin VALUE=0.5 CYCLE_TIME=0.5
SET_PIN PIN=cycle_pwm_pin VALUE=0.75 CYCLE_TIME=0.5
SET_PIN PIN=cycle_pwm_pin VALUE=0.75 CYCLE_TIME=0.75

# PWM tool
# Basic test
SET_PIN PIN=test_pwm_tool VALUE=0
SET_PIN PIN=test_pwm_tool VALUE=0.5
SET_PIN PIN=test_pwm_tool VALUE=0.5
SET_PIN PIN=test_pwm_tool VALUE=0.25
SET_PIN PIN=test_pwm_tool VALUE=1
