# Test config with macros
[stepper_x]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PE5
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PF6
dir_pin: !PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: ^PJ1
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 8
endstop_pin: ^PD3
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 33.5
nozzle_diameter: 0.500
filament_diameter: 3.500
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 210

[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: watermark
min_temp: 0
max_temp: 110

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[gcode_macro TEST_SAVE_RESTORE]
gcode:
  SAVE_GCODE_STATE NAME=TESTIT1
  G92 Z10
  RESTORE_GCODE_STATE NAME=TESTIT1
  G92 Z0

[gcode_macro TEST_expression]
gcode:
  {% if printer.gcode_move.gcode_position.x != 0.0 %}
    M112
  {% else %}
    { action_respond_info("TEST_expression") }
  {% endif %}

[gcode_macro TEST_variable]
variable_t: 12.0
gcode:
  { action_respond_info("TEST_variable") }
  {% if t - 12.0 != printer.toolhead.position.y %}
    M112
  {% endif %}
  {% if printer["gcode_macro TEST_variable"].t - 12.0 != 0.0 %}
    M112
  {% endif %}
  SET_GCODE_VARIABLE MACRO=TEST_variable VARIABLE=t VALUE=17
  TEST_variable_part2

[gcode_macro TEST_variable_part2]
gcode:
  { action_respond_info("TEST_variable_part2") }
  {% if printer["gcode_macro TEST_variable"].t != 17.0 %}
    M112
  {% endif %}

[gcode_macro TEST_param]
gcode:
  { action_respond_info("TEST_param") }
  {% if params.T != "123" %}
    M112
  {% endif %}

[gcode_macro TEST_in]
gcode:
  {% if "abc" in printer or "toolhead" not in printer %}
    M112
  {% endif %}

# A utf8 test (with utf8 characters such as ° )
[gcode_macro TEST_unicode]  ; Also test end-of-line comments ( ° )
variable_ABC: 25            # Another end-of-line comment test ( ° )
description: A unicode test °
gcode: G28

# Main test start point
[gcode_macro TESTIT]
gcode:
  TEST_SAVE_RESTORE
  TEST_expression
  TEST_variable
  TEST_param T=123
  TEST_unicode
  TEST_in
