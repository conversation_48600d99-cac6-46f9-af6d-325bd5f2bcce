# Test config for bed screws tool
[stepper_x]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
rotation_distance: 40
microsteps: 16
endstop_pin: ^PE5
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PF6
dir_pin: !PF7
enable_pin: !PF2
rotation_distance: 40
microsteps: 16
endstop_pin: ^PJ1
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
rotation_distance: 8
microsteps: 16
endstop_pin: ^PD3
position_endstop: 0.5
position_max: 200

[bed_screws]
screw1: 100,50
screw1_name: Front right
screw1_fine_adjust: 200,50
screw2: 75,75
screw2_fine_adjust: 200,75
screw3: 75,75
screw3_name: Last
screw3_fine_adjust: 75,90

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100
