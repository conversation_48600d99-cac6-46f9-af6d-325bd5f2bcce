# Test config for generic cartesian kinematics with dual carriage
[carriage x]
position_endstop: 0
position_max: 300
homing_speed: 50
endstop_pin: ^PE5

[carriage y]
position_endstop: 0
position_max: 200
homing_speed: 50
endstop_pin: ^PJ1

[extra_carriage y1]
primary_carriage: y
endstop_pin: ^PB6

[carriage z]
position_endstop: 0.5
position_max: 100
endstop_pin: ^PD3

[extra_carriage z1]
primary_carriage: z
endstop_pin: ^PD2

[dual_carriage u]
primary_carriage: x
position_endstop: 300
position_max: 300
homing_speed: 50
endstop_pin: ^PE4

[stepper stepper_x]
carriages: x+y
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40

[stepper dual_carriage]
carriages: u-y1
step_pin: PH1
dir_pin: PH0
enable_pin: !PA1
microsteps: 16
rotation_distance: 40

[stepper stepper_y]
carriages: 1*y+0.5*z
step_pin: PF6
dir_pin: !PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40

[stepper stepper_y1]
carriages: 1*y1-0.5*z1
step_pin: PE3
dir_pin: !PH6
enable_pin: !PG5
microsteps: 16
rotation_distance: 40

[stepper stepper_z]
carriages: z
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 8

[stepper stepper_z1]
carriages: z1
step_pin: PG1
dir_pin: PG0
enable_pin: !PH3
microsteps: 16
rotation_distance: 8

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 33.5
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[gcode_macro PARK_extruder]
gcode:
    SET_DUAL_CARRIAGE CARRIAGE=x
    G90
    G1 X0

[gcode_macro T0]
gcode:
    PARK_{printer.toolhead.extruder}
    SET_SERVO SERVO=my_servo angle=100
    ACTIVATE_EXTRUDER EXTRUDER=extruder
    SET_DUAL_CARRIAGE CARRIAGE=x

[extruder1]
step_pin: PC1
dir_pin: PC3
enable_pin: !PC7
microsteps: 16
rotation_distance: 33.5
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK7
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[gcode_macro PARK_extruder1]
gcode:
    SET_DUAL_CARRIAGE CARRIAGE=u
    G90
    G1 X300

[gcode_macro T1]
gcode:
    PARK_{printer.toolhead.extruder}
    SET_SERVO SERVO=my_servo angle=50
    ACTIVATE_EXTRUDER EXTRUDER=extruder1
    SET_DUAL_CARRIAGE CARRIAGE=u

[servo my_servo]
pin: PH4

[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: watermark
min_temp: 0
max_temp: 130

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: generic_cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[input_shaper]
