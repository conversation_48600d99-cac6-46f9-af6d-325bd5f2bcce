<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than diagrams.net -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1701px" height="877px" viewBox="-0.5 -0.5 1701 877" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2021-09-29T00:27:34.825Z&quot; agent=&quot;5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/15.3.5 Chrome/94.0.4606.51 Electron/15.0.0 Safari/537.36&quot; etag=&quot;nOT8JSCUlhXYtC5Iilaa&quot; version=&quot;15.3.5&quot; type=&quot;device&quot; pages=&quot;3&quot;&gt;&lt;diagram id=&quot;RVSrw8KQITSmj-quPW0e&quot; name=&quot;Rect-Basic-Config&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;ydUguJ2sSF7GnP8DQfKP&quot; name=&quot;Round-Basic-Config&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;Wo1tzaOVTCcExmPxfSC3&quot; name=&quot;Interpolation&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><rect x="0" y="0" width="1700" height="876" fill="#ffffff" stroke="none" pointer-events="all"/><ellipse cx="411.89" cy="416.3" rx="393.7" ry="393.7" fill="none" stroke="#330000" stroke-width="3" pointer-events="all"/><ellipse cx="1289.84" cy="416.3" rx="393.7" ry="393.7" fill="none" stroke="#330000" stroke-width="3" pointer-events="all"/><ellipse cx="1250.48" cy="416.3" rx="334.645" ry="334.65" fill="none" stroke="#ff0000" stroke-width="3" pointer-events="all"/><path d="M 1256.38 416.3 L 1405.52 416.31" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1410.77 416.31 L 1403.77 419.81 L 1405.52 416.31 L 1403.77 412.81 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="1250.47" cy="416.3" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 1254.64 746.77 L 1409.11 592.3" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1412.82 588.59 L 1410.35 596.01 L 1409.11 592.3 L 1405.4 591.06 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="1250.47" cy="750.94" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><ellipse cx="1250.47" cy="81.65" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 921.73 416.3 L 1070.87 416.3" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1076.12 416.3 L 1069.12 419.8 L 1070.87 416.3 L 1069.12 412.8 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="915.82" cy="416.3" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 1580.94 412.12 L 1426.47 257.65" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1422.76 253.94 L 1430.19 256.42 L 1426.47 257.65 L 1425.24 261.37 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="1585.11" cy="416.3" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 1244.56 583.62 L 1095.42 583.62" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1090.17 583.62 L 1097.17 580.12 L 1095.42 583.62 L 1097.17 587.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="1250.47" cy="583.62" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 1244.56 248.98 L 1095.43 248.97" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1090.18 248.97 L 1097.18 245.47 L 1095.43 248.97 L 1097.18 252.47 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="1250.47" cy="248.98" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 1089.05 416.3 L 1238.2 416.3" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1243.45 416.3 L 1236.45 419.8 L 1238.2 416.3 L 1236.45 412.8 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="1083.15" cy="416.3" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 1423.7 416.31 L 1572.84 416.3" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1578.09 416.3 L 1571.09 419.8 L 1572.84 416.3 L 1571.09 412.8 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="1417.79" cy="416.31" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 1411.89 583.62 L 1262.74 583.62" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1257.49 583.62 L 1264.49 580.12 L 1262.74 583.62 L 1264.49 587.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="1417.79" cy="583.62" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 1078.97 579.44 L 924.5 424.98" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 920.79 421.27 L 928.22 423.74 L 924.5 424.98 L 923.27 428.69 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="1083.15" cy="583.62" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 1411.89 248.97 L 1262.74 248.98" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1257.49 248.98 L 1264.49 245.48 L 1262.74 248.98 L 1264.49 252.48 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="1417.79" cy="248.97" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 1087.33 244.8 L 1241.8 90.33" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 1245.51 86.62 L 1243.03 94.05 L 1241.8 90.33 L 1238.08 89.1 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="1083.15" cy="248.97" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><rect x="1211.06" y="443.86" width="78.82" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 77px; height: 1px; padding-top: 454px; margin-left: 1212px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">mesh_origin<br />(-10, 0)</div></div></div></foreignObject><text x="1250" y="460" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle" font-weight="bold">mesh_ori...</text></switch></g><rect x="1187.47" y="766.69" width="145.68" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 144px; height: 1px; padding-top: 777px; margin-left: 1188px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Start (-10, -85)</div></div></div></foreignObject><text x="1260" y="783" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle" font-weight="bold">Start (-10, -85)</text></switch></g><rect x="915.83" y="386.31" width="78.82" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 77px; height: 1px; padding-top: 396px; margin-left: 917px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">(-95, 0)</div></div></div></foreignObject><text x="955" y="402" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle" font-weight="bold">(-95, 0)</text></switch></g><rect x="1592.99" y="406.32" width="78.82" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 77px; height: 1px; padding-top: 416px; margin-left: 1594px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">(75, 0)</div></div></div></foreignObject><text x="1632" y="422" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle" font-weight="bold">(75, 0)</text></switch></g><rect x="1211.05" y="50.15" width="78.82" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 77px; height: 1px; padding-top: 60px; margin-left: 1212px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">(-10, 85)</div></div></div></foreignObject><text x="1250" y="66" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle" font-weight="bold">(-10, 85)</text></switch></g><ellipse cx="415.82" cy="416.31" rx="295.275" ry="295.28" fill="none" stroke="#ff0000" stroke-width="3" pointer-events="none"/><ellipse cx="419.76" cy="711.57" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="none"/><ellipse cx="419.76" cy="416.3" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="none"/><ellipse cx="419.76" cy="121.02" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="none"/><ellipse cx="711.1" cy="416.3" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="none"/><ellipse cx="120.55" cy="416.3" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="none"/><ellipse cx="419.76" cy="563.94" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="none"/><ellipse cx="419.75" cy="274.57" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="none"/><ellipse cx="567.4" cy="416.3" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="none"/><ellipse cx="283.93" cy="416.3" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="none"/><ellipse cx="567.4" cy="563.94" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="none"/><ellipse cx="567.4" cy="274.57" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="none"/><ellipse cx="283.93" cy="563.94" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="none"/><ellipse cx="283.93" cy="274.57" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 77px; height: 1px; padding-top: 745px; margin-left: 381px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; font-weight: bold; white-space: normal; overflow-wrap: normal;">Start<br />(0, -75)</div></div></div></foreignObject><text x="420" y="751" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle" font-weight="bold">Start...</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 85px; height: 1px; padding-top: 416px; margin-left: 31px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; font-weight: bold; white-space: normal; overflow-wrap: normal;">(-75, 0)</div></div></div></foreignObject><text x="73" y="422" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle" font-weight="bold">(-75, 0)</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 77px; height: 1px; padding-top: 416px; margin-left: 720px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; font-weight: bold; white-space: normal; overflow-wrap: normal;">(75, 0)</div></div></div></foreignObject><text x="758" y="422" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle" font-weight="bold">(75, 0)</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 77px; height: 1px; padding-top: 96px; margin-left: 381px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; font-weight: bold; white-space: normal; overflow-wrap: normal;">(0, 75)</div></div></div></foreignObject><text x="420" y="102" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle" font-weight="bold">(0, 75)</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 77px; height: 1px; padding-top: 454px; margin-left: 381px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; font-weight: bold; white-space: normal; overflow-wrap: normal;">mesh_origin<br />(0, 0)</div></div></div></foreignObject><text x="420" y="460" fill="#000000" font-family="Helvetica" font-size="20px" text-anchor="middle" font-weight="bold">mesh_ori...</text></switch></g><path d="M 425.67 416.3 L 555.12 416.3" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 560.37 416.3 L 553.37 419.8 L 555.12 416.3 L 553.37 412.8 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 573.3 416.3 L 698.82 416.3" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 704.07 416.3 L 697.07 419.8 L 698.82 416.3 L 697.07 412.8 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 126.45 416.3 L 271.66 416.3" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 276.91 416.3 L 269.91 419.8 L 271.66 416.3 L 269.91 412.8 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 289.84 416.3 L 407.49 416.3" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 412.74 416.3 L 405.74 419.8 L 407.49 416.3 L 405.74 412.8 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 423.94 707.4 L 558.72 572.62" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 562.43 568.9 L 559.96 576.33 L 558.72 572.62 L 555.01 571.38 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 561.5 563.94 L 432.03 563.94" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 426.78 563.94 L 433.78 560.44 L 432.03 563.94 L 433.78 567.44 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 706.92 412.12 L 576.11 283.21" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 572.37 279.53 L 579.81 281.95 L 576.11 283.21 L 574.9 286.93 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 561.49 274.57 L 432.03 274.57" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 426.78 274.57 L 433.78 271.07 L 432.03 274.57 L 433.78 278.07 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 413.85 563.94 L 296.21 563.94" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 290.96 563.94 L 297.96 560.44 L 296.21 563.94 L 297.96 567.44 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 279.76 559.76 L 129.46 424.73" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 125.56 421.22 L 133.1 423.3 L 129.46 424.73 L 128.42 428.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 413.85 274.57 L 296.21 274.57" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 290.96 274.57 L 297.96 271.07 L 296.21 274.57 L 297.96 278.07 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 288.11 270.39 L 411.38 129.99" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 414.85 126.04 L 412.86 133.61 L 411.38 129.99 L 407.6 128.99 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 470px; height: 1px; padding-top: 840px; margin-left: 188px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; font-weight: bold; white-space: normal; overflow-wrap: normal;"><font style="font-size: 26px">Mesh Radius = 75, Mesh Origin = (0,0)</font></div></div></div></foreignObject><text x="424" y="846" fill="#000000" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Mesh Radius = 75, Mesh Origin = (0,0)</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 510px; height: 1px; padding-top: 840px; margin-left: 1035px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 22px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; font-weight: bold; white-space: normal; overflow-wrap: normal;"><font style="font-size: 26px">Mesh Radius = 85, Mesh Origin = (-10,0)</font></div></div></div></foreignObject><text x="1290" y="846" fill="#000000" font-family="Helvetica" font-size="22px" text-anchor="middle" font-weight="bold">Mesh Radius = 85, Mesh Origin = (-10,0)</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Viewer does not support full SVG 1.1</text></a></switch></svg>