<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="901px" height="719px" viewBox="-0.5 -0.5 901 719" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2023-10-09T16:08:10.185Z&quot; agent=&quot;Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36&quot; etag=&quot;9oU6p2ILMxrigHTZD8KX&quot; version=&quot;22.0.3&quot; type=&quot;device&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;9aYWBSPtCap1D5Vuddur&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><rect x="0" y="680" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 695px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><b style="font-size: 20px;">Origin<br />(0,0)</b></div></div></div></foreignObject><text x="30" y="699" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Origin...</text></switch></g><rect x="710" y="10" width="190" height="100" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 182px; height: 1px; padding-top: 0px; margin-left: 715px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 110px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><h1>Legend</h1><p><br /></p></div></div></div></foreignObject><text x="715" y="12" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Legend&#xa;</text></switch></g><rect x="720" y="50" width="30" height="10" fill="#f5f5f5" stroke="#666666" pointer-events="all"/><rect x="720" y="68" width="30" height="10" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><rect x="750" y="40" width="90" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 55px; margin-left: 751px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Object Polygon</div></div></div></foreignObject><text x="795" y="59" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Object Polygon</text></switch></g><rect x="750" y="58" width="90" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 73px; margin-left: 751px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Used Bed Area</div></div></div></foreignObject><text x="795" y="77" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Used Bed Area</text></switch></g><rect x="720" y="85" width="30" height="10" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><rect x="750" y="75" width="140" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 90px; margin-left: 751px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Adapted Bed Mesh Area</div></div></div></foreignObject><text x="820" y="94" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Adapted Bed Mesh Area</text></switch></g><rect x="30" y="10" width="660" height="660" fill="none" stroke="#000000" stroke-width="4" pointer-events="all"/><rect x="215" y="446" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 461px; margin-left: 216px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">(90,75)</div></div></div></foreignObject><text x="245" y="465" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">(90,75)</text></switch></g><rect x="426" y="234" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 249px; margin-left: 427px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">(130,140)</div></div></div></foreignObject><text x="456" y="253" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">(130,140)</text></switch></g><rect x="245" y="258.5" width="210" height="191.5" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><rect x="270" y="280" width="160" height="152" fill-opacity="0.5" fill="#dae8fc" stroke="#6c8ebf" stroke-opacity="0.5" pointer-events="all"/><rect x="360" y="250.5" width="40" height="100" fill-opacity="0.5" fill="#eeeeee" stroke="#36393d" stroke-opacity="0.5" transform="rotate(90,380,300.5)" pointer-events="all"/><rect x="410" y="270.5" width="40" height="9" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 275px; margin-left: 411px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 10px;">(125,135)</font></div></div></div></foreignObject><text x="430" y="279" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">(125,1...</text></switch></g><rect x="317" y="321.5" width="40" height="9" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 326px; margin-left: 318px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 10px;">(95,120)</font></div></div></div></foreignObject><text x="337" y="330" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">(95,12...</text></switch></g><rect x="270" y="300.5" width="40" height="100" fill-opacity="0.5" fill="#eeeeee" stroke="#36393d" stroke-opacity="0.5" pointer-events="all"/><rect x="250" y="400.5" width="40" height="9" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 405px; margin-left: 251px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 10px;">(60,90)</font></div></div></div></foreignObject><text x="270" y="409" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">(60,90)</text></switch></g><rect x="290" y="288.5" width="40" height="9" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 293px; margin-left: 291px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 10px;">(90,130)</font></div></div></div></foreignObject><text x="310" y="297" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">(90,13...</text></switch></g><rect x="330" y="331.5" width="100" height="100" fill-opacity="0.5" fill="#eeeeee" stroke="#36393d" stroke-opacity="0.5" pointer-events="all"/><rect x="310" y="434" width="40" height="9" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 439px; margin-left: 311px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 10px;">(95,80)</font></div></div></div></foreignObject><text x="330" y="442" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">(95,80)</text></switch></g><rect x="410" y="320.5" width="40" height="9" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 325px; margin-left: 411px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 10px;">(125,110)</font></div></div></div></foreignObject><text x="430" y="329" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">(125,1...</text></switch></g><ellipse cx="455.1" cy="449.9" rx="5.905499999999999" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><ellipse cx="350.91" cy="354.9" rx="5.905499999999999" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><ellipse cx="244.1" cy="450.9" rx="5.905499999999999" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><ellipse cx="244.1" cy="354.9" rx="5.905499999999999" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 345 354.9 L 253.37 354.9" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 251.12 354.9 L 254.12 353.4 L 253.37 354.9 L 254.12 356.4 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="455.1" cy="258.09" rx="5.905499999999999" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 355.9 258.09 L 445.82 258.09" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 448.07 258.09 L 445.07 259.59 L 445.82 258.09 L 445.07 256.59 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="350" cy="258.09" rx="5.905499999999999" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 250.9 258.09 L 340.72 258.09" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 342.97 258.09 L 339.97 259.59 L 340.72 258.09 L 339.97 256.59 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="245" cy="258.09" rx="5.905499999999999" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 244.1 349 L 244.11 306.49 L 244.93 267.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 244.97 265.12 L 246.41 268.15 L 244.93 267.37 L 243.41 268.08 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 449.19 354.25 L 403.03 354.31 L 360.18 354.86" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 357.93 354.89 L 360.91 353.35 L 360.18 354.86 L 360.95 356.35 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="455.1" cy="354.25" rx="5.905499999999999" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 455.1 444 L 455.1 363.53" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 455.1 361.28 L 456.6 364.28 L 455.1 363.53 L 453.6 364.28 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 355.9 450.9 L 402.57 450.89 L 445.82 449.97" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 448.07 449.93 L 445.11 451.49 L 445.82 449.97 L 445.04 448.49 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="350" cy="450.9" rx="5.905499999999999" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 250 450.9 L 340.72 450.9" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 342.97 450.9 L 339.97 452.4 L 340.72 450.9 L 339.97 449.4 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>