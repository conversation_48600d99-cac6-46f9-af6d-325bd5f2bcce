<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="891px" height="719px" viewBox="-0.5 -0.5 891 719" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2023-11-20T23:21:00.371Z&quot; agent=&quot;Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36&quot; etag=&quot;uzE3l4u1UXjvh0Ybzm1X&quot; version=&quot;22.1.3&quot; type=&quot;device&quot;&gt;&#10;  &lt;diagram name=&quot;Page-1&quot; id=&quot;9aYWBSPtCap1D5Vuddur&quot;&gt;&#10;    &lt;mxGraphModel dx=&quot;2472&quot; dy=&quot;813&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;850&quot; pageHeight=&quot;1100&quot; background=&quot;#ffffff&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#10;      &lt;root&gt;&#10;        &lt;mxCell id=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-16&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;opacity=50;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;109&quot; y=&quot;241&quot; width=&quot;300&quot; height=&quot;290&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-2&quot; value=&quot;&amp;lt;b style=&amp;quot;font-size: 20px;&amp;quot;&amp;gt;Origin&amp;lt;br&amp;gt;(0,0)&amp;lt;/b&amp;gt;&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-100&quot; y=&quot;730&quot; width=&quot;60&quot; height=&quot;30&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-13&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;610&quot; y=&quot;60&quot; width=&quot;180&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-5&quot; value=&quot;&amp;lt;h1&amp;gt;Legend&amp;lt;/h1&amp;gt;&amp;lt;p&amp;gt;&amp;lt;br&amp;gt;&amp;lt;/p&amp;gt;&quot; style=&quot;text;html=1;strokeColor=default;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;&quot; parent=&quot;sZqi1VJqUI8cdP5_EVks-13&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry width=&quot;180&quot; height=&quot;80&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-6&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;&quot; parent=&quot;sZqi1VJqUI8cdP5_EVks-13&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;9.473684210526317&quot; y=&quot;44.8&quot; width=&quot;28.42105263157894&quot; height=&quot;8&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-8&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;&quot; parent=&quot;sZqi1VJqUI8cdP5_EVks-13&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;9.473684210526317&quot; y=&quot;59.2&quot; width=&quot;28.42105263157894&quot; height=&quot;8&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-9&quot; value=&quot;Object Polygon&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; parent=&quot;sZqi1VJqUI8cdP5_EVks-13&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;37.89473684210527&quot; y=&quot;36.8&quot; width=&quot;85.26315789473684&quot; height=&quot;24&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-10&quot; value=&quot;Used Bed Area&quot; style=&quot;text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;&quot; parent=&quot;sZqi1VJqUI8cdP5_EVks-13&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;37.89473684210527&quot; y=&quot;51.199999999999996&quot; width=&quot;85.26315789473684&quot; height=&quot;24&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-1&quot; value=&quot;&quot; style=&quot;whiteSpace=wrap;html=1;aspect=fixed;fillColor=none;strokeWidth=4;strokeColor=#000000;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-70&quot; y=&quot;60&quot; width=&quot;660&quot; height=&quot;660&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-24&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;opacity=50;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;109&quot; y=&quot;431.5&quot; width=&quot;40&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-25&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;opacity=50;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;308&quot; y=&quot;430.5&quot; width=&quot;100&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-35&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;endSize=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;rSL1Pw579-r8zTYxRfB7-25&quot; target=&quot;sZqi1VJqUI8cdP5_EVks-47&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;Array as=&quot;points&quot;&gt;&#10;              &lt;mxPoint x=&quot;390&quot; y=&quot;531&quot; /&gt;&#10;            &lt;/Array&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-32&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;1&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;104&quot; y=&quot;320.5&quot; width=&quot;629&quot; height=&quot;427.5&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-31&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;sZqi1VJqUI8cdP5_EVks-32&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;113&quot; width=&quot;133&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-14&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;opacity=50;rotation=90;&quot; parent=&quot;sZqi1VJqUI8cdP5_EVks-31&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;84&quot; y=&quot;-95&quot; width=&quot;40&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-26&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;sZqi1VJqUI8cdP5_EVks-32&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry y=&quot;18&quot; width=&quot;629&quot; height=&quot;409.5&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-3&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;opacity=50;&quot; parent=&quot;sZqi1VJqUI8cdP5_EVks-26&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;5&quot; y=&quot;-31&quot; width=&quot;40&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-29&quot; value=&quot;&quot; style=&quot;group&quot; parent=&quot;sZqi1VJqUI8cdP5_EVks-26&quot; vertex=&quot;1&quot; connectable=&quot;0&quot;&gt;&#10;          &lt;mxGeometry x=&quot;60&quot; y=&quot;32&quot; width=&quot;569&quot; height=&quot;377.5&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-15&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;opacity=50;&quot; parent=&quot;sZqi1VJqUI8cdP5_EVks-29&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;17&quot; y=&quot;-47&quot; width=&quot;100&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-47&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#0050ef;strokeColor=#001DBC;fontColor=#ffffff;snapToPoint=1;direction=east;&quot; parent=&quot;sZqi1VJqUI8cdP5_EVks-29&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;238.19075000000004&quot; y=&quot;153.49850000000004&quot; width=&quot;11.811&quot; height=&quot;11.81&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-45&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#0050ef;strokeColor=#001DBC;fontColor=#ffffff;snapToPoint=1;direction=east;&quot; parent=&quot;sZqi1VJqUI8cdP5_EVks-29&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;133.00075000000004&quot; y=&quot;57.498500000000035&quot; width=&quot;11.811&quot; height=&quot;11.81&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-49&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#0050ef;strokeColor=#001DBC;fontColor=#ffffff;snapToPoint=1;direction=east;&quot; parent=&quot;sZqi1VJqUI8cdP5_EVks-26&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.8092499999999632&quot; y=&quot;186.49850000000004&quot; width=&quot;11.811&quot; height=&quot;11.81&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-44&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#0050ef;strokeColor=#001DBC;fontColor=#ffffff;snapToPoint=1;direction=east;&quot; parent=&quot;sZqi1VJqUI8cdP5_EVks-26&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;98.19075000000004&quot; y=&quot;89.49850000000004&quot; width=&quot;11.811&quot; height=&quot;11.81&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-55&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;endSize=2;&quot; parent=&quot;sZqi1VJqUI8cdP5_EVks-26&quot; source=&quot;sZqi1VJqUI8cdP5_EVks-45&quot; target=&quot;sZqi1VJqUI8cdP5_EVks-44&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-30&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#0050ef;strokeColor=#001DBC;fontColor=#ffffff;snapToPoint=1;direction=east;&quot; vertex=&quot;1&quot; parent=&quot;sZqi1VJqUI8cdP5_EVks-26&quot;&gt;&#10;          &lt;mxGeometry x=&quot;-0.8092499999999916&quot; y=&quot;-7.0014999999999645&quot; width=&quot;11.811&quot; height=&quot;11.81&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-39&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;endSize=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;sZqi1VJqUI8cdP5_EVks-41&quot; target=&quot;rSL1Pw579-r8zTYxRfB7-33&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-41&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#0050ef;strokeColor=#001DBC;fontColor=#ffffff;snapToPoint=1;direction=east;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;403.19075000000004&quot; y=&quot;331.18850000000003&quot; width=&quot;11.811&quot; height=&quot;11.81&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-58&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;endSize=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;sZqi1VJqUI8cdP5_EVks-42&quot; target=&quot;sZqi1VJqUI8cdP5_EVks-41&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-150&quot; y=&quot;355.8837209302326&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-42&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#0050ef;strokeColor=#001DBC;fontColor=#ffffff;snapToPoint=1;direction=east;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;296.09075&quot; y=&quot;331.18850000000003&quot; width=&quot;11.811&quot; height=&quot;11.81&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-57&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;endSize=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;sZqi1VJqUI8cdP5_EVks-43&quot; target=&quot;sZqi1VJqUI8cdP5_EVks-42&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-190&quot; y=&quot;355.8837209302326&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-43&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#0050ef;strokeColor=#001DBC;fontColor=#ffffff;snapToPoint=1;direction=east;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;202.09075&quot; y=&quot;331.18850000000003&quot; width=&quot;11.811&quot; height=&quot;11.81&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-54&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;endSize=2;&quot; parent=&quot;1&quot; source=&quot;sZqi1VJqUI8cdP5_EVks-46&quot; target=&quot;sZqi1VJqUI8cdP5_EVks-45&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-46&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#0050ef;strokeColor=#001DBC;fontColor=#ffffff;snapToPoint=1;direction=east;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;402.19075000000004&quot; y=&quot;428.34850000000006&quot; width=&quot;11.811&quot; height=&quot;11.81&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-53&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;endSize=2;&quot; parent=&quot;1&quot; source=&quot;sZqi1VJqUI8cdP5_EVks-47&quot; target=&quot;sZqi1VJqUI8cdP5_EVks-46&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-34&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;startSize=1;endSize=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;sZqi1VJqUI8cdP5_EVks-48&quot; target=&quot;rSL1Pw579-r8zTYxRfB7-27&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-48&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#0050ef;strokeColor=#001DBC;fontColor=#ffffff;snapToPoint=1;direction=east;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;202.09075&quot; y=&quot;524.9985&quot; width=&quot;11.811&quot; height=&quot;11.81&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;sZqi1VJqUI8cdP5_EVks-51&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;endSize=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;&quot; parent=&quot;1&quot; source=&quot;sZqi1VJqUI8cdP5_EVks-49&quot; target=&quot;sZqi1VJqUI8cdP5_EVks-48&quot; edge=&quot;1&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#10;            &lt;mxPoint x=&quot;-190&quot; y=&quot;435.79999999999995&quot; as=&quot;targetPoint&quot; /&gt;&#10;          &lt;/mxGeometry&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-1&quot; value=&quot;&quot; style=&quot;group&quot; vertex=&quot;1&quot; connectable=&quot;0&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;96&quot; y=&quot;231.5&quot; width=&quot;133&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-2&quot; value=&quot;&quot; style=&quot;rounded=0;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#36393d;opacity=50;rotation=90;&quot; vertex=&quot;1&quot; parent=&quot;rSL1Pw579-r8zTYxRfB7-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;43&quot; y=&quot;-20&quot; width=&quot;40&quot; height=&quot;100&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-31&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#0050ef;strokeColor=#001DBC;fontColor=#ffffff;snapToPoint=1;direction=east;&quot; vertex=&quot;1&quot; parent=&quot;rSL1Pw579-r8zTYxRfB7-1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;8.00075000000001&quot; y=&quot;4.4985000000000355&quot; width=&quot;11.811&quot; height=&quot;11.81&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-27&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#0050ef;strokeColor=#001DBC;fontColor=#ffffff;snapToPoint=1;direction=east;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;296.09075&quot; y=&quot;524.9985&quot; width=&quot;11.811&quot; height=&quot;11.81&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-42&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;endSize=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;rSL1Pw579-r8zTYxRfB7-28&quot; target=&quot;rSL1Pw579-r8zTYxRfB7-31&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-28&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#0050ef;strokeColor=#001DBC;fontColor=#ffffff;snapToPoint=1;direction=east;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;202.09075&quot; y=&quot;235.99850000000004&quot; width=&quot;11.811&quot; height=&quot;11.81&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-37&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;endSize=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;rSL1Pw579-r8zTYxRfB7-29&quot; target=&quot;rSL1Pw579-r8zTYxRfB7-30&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-29&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#0050ef;strokeColor=#001DBC;fontColor=#ffffff;snapToPoint=1;direction=east;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;103.09075000000001&quot; y=&quot;427.99850000000004&quot; width=&quot;11.811&quot; height=&quot;11.81&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-41&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;endSize=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;rSL1Pw579-r8zTYxRfB7-32&quot; target=&quot;rSL1Pw579-r8zTYxRfB7-28&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-32&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#0050ef;strokeColor=#001DBC;fontColor=#ffffff;snapToPoint=1;direction=east;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;296.09075&quot; y=&quot;235.99850000000004&quot; width=&quot;11.811&quot; height=&quot;11.81&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-40&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;endSize=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;rSL1Pw579-r8zTYxRfB7-33&quot; target=&quot;rSL1Pw579-r8zTYxRfB7-32&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-33&quot; value=&quot;&quot; style=&quot;ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#0050ef;strokeColor=#001DBC;fontColor=#ffffff;snapToPoint=1;direction=east;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#10;          &lt;mxGeometry x=&quot;403.19075&quot; y=&quot;235.99850000000004&quot; width=&quot;11.811&quot; height=&quot;11.81&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-36&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;endSize=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;sZqi1VJqUI8cdP5_EVks-44&quot; target=&quot;rSL1Pw579-r8zTYxRfB7-29&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;        &lt;mxCell id=&quot;rSL1Pw579-r8zTYxRfB7-38&quot; style=&quot;edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;endSize=2;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;rSL1Pw579-r8zTYxRfB7-30&quot; target=&quot;sZqi1VJqUI8cdP5_EVks-43&quot;&gt;&#10;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#10;        &lt;/mxCell&gt;&#10;      &lt;/root&gt;&#10;    &lt;/mxGraphModel&gt;&#10;  &lt;/diagram&gt;&#10;&lt;/mxfile&gt;&#10;" style="background-color: rgb(255, 255, 255);"><defs/><g><rect x="209" y="191" width="300" height="290" fill-opacity="0.5" fill="#dae8fc" stroke="#6c8ebf" stroke-opacity="0.5" pointer-events="all"/><rect x="0" y="680" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 695px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><b style="font-size: 20px;">Origin<br />(0,0)</b></div></div></div></foreignObject><text x="30" y="699" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Origin...</text></switch></g><rect x="710" y="10" width="180" height="80" fill="none" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 172px; height: 1px; padding-top: 0px; margin-left: 715px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left; max-height: 90px; overflow: hidden;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><h1>Legend</h1><p><br /></p></div></div></div></foreignObject><text x="715" y="12" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">Legend&#xa;</text></switch></g><rect x="719.47" y="54.8" width="28.42" height="8" fill="#f5f5f5" stroke="#666666" pointer-events="all"/><rect x="719.47" y="69.2" width="28.42" height="8" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><rect x="747.89" y="46.8" width="85.26" height="24" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 83px; height: 1px; padding-top: 59px; margin-left: 749px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Object Polygon</div></div></div></foreignObject><text x="791" y="62" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Object Polygon</text></switch></g><rect x="747.89" y="61.2" width="85.26" height="24" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 83px; height: 1px; padding-top: 73px; margin-left: 749px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Used Bed Area</div></div></div></foreignObject><text x="791" y="77" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Used Bed Area</text></switch></g><rect x="30" y="10" width="660" height="660" fill="none" stroke="#000000" stroke-width="4" pointer-events="all"/><rect x="209" y="381.5" width="40" height="100" fill-opacity="0.5" fill="#eeeeee" stroke="#36393d" stroke-opacity="0.5" pointer-events="all"/><rect x="408" y="380.5" width="100" height="100" fill-opacity="0.5" fill="#eeeeee" stroke="#36393d" stroke-opacity="0.5" pointer-events="all"/><path d="M 408 480.5 L 490 480.5 L 498.83 480.07" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 501.07 479.96 L 498.15 481.6 L 498.83 480.07 L 498 478.61 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="401" y="175.5" width="40" height="100" fill-opacity="0.5" fill="#eeeeee" stroke="#36393d" stroke-opacity="0.5" transform="rotate(90,421,225.5)" pointer-events="all"/><rect x="209" y="257.5" width="40" height="100" fill-opacity="0.5" fill="#eeeeee" stroke="#36393d" stroke-opacity="0.5" pointer-events="all"/><rect x="281" y="273.5" width="100" height="100" fill-opacity="0.5" fill="#eeeeee" stroke="#36393d" stroke-opacity="0.5" pointer-events="all"/><ellipse cx="508.1" cy="479.9" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><ellipse cx="402.91" cy="383.9" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><ellipse cx="209.1" cy="480.9" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><ellipse cx="308.1" cy="383.9" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 397 383.9 L 317.37 383.9" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 315.12 383.9 L 318.12 382.4 L 317.37 383.9 L 318.12 385.4 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="209.1" cy="287.4" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 509.1 281.19 L 509.1 201.18" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 509.1 198.93 L 510.6 201.93 L 509.1 201.18 L 507.6 201.93 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="509.1" cy="287.09" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 407.9 287.09 L 499.82 287.09" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 502.07 287.09 L 499.07 288.59 L 499.82 287.09 L 499.07 285.59 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="402" cy="287.09" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 313.9 287.09 L 392.72 287.09" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 394.97 287.09 L 391.97 288.59 L 392.72 287.09 L 391.97 285.59 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="308" cy="287.09" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 502.19 384.25 L 455.5 384.3 L 412.18 383.93" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 409.93 383.91 L 412.94 382.44 L 412.18 383.93 L 412.92 385.44 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="508.1" cy="384.25" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 508.1 474 L 508.1 393.53" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 508.1 391.28 L 509.6 394.28 L 508.1 393.53 L 506.6 394.28 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 313.9 480.9 L 392.72 480.9" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 394.97 480.9 L 391.97 482.4 L 392.72 480.9 L 391.97 479.4 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="308" cy="480.9" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 215 480.9 L 298.72 480.9" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 300.97 480.9 L 297.97 482.4 L 298.72 480.9 L 297.97 479.4 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="239" y="161.5" width="40" height="100" fill-opacity="0.5" fill="#eeeeee" stroke="#36393d" stroke-opacity="0.5" transform="rotate(90,259,211.5)" pointer-events="all"/><ellipse cx="209.91" cy="191.9" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><ellipse cx="402" cy="480.9" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 302.09 191.9 L 219.18 191.9" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 216.93 191.9 L 219.93 190.4 L 219.18 191.9 L 219.93 193.4 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="308" cy="191.9" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 209 378 L 209 335.7 L 209.09 296.68" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 209.09 294.43 L 210.59 297.43 L 209.09 296.68 L 207.59 297.42 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="209" cy="383.9" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 396.09 191.9 L 317.27 191.9" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 315.02 191.9 L 318.02 190.4 L 317.27 191.9 L 318.02 193.4 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="402" cy="191.9" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 503.19 191.9 L 411.27 191.9" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 409.02 191.9 L 412.02 190.4 L 411.27 191.9 L 412.02 193.4 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><ellipse cx="509.1" cy="191.9" rx="5.9055" ry="5.905" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><path d="M 302.19 383.9 L 218.27 383.9" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 216.02 383.9 L 219.02 382.4 L 218.27 383.9 L 219.02 385.4 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 215 287.4 L 258.6 287.4 L 298.72 287.12" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 300.97 287.1 L 297.98 288.62 L 298.72 287.12 L 297.96 285.62 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>