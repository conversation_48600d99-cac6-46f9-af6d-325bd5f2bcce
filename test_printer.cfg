# 测试配置文件 - 用于验证Klipper主机和MCU通讯
# 这是一个最小化配置，仅用于测试连接

[mcu]
# RP2040 MCU配置
serial: /dev/serial/by-id/usb-Klipper_rp2040_E66390C6831D3F26-if00

[printer]
# 基本打印机设置
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

# 最小步进电机配置（用于测试，不会实际移动）
[stepper_x]
step_pin: gpio2
dir_pin: gpio3
enable_pin: !gpio4
microsteps: 16
rotation_distance: 40
endstop_pin: gpio5
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: gpio6
dir_pin: gpio7
enable_pin: !gpio8
microsteps: 16
rotation_distance: 40
endstop_pin: gpio9
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: gpio10
dir_pin: gpio11
enable_pin: !gpio12
microsteps: 16
rotation_distance: 8
endstop_pin: gpio13
position_endstop: 0
position_max: 200
homing_speed: 5

[extruder]
step_pin: gpio14
dir_pin: gpio15
enable_pin: !gpio16
microsteps: 16
rotation_distance: 33.5
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: gpio17
sensor_type: EPCOS 100K B57560G104F
sensor_pin: gpio26
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: gpio18
sensor_type: EPCOS 100K B57560G104F
sensor_pin: gpio27
control: watermark
min_temp: 0
max_temp: 130
