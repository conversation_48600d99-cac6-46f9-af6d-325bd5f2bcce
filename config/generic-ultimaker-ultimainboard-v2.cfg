# This file contains common pin mappings for Ultimaker UltiMainboard v2
# boards. To use this config, the firmware should be compiled for the
# AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PA3
dir_pin: !PA1
enable_pin: !PA5
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PA0
position_endstop: 0
position_max: 230
homing_speed: 50.0

[stepper_y]
step_pin: PC5
dir_pin: PC4
enable_pin: !PC6
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PA4
position_endstop: 225
position_max: 225
homing_speed: 50.0

[stepper_z]
step_pin: PC2
dir_pin: !PC1
enable_pin: !PC3
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PA7
position_endstop: 215
position_max: 215
homing_speed: 20.0

[extruder]
step_pin: PL7
dir_pin: PL6
enable_pin: !PC0
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 2.850
heater_pin: PE4
sensor_type: PT100 INA826
sensor_pin: PK0
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 275

# Dual extruder support.
#[extruder1]
#step_pin: PL0
#dir_pin: PL2
#enable_pin: !PL1
#microsteps: 16
#rotation_distance: 33.500
#nozzle_diameter: 0.400
#filament_diameter: 2.850
#heater_pin: PE5
#sensor_type: PT100 INA826
#sensor_pin: PK1
#control: pid
#pid_Kp: 22.2
#pid_Ki: 1.08
#pid_Kd: 114
#min_temp: 0
#max_temp: 275

[heater_bed]
heater_pin: PG5
sensor_type: PT100 INA826
sensor_pin: PK2
control: watermark
min_temp: 0
max_temp: 100

[fan]
pin: PH4

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 500
max_accel: 3000
max_z_velocity: 25
max_z_accel: 30

[output_pin case_light]
pin: PH5
value: 1.0

# Motor current settings.
[output_pin stepper_xy_current]
pin: PL5
pwm: True
scale: 2.000
# Max power setting.
cycle_time: .000030
hardware_pwm: True
value: 1.200
# Power adjustment setting.

[output_pin stepper_z_current]
pin: PL4
pwm: True
scale: 2.000
cycle_time: .000030
hardware_pwm: True
value: 1.200

[output_pin stepper_e_current]
pin: PL3
pwm: True
scale: 2.000
cycle_time: .000030
hardware_pwm: True
value: 1.250
