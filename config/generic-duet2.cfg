# This file contains common pin mappings for Duet2 Eth/Wifi boards. To
# use this config, the firmware should be compiled for the SAM4E8E.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PD6
dir_pin: PD11
enable_pin: !PC6
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC14
position_endstop: 0
position_max: 250

[tmc2660 stepper_x]
cs_pin: PD14
spi_bus: usart1
run_current: 1.000
sense_resistor: 0.051

[stepper_y]
step_pin: PD7
dir_pin: !PD12
enable_pin: !PC6
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA2
position_endstop: 0
position_max: 210

[tmc2660 stepper_y]
cs_pin: PC9
spi_bus: usart1
run_current: 1.000
sense_resistor: 0.051

[stepper_z]
step_pin: PD8
dir_pin: PD13
enable_pin: !PC6
microsteps: 16
rotation_distance: 8
endstop_pin: ^PD29
#endstop_pin: PD10  # E0 endstop
#endstop_pin: PC16  # E1 endstop
position_endstop: 0.5
position_max: 200

[tmc2660 stepper_z]
cs_pin: PC10
spi_bus: usart1
run_current: 1.000
sense_resistor: 0.051

[extruder]
step_pin: PD5
dir_pin: PA1
enable_pin: !PC6
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: !PA20
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC15
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[tmc2660 extruder]
cs_pin: PC17
spi_bus: usart1
run_current: 1.000
sense_resistor: 0.051

#[extruder1]
#step_pin: PD4
#dir_pin: PD9
#enable_pin: !PC6
#heater_pin: !PA16
#sensor_pin: PC12
#...
#[tmc2660 extruder1]
#cs_pin: PC25
#spi_bus: usart1
#sense_resistor: 0.051
#...

[heater_bed]
heater_pin: !PA19
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC13
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PC23  # FAN0

#[heater_fan heatbreak_cooling_fan]
#pin: PC26  # FAN1

#[heater_fan board_cooling_fan]
#pin: PA0  # FAN2

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100
