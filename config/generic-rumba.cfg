# This file contains common pin mappings for RUMBA boards.  To use
# this config, the firmware should be compiled for the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PH0
dir_pin: PH1
enable_pin: !PL1
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC0
#endstop_pin: ^PC1
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PF0
dir_pin: !PL2
enable_pin: !PF1
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC2
#endstop_pin: ^PC3
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PF3
dir_pin: PF2
enable_pin: !PK0
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC4
#endstop_pin: ^PC5
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PA1
dir_pin: PA0
enable_pin: !PA2
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PE4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK7
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

#[extruder1]
#step_pin: PA4
#dir_pin: PA3
#enable_pin: !PA5
#heater_pin: PE5
#sensor_pin: PK6
#...

#[extruder2]
#step_pin: PA7
#dir_pin: PA6
#enable_pin: !PG2
#heater_pin: PH3
#sensor_pin: PK5
#...

[heater_bed]
heater_pin: PH6
sensor_type: Generic 3950
sensor_pin: PK3
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PH4

#[heater_fan fan1]
#pin: PH5

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

# "RepRapDiscount 2004 Smart Controller" type displays
#[display]
#lcd_type: hd44780
#rs_pin: PD2
#e_pin: PL7
#d4_pin: PD3
#d5_pin: PD7
#d6_pin: PG0
#d7_pin: PG1
#encoder_pins: ^PB5, ^PB6
#click_pin: ^!PL6

# "RepRapDiscount 128x64 Full Graphic Smart Controller" type displays
#[display]
#lcd_type: st7920
#cs_pin: PD2
#sclk_pin: PD3
#sid_pin: PL7
#encoder_pins: ^PB5, ^PB6
#click_pin: ^!PL6
