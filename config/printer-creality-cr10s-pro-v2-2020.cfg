# This file contains pin mappings for the Creality CR-10S Pro V2. To use
# this config, the firmware should be compiled for the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

## General Config
[mcu]
serial: /dev/serial/by-id/<YOUR_USB_ID>

[printer]
kinematics: cartesian
max_velocity: 200
max_accel: 1500
max_z_velocity: 10
max_z_accel: 100

## Stepper Motors and Extruder
[stepper_x]
step_pin: PF0 #ar54
dir_pin: PF1 #ar55
enable_pin: !PD7 #!ar38
rotation_distance: 40
microsteps: 16
full_steps_per_rotation: 200
endstop_pin: ^PE5 #^ar3
position_endstop: 0
position_min: 0
position_max: 300
homing_speed: 50
homing_retract_dist: 5

[stepper_y]
step_pin: PF6 #ar60
dir_pin: !PF7 #ar61
enable_pin: !PF2 #!ar56
rotation_distance: 40
microsteps: 16
full_steps_per_rotation: 200
endstop_pin: ^PJ1 #^ar14
position_endstop: 0
position_min: 0
position_max: 310
homing_speed: 50
homing_retract_dist: 5

[stepper_z]
step_pin: PL3 #ar46
dir_pin: !PL1 #!ar48
enable_pin: !PK0 #!ar62
rotation_distance: 8
microsteps: 16
full_steps_per_rotation: 200
endstop_pin: probe:z_virtual_endstop
position_min: -3
position_max: 363 # you can go higher but then the cables crimp

[extruder]
step_pin: PA4 # ar26
dir_pin: PA6 # !ar28
enable_pin: !PA2 # !ar24
rotation_distance: 22.900
microsteps: 16
full_steps_per_rotation: 200
nozzle_diameter: 0.400
filament_diameter: 1.750
max_extrude_only_distance: 500.0
max_extrude_only_velocity: 70.0
max_extrude_only_accel: 1000.0
heater_pin: PB4 #ar10
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5 #analog13
control: pid #calibrated in Klipper, you will need to run this for your machine
pid_kp: 28.359
pid_ki: 1.616
pid_kd: 124.426
min_extrude_temp: 170
min_temp: 5
max_temp: 275

[fan]
pin: PH6 #ar9

[heater_bed]
heater_pin: PH5 #ar8
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6 #analog14
control: pid #calibrated in Klipper, you will need to run this for your machine
pid_kp: 70.936
pid_ki: 1.151
pid_kd: 1093.298
min_temp: 5
max_temp: 140

## BLTouch and Safe Z Settings
[bltouch]
sensor_pin: ^PD2
control_pin: PB5
stow_on_each_sample: False # Be careful
probe_with_touch_mode: True
x_offset: -27
y_offset: -2
z_offset: 2 # you will need to calibrate this in Klipper
speed: 4.0
samples: 2
sample_retract_dist: 3.0

[safe_z_home]
home_xy_position: 177,155
speed: 80.0
z_hop: 10.0
z_hop_speed: 5.0

[bed_mesh]
speed: 120
horizontal_move_z: 5
mesh_min: 5, 5
mesh_max: 268, 305
probe_count: 5,5
fade_start: 1
fade_end: 10

[filament_switch_sensor e0_sensor]
switch_pin: PE4 #ar2
pause_on_runout: False
runout_gcode:
  PAUSE_PARK

## Calibrating the Bed
# this is for the bltouch to calibrate the bed
[screws_tilt_adjust]
screw1: 40,40
screw1_name: front left screw
screw2: 295,40
screw2_name: front right screw
screw3: 295,280
screw3_name: rear right screw
screw4: 40,280
screw4_name: rear left screw
speed: 50
horizontal_move_z: 10
screw_thread: CW-M3

[bed_screws]
screw1: 13,38
screw1_name: front left screw
screw2: 268,38
screw2_name: front right screw
screw3: 268,278
screw3_name: rear right screw
screw4: 13,38
screw4_name: rear left screw
horizontal_move_z: 5

[pause_resume]
