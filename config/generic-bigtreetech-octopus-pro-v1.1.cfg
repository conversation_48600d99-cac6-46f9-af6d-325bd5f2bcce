# This file contains common pin mappings for the BigTreeTech Octopus
# Pro v1.1 board.

# Important! Do not use this config with an Octopus Pro v1.0 board nor
# non-Pro board.

# To use this config, during "make menuconfig", select "Enable
# low-level configuration options", select the STM32H723
# micro-controller, select a "128KiB bootloader", and select a "25Mhz
# crystal".

# See docs/Config_Reference.md for a description of parameters.

# Driver0
[stepper_x]
step_pin: PF13
dir_pin: PF12
enable_pin: !PF14
microsteps: 16
rotation_distance: 40
endstop_pin: PG6
position_endstop: 0
position_max: 200
homing_speed: 50

# Driver1
[stepper_y]
step_pin: PG0
dir_pin: PG1
enable_pin: !PF15
microsteps: 16
rotation_distance: 40
endstop_pin: PG9
position_endstop: 0
position_max: 200
homing_speed: 50

# Driver2
[stepper_z]
step_pin: PF11
dir_pin: PG3
enable_pin: !PG5
microsteps: 16
rotation_distance: 8
endstop_pin: PG10
position_endstop: 0.5
position_max: 200

# Driver3
# The Octopus only has 4 heater outputs which leaves an extra stepper
# This can be used for a second Z stepper, dual_carriage, extruder co-stepper,
# or other accesory such as an MMU
#[stepper_]
#step_pin: PG4
#dir_pin: PC1
#enable_pin: !PA2
#endstop_pin: PG11
#...

# Driver4
[extruder]
step_pin: PF9
dir_pin: PF10
enable_pin: !PG2
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA0 # HE0
sensor_pin:  PF4 # T0
sensor_type: EPCOS 100K B57560G104F
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

#[filament_switch_sensor material_0]
#switch_pin: PG12

# Driver5
#[extruder1]
#step_pin: PC13
#dir_pin: PF0
#enable_pin: !PF1
#heater_pin: PA3 # HE1
#sensor_pin: PF5 # T1
#...

#[filament_switch_sensor material_1]
#switch_pin: PG13

# Driver6
#[extruder2]
#step_pin: PE2
#dir_pin: PE3
#enable_pin: !PD4
#heater_pin: PB0 # HE2
#sensor_pin: PF6 # T2
#...

#[filament_switch_sensor material_2]
#switch_pin: PG14

# Driver7
#[extruder3]
#step_pin: PE6
#dir_pin: PA14
#enable_pin: !PE0
#heater_pin: PB11 # HE3
#sensor_pin: PF7 # T3
#...

#[filament_switch_sensor material_3]
#switch_pin: PG15

[heater_bed]
heater_pin: PA1
sensor_pin: PF3 # TB
sensor_type: ATC Semitec 104GT-2
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PA8

#[heater_fan fan1]
#pin: PE5

#[heater_fan fan2]
#pin: PD12

#[heater_fan fan3]
#pin: PD13

#[heater_fan fan4]
#pin: PD14

#[controller_fan fan5]
#pin: PD15

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00
# CAN bus is also available on this board

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

########################################
# TMC2209 configuration
########################################

#[tmc2209 stepper_x]
#uart_pin: PC4
##diag_pin: PG6
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2209 stepper_y]
#uart_pin: PD11
##diag_pin: PG9
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2209 stepper_z]
#uart_pin: PC6
##diag_pin: PG10
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc2209 stepper_]
#uart_pin: PC7
##diag_pin: PG11
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc2209 extruder]
#uart_pin: PF2
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2209 extruder1]
#uart_pin: PE4
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2209 extruder2]
#uart_pin: PE1
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2209 extruder3]
#uart_pin: PD3
#run_current: 0.800
#stealthchop_threshold: 999999

########################################
# TMC2130 configuration
########################################

#[tmc2130 stepper_x]
#cs_pin: PC4
#spi_bus: spi1
##diag1_pin: PG6
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_y]
#cs_pin: PD11
#spi_bus: spi1
##diag1_pin: PG9
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_z]
#cs_pin: PC6
#spi_bus: spi1
##diag1_pin: PG10
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc2130 stepper_]
#cs_pin: PC7
#spi_bus: spi1
##diag1_pin: PG11
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder]
#cs_pin: PF2
#spi_bus: spi1
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder1]
#cs_pin: PE4
#spi_bus: spi1
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder2]
#cs_pin: PE1
#spi_bus: spi1
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder3]
#cs_pin: PD3
#spi_bus: spi1
#run_current: 0.800
#stealthchop_threshold: 999999

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PE8, EXP1_2=PE7,
    EXP1_3=PE9, EXP1_4=PE10,
    EXP1_5=PE12, EXP1_6=PE13,    # Slot in the socket on this side
    EXP1_7=PE14, EXP1_8=PE15,
    EXP1_9=<GND>, EXP1_10=<5V>,

    # EXP2 header
    EXP2_1=PA6, EXP2_2=PA5,
    EXP2_3=PB1, EXP2_4=PA4,
    EXP2_5=PB2, EXP2_6=PA7,      # Slot in the socket on this side
    EXP2_7=PC15, EXP2_8=<RST>,
    EXP2_9=<GND>, EXP2_10=PC5

# See the sample-lcd.cfg file for definitions of common LCD displays.

# A [probe] section can be defined instead with a pin: setting identical
# to the sensor_pin: for a bltouch
#[bltouch]
#sensor_pin: PB7
#control_pin: PB6
#z_offset: 0

#[neopixel my_neopixel]
#pin: PB10
