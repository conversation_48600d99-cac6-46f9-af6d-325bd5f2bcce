# This file contains common pin mappings for the 2018 Creality
# Ender 3. To use this config, the firmware should be compiled for the
# AVR atmega1284p.

# Note, a number of Melzi boards are shipped with a bootloader that
# requires the following command to flash the board:
#  avrdude -p atmega1284p -c arduino -b 57600 -P /dev/ttyUSB0 -U out/klipper.elf.hex
# If the above command does not work and "make flash" does not work
# then one may need to flash a bootloader to the board - see the
# Klipper docs/Bootloaders.md file for more information.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PD7
dir_pin: !PC5
enable_pin: !PD6
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC2
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_y]
step_pin: PC6
dir_pin: !PC7
enable_pin: !PD6
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC3
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_z]
step_pin: PB3
dir_pin: PB2
enable_pin: !PA5
microsteps: 16
rotation_distance: 8
endstop_pin: ^PC4
position_endstop: 0.0
position_max: 250

[extruder]
max_extrude_only_distance: 100.0
step_pin: PB1
dir_pin: !PB0
enable_pin: !PD6
microsteps: 16
rotation_distance: 33.683
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PD5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA7
control: pid
# tuned for stock hardware with 200 degree Celsius target
pid_Kp: 21.527
pid_Ki: 1.063
pid_Kd: 108.982
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PD4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA6
control: pid
# tuned for stock hardware with 50 degree Celsius target
pid_Kp: 54.027
pid_Ki: 0.770
pid_Kd: 948.182
min_temp: 0
max_temp: 130

[fan]
pin: PB4

[mcu]
serial: /dev/ttyUSB0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[bed_screws]
screw1: 30.5, 37
screw2: 30.5, 207
screw3: 204.5, 207
screw4: 204.5, 37

[display]
lcd_type: st7920
cs_pin: PA3
sclk_pin: PA1
sid_pin: PC1
encoder_pins: ^PD2, ^PD3
click_pin: ^!PC0

# The print bed can move so far to the front, that the nozzle can reach the
# plastic cover of the print bed heater cable (only when the bed is moved by
# hand). By homing the Y axis before the X axis, it is ensured the nozzle will
# not melt through the plastic part.
# BEWARE: You will lose the ability to home axes individually. The printer will
# always home all axes for every G28 command.
#[homing_override]
#gcode:
# G28 Y0
# G28 X0
# G28 Z0
