# This file contains common pin mappings for the Alfawise U30 printer.
# To use this config, the firmware should be compiled for the
# STM32F103. When running "make menuconfig", enable "extra low-level
# configuration setup", select the 64KiB bootloader, serial (on USART1
# PA10/PA9) communication, and set "GPIO pins to set at
# micro-controller startup" to "!PC4,!PD12".

# The "make flash" command does not work on the Alfawise U30. Instead,
# after running "make", copy the generated "out/klipper.bin" file to a
# file named "project.bin" on an SD card and then restart the Alfawise
# with that SD card.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PB4
dir_pin: !PB3
enable_pin: !PB5
microsteps: 16
rotation_distance: 40
endstop_pin: !PC1
position_endstop: 0
position_max: 230
homing_speed: 50

[stepper_y]
step_pin: PB7
dir_pin: PB6
enable_pin: !PB8
microsteps: 16
rotation_distance: 40
endstop_pin: !PC15
position_endstop: 0
position_max: 222
homing_speed: 50

[stepper_z]
step_pin: PE0
dir_pin: !PB9
enable_pin: !PE1
microsteps: 16
rotation_distance: 8
endstop_pin: !PE6
position_endstop: 0.0
position_max: 250

[extruder]
step_pin: PE3
dir_pin: PE2
enable_pin: !PE4
microsteps: 16
rotation_distance: 33.683
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PD3
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA0
control: pid
pid_Kp: 21.527
pid_Ki: 1.063
pid_Kd: 108.982
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PA8
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA1
control: pid
pid_Kp: 54.027
pid_Ki: 0.770
pid_Kd: 948.182
min_temp: 0
max_temp: 130

[fan]
pin: PA15

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB2.0-Serial-if00-port0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[static_digital_output reset_display]
pins: !PC4, !PD12
