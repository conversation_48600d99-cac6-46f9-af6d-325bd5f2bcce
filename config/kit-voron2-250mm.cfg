# VORON2 250mm config

# This is a base printer.cfg file for the VORON2 printer and matches the manual/build guide exactly
# for controllers used (dual RAMPS) and pin layout for all connected components.
# Created by "<PERSON><PERSON>" with help from the VORON community.

# For other build sizes, controllers, or non-standard pin connections, please see
# https://github.com/mzbotreprap/VORON/tree/master/Firmware/Klipper/Voron_2.1/Klipper/Configurations
# for other example Klipper configs created by the VORON community.

# This file is only an example - be sure to review and update it
# according to the specifics of your printer.

# See docs/Config_Reference.md for a description of parameters.

# *** THINGS TO CHANGE/CHECK: ***
# Arduino paths                         [mcu] section
# Thermistor types                      [extruder] and [heater_bed] sections - See 'sensor types' list at end of file
# FSR switch (z endstop) location       [homing_override] section
# FSR switch (z endstop) offset for Z0  [stepper_z] section
# Probe points                          [quad_gantry_level] section
# Min & Max gantry corner positions     [quad_gantry_level] section
# PID tune                              [extruder] and [heater_bed] sections
# Fine tune E steps                     [extruder] section

[mcu]
# Mcu for X/Y/E steppers
serial: /dev/serial/by-id/**INSERT_YOUR_ARDUINO_DEFINITION_HERE**
#   Obtain definition by "ls -l /dev/serial/by-id/"
restart_method: arduino

[mcu z]
# Mcu for Z steppers
serial: /dev/serial/by-id/**INSERT_YOUR_ARDUINO_DEFINITION_HERE**
#   Obtain definition by "ls -l /dev/serial/by-id/"
restart_method: arduino

[printer]
kinematics: corexy
max_velocity: 350
max_accel: 3000
max_z_velocity: 50
max_z_accel: 350

[stepper_x]
# B Stepper
step_pin: PF0
dir_pin: !PF1
enable_pin: !PD7
#   X on mcu_xye
microsteps: 16
rotation_distance: 40
#   80 steps per mm - 1.8 deg - 1/16 microstepping
endstop_pin: ^PE4
#   X_MAX on mcu_xye
position_min: 0
position_endstop: 250
position_max: 250
homing_speed: 100
homing_retract_dist: 5

[stepper_y]
# A Stepper
step_pin: PF6
dir_pin: !PF7
enable_pin: !PF2
#   Y on mcu_xye
microsteps: 16
rotation_distance: 40
#   80 steps per mm - 1.8 deg - 1/16 microstepping
endstop_pin: ^PJ0
#   Y_MAX on mcu_xye
position_min: 0
position_endstop: 250
position_max: 250
homing_speed: 100
homing_retract_dist: 5

[stepper_z]
# Z0 Stepper - Front Left
step_pin: z:PF0
dir_pin: !z:PF1
enable_pin: !z:PD7
#   X on mcu_z
microsteps: 16
rotation_distance: 8
#   400 steps per mm - 1.8 deg - 1/16 microstepping
endstop_pin: ^!z:PD3
#   Z_MIN on mcu_z
position_endstop: -0.2
#   Offset (in mm) for nozzle to bed off z switch
position_max: 250
position_min: -2
#   Set to -2 to allow room for squaring gantry with quad_gantry_level
homing_speed: 15.0
second_homing_speed: 3.0
homing_retract_dist: 3.0

[stepper_z1]
# Z1 Stepper - Rear Left
step_pin: z:PF6
dir_pin: z:PF7
enable_pin: !z:PF2
#   Y on mcu_z
microsteps: 16
rotation_distance: 8
#   400 steps per mm - 1.8 deg - 1/16 microstepping

[stepper_z2]
# Z2 Stepper - Rear Right
step_pin: z:PL3
dir_pin: !z:PL1
enable_pin: !z:PK0
#   Z on mcu_z
microsteps: 16
rotation_distance: 8
#   400 steps per mm - 1.8 deg - 1/16 microstepping

[stepper_z3]
# Z3 Stepper - Front Right
step_pin: z:PA4
dir_pin: z:PA6
enable_pin: !z:PA2
#   E0 on mcu_z
microsteps: 16
rotation_distance: 8
#   400 steps per mm - 1.8 deg - 1/16 microstepping

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
#   E0 on mcu_xye
microsteps: 16
rotation_distance: 5.76576
#   555 steps per mm - 1.8 deg - 1/16 microstepping (Mobius2)
nozzle_diameter: 0.400
filament_diameter: 1.750
max_extrude_only_distance: 780.0
#   This is set high to allow the load/unload filament macros to run
heater_pin: PB4
#   D10 on mcu_xye
max_power: 1.0
sensor_type: Generic 3950
sensor_pin: PK5
#   T0 on mcu_xye
smooth_time: 3.0
control: pid
pid_Kp: 16.430
pid_Ki: 0.755
pid_Kd: 89.337
min_extrude_temp: 170
min_temp: 0
max_temp: 270

[probe]
# Inductive Probe
pin: ^z:PD2
#   Z_MAX on mcu_z
x_offset: 0.0
y_offset: 25.0
z_offset: 0.00
speed: 2.0
samples: 4
#   Number of times to probe a point
sample_retract_dist: 6.0
#   How far to retract (in mm) from the probe point for multi-probe samples

[fan]
# Print cooling fan
pin: PH6
#   D9 on mcu_xye
kick_start_time: 0.500

[heater_fan hotend_fan]
# Hotend fan
pin: z:PH6
#   D9 on mcu_z
kick_start_time: 0.500
heater: extruder
heater_temp: 50.0

[heater_fan controller_fan]
# Controller fan
pin: z:PB4
#   D10 on mcu_z
kick_start_time: 0.500
heater: heater_bed
heater_temp: 45.0

[heater_fan exhaust_fan]
# Exhaust fan
pin: z:PH5
#   D8 on mcu_z
kick_start_time: 0.500
heater: heater_bed
heater_temp: 60.0

[heater_bed]
heater_pin: z:PB5
#   D11 (servo) on mcu_z
sensor_type: NTC 100K MGB18-104F39050L32
sensor_pin: z:PK7
#   T2 on mcu_z
smooth_time: 3.0
max_power: 0.75
control: pid
pid_Kp: 47.690
pid_Ki: 1.556
pid_Kd: 365.338
min_temp: 0
max_temp: 110

[homing_override]
axes: z
set_position_z: 0
gcode:
   G90
   G0 Z5 F600
   G28 X Y
   G0 X179 Y249.5 F3600
#   XY Location of the FSR Switch
   G28 Z
   G0 Z10 F1800
   G0 X125 Y125 Z20 F3600

[quad_gantry_level]
#   Use QUAD_GANTRY_LEVEL to level a gantry.
gantry_corners:
   -55, -7
   305, 320
#   Min & Max gantry corners - measure from nozzle at MIN (0,0) and MAX (250,250) to respective belt positions
points:
   25, 0
   25, 200
   225, 200
   225, 0
#   Probe points
speed: 200
horizontal_move_z: 6

[display]
# RepRapDiscount 128x64 Full Graphic Smart Controller
lcd_type: st7920
cs_pin: z:PH1
sclk_pin: z:PA1
sid_pin: z:PH0
#   LCD connector on mcu_z
menu_timeout: 40
encoder_pins: ^z:PC4, ^z:PC6
click_pin: ^!z:PC2
kill_pin: ^!z:PG0


###   Macros   ###

[gcode_macro G32]
gcode:
    G28
    QUAD_GANTRY_LEVEL
    QUAD_GANTRY_LEVEL
    G0 X125 Y125 Z20 F6000

[gcode_macro PRINT_START]
#   Use PRINT_START for the slicer starting script - PLEASE CUSTOMISE THE SCRIPT FOR YOUR SLICER OF CHOICE
gcode:
    M117 Homing...                 ; display message
    G28                            ; home all axes
    G1 Z20 F3000                   ; move nozzle away from bed
    M117 Preheat (Print)           ; display message
    M104 S0                        ; turn off hotend while waiting for bed to get to temp

[gcode_macro PRINT_END]
#   Use PRINT_END for the slicer ending script - PLEASE CUSTOMISE THE SCRIPT FOR YOUR SLICER OF CHOICE
gcode:
    M400                           ; wait for buffer to clear
    G92 E0                         ; zero the extruder
    G1 E-4.0 F3600                 ; retract
    G91                            ; relative positioning
    G0 Z1.00 X20.0 Y20.0 F20000    ; move nozzle to remove stringing
    M104 S0                        ; turn off hotend
    M140 S0                        ; turn off bed
    M106 S0                        ; turn off fan
    G1 Z20 F3000                   ; move nozzle up 20mm
    G90                            ; absolute positioning
    G0  X125 Y245 F3600            ; park nozzle at rear
    M117 Finished!                 ; display message

[gcode_macro UNLOAD_FILAMENT]
gcode:
    M83
    G1 E10 F300
    G1 E-780 F1800
    M82

[gcode_macro LOAD_FILAMENT]
gcode:
    M83
    G1 E750 F1800
    G1 E30 F300
    G1 E15 F150
    M82
