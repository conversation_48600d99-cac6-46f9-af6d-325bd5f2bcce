# This file contains common pin mappings for the 2020 Creality CR-10
# V3. The mainboard is a Creality 3D v2.5.2 (8-bit mainboard with
# ATMega2560). To use this config, the firmware should be compiled for
# the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

# For better compatibility with GCodes generated for Marlin, you
# may wish to add the following section, if you have BLTouch:
#[gcode_macro G29]
#gcode:
#    BED_MESH_CALIBRATE

[stepper_x]
step_pin: PF0 #ar54
dir_pin: PF1 #ar55
enable_pin: !PD7 #!ar38
microsteps: 16
rotation_distance: 40
endstop_pin: ^PE5 #^ar3
position_endstop: 0
position_max: 300
homing_speed: 50

[stepper_y]
step_pin: PF6 #ar60
dir_pin: PF7 #ar61
enable_pin: !PF2 #!ar56
microsteps: 16
rotation_distance: 40
endstop_pin: ^PJ1 #^ar14
position_endstop: 0
position_max: 300
homing_speed: 50

[stepper_z]
step_pin: PL3 #ar46
dir_pin: !PL1 #!ar48
enable_pin: !PK0 #!ar62
microsteps: 16
rotation_distance: 8
position_max: 400
#Uncomment if you have a BL-Touch:
#position_min: -4
#endstop_pin: probe:z_virtual_endstop
#and comment the following lines:
position_endstop: 0.0
endstop_pin: ^PD3 #ar18

[safe_z_home]
home_xy_position: 104.25,147.6
speed: 80
z_hop: 10
z_hop_speed: 10

[extruder]
step_pin: PA4 # ar26
dir_pin: !PA6 # !ar28
enable_pin: !PA2 # !ar24
microsteps: 16
rotation_distance: 7.7201944 # 16 microsteps * 200 steps/rotation / steps/mm
#Correction formula is new_rotation_distance = old_rotation_distance * mmsExtracted / 100.0
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4 #ar10
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5 #analog13
control: pid
pid_kp: 22.107
pid_ki: 1.170
pid_kd: 104.458
min_temp: 0
max_temp: 255

[heater_bed]
heater_pin: PH5 #ar8
sensor_type: ATC Semitec 104GT-2
sensor_pin: PK6 #analog14
control: pid
#Stock PID configuration taken from Marlin
pid_Kp: 201.86
pid_Ki: 10.67
pid_Kd: 954.96
min_temp: 0
max_temp: 130

[fan]
pin: PH6 #ar9

[mcu]
serial: /dev/ttyUSB0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[display]
lcd_type: st7920
cs_pin: PH1 #ar16
sclk_pin: PA1 #ar23
sid_pin: PH0 #ar17
encoder_pins: ^PC4, ^PC6 #^ar33, ^ar31
click_pin: ^!PC2 #^!ar35


#Uncomment the following lines if you have a BL-Touch
#[bltouch]
#sensor_pin: ^PD2 #^ar19
#control_pin: PB5 #ar11
#set_output_mode: 5V
#pin_move_time: 0.4
#stow_on_each_sample: False
#probe_with_touch_mode: False
#x_offset: 45.75
#y_offset: -3.40
#z_offset:  3.28
#samples: 2
#sample_retract_dist: 2
#samples_result: average

#Uncomment the following lines if you have a BL-Touch
#[bed_mesh]
#speed: 50
#horizontal_move_z: 6
#mesh_min: 46.50,0.75
#mesh_max: 253.5,295.85
#probe_count: 7,7
#algorithm: bicubic

[pause_resume]
recover_velocity: 50

[filament_switch_sensor fil_runout_sensor]
pause_on_runout: True
switch_pin: PE4 #ar2

[bed_screws]
screw1: 33,29
screw1_name: front left screw
screw2: 273,29
screw2_name: front right screw
screw3: 273,269
screw3_name: rear right screw
screw4: 33,269
screw4_name: rear left screw

#Uncomment the following lines if you have a BL-Touch
#[screws_tilt_adjust]
#screw1: 0,29
#screw1_name: front left screw
#screw2: 228,29
#screw2_name: front right screw
#screw3: 228,269
#screw3_name: rear right screw
#screw4: 0,269
#screw4_name: rear left screw
#speed: 50
#horizontal_move_z: 10
#screw_thread: CW-M3
