# Klipper configuration for the TronXY Crux1 printer
# CXY-V10.1-220921 mainboard, GD32F4XX or STM32F446 MCU
#
# =======================
# BUILD AND FLASH OPTIONS
# =======================
#
# MCU-architecture:  STMicroelectronics
# Processor model:   STM32F446
# Bootloader offset: 64KiB
# Comms interface:   Serial on USART1 PA10/PA9
#
# Build the firmware with these options
# Rename the resulting klipper.bin into fmw_tronxy.bin
# Put the file into a directory called "update" on a FAT32 formatted SD card.
# Turn off the printer, plug in the SD card and turn the printer back on
# Flashing will start automatically and progress will be indicated on the LCD
# Once the flashing is completed the display will get stuck on the white Tronxy logo bootscreen
# The LCD display will NOT work anymore after flashing Klipper onto this printer

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 250
max_accel: 1500
square_corner_velocity: 5
max_z_velocity: 15
max_z_accel: 100

[controller_fan drivers_fan]
pin: PD7

[pwm_cycle_time BEEPER_pin]
pin: PA8
value: 0
shutdown_value: 0
cycle_time: 0.001

[safe_z_home]
home_xy_position: 0, 0
speed: 100
z_hop: 10
z_hop_speed: 5

[stepper_x]
step_pin: PE5
dir_pin: PF1
enable_pin: !PF0
microsteps: 16
rotation_distance: 20
endstop_pin: ^!PC15
position_endstop: -1
position_min: -1
position_max: 180
homing_speed: 100
homing_retract_dist: 10
second_homing_speed: 25

[stepper_y]
step_pin: PF9
dir_pin: !PF3
enable_pin: !PF5
microsteps: 16
rotation_distance: 20
endstop_pin: ^!PC14
position_endstop: -3
position_min: -3
position_max: 180
homing_retract_dist: 10
homing_speed: 100
second_homing_speed: 25

[stepper_z]
step_pin: PA6
dir_pin: !PF15
enable_pin: !PA5
microsteps: 16
rotation_distance: 4
endstop_pin: ^!PC13
position_endstop: 0
position_max: 180
position_min: 0

[extruder]
step_pin: PB1
dir_pin: PF13
enable_pin: !PF14
microsteps: 16
rotation_distance: 16.75
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PG7
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC3
control: pid
pid_kp: 22.2
pid_ki: 1.08
pid_kd: 114.00
min_temp: 0
max_temp: 250
min_extrude_temp: 170
max_extrude_only_distance: 450

[heater_fan hotend_fan]
heater: extruder
heater_temp: 50.0
pin: PG9

[fan]
pin: PG0

[filament_switch_sensor filament_sensor]
pause_on_runout: True
switch_pin: ^!PE6

[heater_bed]
heater_pin: PE2
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC2
min_temp: 0
max_temp: 130
control: pid
pid_kp: 10.00
pid_ki: 0.023
pid_kd: 305.4

[bed_screws]
screw1: 17.5, 11
screw1_name: front_left
screw2: 162.5, 11
screw2_name: front_right
screw3: 162.5, 162.5
screw3_name: back_right
screw4: 17.5, 162.5
screw4_name: back_left
