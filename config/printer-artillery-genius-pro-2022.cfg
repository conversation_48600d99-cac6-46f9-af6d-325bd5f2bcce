# This file contains pin mappings for the Artillery Genius Pro (2022)
# with a Artillery_Ruby-v1.2 board. To use this config, during "make menuconfig"
# select the STM32F401 with "No bootloader" and USB (on PA11/PA12)
# communication.

# To flash this firmware, set the physical bridge between +3.3V and Boot0 PIN
# on Artillery_Ruby mainboard. Then run the command:
#   make flash FLASH_DEVICE=/dev/serial/by-id/usb-Klipper_stm32f401xc_*-if00

# See docs/Config_Reference.md for a description of parameters.

[extruder]
max_extrude_only_distance: 700.0
step_pin: PA7
dir_pin: PA6
enable_pin: !PC4
microsteps: 16
rotation_distance: 7.1910
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC9
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC0
min_temp: 0
max_temp: 250
control: pid
pid_Kp: 23.223
pid_Ki: 1.518
pid_Kd: 88.826

[stepper_x]
step_pin: !PB14
dir_pin: PB13
enable_pin: !PB15
microsteps: 16
rotation_distance: 40
endstop_pin: !PA2
position_endstop: 0
position_max: 220
homing_speed: 60

[stepper_y]
step_pin: PB10
dir_pin: PB2
enable_pin: !PB12
microsteps: 16
rotation_distance: 40
endstop_pin: !PA1
position_endstop: 0
position_max: 220
homing_speed: 60

[stepper_z]
step_pin: PB0
dir_pin: !PC5
enable_pin: !PB1
microsteps: 16
rotation_distance: 8
endstop_pin: probe:z_virtual_endstop
position_max: 250
position_min: -5

[heater_bed]
heater_pin: PA8
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC1
min_temp: 0
max_temp: 130
control: pid
pid_Kp: 23.223
pid_Ki: 1.518
pid_Kd: 88.826

[bed_screws]
screw1: 38,45
screw2: 180,45
screw3: 180,180
screw4: 38,180

[fan]
pin: PC8
off_below: 0.1

[heater_fan hotend_fan]
pin: PC7
heater: extruder
heater_temp: 50.0

[controller_fan stepper_fan]
pin: PC6
idle_timeout: 300

[mcu]
serial: /dev/serial/by-id/usb-Klipper_stm32f401xc_

[printer]
kinematics: cartesian
max_velocity: 500
max_accel: 4000
max_z_velocity: 50
square_corner_velocity: 5.0
max_z_accel: 100

[bltouch]
sensor_pin: PC2
control_pin: PC3
x_offset:27.25
y_offset:-12.8
z_offset: 0.25
speed:10
samples:1
samples_result:average

[bed_mesh]
speed: 800
mesh_min: 30, 20
mesh_max: 210, 200
probe_count: 5,5
algorithm: bicubic
move_check_distance: 3.0

[safe_z_home]
home_xy_position: 110,110
speed: 100
z_hop: 10
z_hop_speed: 5
