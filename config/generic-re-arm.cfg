# This file contains common pin mappings for Re-Arm. To use this
# config, the firmware should be compiled for the LPC1768.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: P2.1
dir_pin: P0.11
enable_pin: !P0.10
microsteps: 16
rotation_distance: 40
endstop_pin: ^P1.24
#endstop_pin: ^P1.25
position_endstop: 0.5
position_min: 0
position_max: 200
homing_speed: 50

# The stepper_y section is used to describe the Y axis as well as the
# stepper controlling the X-Y movement.
[stepper_y]
step_pin: P2.2
dir_pin: P0.20
enable_pin: !P0.19
microsteps: 16
rotation_distance: 40
endstop_pin: ^P1.26
#endstop_pin: ^P1.27
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: P2.3
dir_pin: P0.22
enable_pin: !P0.21
microsteps: 16
rotation_distance: 8
endstop_pin: ^P1.29
#endstop_pin: ^P1.28
position_endstop: 0.5
position_min: 0
position_max: 200

[extruder]
step_pin: P2.0
dir_pin: P0.5
enable_pin: !P0.4
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: P2.5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: P0.23
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

#[extruder1]
#step_pin: P2.8
#dir_pin: P2.13
#enable_pin: !P4.29
#heater_pin: P2.4
#sensor_pin: P0.25
#...

[heater_bed]
heater_pin: P2.7
sensor_type: EPCOS 100K B57560G104F
sensor_pin: P0.24
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: P2.4

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100


# "RepRapDiscount 128x64 Full Graphic Smart Controller" type displays
# Re-Arm will only work with this type of display
#[display]
#lcd_type: st7920
#cs_pin: P0.16
#sclk_pin: P0.15
#sid_pin: P0.18
#encoder_pins: ^P3.25, ^P3.26
#click_pin: ^!P2.11
#kill_pin: ^!P1.22
# Ground the buzzer pin to prevent stray voltages causing an audible "whine"
#[static_digital_output buzzer]
#pins: !P1.30
