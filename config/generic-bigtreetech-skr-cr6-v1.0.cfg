# This file contains common pin mappings for the BIGTREETECH SKR CR6
# V1.0. To use this config, the firmware should be compiled for the
# STM32F103 with a "28KiB bootloader" and USB communication. Also,
# select "Enable extra low-level configuration options" and configure
# "GPIO pins to set at micro-controller startup" to "!PA14".

# The "make flash" command does not work on the SKR CR6. Instead,
# after running "make", copy the generated "out/klipper.bin" file to a
# file named "firmware.bin" on an SD card and then restart the SKR
# CR6 V1.0 with that SD card.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PB13
dir_pin: !PB12
enable_pin: !PB14
microsteps: 16
rotation_distance: 40
endstop_pin: PC0
position_min: -5
position_endstop: -5
position_max: 235
homing_speed: 50

[tmc2209 stepper_x]
uart_pin: PC11
tx_pin: PC10
run_current: 0.550
stealthchop_threshold: 999999
uart_address: 0

[stepper_y]
step_pin: PB10
dir_pin: PB2
enable_pin: !PB11
microsteps: 16
rotation_distance: 40
endstop_pin: PC1
position_min: -2
position_endstop: -2
position_max: 235
homing_speed: 50

[tmc2209 stepper_y]
uart_pin: PC11
tx_pin: PC10
uart_address: 1
run_current: 0.550
stealthchop_threshold: 999999

[stepper_z]
step_pin: PB0
dir_pin: !PC5
enable_pin: !PB1
microsteps: 16
rotation_distance: 8
endstop_pin: PC2
position_endstop: 0.0
position_min: -1.0
position_max: 250

[tmc2209 stepper_z]
uart_pin: PC11
tx_pin: PC10
uart_address: 2
run_current: 0.550
stealthchop_threshold: 999999

[extruder]
step_pin: PB3
dir_pin: !PB4
enable_pin: !PD2
microsteps: 16
rotation_distance: 30.4768
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC8
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA0
control: pid
pid_Kp: 14.32
pid_Ki: 0.81
pid_Kd: 63.12
min_temp: 0
max_temp: 275

[tmc2209 extruder]
uart_pin: PC11
tx_pin: PC10
uart_address: 3
run_current: 0.600
stealthchop_threshold: 999999

[heater_bed]
heater_pin: PC9
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC3
control: pid
pid_Kp: 79.49
pid_Ki: 1.17
pid_Kd: 1349.52
min_temp: 0
max_temp: 120

[fan]
pin: PC6

[mcu]
serial: /dev/serial/by-id/usb-Klipper_stm32f103xe_000000000000000000000000-if00

[printer]
kinematics: cartesian
max_velocity: 500
max_accel: 500
max_z_velocity: 5
max_z_accel: 100

[static_digital_output usb_pullup_enable]
pins: !PA14
