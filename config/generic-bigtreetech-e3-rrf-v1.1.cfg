# This file contains common pin mappings for the BigTreeTech E3 RRF 1.1.
# To use this config, the firmware should be compiled for the
# STM32F407 with a "32KiB bootloader".

# The "make flash" command does not work on the E3 RRF 1.1. Instead,
# after running "make", copy the generated "out/klipper.bin" file to a
# file named "firmware.bin" on an SD card and then restart the E3 RRF
# 1.1 with that SD card.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PD5
dir_pin: !PD4
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC0
position_endstop: 0
position_max: 235
homing_speed: 50

[tmc2209 stepper_x]
uart_pin: PD6
run_current: 0.580
stealthchop_threshold: 999999

[stepper_y]
step_pin: PD0
dir_pin: !PA15
enable_pin: !PD3
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC1
position_endstop: 0
position_max: 235
homing_speed: 50

[tmc2209 stepper_y]
uart_pin: PD1
run_current: 0.580
stealthchop_threshold: 999999

[stepper_z]
step_pin: PC6
dir_pin: PC7
enable_pin: !PD14
microsteps: 16
rotation_distance: 8
endstop_pin: ^PC2
position_endstop: 0.0
position_max: 250

[tmc2209 stepper_z]
uart_pin: PD15
run_current: 0.580
stealthchop_threshold: 999999

[extruder]
step_pin: PD12
dir_pin: !PD13
enable_pin: !PD10
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB3
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA0
control: pid
pid_Kp: 21.527
pid_Ki: 1.063
pid_Kd: 108.982
min_temp: 0
max_temp: 250

[tmc2209 extruder]
uart_pin: PD11
run_current: 0.650
stealthchop_threshold: 999999

[heater_bed]
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA1
control: pid
pid_Kp: 54.027
pid_Ki: 0.770
pid_Kd: 948.182
min_temp: 0
max_temp: 130

[heater_fan heatbreak_cooling_fan]
pin: PB6

[fan]
pin: PB5

[mcu]
serial: /dev/serial/by-id/usb-Klipper_stm32f407xx_370025000247303034313331-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

# Warning: display section not tested!

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PE8, EXP1_3=PE7,   EXP1_5=PB2,  EXP1_7=PB1,  EXP1_9=<GND>,
    EXP1_2=PE9, EXP1_4=<RST>, EXP1_6=PE10, EXP1_8=PE11, EXP1_10=<5V>

# See the sample-lcd.cfg file for definitions of common LCD displays.
