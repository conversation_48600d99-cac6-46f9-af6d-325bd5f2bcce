# This file contains common pin mappings for the BIGTREETECH SKR E3
# Turbo. To use this config, the firmware should be compiled for the
# LPC1769.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: P1.4
dir_pin: !P1.8
enable_pin: !P1.0
microsteps: 16
rotation_distance: 40
endstop_pin: ^P1.29
position_endstop: 0
position_max: 235
homing_speed: 50

[tmc2209 stepper_x]
uart_pin: P1.1
#diag_pin: P1.29
run_current: 0.580
stealthchop_threshold: 999999

[stepper_y]
step_pin: P1.14
dir_pin: !P1.15
enable_pin: !P1.9
microsteps: 16
rotation_distance: 40
endstop_pin: ^P1.28
position_endstop: 0
position_max: 235
homing_speed: 50

[tmc2209 stepper_y]
uart_pin: P1.10
#diag_pin: P1.28
run_current: 0.580
stealthchop_threshold: 999999

[stepper_z]
step_pin: P4.29
dir_pin: P4.28
enable_pin: !P1.16
microsteps: 16
rotation_distance: 8
endstop_pin: ^P1.27
position_endstop: 0.0
position_max: 250

[tmc2209 stepper_z]
uart_pin: P1.17
#diag_pin: P1.27
run_current: 0.580
stealthchop_threshold: 999999

[extruder]
step_pin: P2.6
dir_pin: !P2.7
enable_pin: !P0.4
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: P2.3
sensor_type: EPCOS 100K B57560G104F
sensor_pin: P0.24
control: pid
pid_Kp: 21.527
pid_Ki: 1.063
pid_Kd: 108.982
min_temp: 0
max_temp: 250

[tmc2209 extruder]
uart_pin: P0.5
#diag_pin: P1.26
run_current: 0.650
stealthchop_threshold: 999999

#[extruder1]
#step_pin: P2.11
#dir_pin: P2.12
#enable_pin: !P0.21
#heater_pin: P2.4
#sensor_pin: P0.23
#...

#[tmc2209 extruder1]
#uart_pin: P0.22
##diag_pin: P1.25
#...

[heater_bed]
heater_pin: P2.5
sensor_type: ATC Semitec 104GT-2
sensor_pin: P0.25
control: pid
pid_Kp: 54.027
pid_Ki: 0.770
pid_Kd: 948.182
min_temp: 0
max_temp: 130

[fan]
pin: P2.1

[heater_fan heatbreak_cooling_fan]
pin: P2.2

[mcu]
serial: /dev/serial/by-id/usb-Klipper_lpc1769_00000000000000000000000000000000-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[static_digital_output tmc_standby_pins]
pins: !P3.26, !P3.25, !P1.18, !P1.19, !P2.13

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=P2.8,  EXP1_3=P0.19, EXP1_5=P0.20, EXP1_7=P0.17, EXP1_9=<GND>,
    EXP1_2=P0.16, EXP1_4=<RST>, EXP1_6=P0.15, EXP1_8=P0.18, EXP1_10=<5V>

# See the sample-lcd.cfg file for definitions of common LCD displays.
