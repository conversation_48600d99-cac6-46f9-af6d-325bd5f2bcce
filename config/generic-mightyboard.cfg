# This file contains common pin mappings for the Makerbot
# Mightyboard. To use this config, the firmware should be compiled for
# the Atmel atmega1280.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF1
dir_pin: !PF0
enable_pin: !PF2
microsteps: 16
rotation_distance: 32
endstop_pin: ^!PL1
position_endstop: 152
position_max: 153
position_min: -120
homing_speed: 50

[stepper_y]
step_pin: PF5
dir_pin: !PF4
enable_pin: !PF6
microsteps: 16
rotation_distance: 32
endstop_pin: ^!PL3
position_endstop: 77
position_max: 78
position_min: -84
homing_speed: 50

[stepper_z]
step_pin: PK1
dir_pin: !PK0
enable_pin: !PK2
microsteps: 16
rotation_distance: 8
endstop_pin: !PL6
position_endstop: 0
position_max: 230
position_min: 0

[extruder]
step_pin: PA3
dir_pin: !PA2
enable_pin: !PA4
microsteps: 16
rotation_distance: 33.238
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PH3
sensor_type: MAX6675
sensor_pin: PE3
spi_software_miso_pin: PE5
spi_software_sclk_pin: PE2
spi_software_mosi_pin: PA1 #dummy mosi pin
control: pid
pid_Kp: 26.414
pid_Ki: 1.115
pid_Kd: 156.5054
min_temp: 0
max_temp: 260

[heater_fan extruder_fan]
pin: PH4

[fan]
pin: PL5

[heater_bed]
heater_pin: PL4
sensor_type: ATC Semitec 104GT-2
sensor_pin: PK7
control: pid
pid_Kp: 70.037
pid_Ki: 1.710
pid_Kd: 717.000
min_temp: 0
max_temp: 130

[mcu]
serial: /dev/ttyAMA0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[mcp4018 x_axis_pot]
i2c_software_scl_pin: PJ5
i2c_software_sda_pin: PF3
wiper: 0.50
scale: 0.773

[mcp4018 y_axis_pot]
i2c_software_scl_pin: PJ5
i2c_software_sda_pin: PF7
wiper: 0.50
scale: 0.773

[mcp4018 z_axis_pot]
i2c_software_scl_pin: PJ5
i2c_software_sda_pin: PK3
wiper: 0.50
scale: 0.773

[mcp4018 a_axis_pot]
i2c_software_scl_pin: PJ5
i2c_software_sda_pin: PA5
wiper: 0.50
scale: 0.773

[mcp4018 b_axis_pot]
i2c_software_scl_pin: PJ5
i2c_software_sda_pin: PJ6
wiper: 0.50
scale: 0.773

[display]
lcd_type: hd44780_spi
spi_software_mosi_pin: PC3
spi_software_sclk_pin: PC2
#miso not used, dummy pin.
spi_software_miso_pin: PJ1
latch_pin: PC4

click_pin: ^PJ0
back_pin: ^PJ2
up_pin: ^PJ4
down_pin: ^PJ3

[pca9533 led_strip]
#set_led led=led_strip red=1 green=1 blue=1
i2c_bus: twi
i2c_address: 98
initial_RED: 1
initial_GREEN: 1
initial_BLUE: 1
