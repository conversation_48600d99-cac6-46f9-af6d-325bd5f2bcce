# Klipper configuration for Modix Big60 w/ Duet 2 Wifi
# Dual Volcano hotends, PT100 RTDs, BLtouch

# Klipper should be compiled for SAM4E8E for this printer.

# See docs/Config_Reference.md for a description of parameters.


# Printer configuration

[mcu]
serial: /dev/serial/by-id/usb-Klipper_sam4e8e_1234567890-if00

[printer]
kinematics: cartesian
max_velocity: 160
max_accel: 1000
max_z_velocity: 6
max_z_accel: 120

[gcode_arcs]
resolution: 0.1

[pause_resume]
recover_velocity: 50


# Axis configuration

[stepper_x]
step_pin: PD6
dir_pin: PD11
enable_pin: !PC6
microsteps: 256
rotation_distance: 32
endstop_pin: ^PC14
position_endstop: 0
position_min: 0
position_max: 600
homing_speed: 50
second_homing_speed: 2
homing_retract_dist: 5

[stepper_y]
step_pin: PD7
dir_pin: !PD12
enable_pin: !PC6
microsteps: 256
rotation_distance: 32
endstop_pin: ^PA2
position_endstop: 600
position_min: 0
position_max: 600
homing_speed: 50
second_homing_speed: 2
homing_retract_dist: 5
homing_positive_dir: True

[stepper_z]
step_pin: PD8
dir_pin: !PD13
enable_pin: !PC6
microsteps: 256
rotation_distance: 1.6000
endstop_pin: probe:z_virtual_endstop
position_min: -5
position_max: 660
homing_speed: 2.5
second_homing_speed: 1
homing_retract_dist: 5

[extruder]
step_pin: PD5
dir_pin: PA1
enable_pin: !PC6
microsteps: 256
rotation_distance: 22.9344
gear_ratio: 66:22
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: !PA20
sensor_type: MAX31865
spi_bus: usart0
sensor_pin: PB2
rtd_nominal_r: 100
rtd_reference_r: 400
rtd_num_of_wires: 2
rtd_use_50Hz_filter: True
control: pid
pid_kp: 22.2
pid_ki: 1.08
pid_kd: 114
min_temp: 0
max_temp: 400

[extruder1]
step_pin: PD4
dir_pin: !PD9
enable_pin: !PC6
microsteps: 256
rotation_distance: 22.9344
gear_ratio: 66:22
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: !PA16
sensor_type: MAX31865
spi_bus: usart0
sensor_pin: PC18
rtd_nominal_r: 100
rtd_reference_r: 400
rtd_num_of_wires: 2
rtd_use_50Hz_filter: True
control: pid
pid_kp: 22.2
pid_ki: 1.08
pid_kd: 114
min_temp: 0
max_temp: 400


# Stepper driver configuration

[tmc2660 stepper_x]
cs_pin: PD14
spi_speed: 2000000
spi_bus: usart1
run_current: 1.600
sense_resistor: 0.051

[tmc2660 stepper_y]
cs_pin: PC9
spi_speed: 2000000
spi_bus: usart1
run_current: 1.200
sense_resistor: 0.051

[tmc2660 stepper_z]
cs_pin: PC10
spi_speed: 2000000
spi_bus: usart1
run_current: 2.400
sense_resistor: 0.051

[tmc2660 extruder]
cs_pin: PC17
spi_speed: 2000000
spi_bus: usart1
run_current: 1.200
sense_resistor: 0.051

[tmc2660 extruder1]
cs_pin: PC25
spi_speed: 2000000
spi_bus: usart1
run_current: 1.200
sense_resistor: 0.051


# Fans

[fan]
pin: PC23


# Filament sensors

[filament_switch_sensor e0_sensor]
pause_on_runout: True
switch_pin: PD10
runout_gcode:
  PAUSE_PARK

[filament_switch_sensor e1_sensor]
pause_on_runout: True
switch_pin: PC16
runout_gcode:
  PAUSE_PARK


# Bed leveling

[bltouch]
sensor_pin: PC1
control_pin: PC3
x_offset: -14
y_offset: 21
z_offset: 2.000
speed: 1
lift_speed: 5

[bed_mesh]
mesh_min: 25, 25
mesh_max: 595, 585
probe_count: 11, 11
speed: 200
horizontal_move_z: 5
mesh_pps: 2,2
algorithm: bicubic
bicubic_tension: 0.15
fade_start: 0.5
fade_end: 2.5

[bed_screws]
screw1: 0,0
screw1_name: Front Left
screw2: 600,0
screw2_name: Front Right
screw3: 600,600
screw3_name: Rear Right
screw4: 0,600
screw4_name: Rear Left
horizontal_move_z: 5
probe_height: 0
speed: 100
probe_speed: 5


# Safe homing

[safe_z_home]
home_xy_position: 310,305
speed: 100
z_hop: 10
z_hop_speed: 5
move_to_previous: True


# Macros

[gcode_macro T0]
gcode:
  ACTIVATE_EXTRUDER EXTRUDER=extruder
  SET_GCODE_OFFSET Y=0

[gcode_macro T1]
gcode:
  ACTIVATE_EXTRUDER EXTRUDER=extruder1
  SET_GCODE_OFFSET Y=-51.5

[gcode_macro PAUSE_PARK]
gcode:
  G91
  G0 Z5 F120
  G90
  G0 X50 Y50 F3000
