# Klipper firmware config file for Anycubic 4Max. To use this config,
# the firmware should be compiled for the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PE5
position_min: -2
position_endstop: -2
position_max: 205
homing_speed: 60.0

[stepper_y]
step_pin: PF6
dir_pin: PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PJ1
position_endstop: 0
position_max: 215
homing_speed: 60.0

[stepper_z]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PD3
position_endstop: 0.5
position_max: 305
homing_speed: 8.0

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 33.133
nozzle_diameter: 0.400
filament_diameter: 1.750
max_extrude_only_distance: 2000
heater_pin: PB4
sensor_type: ATC Semitec 104GT-2
sensor_pin: PK5
control: pid
pid_kp: 27.725
pid_ki: 1.224
pid_kd: 156.991
min_temp: 0
max_temp: 300

[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: pid
pid_kp: 73.735
pid_ki: 1.437
pid_kd: 945.653
min_temp: 0
max_temp: 110

[fan]
pin: PH6
kick_start_time: 1.0

[mcu]
serial: /dev/serial/by-id/usb-Silicon_Labs_CP2102_USB_to_UART_Bridge_Controller_0001-if00-port0

[printer]
kinematics: cartesian
max_velocity: 1200
max_accel: 1500
max_z_velocity: 40
max_z_accel: 60

[heater_fan extruder_fan]
pin: PL5

[heater_fan stepstick_fan]
pin: PH4
kick_start_time: 1.0

[display]
lcd_type: st7920
cs_pin: PH1
sclk_pin: PA1
sid_pin: PH0
encoder_pins: ^PC6, ^PC4
click_pin: ^!PC2
kill_pin: ^!PG0

[filament_switch_sensor e0_sensor]
switch_pin: PD2

[gcode_macro START_PRINT]
gcode:
    M117 Starting...
    G90 ; absolute positioning
    M107 ; start with the fan off
    G28 ; Home
    G0 X5 Y5 F4500 ; Go to front
    G0 Z0.3 ; Drop to bed
    G92 E0 ; zero the extruded length
    G1 Y40 E15 F500 ; Extrude 15mm of filament in a 4cm line
    G92 E0 ; zero the extruded length
    G1 Y80 F4000 ; Quickly wipe away from the filament line
    G1 Z1 ; Raise and begin printing.
    M117 Printing...

[gcode_macro END_PRINT]
gcode:
    M117 End printing.
    G91 ; relative positioning
    G1 E-1 F300 ;retract the filament a bit before lifting the nozzle to release some of the pressure
    G1 Z+5 E-2 F1000 ;move Z up a bit and extract a bit more
    G90 ; absolute positioning
    G1 X0 F2000 ; move X to min endstops so the head is out of the way
    G1 Y200 F2000 ; Move Y to the back
    m104 S0 ; turn hotend heating off
    M140 S0 ; turn bed heating off
    M107 ; turn fan off
    M84 ; steppers off
