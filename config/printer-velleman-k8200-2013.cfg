# This file contains common pin mappings for the Velleman K8200 and
# 3Drag 3D printers (circa 2013). To use this config, the firmware
# should be compiled for the AVR atmega2560.

# Based on config from <PERSON> and <PERSON>.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF0
dir_pin: !PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PE5
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PF6
dir_pin: !PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: ^PJ1
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PL3
dir_pin: !PL1
enable_pin: !PK1
microsteps: 16
rotation_distance: 8
endstop_pin: ^PD3
position_endstop: 0.5
# Set position_max to 200 if you have the original Z-axis setup.
position_max: 250

[extruder]
step_pin: PA4
# Remove the "!" from dir_pin if you have an original extruder
dir_pin: !PA6
enable_pin: !PA2
# You will have to calculate your own rotation_distance.
# This is for the belted extruder https://www.thingiverse.com/thing:339928
microsteps: 16
rotation_distance: 4.266
nozzle_diameter: 0.400
filament_diameter: 2.85
heater_pin: PB4
sensor_type: ATC Semitec 104GT-2
sensor_pin: PK5
control: pid
pid_Kp: 21.503
pid_Ki: 1.103
pid_Kd: 104.825
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PH6
sensor_type: ATC Semitec 104GT-2
sensor_pin: PK6
control: pid
pid_Kp: 75.283
pid_Ki: 0.588
pid_Kd: 2408.103
min_temp: 0
max_temp: 130

[fan]
pin: PH5
kick_start_time: 0.500

[mcu]
serial: /dev/ttyUSB0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 1000
max_z_velocity: 10
max_z_accel: 100

# The LCD is untested - "RepRapDiscount 2004 Smart Controller" displays
#[display]
#lcd_type: hd44780
#rs_pin: PA5
#e_pin: PA7
#d4_pin: PC0
#d5_pin: PC2
#d6_pin: PC4
#d7_pin: PC6
#encoder_pins: ^PH1, ^PH0
#click_pin: ^!PA1
