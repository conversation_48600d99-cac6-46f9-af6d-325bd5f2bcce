# Support for the Wanhao Duplicator 6 and its clones (eg, Monoprice
# Ultimate). To use this config, the firmware should be compiled for
# the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PA3
dir_pin: !PA1
enable_pin: !PA5
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PA0
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PC5
dir_pin: PC4
enable_pin: !PC6
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PA4
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PC2
dir_pin: !PC1
enable_pin: !PC3
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PA7
position_endstop: 0.5
position_max: 175
homing_speed: 25

[extruder]
step_pin: PL7
dir_pin: !PL6
enable_pin: !PC0
microsteps: 16
rotation_distance: 33.291
nozzle_diameter: 0.400
filament_diameter: 1.7500
heater_pin: PE4
sensor_type: PT100 INA826
sensor_pin: PK0
control: pid
pid_Kp: 26.571
pid_Ki: 0.927
pid_Kd: 190.318
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PG5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK2
control: pid
pid_Kp: 59.593
pid_Ki: 3.01
pid_Kd: 294.985
min_temp: 0
max_temp: 110

[fan]
pin: PH4

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

# Software control for Stepper current
[output_pin stepper_xy_current]
pin: PL5
pwm: True
scale: 2.782
cycle_time: .000030
hardware_pwm: True
value: 1.2

[output_pin stepper_z_current]
pin: PL4
pwm: True
scale: 2.782
cycle_time: .000030
hardware_pwm: True
value: 1.2

[output_pin stepper_e_current]
pin: PL3
pwm: True
scale: 2.782
cycle_time: .000030
hardware_pwm: True
value: 1.0

[display]
lcd_type: ssd1306
reset_pin: PE3
encoder_pins: ^PG1, ^PG0
click_pin: ^!PD2

[output_pin caselight]
pin: PH5
value: 0
pwm: True

[gcode_macro LIGHTS_OFF]
gcode:
    SET_PIN PIN=caselight VALUE=0

[gcode_macro LIGHTS_ON]
gcode:
    SET_PIN PIN=caselight VALUE=1
