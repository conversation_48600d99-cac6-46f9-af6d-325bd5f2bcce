# This file contains common pin mappings for the LDO Leviathan v1.2.

# To use this config, during "make menuconfig", select "Enable
# low-level configuration options", select the STM32F446 micro-controller,
# select a "32KiB bootloader", and select a "12Mhz crystal".

# See docs/Config_Reference.md for a description of parameters.

# HV-STEPPER-0
[stepper_x]
step_pin: PB10
dir_pin: PB11
enable_pin: !PG0
microsteps: 32
rotation_distance: 40
endstop_pin: PC1 # X-ENDSTOP
position_endstop: 0
position_max: 200
homing_speed: 50

[tmc5160 stepper_x]
spi_bus: spi4
cs_pin: PE15
#diag0_pin: PG1
interpolate: False
sense_resistor: 0.075
run_current: 0.8
stealthchop_threshold: 0

# HV-STEPPER-1
[stepper_y]
step_pin: PF15
dir_pin: PF14
enable_pin: !PE9
microsteps: 32
rotation_distance: 40
endstop_pin: PC2 # Y-ENDSTOP
position_endstop: 0
position_max: 200
homing_speed: 50

[tmc5160 stepper_y]
spi_bus: spi4
cs_pin: PE11
#diag0_pin: PE10
interpolate: False
sense_resistor: 0.075
run_current: 0.8
stealthchop_threshold: 0

# STEPPER-0
[stepper_z]
step_pin: PD4
dir_pin: PD3
enable_pin: !PD7
microsteps: 32
rotation_distance: 8
endstop_pin: PC3 # Z-ENDSTOP
position_endstop: 0
position_max: 200

[tmc2209 stepper_z]
uart_pin: PD5
#diag_pin: PD6
interpolate: False
run_current: 0.6
stealthchop_threshold: 999999

# The Leviathan was developed for Voron printers. It therefore has several
# steppers for the z-axes, but only one heater for one extruder.

# STEPPER-1
#[stepper_z1]
#step_pin: PC12
#dir_pin: PC11
#enable_pin: !PD2
#microsteps: 32
#rotation_distance: 8
#
#[tmc2209 stepper_z1]
#uart_pin: PD5
##diag_pin: PD6
#interpolate: False
#run_current: 0.6
#stealthchop_threshold: 999999

# STEPPER-2
#[stepper_z2]
#step_pin: PC9
#dir_pin: PC8
#enable_pin: !PC10
#microsteps: 32
#rotation_distance: 8
#
#[tmc2209 stepper_z2]
#uart_pin: PA8
##diag_pin: PA15
#interpolate: False
#run_current: 0.6
#stealthchop_threshold: 999999

# STEPPER-3
#[stepper_z3]
#step_pin: PG7
#dir_pin: PG6
#enable_pin: !PC7
#microsteps: 32
#rotation_distance: 8
#
#[tmc2209 stepper_z2]
#uart_pin: PG8
##diag_pin: PC6
#interpolate: False
#run_current: 0.6
#stealthchop_threshold: 999999

# STEPPER-4
[extruder]
step_pin: PD10
dir_pin: PD9
enable_pin: !PD13
microsteps: 32
rotation_distance: 22.67
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PG10 # HEATER
sensor_pin:  PA2 # TH1
sensor_type: ATC Semitec 104NT-4-R025H42G
pullup_resistor: 2200
control: pid
pid_Kp: 36.787
pid_Ki: 4.716
pid_Kd: 71.735
min_temp: 0
max_temp: 250

[tmc2209 stepper_z]
uart_pin: PD11
#diag_pin: PD12
interpolate: False
run_current: 0.5
stealthchop_threshold: 0

#[filament_switch_sensor material_0]
#switch_pin: PC0 # FILAMENT-SENSOR

[heater_bed]
heater_pin: PG11 # HEATBED
sensor_pin: PA1 # TH0
sensor_type: ATC Semitec 104GT-2
pullup_resistor: 2200
control: pid
pid_kp: 56.723
pid_ki: 5.561
pid_kd: 144.642
min_temp: 0
max_temp: 130

[fan]
pin: PB7 # FAN0
#tachometer_pin: PB0

#[heater_fan fan1]
#pin: PB3
#tachometer_pin: PB4

#[heater_fan fan2]
#pin: PF7
#tachometer_pin: PF6

#[controller_fan fan3]
#pin: PF9
#tachometer_pin: PF8

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00
# CAN bus is also available on this board

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PG9, EXP1_2=PG12,
    EXP1_3=PG13, EXP1_4=PG14,
    EXP1_5=PC13, EXP1_6=PC14,
    EXP1_7=PC15, EXP1_8=PF0,
    EXP1_9=<GND>, EXP1_10=<5V>,

    # EXP2 header
    EXP2_1=PA6, EXP2_2=PA5,
    EXP2_3=PE2, EXP2_4=PE4,
    EXP2_5=PE3, EXP2_6=PA7,
    EXP2_7=PE5, EXP2_8=<RST>,
    EXP2_9=<GND>, EXP2_10=PE4,

    # See the sample-lcd.cfg file for definitions of common LCD displays.

    # EXTENSION PORT
    EXP3_1=<5V>, EXP3_2=<5V>,       # max. 0.5A
    EXP3_3=<GND>, EXP3_4=<GND>,
    EXP3_5=<3.3V>, EXP3_6=<3.3V>,   # max. 0.5A
    EXP3_7=PF5, EXP3_8=PF4,
    EXP3_9=PF3, EXP3_10=PF2,
    EXP3_11=PC4, EXP3_12=PC5,       # EXP3_11 and EXP3_12 are ADC inputs
    EXP3_13=PB0, EXP3_14=PB1,       # EXP3_13 and EXP3_14 are ADC inputs
    EXP3_15=PE8, EXP3_16=PE7,       # EXP3_15 is UART5_TX, EXP3_16 is UART5_RX
    EXP3_17=PG5, EXP3_18=PG4,
    EXP3_19=PG3, EXP3_20=PG2,
    EXP3_21=PD15, EXP3_22=PD14,
    EXP3_23=PB15, EXP3_24=PB14,     # EXP3_23 is SPI2_MOSI
                                    # EXP3_24 is SPI2_MISO
    EXP3_25=PB13, EXP3_26=PB12,     # EXP3_25 is SPI2_SCK + CAN2_TX
                                    # EXP3_26 is SPI2_CS + CAN2_RX
    EXP3_27=<GND>, EXP3_28=<GND>,
    EXP3_29=<24V>, EXP3_30=<24V>,   # max. 0.5A

#[probe]
#sensor_pin: PF1 # Z-PROBE
#z_offset: 0

#[led my_led]
#white_pin: PE6 # LED-Strip

#[neopixel my_neopixel]
#pin: PF10 # NEOPIXEL

#[temperature_sensor TH2]
#sensor_type: ATC Semitec 104GT-2
#sensor_pin: PA0 # TH2
#pullup_resistor: 2200

#[temperature_sensor TH3]
#sensor_type: ATC Semitec 104GT-2
#sensor_pin: PA3 # TH3
#pullup_resistor: 2200
