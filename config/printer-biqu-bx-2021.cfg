# This file contains common pin mappings for the BigTreeTech SKR SE BX.
# To use this config, the firmware should be compiled for the
# STM32H743 with a "128KiB bootloader". Additionally, GPIO pins PB5
# and PE5 need to be set at microcontroller startup.

######################################################################
# NOTE: In order enable the TFT70-BX display when the printer first
# starts, add PB5 and PE5 to the `GPIO pins to set at micro-controller
# startup` section when running "make menuconfig"
######################################################################

# The "make flash" command does not work on the SKR SE BX. Instead,
# after running "make", copy the generated "out/klipper.bin" file to a
# file named "firmware.bin" on an SD card and then restart the SKR SE BX
# with that SD card. After klipper has been flashed once to the board,
# you can update klipper by leaving a microSD inserted and running the
# scripts/flash-sd.sh script.

# See docs/Config_Reference.md for a description of parameters.

########################################
# Steppers
########################################

[stepper_x]
step_pin: PG13
dir_pin: !PG12
enable_pin: !PG14
microsteps: 16
rotation_distance: 40
full_steps_per_rotation: 400
endstop_pin: tmc2209_stepper_x:virtual_endstop
position_endstop: -13
position_min: -13
position_max: 250
homing_speed: 30
homing_retract_dist: 0

[stepper_y]
step_pin: PB3
dir_pin: !PD3
enable_pin: !PB4
microsteps: 16
rotation_distance: 40
full_steps_per_rotation: 400
endstop_pin: tmc2209_stepper_y:virtual_endstop
position_endstop: -7
position_min: -7
position_max: 250
homing_speed: 30
homing_retract_dist: 0

[stepper_z]
step_pin: PD7
dir_pin: PD6
enable_pin: !PG9
microsteps: 16
rotation_distance: 8
full_steps_per_rotation: 400
endstop_pin: probe:z_virtual_endstop
position_min: -2
position_max: 250

[stepper_z1]
step_pin: PA8
dir_pin: PC9
enable_pin: !PD2
microsteps: 16
rotation_distance: 8
full_steps_per_rotation: 400

[extruder]
step_pin: PC14
dir_pin: !PC13
enable_pin: !PC15
microsteps: 16
rotation_distance: 24.031
gear_ratio: 7:1
full_steps_per_rotation: 200
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PH4
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 350

[safe_z_home]
home_xy_position: 125,125
speed: 200
z_hop: 10
z_hop_speed: 25

########################################
# TMC2209 configuration
########################################

[tmc2209 stepper_x]
uart_pin: PG10
diag_pin: ^PB11
run_current: 0.800
sense_resistor: 0.150
driver_SGTHRS: 127
stealthchop_threshold: 999999

[tmc2209 stepper_y]
uart_pin: PD4
diag_pin: ^PB12
run_current: 0.800
sense_resistor: 0.150
driver_SGTHRS: 137
stealthchop_threshold: 999999

[tmc2209 stepper_z]
uart_pin: PD5
run_current: 1.000
sense_resistor: 0.150
stealthchop_threshold: 999999

[tmc2209 stepper_z1]
uart_pin: PC8
run_current: 1.000
sense_resistor: 0.150
stealthchop_threshold: 999999

[tmc2209 extruder]
uart_pin: PI8
run_current: 0.800
sense_resistor: 0.150
stealthchop_threshold: 0

########################################
# PRINTER
########################################

[mcu]
serial: /dev/ttyAMA0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 200
max_accel: 1000
max_z_velocity: 10
max_z_accel: 1000

[fan]
pin: PA5

[heater_fan extruder_fan]
pin: PA6
heater: extruder

[controller_fan controller_fan]
pin: PA7
idle_timeout: 300 # 5 minute timeout

[output_pin motor_power]
pin: PI11
value: 1

[idle_timeout]
gcode:
    TURN_OFF_HEATERS
    M84
    SET_PIN PIN=screen VALUE=0
    SET_LED LED=led BLUE=0.0 RED=0.0 GREEN=0.0

[pause_resume]

########################################
# DISPLAY
########################################

[output_pin screen]
pin: PB5
value: 1

[display_status]

[gcode_button lcd_button]
pin: PH8
press_gcode:
    SET_PIN PIN=screen VALUE=1
    SET_LED LED=led BLUE=1.0 RED=1.0 GREEN=1.0

[output_pin beeper]
pin: PA14
pwm: True
cycle_time: 0.001

########################################
# LEDS
########################################

[neopixel led]
pin: PH3
chain_count: 15

[neopixel knob]
pin: PB1
chain_count: 2

[delayed_gcode welcome]
initial_duration: 0.1
gcode:
    SET_LED LED=knob RED=0.0 BLUE=1.0 GREEN=0.0
    SET_LED LED=led RED=0.0 BLUE=1.0 GREEN=0.0
    G4 P1000
    SET_LED LED=led RED=1.0 BLUE=0.0 GREEN=0.0
    G4 P1000
    SET_LED LED=led RED=0.0 BLUE=0.0 GREEN=1.0
    G4 P1000
    SET_LED LED=led RED=1.0 BLUE=1.0 GREEN=1.0

########################################
# BED
########################################

[heater_bed]
heater_pin: PA4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PH5
control: watermark
min_temp: 0
max_temp: 250

[probe]
pin: PH2
x_offset: -30.1
y_offset: 26.78
z_offset: 0
speed: 5
samples: 3
samples_result: median
sample_retract_dist: 3.0
samples_tolerance: 0.006
samples_tolerance_retries: 5

[bed_mesh]
speed: 120
mesh_min: 10,19.78
mesh_max: 219.9,230
probe_count: 4,4

[screws_tilt_adjust]
screw1: 58,-7
screw1_name: front left
screw2: 245,-7
screw2_name: front right
screw3: 245,179
screw3_name: rear right
screw4: 58,179
screw4_name: rear left
speed: 100
screw_thread: CCW-M3

########################################
## MACROS
########################################

# Slicer setup: "print_start NOZZLE=<temp> BED=<temp>
# This macro does a preheat on the probe for better accuracy and needs
# the temps passed in. examples:
# Cura: PRINT_START BED={material_bed_temperature_layer_0} NOZZLE={material_print_temperature_layer_0}
# PrusaSlicer: PRINT_START NOZZLE=[first_layer_temperature] BED=[bed_temperature]
#   Use PRINT_END for the slicer ending script
[gcode_macro PRINT_START]
gcode:
    # Turn on screen if it's not on
    SET_PIN PIN=screen VALUE=1
    G28
    G0 Z1
    # Warm up nozzle, not to full temps yet
    M104 S150
    # Set LED to Purple for bed heating
    SET_LED LED=led BLUE=0.94 RED=0.63 GREEN=0.13
    M117 Heating Bed

    # Allow probe to warm up, then re-home Z
    M190 S60
    M105
    G4 P90000
    G28 Z

    M190 S{params.BED}
    M105
    G90 # Ensure we are in absolute mode
    G21
    M83 # Set the extruder to relative mode
    G92 E0

    # Set LED to Red for nozzle heating
    SET_LED LED=led BLUE=0.0 RED=1.0 GREEN=0.0
    M117 Heating Nozzle
    G0 X2 Y0 F6000
    G0 Z0.4
    M109 S{params.NOZZLE}
    M105
    # Set LED to white for printing
    SET_LED LED=led BLUE=1.0 RED=1.0 GREEN=1.0
    M117 Printing

    # Purge Line
    G1 X120 E30 F1200
    G1 Y1
    G1 X2 E30 F1200
    G92 E0

    G1 Z1.0 F600
    G92 E0
    G0 F9000
    G90 # Set back to Absolute mode
