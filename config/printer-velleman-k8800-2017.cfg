# This file contains common pin mappings for the Velleman K8800
# printers (circa 2017). To use this config, the firmware should be
# compiled for the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.
[board_pins]
aliases:
    # Common EXP1 header found on many "all-in-one" ramps clones
    EXP1_1=PC0, EXP1_3=PH0, EXP1_5=PA1, EXP1_7=PA5, EXP1_9=<GND>,
    EXP1_2=PC2, EXP1_4=PH1, EXP1_6=PA3, EXP1_8=PA7, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PB3, EXP2_3=PC6, EXP2_5=PC4, EXP2_7=PL0, EXP2_9=<GND>,
    EXP2_2=PB1, EXP2_4=PB0, EXP2_6=PB2, EXP2_8=PG0, EXP2_10=<RST>
    # Pins EXP2_1, EXP2_6, EXP2_2 are also MI<PERSON>, MOSI, SCK of bus "spi"
    # Note, some boards wire: EXP2_8=<RST>, EXP2_10=PG0

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB2.0-Serial-if00-port0

# X
[stepper_a]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 32
rotation_distance: 55  # 22 tooth pulley with 2.5mm pitch belt
endstop_pin: ^!PE5
homing_speed: 50
position_endstop: 295
arm_length: 207.0

#Y
[stepper_b]
step_pin: PF6
dir_pin: PF7
enable_pin: !PF2
microsteps: 32
rotation_distance: 55
endstop_pin: ^!PJ1

#Z
[stepper_c]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK1
microsteps: 32
rotation_distance: 55
endstop_pin: ^!PK4

[extruder]
step_pin: PA4
dir_pin: !PA6
enable_pin: !PA2
microsteps: 32
rotation_distance: 42.4666
nozzle_diameter: 0.3500
filament_diameter: 1.750
heater_pin: PB4
sensor_type: ATC Semitec 104GT-2
sensor_pin: PK5
control: pid
pid_Kp: 15.20
pid_Ki: 1.75
pid_Kd: 50.42
min_temp: 0
max_temp: 295

[printer]
kinematics: delta
max_velocity: 250
max_accel: 4000
max_z_velocity: 250
delta_radius: 100
print_radius: 105.00
minimum_z_position: -1

[delta_calibrate]
radius: 75

[fan]
pin: PH5

[controller_fan default_controller_fan]
pin: PH6

[probe]
pin: PK6
speed: 12
z_offset: -15

[bed_mesh]
speed: 120
horizontal_move_z: 5
mesh_radius:65
mesh_origin: 0, 0
round_probe_count: 7
algorithm: bicubic

[display]
lcd_type: uc1701
contrast: 55
cs_pin: PA7
a0_pin: PA5
encoder_pins: ^!PH1, ^!PH0
# PR is open to enable this, without this, the encoder does not work
#encoder_steps_per_detent: 2
# encoder_fast_rate: 0.1
click_pin: ^!PA1
#kill_pin: ^!PD1

[output_pin BEEPER]
pin: PH3
pwm: True
value: 0
shutdown_value: 0
scale: 100

[output_pin led]
pin: PH4
pwm: False

[gcode_macro M300]
gcode:
    # Use a default 1kHz tone if S is omitted.
    {% set S = params.S|default(1000.0)|float %}
    # Use a 10ms duration is P is omitted.
    {% set P = params.P|default(100)|int %}
    SET_PIN PIN=BEEPER VALUE=50 CYCLE_TIME={ 1.0 / S }
    G4 P{P}
    SET_PIN PIN=BEEPER VALUE=0
