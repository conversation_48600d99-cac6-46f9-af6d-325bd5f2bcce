# This file contains a configuration snippet for a CoreXYUV
# printer with an independent dual extruder moving over X and Y axes.

# See docs/Config_Reference.md for a description of parameters.

[carriage x]
position_endstop: 0
position_max: 300
homing_speed: 50
endstop_pin: ^PE5

[carriage y]
position_endstop: 0
position_max: 200
homing_speed: 50
endstop_pin: ^PJ1

[dual_carriage u]
primary_carriage: x
safe_distance: 70
position_endstop: 300
position_max: 300
homing_speed: 50
endstop_pin: ^PE4

[dual_carriage v]
primary_carriage: y
safe_distance: 50
position_endstop: 200
position_max: 200
homing_speed: 50
endstop_pin: ^PD4

[stepper a]
carriages: x+y
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40

[stepper b]
carriages: u-v
step_pin: PH1
dir_pin: PH0
enable_pin: !PA1
microsteps: 16
rotation_distance: 40

[stepper c]
carriages: x-y
step_pin: PF6
dir_pin: !PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40

[stepper d]
carriages: u+v
step_pin: PE3
dir_pin: !PH6
enable_pin: !PG5
microsteps: 16
rotation_distance: 40

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 33.5
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[gcode_macro PARK_extruder]
gcode:
    SET_DUAL_CARRIAGE CARRIAGE=x
    SET_DUAL_CARRIAGE CARRIAGE=y
    G90
    G1 X0 Y0

[gcode_macro T0]
gcode:
    PARK_{printer.toolhead.extruder}
    ACTIVATE_EXTRUDER EXTRUDER=extruder
    SET_DUAL_CARRIAGE CARRIAGE=x
    SET_DUAL_CARRIAGE CARRIAGE=y

[extruder1]
step_pin: PC1
dir_pin: PC3
enable_pin: !PC7
microsteps: 16
rotation_distance: 33.5
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK7
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[gcode_macro PARK_extruder1]
gcode:
    SET_DUAL_CARRIAGE CARRIAGE=u
    SET_DUAL_CARRIAGE CARRIAGE=v
    G90
    G1 X300 Y200

[gcode_macro T1]
gcode:
    PARK_{printer.toolhead.extruder}
    ACTIVATE_EXTRUDER EXTRUDER=extruder1
    SET_DUAL_CARRIAGE CARRIAGE=u
    SET_DUAL_CARRIAGE CARRIAGE=v

# A helper script to activate copy mode
[gcode_macro ACTIVATE_COPY_MODE]
gcode:
    SET_DUAL_CARRIAGE CARRIAGE=x MODE=PRIMARY
    SET_DUAL_CARRIAGE CARRIAGE=y MODE=PRIMARY
    G1 X0 Y0
    ACTIVATE_EXTRUDER EXTRUDER=extruder
    SET_DUAL_CARRIAGE CARRIAGE=u MODE=PRIMARY
    SET_DUAL_CARRIAGE CARRIAGE=v MODE=PRIMARY
    G1 X150 Y100
    SET_DUAL_CARRIAGE CARRIAGE=u MODE=COPY
    SET_DUAL_CARRIAGE CARRIAGE=v MODE=COPY
    SYNC_EXTRUDER_MOTION EXTRUDER=extruder1 MOTION_QUEUE=extruder

# A helper script to activate mirror mode
[gcode_macro ACTIVATE_MIRROR_MODE]
gcode:
    SET_DUAL_CARRIAGE CARRIAGE=x MODE=PRIMARY
    SET_DUAL_CARRIAGE CARRIAGE=y MODE=PRIMARY
    G1 X0 Y0
    ACTIVATE_EXTRUDER EXTRUDER=extruder
    SET_DUAL_CARRIAGE CARRIAGE=u MODE=PRIMARY
    SET_DUAL_CARRIAGE CARRIAGE=v MODE=PRIMARY
    G1 X300 Y100
    SET_DUAL_CARRIAGE CARRIAGE=u MODE=MIRROR
    SET_DUAL_CARRIAGE CARRIAGE=v MODE=COPY
    SYNC_EXTRUDER_MOTION EXTRUDER=extruder1 MOTION_QUEUE=extruder

[printer]
kinematics: generic_cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

## An optional input shaper support
#[input_shaper]
## The section is intentionally empty
#
#[delayed_gcode init_shaper]
#initial_duration: 0.1
#gcode:
#    SET_DUAL_CARRIAGE CARRIAGE=u
#    SET_DUAL_CARRIAGE CARRIAGE=v
#    SET_INPUT_SHAPER SHAPER_TYPE_X=<dual_carriage_x_shaper> SHAPER_FREQ_X=<dual_carriage_x_freq> SHAPER_TYPE_Y=<dual_carriage_y_shaper> SHAPER_FREQ_Y=<dual_carriage_y_freq>
#    SET_DUAL_CARRIAGE CARRIAGE=x MODE=PRIMARY
#    SET_DUAL_CARRIAGE CARRIAGE=y MODE=PRIMARY
#    SET_INPUT_SHAPER SHAPER_TYPE_X=<primary_carriage_x_shaper> SHAPER_FREQ_X=<primary_carriage_x_freq> SHAPER_TYPE_Y=<primary_carriage_y_shaper> SHAPER_FREQ_Y=<primary_carriage_y_freq>
