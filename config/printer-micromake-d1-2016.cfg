# This file contains a configuration for the "Micromake D1" delta
# printer (using the Makeboard 1.3 electronics). To use this config,
# the firmware should be compiled for the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

[stepper_a]
step_pin: PF0
dir_pin: !PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 32
endstop_pin: ^PE4
homing_speed: 100
position_endstop: 319.5
arm_length: 217.0

[stepper_b]
step_pin: PF6
dir_pin: !PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 32
endstop_pin: ^PJ0

[stepper_c]
step_pin: PL3
dir_pin: !PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 32
endstop_pin: ^PD2

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 20.067
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250
max_extrude_only_distance: 100.0

[fan]
pin: PH6

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: delta
max_velocity: 300
max_accel: 3000
max_z_velocity: 150
delta_radius: 95

[delta_calibrate]
radius: 80

#[probe]
#pin: ^!PD3

[display]
lcd_type: hd44780
rs_pin: PH1
e_pin: PH0
d4_pin: PA1
d5_pin: PA3
d6_pin: PA5
d7_pin: PA7
encoder_pins: ^PC6, ^PC4
click_pin: ^!PC2
kill_pin: ^!PG0
