# This file contains pin mappings for the Creality CR6-SE
# with Rev. 4.5.3 Motherboard (Late 2020/2021) as the heater pins changed.
# This config also works for the CR-ERA_V1.1.0.3
# To use this config, during "make menuconfig" select the STM32F103
# with a "28KiB bootloader" and serial (on USART1 PA10/PA9)
# communication.

# Flash this firmware by copying "out/klipper.bin" to a SD card and
# turning on the printer with the card inserted. The firmware
# filename must end in ".bin" and must not match the last filename
# that was flashed.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PB8
dir_pin: !PB7
enable_pin: !PC3
rotation_distance: 40
microsteps: 16
endstop_pin: PC4
position_min: -5
position_endstop: -5
position_max: 235
homing_speed: 50

[stepper_y]
step_pin: PB6
dir_pin: PB5
enable_pin: !PC3
rotation_distance: 40
microsteps: 16
endstop_pin: PC5
position_min: -2
position_endstop: -2
position_max: 235
homing_speed: 50

[stepper_z]
step_pin: PB4
dir_pin: !PB3
enable_pin: !PC3
rotation_distance: 8
microsteps: 16
endstop_pin: probe:z_virtual_endstop
position_min: -1.5
position_max: 250
homing_speed: 4
second_homing_speed: 1
homing_retract_dist: 2.0

[extruder]
max_extrude_only_distance: 1000.0
step_pin: PC2
dir_pin: !PB9
enable_pin: !PC3
rotation_distance: 30.4768
microsteps: 16
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB14
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PB1
control: pid
# tuned for stock hardware with 200 degree Celsius target
pid_Kp: 14.32
pid_Ki: 0.81
pid_Kd: 63.12
min_temp: 0
max_temp: 275

[heater_bed]
heater_pin: PB13
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PB0
control: pid
# tuned for stock hardware with 50 degree Celsius target
pid_Kp: 79.49
pid_Ki: 1.17
pid_Kd: 1349.52
min_temp: 0
max_temp: 120

[fan]
pin: PB15
kick_start_time: 0.5

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

# Before printing the PROBE_CALIBRATE command needs to be issued
# to run the probe calibration procedure, described at
# docs/Probe_Calibrate.md, to find the correct z_offset.
[probe]
pin: PA4
x_offset: 0.0
y_offset: 0.0
z_offset: 0.0
speed: 2.0
samples: 5

[safe_z_home]
home_xy_position: 117, 117
z_hop: 10

[filament_switch_sensor filament_sensor]
pause_on_runout: true
switch_pin: ^!PA7

[output_pin LED_pin]
pin: PA6

[static_digital_output daughterboard_communication]
pins: !PA5

[printer]
kinematics: cartesian
max_velocity: 500
max_accel: 500
max_z_velocity: 5
max_z_accel: 100
