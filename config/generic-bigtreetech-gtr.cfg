# This file contains common pin mappings for the BigTreeTech GTR.
# To use this config, the firmware should be compiled for the
# STM32F407 with a "32KiB bootloader".

# The "make flash" command does not work on the GTR. Instead,
# after running "make", copy the generated "klipper/out/klipper.bin" file to a
# file named "firmware.bin" on an SD card and then restart the GTR
# with that SD card.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC15
dir_pin: PF0
enable_pin: !PF1
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PF2
position_endstop: 0
position_max: 220
homing_speed: 50

[stepper_y]
step_pin: PE3
dir_pin: PE2
enable_pin: !PE4
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PC13
position_endstop: 0
position_max: 250
homing_speed: 50

[stepper_z]
step_pin: PB8
dir_pin: PB7
enable_pin: !PB9
microsteps: 16
rotation_distance: 8
endstop_pin: ^PE0
position_endstop: 0
position_max: 200
homing_speed: 12
second_homing_speed: 1

[extruder]
step_pin: PG12
dir_pin: PG11
enable_pin: !PG13
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB1 # Heat0
sensor_pin:  PC1 # T0 Header
sensor_type: EPCOS 100K B57560G104F
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 290

#[extruder1]
#step_pin: PD6
#dir_pin: PD5
#enable_pin: !PD7
#heater_pin: PA1 # Heat1
#sensor_pin: PC2 # T1
#...

#[extruder2]
#step_pin: PD1
#dir_pin: PD0
#enable_pin: !PD2
#heater_pin: PB0 # Heat2
#sensor_pin: PC3 # T2
#...

[heater_bed]
heater_pin: PA2
sensor_pin: PC0 # BED
sensor_type: ATC Semitec 104GT-2
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PE5 # FAN0

[heater_fan fan1]
pin: PE6 # FAN1

#[heater_fan fan2]
#pin: PC8 # FAN2

[temperature_sensor mcu_temp]
sensor_type: temperature_mcu

[temperature_sensor k_therm]
sensor_type: MAX31855
sensor_pin: PH9
spi_bus: spi2b

[mcu]
restart_method: command
serial: dev/serial0
# setup for PA9, PA10 USART1_tx / rx directly connected to the RPI GPIO TX / RX pins
# These are the pins the GTR uses for its built in RPI 40 pin connector
# only connect GND, TX, RX - supply 5v power to the RPI separately
# do not cross over TX/RX - it is done internally on the GTR PCB.
# Use "sudo raspi-config" to disable the serial terminal - but enable the serial port.
# When running "make menuconfig" you must un-select the USB Serial check box

[printer]
kinematics: cartesian
max_velocity: 500
max_accel: 3000
max_z_velocity: 12
max_z_accel: 5

########################################
# TMC2208 configuration
########################################

#[tmc2208 stepper_x]
#uart_pin: PC14
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 stepper_y]
#uart_pin: PE1
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 stepper_z]
#uart_pin: PB5
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc2208 extruder]
#uart_pin: PG10
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 extruder1]
#uart_pin: PD4
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 extruder2]
#uart_pin: PC12
#run_current: 0.800
#stealthchop_threshold: 999999


########################################
# TMC2130 configuration
########################################

#[tmc2130 stepper_x]
#cs_pin: PC14
#run_current: 0.800
#stealthchop_threshold: 0
#spi_software_mosi_pin: PG15
#spi_software_miso_pin: PB6
#spi_software_sclk_pin: PB3

#[tmc2130 stepper_y]
#cs_pin: PE1
#sense_resistor: 0.075
#run_current: 0.800
#stealthchop_threshold: 0
#spi_software_mosi_pin: PG15
#spi_software_miso_pin: PB6
#spi_software_sclk_pin: PB3

#[tmc2130 stepper_z]
#cs_pin: PB5
#sense_resistor: 0.075
#run_current: 0.650
#stealthchop_threshold: 0
#spi_software_mosi_pin: PG15
#spi_software_miso_pin: PB6
#spi_software_sclk_pin: PB3

#[tmc2130 extruder]
#cs_pin: PG10
#sense_resistor: 0.075
#run_current: 0.800
#stealthchop_threshold: 0
#spi_software_mosi_pin: PG15
#spi_software_miso_pin: PB6
#spi_software_sclk_pin: PB3

#[tmc2130 extruder1]
#cs_pin: PD4
#sense_resistor: 0.075
#run_current: 0.800
#stealthchop_threshold: 0
#spi_software_mosi_pin: PG15
#spi_software_miso_pin: PB6
#spi_software_sclk_pin: PB3

#[tmc2130 extruder2]
#cs_pin: PC12
#sense_resistor: 0.075
#run_current: 0.800
#stealthchop_threshold: 0
#spi_software_mosi_pin: PG15
#spi_software_miso_pin: PB6
#spi_software_sclk_pin: PB3

########################################
# TMC5160 configuration
########################################

#[tmc5160 stepper_x]
#cs_pin: PC14
#sense_resistor: 0.075
#run_current: 1
#stealthchop_threshold: 0
#spi_software_mosi_pin: PG15
#spi_software_miso_pin: PB6
#spi_software_sclk_pin: PB3

#[tmc5160 stepper_y]
#cs_pin: PE1
#sense_resistor: 0.075
#run_current: 1
#stealthchop_threshold: 0
#spi_software_mosi_pin: PG15
#spi_software_miso_pin: PB6
#spi_software_sclk_pin: PB3

#[tmc5160 stepper_z]
#cs_pin: PB5
#sense_resistor: 0.075
#run_current: 0.4
#stealthchop_threshold: 0
#spi_software_mosi_pin: PG15
#spi_software_miso_pin: PB6
#spi_software_sclk_pin: PB3

#[tmc5160 extruder]
#cs_pin: PG10
#sense_resistor: 0.075
#run_current: 0.5
#stealthchop_threshold: 0
#spi_software_mosi_pin: PG15
#spi_software_miso_pin: PB6
#spi_software_sclk_pin: PB3

#[tmc5160 extruder1]
#cs_pin: PD4
#sense_resistor: 0.075
#run_current: 0.800
#stealthchop_threshold: 0
#spi_software_mosi_pin: PG15
#spi_software_miso_pin: PB6
#spi_software_sclk_pin: PB3

#[tmc5160 extruder2]
#cs_pin: PC12
#sense_resistor: 0.075
#run_current: 0.800
#stealthchop_threshold: 0
#spi_software_mosi_pin: PG15
#spi_software_miso_pin: PB6
#spi_software_sclk_pin: PB3

########################################
# EXP1 / EXP2 (display) pins
########################################

# display section not tested - pinout should be correct but my LCD did not work yet

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PC11, EXP1_3=PC10, EXP1_5=PG8, EXP1_7=PG6, EXP1_9=<GND>,
    EXP1_2=PA15, EXP1_4=PA8, EXP1_6=PG7, EXP1_8=PG5, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PB14, EXP2_3=PD10, EXP2_5=PH10, EXP2_7=PB10,  EXP2_9=<GND>,
    EXP2_2=PB13, EXP2_4=PB12, EXP2_6=PB15, EXP2_8=<RST>, EXP2_10=<NC>
    # not sure on this: Pins EXP2_1, EXP2_6, EXP2_2 are also MISO, MOSI, SCK of bus "spi2"

# See the sample-lcd.cfg file for definitions of common LCD displays.

#[display]
#lcd_type: st7920
#cs_pin: EXP1_4
#sclk_pin: EXP1_5
#sid_pin: EXP1_3
#encoder_pins: ^EXP2_5, ^EXP2_3
#click_pin: ^!EXP1_2
#kill_pin: ^!EXP2_8

#[output_pin beeper]
#pin: EXP1_1
