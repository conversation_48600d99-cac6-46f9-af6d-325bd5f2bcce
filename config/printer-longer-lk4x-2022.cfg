# This file contains pin mappings for the stock 2022 LONGER3D LK4 X
# with the 32-bit LGT_KIT_V2_X board. To use this config, during
# "make menuconfig" select the STM32F103 with a "32KiB bootloader" and
# serial (on USART1 PA10/PA9) communication.

# Flash this firmware by copying "out/klipper.bin" to a SD card,
# then rename it to "firmware.bin"and turning on the printer with
# the card inserted.

# See docs/Config_Reference.md for a description of parameters.

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 4000
max_z_velocity: 5
max_z_accel: 100

[stepper_x]
step_pin: PD3
dir_pin: PD2
enable_pin: !PD4
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PE6
position_endstop: 0
position_max: 265
homing_speed: 80

[stepper_y]
step_pin: PD6
dir_pin: PD5
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PE4
position_endstop: 0
position_max: 220
homing_speed: 80

[stepper_z]
step_pin: PB4
dir_pin: !PB3
enable_pin: !PB5
microsteps: 16
rotation_distance: 8
# Without BLTouch
# endstop_pin: ^!PE2
# With BLTouch
endstop_pin: probe: z_virtual_endstop
# position_endstop: 0.0
position_min: -5
position_max: 250

[extruder]
max_extrude_only_distance: 200
step_pin: PB9
dir_pin: PB8
enable_pin: !PE0
microsteps: 16
rotation_distance: 4.4504
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA7
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC1
control: pid
# tuned for stock hardware with 200 degree Celsius target
pid_Kp: 25.011
pid_Ki: 1.463
pid_Kd: 106.922
min_temp: 0
max_temp: 260

[heater_bed]
heater_pin: PA0
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC0
control: pid
# tuned for stock hardware with 60 degree Celsius target
pid_Kp: 69.370
pid_Ki: 1.526
pid_Kd: 788.215
min_temp: 0
max_temp: 110

[fan]
pin: PA1

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[bed_screws]
screw1: 30,30
screw2: 190,30
screw3: 190,190
screw4: 30,190

# Pin mappings for BL_T port
[bltouch]
sensor_pin: ^PE1
control_pin: PA8
x_offset: -51
y_offset: -8
z_offset: 0 # You need to adjust this for your printer

[bed_mesh]
speed: 120
mesh_min: 10, 10
mesh_max: 210, 210
probe_count: 4, 4

[safe_z_home]
home_xy_position: 161, 118
speed: 80
z_hop: 10                 # Move up 10mm
z_hop_speed: 5
