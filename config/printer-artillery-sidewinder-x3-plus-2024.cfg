# For the Artillery Sidewinder X3 Pro/Plus that came factory installed with V1.29 firmware, follow these steps.
#   - Compile with the processor model STM32F401.
#   - Select the 48KiB bootloader,
#   - Select USB PA11/PA12 for USB communication interface.
#   - Select USART2 PA3/PA2 for UART communication via the Wi-Fi Tx/Rx pins
# To set 48KiB bootloader, you need to make a change to make menuconfig Kconfig file
# Here is a link to a how-to video: https://youtu.be/dpc76zN7Dh0
# Rename klipper.bin to yuntu.bin
# Copy the file out/yuntu.bin to an SD card and then restart the printer with that SD card
#
# For models that did not come with V1.29 installed
#   - Compile with the processor model STM32F401.
#   - Select the NO BOOTLOADER
#   - Select USB PA11/PA12 for USB communication interface.
#   - Select USART2 PA3/PA2 for UART communication via the Wi-Fi Tx/Rx pins
#   - quit, save, make
#   - Connect your printer to a computer running Pronterface, Octoprint, Repetier, BedLeveler5000 (anything with Console capability)
#   - Power on the machine and send M997 through console into Marlin, this will put the board into "DFU" mode
#   - DO NOT TURN OFF THE PRINTER
#   - Connect your Linux/Klipper device to the USB port
#   - Run lsusb and verify that the STM32 DFU device is visible (Bus 001 Device 006: ID 0483:df11 STMicroelectronics STM Device in DFU Mode)
#   - Run sudo make flash 0483:df11
#   - Run lsusb again and there should be two devices:
#        Bus 001 Device 007: ID 1d50:614e OpenMoko, Inc. stm32f401xc
#        Bus 001 Device 003: ID 0cf3:e010 Qualcomm Atheros Communications stm32f401xc
# See docs/Config_Reference.md for a description of parameters.

[mcu]
serial: /dev/ttyACM0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 15
max_z_accel: 100
square_corner_velocity: 5

[led LED_Light]
white_pin: PC2
initial_white: 1.0

[neopixel hotend_neopixel]
pin: PD2
color_order: GRB
initial_RED: 1.0
initial_GREEN: 1.0
initial_BLUE: 1.0

[stepper_x]
step_pin: PA8
dir_pin: PC9
enable_pin: !PA15
microsteps: 16
rotation_distance: 40
endstop_pin: !PB9
position_min: 0
position_endstop: 0
position_max: 315
homing_speed: 50

[stepper_y]
step_pin: PC7
dir_pin: !PC6
enable_pin: !PC8
microsteps: 16
rotation_distance: 40
endstop_pin: !PB8
position_endstop: 0
position_max: 315
homing_speed: 50

[stepper_z]
step_pin: PB10
dir_pin: !PA4
enable_pin: !PC4
rotation_distance: 8
microsteps: 16
position_min: -1
position_max: 400
endstop_pin: probe:z_virtual_endstop # Use Z- as endstop
#homing_speed: 10.0

[extruder]
max_extrude_only_distance: 100.0
step_pin: PC11
dir_pin: !PC10
enable_pin: !PC12
microsteps: 64
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA6
sensor_type: EPCOS 100K B57560G104F #Generic 3950
sensor_pin: PC5
min_extrude_temp: 170
min_temp: 0
max_temp: 300
# Calibrate E-Steps https://www.klipper3d.org/Rotation_Distance.html#calibrating-rotation_distance-on-extruders
rotation_distance: 17.75
# Calibrate PID: https://www.klipper3d.org/Config_checks.html#calibrate-pid-settings
#  - Example: PID_CALIBRATE HEATER=extruder TARGET=200
control: pid
pid_kp: 30.356
pid_ki: 1.857
pid_kd: 124.081
# Calibrate PA: https://www.klipper3d.org/Pressure_Advance.html

[heater_bed]
heater_pin: PA7
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC0
max_temp: 100
min_temp: 0
# Calibrate PID: https://www.klipper3d.org/Config_checks.html#calibrate-pid-settings
#  - Example: PID_CALIBRATE HEATER=heater_bed TARGET=60
control: pid
pid_kp: 64.230
pid_ki: 0.723
pid_kd: 1425.905

[heater_fan hotend_fan]
pin: PB1
heater: extruder
heater_temp: 50.0

[fan]
pin: PB0

[temperature_fan Artillery_MCU]
sensor_type: temperature_mcu
pin: PA5
max_temp: 60.0
target_temp: 40.0
min_temp: 0
shutdown_speed: 0.0
kick_start_time: 0.5
off_below: 0.19
max_speed: 1.0
min_speed: 0.0
control: watermark

[filament_switch_sensor filament_sensor]
pause_on_runout: true
switch_pin: PC1

[probe]
pin: PC14
x_offset:45.2
y_offset:11.6
speed:5
lift_speed:15
z_offset: 2.350

[safe_z_home]
home_xy_position: 110, 145 #   X, Y coordinate (e.g. 100, 100) where the Z homing should be
speed: 300.0
z_hop: 10
z_hop_speed: 15.0

[bed_mesh]
speed: 300
horizontal_move_z: 6
mesh_min: 46,15
mesh_max: 300,300
probe_count: 10, 10
fade_start: 1.0
fade_end: 0.0
algorithm: bicubic

[screws_tilt_adjust]
screw1: 120, 153
screw1_name: center reference
screw2: 7, 45
screw2_name: front left
screw3: 210, 45
screw3_name: front right
screw4: 227, 145
screw4_name: right center
screw5: 210, 245
screw5_name: rear right
screw6: 7, 245
screw6_name: rear left
screw7: 7, 145
screw7_name: left center
horizontal_move_z: 8
speed: 300
screw_thread: CW-M4
