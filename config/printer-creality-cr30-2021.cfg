# This file contains pin mappings for the Creality CR-30 PrintMill. To
# use this config, during "make menuconfig" select the STM32F103 with
# a "28KiB bootloader" and serial (on USART1 PA10/PA9) communication.

# If you prefer a direct serial connection, in "make menuconfig"
# select "Enable extra low-level configuration options" and select
# serial (on USART3 PB11/PB10), which is broken out on the 10 pin IDC
# cable used for the LCD module as follows:
# 3: Tx, 4: Rx, 9: GND, 10: VCC

# Flash this firmware by copying "out/klipper.bin" to a SD card and
# turning on the printer with the card inserted. The firmware
# filename must end in ".bin" and must not match the last filename
# that was flashed.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC2
dir_pin: !PB9
enable_pin: !PC3
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PA3
position_endstop: 0
position_max: 220
homing_speed: 50

[stepper_y]
step_pin: PB8
dir_pin: !PB7
enable_pin: !PC3
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PA7
position_endstop: 0
position_max: 240

[stepper_z]
step_pin: PB6
dir_pin: !PB5
enable_pin: !PC3
microsteps: 16
rotation_distance: 2.75
endstop_pin: ^!PA5
position_endstop: 0.0
position_max: 20000000

[extruder]
step_pin: PB4
dir_pin: !PB3
enable_pin: !PC3
microsteps: 16
rotation_distance: 23.0
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA0
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC5
control: pid
pid_Kp: 21.527
pid_Ki: 1.063
pid_Kd: 108.982
min_temp: 0
max_temp: 250

[filament_switch_sensor spool]
pause_on_runout: True
switch_pin: ^!PA6

[heater_bed]
heater_pin: PA1
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC4
control: pid
pid_Kp: 54.027
pid_Ki: 0.770
pid_Kd: 948.182
min_temp: 0
max_temp: 130

# K-FAN1
[fan]
pin: PA2

# K-FAN2
[fan_generic k_fan2]
pin: PC0

# K-FAN3
[fan_generic k_fan3]
pin: PC1

[temperature_sensor mcu_temp]
sensor_type: temperature_mcu
min_temp: 0
max_temp: 100

[output_pin led]
pin: PC14

# Neopixel LED support
# [neopixel led_neopixel]
# pin: PC14

# BL-touch
# [bltouch]
# control_pin: PB0
# sensor_pin: PB1

# TMC Uart Mod Pins:
# https://github.com/adelyser/Marlin-CR30/wiki
# [tmc2208 stepper_x]
# uart_pin: PB0
#
# [tmc2208 stepper_y]
# uart_pin: PB1
#
# [tmc2208 stepper_z]
# uart_pin: PA13
#
# [tmc2208 extruder]
# uart_pin: PA14

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[display]
lcd_type: st7920
cs_pin: PB12
sclk_pin: PB13
sid_pin: PB15
encoder_pins: ^PB14, ^PB10
click_pin: ^!PB2

[output_pin beeper]
pin: PC6

[printer]
kinematics: corexy
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

# Override to support unlimited belt size
# (homing Z simply resets its virtual position to 0.0)
[homing_override]
axes: z
set_position_z: 0
gcode:
  {% if params.X is not defined and params.Y is not defined and params.Z is not defined %}G28 X0 Y0{% endif %}
  {% if params.X is defined %}G28 X0{% endif %}
  {% if params.Y is defined %}G28 Y0{% endif %}
  G92 Z0

# Eject all printed parts from the belt
[gcode_macro BED_EJECT]
gcode:
  TURN_OFF_HEATERS
  G28 ; Re-home
  G1 X50 Y50 ; Move hotend out of the way of the belt
  G1 Z420 F4000 ; Unload belt
  G28 Z0 ; Re-home Z
