# This file is an example config file for rotary delta style printers.
# One may copy and edit this file to configure a new delta printer.

# ROTARY DELTA KINEMATICS ARE A WORK IN PROGRESS. Homing moves may
# timeout and some boundary checks are not implemented.

# See docs/Config_Reference.md for a description of parameters.

[stepper_a]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
gear_ratio: 107.000:16, 60:16
endstop_pin: ^PE4
homing_speed: 50
position_endstop: 252
upper_arm_length: 170.000
lower_arm_length: 320.000

[stepper_b]
step_pin: PF6
dir_pin: PF7
enable_pin: !PF2
microsteps: 16
gear_ratio: 107.000:16, 60:16
endstop_pin: ^PJ0

[stepper_c]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
microsteps: 16
gear_ratio: 107.000:16, 60:16
endstop_pin: ^PD2

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4
sensor_type: ATC Semitec 104GT-2
sensor_pin: PK5
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: watermark
min_temp: 0
max_temp: 130

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: rotary_delta
max_velocity: 300
max_accel: 3000
max_z_velocity: 50
shoulder_radius: 33.900
shoulder_height: 412.900

[delta_calibrate]
radius: 50
