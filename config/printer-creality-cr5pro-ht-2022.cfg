# This file contains common pin mappings for the Creality CR5 Pro HT.
# The mainboard is a Creality 3D v2.5.1 (8-bit mainboard with
# ATMega2560). To use this config, the firmware should be compiled for
# the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.
[stepper_x]
step_pin: PF0 #ar54
dir_pin: !PF1 #ar55
enable_pin: !PD7 #!ar38
microsteps: 16
rotation_distance: 40 # 16 microsteps * 200 steps/rotation / 80 steps/mm
endstop_pin: ^!PE5 #^ar3
position_min: 0
position_max: 300
position_endstop: 0
homing_speed: 50

[stepper_y]
step_pin: PF6 #ar60
dir_pin: !PF7 #ar61
enable_pin: !PF2 #!ar56
microsteps: 16
rotation_distance: 40 # 16 microsteps * 200 steps/rotation / 80 steps/mm
endstop_pin: ^!PJ1 #^ar14
position_endstop: 0
position_max: 220
homing_speed: 50

[stepper_z]
step_pin: PL3 #ar46
dir_pin: !PL1 #!ar48
enable_pin: !PK0 #!ar62
microsteps: 16
rotation_distance: 4 # 16 microsteps * 200 steps/rotation / 800 steps/mm
position_max: 380
position_min: -10
endstop_pin: probe:z_virtual_endstop

[safe_z_home]
home_xy_position: 140, 110
speed: 80
z_hop: 10
z_hop_speed: 10

[extruder]
step_pin: PA4 # ar26
dir_pin: !PA6 # !ar28
enable_pin: !PA2 # !ar24
microsteps: 16
rotation_distance: 23.24736 # 16 microsteps * 200 steps/rotation / 137.65 steps/mm
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4 #ar10
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5 #analog13
control: pid
pid_kp: 17.647
pid_ki: 1.079
pid_kd: 72.131
min_temp: 0
max_temp: 300

[heater_bed]
heater_pin: PH5 #ar8
sensor_type: ATC Semitec 104GT-2
sensor_pin: PK6 #analog14
control: pid
pid_kp: 75.501
pid_ki: 2.542
pid_kd: 560.598
min_temp: 0
max_temp: 130

[fan]
pin: PH6 #ar9

[mcu]
serial: /dev/ttyUSB0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 2000
max_z_velocity: 5
max_z_accel: 100

#[display]
# DWIN screen currently unsupported

[bltouch]
sensor_pin: ^PD2 #^ar19
control_pin: PB5 #ar11
set_output_mode: 5V
pin_move_time: 0.4
x_offset: 37.0
y_offset: 0.0
z_offset: 2.40
samples: 2
sample_retract_dist: 2
samples_result: average

[bed_mesh]
speed: 50
horizontal_move_z: 6
mesh_min: 47,10
mesh_max: 270,210
probe_count: 7,7
algorithm: bicubic

[pause_resume]
recover_velocity: 50

[filament_switch_sensor fil_runout_sensor]
pause_on_runout: True
switch_pin: !PE4 #ar2

[bed_screws]
screw1: 8,5
screw1_name: front left screw
screw2: 8,210
screw2_name: rear left screw
screw3: 218,210
screw3_name: rear right screw
screw4: 218,5
screw4_name: front right screw

[screws_tilt_adjust]
screw1: 8,5
screw1_name: front left screw
screw2: 8,210
screw2_name: rear left screw
screw3: 218,210
screw3_name: rear right screw
screw4: 218,5
screw4_name: front right screw
speed: 50
horizontal_move_z: 10
screw_thread: CW-M4

[output_pin case_light]
pin: PH4 #ar7
value: 1

[gcode_macro CASE_LIGHT_ON]
gcode:
    SET_PIN PIN=case_light VALUE=1

[gcode_macro CASE_LIGHT_OFF]
gcode:
    SET_PIN PIN=case_light VALUE=0
