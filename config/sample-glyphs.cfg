# Some alternate glyphs for use with 128x64 LCDs. These are used by
# adding them to your printer.cfg.

# See docs/Config_Reference.md for a description of parameters.


# ===============================
# Speedometer feedrate
# ===============================

[display_glyph feedrate]
data:
  ................
  ................
  ................
  ......******....
  ....**********..
  ...****....****.
  ..***........**.
  .***..........*.
  .**..*..........
  ***...*.........
  **.....*........
  **......**......
  **......***.....
  **.......**.....
  ................
  ................



# ===============================
# Blower-style fan
# ===============================

[display_glyph fan1]
data:
  ................
  ......****......
  ....**...***....
  ...*..*...*.*...
  ..*..........*..
  ..**.........*..
  .**....**...*.*.
  .*....*..*....*.
  .*....*..*....*.
  .*.*...**....**.
  ..*.........**..
  ..*..........*..
  ...*.*...*..*...
  ....***...**....
  ......****......
  ................

[display_glyph fan2]
data:
  ................
  ......****......
  ....**.*..**....
  ...*....*...*...
  ..*.*......*.*..
  ..*..........*..
  .*.....**.....*.
  .*.*..*..*...**.
  .**...*..*..*.*.
  .*.....**.....*.
  ..*..........*..
  ..*.*......*.*..
  ...*...*....*...
  ....**..*.**....
  ......****......
  ................



# ===============================
# Alternate square bed
# ===============================

[display_glyph bed]
data:
  ................
  ................
  ................
  ................
  ................
  ................
  ................
  ................
  ................
  ................
  ...**********...
  ..*..........*..
  .*............*.
  *..............*
  ****************
  ................

[display_glyph bed_heat1]
data:
  ................
  ......*...*.....
  ......*...*.....
  .....*...*......
  .....*...*......
  ......*...*.....
  ......*...*.....
  .....*...*......
  .....*...*......
  ................
  ...**********...
  ..*..........*..
  .*............*.
  *..............*
  ****************
  ................

[display_glyph bed_heat2]
data:
  ................
  .....*...*......
  .....*...*......
  ......*...*.....
  ......*...*.....
  .....*...*......
  .....*...*......
  ......*...*.....
  ......*...*.....
  ................
  ...**********...
  ..*..........*..
  .*............*.
  *..............*
  ****************
  ................



# ===============================
# Round bed
# ===============================

[display_glyph bed]
data:
  ................
  ................
  ................
  ................
  ................
  ................
  ................
  .....******.....
  ...**......**...
  ..*..........*..
  .*............*.
  .*............*.
  ..*..........*..
  ...**......**...
  .....******.....
  ................

[display_glyph bed_heat1]
data:
  .....*..*..*....
  .....*..*..*....
  ....*..*..*.....
  ....*..*..*.....
  .....*..*..*....
  .....*..*..*....
  ................
  .....******.....
  ...**......**...
  ..*..........*..
  .*............*.
  .*............*.
  ..*..........*..
  ...**......**...
  .....******.....
  ................

[display_glyph bed_heat2]
data:
  ....*..*..*.....
  ....*..*..*.....
  .....*..*..*....
  .....*..*..*....
  ....*..*..*.....
  ....*..*..*.....
  ................
  .....******.....
  ...**......**...
  ..*..........*..
  .*............*.
  .*............*.
  ..*..........*..
  ...**......**...
  .....******.....
  ................
