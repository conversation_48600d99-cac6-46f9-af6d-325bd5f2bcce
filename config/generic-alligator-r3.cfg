# This file contains common pin mappings for Alligator (Rev.3) board.
# To use this config, the firmware should be compiled for the Arduino
# Due.

# Remember flash procedure:
# sudo /etc/init.d/alligator-manager --erase
# sudo bossac -e -w -v -b -R -i -p ttyAMA0 klipper.bin
# See alligator github for alligator manager:
# https://github.com/3Dartists/alligator-manager

# See docs/Config_Reference.md for a description of parameters.

[static_digital_output DRV8825_microstepping]
pins:PC10
pins:PC29
pins:PC19
pins:PC18

[dac084S085 stepper_digipot]
enable_pin: PD2
spi_bus: spi0
# Scale the config so that the channel value can be specified in amps.
# (For Alligator v3.0 boards, use 2.50)
scale: 2.50
# Channel A in this example is X, B is Y, C is Z, D is E0,1,2,3.
channel_A: 1.5
channel_B: 1.5
channel_C: 1.5
channel_D: 1.0
# channel D will be the current used by all extruders if> 1

[stepper_x]
step_pin: PB24
dir_pin: !PB25
enable_pin: !PA15
microsteps: 32 # number of microstep 16, 32
rotation_distance: 16
endstop_pin: ^!PC5
position_endstop: -30
position_max: 220
position_min: -30
homing_speed: 50

[stepper_y]
step_pin: PB22
dir_pin: !PB23
enable_pin: !PC28
microsteps: 32
rotation_distance: 16
endstop_pin: ^!PC3
position_endstop: -8
position_min: -8
position_max: 220
homing_speed: 50

[stepper_z]
step_pin: PB20
dir_pin: PB21
enable_pin: !PA29
microsteps: 32
rotation_distance: 4
endstop_pin: ^!PC2
position_endstop: 0
position_max: 240
position_min: -1

[extruder]
step_pin: PB18
dir_pin: PB19
enable_pin: !PC27
microsteps: 32
rotation_distance: 7
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA19
sensor_pin: PC25
sensor_type: ATC Semitec 104NT-4-R025H42G
control: pid
pid_kp: 15.572
pid_ki: 0.446
pid_kd: 136.064
min_temp: 0
max_temp: 270

#[extruder1]
#step_pin: PB17
#dir_pin: !PB14
#enable_pin: !PD6
#microsteps: 32
#heater_pin: PC22
#sensor_pin: PA29

#[extruder2]
#step_pin: PB12
#dir_pin: !PB13
#enable_pin: !PD8
#microsteps: 32
#heater_pin: PC21
#sensor_pin: PC28

#[extruder3]
#step_pin: PB15
#dir_pin: !PB16
#enable_pin: !PD9
#microsteps: 32
#heater_pin: PA29
#sensor_pin: PB25

[heater_bed]
heater_pin: PA0
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA8
control: pid
pid_kp: 73.517
pid_ki: 1.132
pid_kd: 1193.728
min_temp: 0
max_temp: 130

[fan]
pin: PA7

#[heater_fan fan1]
#pin: PA5
#heater: extruder

[mcu]
serial: /dev/ttyAMA0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 1000
max_z_velocity: 20
max_z_accel: 100
