# This file contains common pin mappings for Minitronics v1.0
# boards. To use this config, the firmware should be compiled for the
# AVR atmega1280.

# The "make flash" command does not work on the Minitronics v1.0
# because the board actually has an atmega1281 chip. Use the following
# command to flash the board:
#  avrdude -carduino -patmega1281 -P/dev/ttyUSB0 -b57600 -D -Uout/klipper.elf.hex

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF2
dir_pin: PF1
enable_pin: !PF3
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PE3
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PA1
dir_pin: PA2
enable_pin: !PA0
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PE4
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PA4
dir_pin: !PA5
enable_pin: !PA3
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PB4
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PA7
dir_pin: PA6
enable_pin: !PG2
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF7
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PE5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF6
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PB7

[mcu]
serial: /dev/ttyUSB0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[static_digital_output yellow_led]
pins: PF0
