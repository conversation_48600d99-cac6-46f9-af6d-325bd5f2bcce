# This file contains a configuration snippet for a dual extruder
# printer using dual carriages (an "IDEX" printer).

# See docs/Config_Reference.md for a description of parameters.

# Definition for the primary carriage (holding the primary extruder)
[stepper_x]
step_pin: ar54
dir_pin: ar55
enable_pin: !ar38
microsteps: 16
rotation_distance: 40
endstop_pin: ^ar3
position_endstop: 0
position_max: 200
homing_speed: 50

# The definition for the primary extruder
[extruder]
step_pin: ar26
dir_pin: ar28
enable_pin: !ar24
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: ar10
sensor_type: EPCOS 100K B57560G104F
sensor_pin: analog13
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

# Helper script to park the carriage (called from T0 and T1 macros)
[gcode_macro PARK_extruder]
gcode:
    SAVE_GCODE_STATE NAME=park0
    G90
    G1 X0
    RESTORE_GCODE_STATE NAME=park0

# Activate the primary extruder
[gcode_macro T0]
gcode:
    PARK_{printer.toolhead.extruder}
    ACTIVATE_EXTRUDER EXTRUDER=extruder
    SET_DUAL_CARRIAGE CARRIAGE=0
    SET_GCODE_OFFSET Y=0

# Definition for the secondary carriage and extruder1
[dual_carriage]
axis: x
step_pin: ar16
dir_pin: ar17
enable_pin: !ar23
microsteps: 16
rotation_distance: 40
endstop_pin: ^ar2
position_endstop: 200
position_max: 200
homing_speed: 50
# A minimum distance between the carriages to enforce
safe_distance: 40

[extruder1]
step_pin: ar36
dir_pin: ar34
enable_pin: !ar30
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: ar11
sensor_type: EPCOS 100K B57560G104F
sensor_pin: analog15
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[gcode_macro PARK_extruder1]
gcode:
    SAVE_GCODE_STATE NAME=park1
    G90
    G1 X200
    RESTORE_GCODE_STATE NAME=park1

[gcode_macro T1]
gcode:
    PARK_{printer.toolhead.extruder}
    ACTIVATE_EXTRUDER EXTRUDER=extruder1
    SET_DUAL_CARRIAGE CARRIAGE=1
    SET_GCODE_OFFSET Y=15

# A helper script to activate copy mode
[gcode_macro ACTIVATE_COPY_MODE]
gcode:
    SET_DUAL_CARRIAGE CARRIAGE=0 MODE=PRIMARY
    G1 X0
    ACTIVATE_EXTRUDER EXTRUDER=extruder
    SET_DUAL_CARRIAGE CARRIAGE=1 MODE=PRIMARY
    G1 X100
    SET_DUAL_CARRIAGE CARRIAGE=1 MODE=COPY
    SYNC_EXTRUDER_MOTION EXTRUDER=extruder1 MOTION_QUEUE=extruder

# A helper script to activate mirror mode
[gcode_macro ACTIVATE_MIRROR_MODE]
gcode:
    SET_DUAL_CARRIAGE CARRIAGE=0 MODE=PRIMARY
    G1 X0
    ACTIVATE_EXTRUDER EXTRUDER=extruder
    SET_DUAL_CARRIAGE CARRIAGE=1 MODE=PRIMARY
    G1 X200
    SET_DUAL_CARRIAGE CARRIAGE=1 MODE=MIRROR
    SYNC_EXTRUDER_MOTION EXTRUDER=extruder1 MOTION_QUEUE=extruder

## An optional input shaper support
#[input_shaper]
## The section is intentionally empty
#
#[delayed_gcode init_shaper]
#initial_duration: 0.1
#gcode:
#    SET_DUAL_CARRIAGE CARRIAGE=1
#    SET_INPUT_SHAPER SHAPER_TYPE_X=<dual_carriage_shaper> SHAPER_FREQ_X=<dual_carriage_freq> SHAPER_TYPE_Y=<y_shaper> SHAPER_FREQ_Y=<y_freq>
#    SET_DUAL_CARRIAGE CARRIAGE=0
#    SET_INPUT_SHAPER SHAPER_TYPE_X=<primary_carriage_shaper> SHAPER_FREQ_X=<primary_carriage_freq> SHAPER_TYPE_Y=<y_shaper> SHAPER_FREQ_Y=<y_freq>
