# This file contains a configuration for the Anycubic Vyper printer from 2021.
# It uses a clone of the STM32F103 chip named GD32F103.
# There are 2 Mainboard versions out there v0.0.5 and v0.0.6, this config has
# been tested with the v0.0.6 only. It should probably work for v0.0.5 as well
# since there are no different changes in the Anycubic Marlin firmware.
# The LCD is not supported yet (might work, but didn't try yet).
#
# To create the Firmware you need to use the following configuration:
#     - Micro-controller: STM32
#     - Processor model: STM32F103
#     - Bootloader offset: 32KiB
#     - Communication interface: Serial (on USART1 PA10/PA9)
#
# To install the Firmware rename the klipper bin to `main_board_20xxxxxx.bin`
# and copy it to an SD Card.
# Power off the Printer, insert the SD Card and power it on.
# It should beep several times and the LCD should be stuck on the Splash screen.
# Now you should be able to connect to the Printer, you might need to change
# the mcu/serial setting in this config according to your set up.
#
# If you want to revert the Firmware back to Marlin you simply need to flash it
# with a anycubic firmware through the SD Card

[stepper_x]
step_pin: PC2
dir_pin: PB9
enable_pin: !PC3
microsteps: 16
rotation_distance: 40
endstop_pin: !PA7
position_min: -3
position_endstop: -3
position_max: 245
homing_speed: 30.0

[stepper_y]
step_pin: PB8
dir_pin: PB7
enable_pin: !PC13
microsteps: 16
rotation_distance: 32
endstop_pin: !PC5
position_min: -17
position_endstop: -17
position_max: 245
homing_speed: 30.0

[stepper_z]
step_pin: PB6
dir_pin: !PB5
enable_pin: !PC14
microsteps: 16
rotation_distance: 8
endstop_pin: PB2
position_endstop: 0.0
position_max: 260
position_min: -3
homing_speed: 5.0

[stepper_z1]
step_pin: PC0
dir_pin: !PC1
enable_pin: !PC15
microsteps: 16
rotation_distance: 8
endstop_pin: PC6

[extruder]
step_pin: PB4
dir_pin: !PB3
enable_pin: !PA15
microsteps: 16
rotation_distance: 22.76500  #has to be calibrated by everyone, official document: diameter = 7.25
gear_ratio: 50:17
full_steps_per_rotation: 200
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA1
sensor_type: ATC Semitec 104GT-2
sensor_pin: PC4
control: pid
pid_kp: 28.937
pid_ki: 1.295
pid_kd: 161.688
min_temp: 0
max_temp: 260

[heater_fan extruder_fan]
pin: PB1

[heater_bed]
heater_pin: PA4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PB0
control: pid
pid_kp: 67.648
pid_ki: 1.044
pid_kd: 1095.893
min_temp: 0
max_temp: 110

[fan]
pin: PA0

[controller_fan controller_fan]
pin: PA14
stepper: stepper_x,stepper_y,stepper_z,stepper_z1

[probe]
pin: !PB12
z_offset: 0
activate_gcode:
    probe_reset

[output_pin BEEPER_PIN]
pin: PB15
pwm: True
value: 0
shutdown_value: 0
cycle_time: 0.001
scale: 1

[output_pin probe_reset_pin]
pin: PB13

[output_pin LED]
pin: mcu:PA13
pwm: False
value: 0

[bed_mesh]
mesh_min: 15,15
mesh_max: 230, 230

[filament_switch_sensor runout]
pause_on_runout: True
switch_pin: PA5

[mcu]
serial: /dev/ttyUSB0
restart_method: command

[safe_z_home]
home_xy_position: -3,-17
z_hop: 10

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 10
max_z_accel: 100

[gcode_macro probe_reset]
gcode:
    SET_PIN PIN=probe_reset_pin VALUE=0
    G4 P300
    SET_PIN PIN=probe_reset_pin VALUE=1
    G4 P100
