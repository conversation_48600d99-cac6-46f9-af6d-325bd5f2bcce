# This file contains common pin mappings for the Mellow Fly-E3-v2.
# To use this config, the firmware should be compiled for the
# STM32F407 with a "32KiB bootloader".

# The "make flash" command does not work on the Fly-E3-v2. Instead,
# after running "make", copy the generated "out/klipper.bin" file to a
# file named "firmware.bin" or "klipper.bin" on an SD card and then restart the Fly-E3-v2
# with that SD card.

# See docs/Config_Reference.md for a description of parameters.

[mcu]
serial: /dev/serial/by-id/usb-Klipper_stm32f407xx_27004A001851323333353137-if00

[stepper_x]
step_pin: PE5
dir_pin: PC0
enable_pin: !PC1
microsteps: 16
rotation_distance: 30
full_steps_per_rotation: 200
endstop_pin: PE7 #X-STOP
position_endstop: 0
position_max: 200
homing_speed: 50
second_homing_speed: 10
homing_retract_dist: 5.0
homing_positive_dir: false
step_pulse_duration: 0.000004

[stepper_y]
step_pin: PE4
dir_pin: !PC13
enable_pin: !PC14
microsteps: 16
rotation_distance: 30
full_steps_per_rotation: 200
endstop_pin: PE8 #Y-STOP
position_endstop: 0
position_max: 200
homing_speed: 50
second_homing_speed: 10
homing_retract_dist: 5.0
homing_positive_dir: false
step_pulse_duration: 0.000004

[stepper_z]
step_pin: PE1
dir_pin: !PB7
enable_pin: !PE3
microsteps: 16
rotation_distance: 30
full_steps_per_rotation: 200
endstop_pin: PE9 #Z-STOP
position_min: 0
position_endstop: 0
position_max: 200
homing_speed: 5
second_homing_speed: 3
homing_retract_dist: 5.0
homing_positive_dir: false
step_pulse_duration: 0.000004

[extruder]
step_pin: PE2
dir_pin: PD5
enable_pin: !PD6
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC6 #E0

########################################
# Extruder 100K thermistor configuration
########################################
sensor_type: ATC Semitec 104GT-2
sensor_pin: PC4 #T0 TEMP
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 275
########################################
# Extruder MAX31865 PT100 2 wire config
########################################
# sensor_type: MAX31865
# sensor_pin: PD15 #PT-100
# spi_speed: 4000000
# spi_software_sclk_pin: PD12
# spi_software_mosi_pin: PD11
# spi_software_miso_pin: PD13
# rtd_nominal_r: 100
# rtd_reference_r: 430
# rtd_num_of_wires: 2
# rtd_use_50Hz_filter: True
min_temp: 0
max_temp: 300

#[extruder1]
#step_pin: PE0
#dir_pin: PD1
#enable_pin: !PD3
#microsteps: 16
#heater_pin: PC7 #E1
#sensor_pin: PC5 #T1 TEMP

########################################
# TMC2209 configuration
########################################

[tmc2209 stepper_x]
uart_pin: PC15
interpolate: False
run_current: 0.3
sense_resistor: 0.110
stealthchop_threshold: 999999

[tmc2209 stepper_y]
uart_pin: PB6
interpolate: False
run_current: 0.3
sense_resistor: 0.110
stealthchop_threshold: 999999

[tmc2209 stepper_z]
uart_pin: PD7
interpolate: False
run_current: 0.4
sense_resistor: 0.110
stealthchop_threshold: 999999

[tmc2209 extruder]
uart_pin: PD4
interpolate: False
run_current: 0.27
sense_resistor: 0.075
stealthchop_threshold: 999999

#[tmc2209 extruder1]
#uart_pin: PD0
#interpolate: False
#run_current: 0.27
#sense_resistor: 0.075
#stealthchop_threshold: 999999


#######################################
# Heated Bed
#######################################

[heater_bed]
heater_pin: PB0 #BED
sensor_type: Generic 3950
sensor_pin: PB1 #B-TEMP
max_power: 1.0
min_temp: 0
max_temp: 120
control: pid
pid_kp: 58.437
pid_ki: 2.347
pid_kd: 363.769

#######################################
# LIGHTING
#######################################

#[led Toolhead]
#white_pin: PA2 #FAN2
#cycle_time: 0.010
#initial_white: 0

#######################################
# COOLING
#######################################

[heater_fan hotend_fan]
pin: PA1 #FAN1
max_power: 1.0
kick_start_time: 0.5
heater: extruder
heater_temp: 50
fan_speed: 1.0

[controller_fan controller_fan]
pin: PA0 #FAN0
max_power: 1.0
kick_start_time: 0.5
heater: extruder
stepper: stepper_x, stepper_y, stepper_z
fan_speed: 1.0
idle_timeout: 60

[fan]
pin: PA3 #FAN3
max_power: 1.0
off_below: 0.2

[temperature_sensor Mellow_Fly_E3_V2]
sensor_type: temperature_mcu
min_temp: 5
max_temp: 80

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 50
max_z_accel: 100

########################################
# EXP1 / EXP2 (display) pins
########################################
[board_pins]
aliases:
    EXP1_1=PD10, EXP1_3=PA8,   EXP1_5=PE15,   EXP1_7=PA14,  EXP1_9=<GND>,
    EXP1_2=PA9,  EXP1_4=PA10,  EXP1_6=PE14,   EXP1_8=PA13,  EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PA6, EXP2_3=PB11,  EXP2_5=PB10,  EXP2_7=PE13,   EXP2_9=<GND>,
    EXP2_2=PA5, EXP2_4=PA4,   EXP2_6=PA7,   EXP2_8=<RST>, EXP2_10=<NC>,

# See the sample-lcd.cfg file for definitions of common LCD displays.

#######################################
# BL-Touch
#######################################

#[bltouch]
#sensor_pin: PC2
#control_pin: PE6
#z_offset: 0
