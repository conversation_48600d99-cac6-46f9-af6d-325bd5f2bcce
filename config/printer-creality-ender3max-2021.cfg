# This file contains pin mappings for the stock 2020 Creality Ender 3
# MAX. To use this config, during "make menuconfig" select the
# STM32F103 with a "28KiB bootloader" and serial (on USART1 PA10/PA9)
# communication.

# Because this printer has factory wiring, mounts, and firmware for
# a BLTouch, but does not ship with one at this time, default values
# for the sensor have been specified, but disabled, in anticipation of
# future revisions or user modification. User should take care to
# customize the offsets, particularly z-offset, for their specific unit.

# If you prefer a direct serial connection, in "make menuconfig"
# select "Enable extra low-level configuration options" and select
# serial (on USART3 PB11/PB10), which is broken out on the 10 pin IDC
# cable used for the LCD module as follows:
# 3: Tx, 4: Rx, 9: GND, 10: VCC

# Flash this firmware by copying "out/klipper.bin" to a SD card and
# turning on the printer with the card inserted. The firmware
# filename must end in ".bin" and must not match the last filename
# that was flashed.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC2
dir_pin: PB9
enable_pin: !PC3
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA5
position_endstop: 0
position_max: 300
homing_speed: 50

[stepper_y]
step_pin: PB8
dir_pin: PB7
enable_pin: !PC3
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA6
position_endstop: 0
position_max: 300
homing_speed: 50

[stepper_z]
step_pin: PB6
dir_pin: !PB5
enable_pin: !PC3
microsteps: 16
rotation_distance: 8
position_endstop: 0.0                     # disable to use BLTouch
endstop_pin: ^PA7                         # disable to use BLTouch
#endstop_pin: probe:z_virtual_endstop    # enable to use BLTouch
#position_min: -5                        # enable to use BLTouch
position_max: 340

# [safe_z_home]                         # enable for BLTouch
# home_xy_position: 150, 150
# speed: 100
# z_hop: 10
# z_hop_speed: 5

# [bltouch]                             # enable for BLTouch - fast-mode
# sensor_pin: ^PB1
# control_pin: PB0
# pin_up_touch_mode_reports_triggered: True
# probe_with_touch_mode: True
# x_offset: 50                          # modify as needed for bltouch location
# y_offset: -6                          # modify as needed for bltouch location
# z_offset: 0.0                         # modify as needed for bltouch or run PROBE_CALIBRATE
# speed: 10
# samples: 3
# sample_retract_dist: 5.0              # Can be set lower, example 2.5 depending on height of bltouch from bed
# lift_speed: 40
# samples_tolerance_retries: 3
# speed: 10
# samples: 2

# [bed_mesh]                            # enable for BLTouch
# speed: 300
# mesh_min: 50, 0
# mesh_max: 290, 290
# algorithm: bicubic
# probe_count: 7,7                      # 49 points due to large bed size
# horizontal_move_z: 5


# [gcode_macro G29]                    # If moving from marlin to klipper uncomment to mimic G29
# gcode:
#  BED_MESH_CALIBRATE
#  G1 X0 Y0 Z10 F4000

[extruder]
max_extrude_only_distance: 100.0
step_pin: PB4
dir_pin: PB3
enable_pin: !PC3
microsteps: 16
rotation_distance: 31.901           # Calibrate - see https://www.klipper3d.org/Rotation_Distance.html
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA1
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC5
control: pid
pid_Kp: 21.527
pid_Ki: 1.063
pid_Kd: 108.982
min_temp: 0
max_temp: 250
pressure_advance: 0.0               # Calibrate - see https://www.klipper3d.org/Pressure_Advance.html


[heater_bed]
heater_pin: PA2
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC4
control: pid
pid_Kp: 54.027
pid_Ki: 0.770
pid_Kd: 948.182
min_temp: 0
max_temp: 130

[fan]
pin: PA0

[filament_switch_sensor e0_sensor]
switch_pin: PA4

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0    # Run ls /dev/serial/by-id/* for micro-controller name
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[display]
lcd_type: st7920
cs_pin: PB12
sclk_pin: PB13
sid_pin: PB15
encoder_pins: ^PB14, ^PB10
click_pin: ^!PB2
