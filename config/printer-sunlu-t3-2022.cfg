# This file contains common pin mappings for the SUNLU Terminator T3 board

# To use this config, the firmware should be compiled for the
# STM32F103 with a "28KiB bootloader" and USB communication.
# Select "Disable SWD at startup (for GigaDevice stmf32f103 clones)"
# Also, select "Enable extra low-level configuration options" and configure
# "GPIO pins to set at micro-controller startup" to "!PA14".

# The "make flash" command does not work on the SUNLU Terminator T3 board. Instead,
# after running "make", copy the generated "out/klipper.bin" file to a
# file named "firmware.bin" on an SD card and then restart the board with that SD card.

# See docs/Config_Reference.md for a description of parameters.

# Rename the file to printer.cfg

##################################################################
# Printer
##################################################################

[mcu]
#obtain your MCU id using ls /dev/serial/by-path/*
serial: dev/serial/by-id/usb-Klipper_stm32f103xe_00000000000

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[static_digital_output usb_pullup_enable]
pins: !PA14

[bltouch]
sensor_pin: PC14
control_pin: PA1
x_offset: -28.45
y_offset: 4
z_offset: 1.915
pin_up_touch_mode_reports_triggered: FALSE #needed bc of the bltouch clone used by sunlu

[safe_z_home]
home_xy_position: 115,115
speed: 75
z_hop: 10
z_hop_speed: 5

[bed_mesh]
speed: 120
horizontal_move_z: 5
mesh_min: 10, 10
mesh_max: 190, 220
probe_count: 5,5
fade_start: 1
fade_end: 10

[bed_screws]
#for BED_SCREWS_ADJUST
screw1: 31,38 #X,Y Position
screw1_name: Front Left
screw2: 201,38 #X,Y Position
screw2_name: Front Right
screw3: 201,204 #X,Y Position
screw3_name: Rear Right
screw4: 31,204 #X,Y Position
screw4_name: Rear Left

##IMPORTANT. If using the filament sensor add CLEAR_PAUSE to your slicer's start gcode or to your print start macro.##
##The act of loading and unloading filament will trigger a paused state##
[filament_motion_sensor Filament_Sensor]
detection_length: 7.0
extruder: extruder
switch_pin: !PC15
pause_on_runout: FALSE
runout_gcode: PAUSE

#########################################################
# Motion Axis
#########################################################

[stepper_x]
step_pin: PB13
dir_pin: !PB12
enable_pin: !PB14
microsteps: 16
rotation_distance: 40
endstop_pin: !PC0
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_y]
step_pin: PB10
dir_pin: !PB2
enable_pin: !PB11
microsteps: 16
rotation_distance: 40
endstop_pin: !PC1
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_z]
step_pin: PB0
dir_pin: PC5
enable_pin: !PB1
microsteps: 16
rotation_distance: 4
position_max: 250
endstop_pin: probe:z_virtual_endstop

###################################################
# Heaters
###################################################

[extruder]
step_pin: PB3
dir_pin: !PB4
enable_pin: !PD2
microsteps: 16
rotation_distance: 23.18840579710145
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC8
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA0
control: pid
pid_Kp: 19.479
pid_Ki: 1.073
pid_Kd: 88.385
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PC9
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC3
control: pid
pid_Kp: 62.673
pid_Ki: 1.530
pid_Kd: 641.619
min_temp: 0
max_temp: 130

#########################################
# Fans
#########################################

[heater_fan Hotend]
pin: PC7
heater: extruder
heater_temp: 50.0

[fan]
pin: PC6

###############################################
# Stock Screen
###############################################

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PB5,  EXP1_3=PA9,   EXP1_5=PA10, EXP1_7=PB8,  EXP1_9=<GND>,
    EXP1_2=PA15, EXP1_4=<RST>, EXP1_6=PB9,  EXP1_8=PB15, EXP1_10=<5V>

[display]
lcd_type: st7920
cs_pin: PB8               #EXP1_7
sclk_pin: PB9             #EXP1_6
sid_pin: PB15             #EXP1_8
encoder_pins: ^PA10, ^PA9 #^EXP1_5, ^EXP1_3
click_pin: ^!PA15         #^!EXP1_2

[output_pin beeper]
pin: PB5    #EXP1_1
pwm: True
value: 0
shutdown_value: 0
cycle_time: 0.001
scale: 1
[gcode_macro M300]
gcode:
  {% set S = params.S|default(1000)|int %} ; S sets the tone frequency
  {% set P = params.P|default(100)|int %} ; P sets the tone duration
  {% set L = 0.5 %} ; L varies the PWM on time, close to 0 or 1 the tone gets a bit quieter. 0.5 is a symmetric waveform
  {% if S <= 0 %} ; dont divide through zero
  {% set F = 1 %}
  {% set L = 0 %}
  {% elif S >= 10000 %} ;max frequency set to 10kHz
  {% set F = 0 %}
  {% else %}
  {% set F = 1/S %} ;convert frequency to seconds
  {% endif %}
    SET_PIN PIN=beeper VALUE={L} CYCLE_TIME={F} ;Play tone
  G4 P{P} ;tone duration
    SET_PIN PIN=beeper VALUE=0
