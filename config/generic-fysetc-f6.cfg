# This file contains common pin mappings for a Fysetc F6 board.
# To use this config, the firmware should be compiled for the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: PK1  # PK2 for X-max
position_endstop: 0
position_max: 200

[stepper_y]
step_pin: PF6
dir_pin: PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: PJ1  # PJ0 for Y-max
position_endstop: 0
position_max: 200

[stepper_z]
step_pin: PL6
dir_pin: PL1
enable_pin: !PF4
microsteps: 16
rotation_distance: 8
endstop_pin: PB6  # PE4 for Z-max
position_endstop: 0
position_max: 400

[extruder]
step_pin: PA4
dir_pin: !PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PE3
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK4
control: pid
pid_Kp: 22
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 260

#[extruder1]
#step_pin: PC1
#dir_pin: !PC3
#enable_pin: !PC7
#heater_pin: PH3
#sensor_pin: PK5

#[extruder2]
#step_pin: PF5
#dir_pin: !PF3
#enable_pin: !PG1
#heater_pin: PH4
#sensor_pin: PK6

[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK7
control: watermark
min_temp: 0
max_temp: 130

#fan for printed model FAN0
[fan]
pin: PL5

#fan for hotend FAN1
#[heater_fan heatbreak_cooling_fan]
#pin: PL4
#shutdown_speed: 1

#fan for control board FAN2
#[heater_fan my_control_fan]
#pin: PL3

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB2.0-Serial-if00-port0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

#Prevents communication issues with SPI drivers
[static_digital_output disable_sdcard]
pins: PB0

########################################
# TMC UART configuration
########################################

# For TMC UART
#   1) Remove all jumpers below the stepper drivers.
#   2) Place jumper on the left and middle pin of the three pin header.

#[tmc2208 stepper_x]
#uart_pin: PG3
#tx_pin: PJ2
#run_current: 0.8
#stealthchop_threshold: 999999

#[tmc2208 stepper_y]
#uart_pin: PJ3
#tx_pin: PJ4
#run_current: 0.8
#stealthchop_threshold: 999999

#[tmc2208 stepper_z]
#uart_pin: PE2
#tx_pin: PE6
#run_current: 0.8
#stealthchop_threshold: 999999

#[tmc2208 extruder]
#uart_pin: PJ5
#tx_pin: PJ6
#run_current: 0.8
#stealthchop_threshold: 999999

#[tmc2208 extruder1]
#uart_pin: PE7
#tx_pin: PD4
#run_current: 0.8
#stealthchop_threshold: 999999

#[tmc2208 extruder2]
#uart_pin: PA1
#tx_pin: PD5
#run_current: 0.8
#stealthchop_threshold: 999999

########################################
# TMC SPI configuration
########################################

# For TMC SPI
#   1) Remove all jumpers below the stepper drivers.
#   2) Place jumper on the middle and right pin of the small three pin header.
#   3) Place jumpers on the four small two pin headers.

# For TMC Sensorless homing / DIAG1
#   1) Place jumper on the small two pin header near the endstop.

#[tmc2130 stepper_x]
#cs_pin: PG4
#diag1_pin: PK1
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_y]
#cs_pin: PG2
#diag1_pin: PJ1
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_z]
#cs_pin: PJ7
#diag1_pin: PB6
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder]
#cs_pin: PL2
#diag1_pin: PE4
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder1]
#cs_pin: PC5
#diag1_pin: PJ0
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder2]
#cs_pin: PL7
#diag1_pin: PK2
#run_current: 0.800
#stealthchop_threshold: 999999

########################################
# EXP1 / EXP2 (display) pins
########################################

# These must be turned 180° when compared to the default RAMPS layout.
# The aliases below are 180° turned from what Fysetc considers pin 1,
# but visually correspond to the plugs on the board.

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PC0, EXP1_2=PC2,
    EXP1_3=PH0, EXP1_4=PH1,
    EXP1_5=PA1, EXP1_6=PA3,    # Slot in the socket on this side
    EXP1_7=PA5, EXP1_8=PA7,
    EXP1_9=<GND>, EXP1_10=<5V>,

    # EXP2 header
    EXP2_1=PB3, EXP2_2=PB1,
    EXP2_3=PC6, EXP2_4=PB0,
    EXP2_5=PC4, EXP2_6=PB2,    # Slot in the socket on this side
    EXP2_7=PL0, EXP2_8=<RST>,
    EXP2_9=<GND>, EXP2_10=<5V> # or PG0 via jumper

# See the sample-lcd.cfg file for definitions of common LCD displays.

########################################
# Servos
########################################

# All Servo pins support hardware PWM.

#[servo my_servo1]
#pin: PB7

#[servo my_servo2]
#pin: PB5

#[servo my_servo3]
#pin: PB4

#[servo my_servo4]
#pin: PG5

########################################
# RGB header
########################################

# All RGB pins support hardware PWM.

#[output_pin blue]
#pin: PH6

#[output_pin red]
#pin: PE5

#[output_pin green]
#pin: PG5

########################################
# AUX-1 header
########################################

# Various analog and digital pins
# PK0 (analog), PK3 (analog), <GND>, <5V>
# PE0 (RXD0)  , PE1 (TXD0)  , <GND>, <5V>

########################################
# SD header
########################################

# Various digital / SPI pins
# PL0 , PB2, PB0, RST
# <5V>, PB3, PB1, <GND>

########################################
# UART header
########################################

# Various digital / UART pins
# <5V>
# <GND>
# PD2
# PD3

########################################
# I2C header
########################################

# SCL, SDA, <5V>, <GND>
