# Support for the green Tevo Tarantula Pro. To use this config, the firmware
# should be compiled for the AVR atmega2560.
# Note that this config file is for the "old green" Tarantula pro, with a
# MKS Gen_l 8-bit board.
# It _will not_ work out of the box for the "new orange" Tarantula pro with a
# MKS Sgen_l 32-bit board.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF0
dir_pin: !PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PE5
position_endstop: -2
position_max: 220
position_min: -2
homing_speed: 25.0

[stepper_y]
step_pin: PF6
dir_pin: PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PJ1
position_endstop: 0
position_max: 220
homing_speed: 25.0

[stepper_z]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PD3
position_endstop: 0
position_max: 200

# Enable for dual-z addon
#[stepper_z1]
#step_pin: PC1
#dir_pin: PC3
#enable_pin: !PC7
#microsteps: 16
#rotation_distance: 8

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 7.904
nozzle_diameter: 0.400
filament_diameter: 1.75

heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5
control: pid
pid_Kp: 22.5
pid_Ki: 1.78
pid_Kd: 74.16
min_temp: 0
max_temp: 220

[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: watermark
min_temp: 0
max_temp: 110

[fan]
pin: PH6

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB2.0-Serial-if00-port0

[printer]
kinematics: cartesian
max_velocity: 400
max_accel: 3000
max_z_velocity: 50
max_z_accel: 100

[heater_fan nozzle_fan]
pin: PH4

[display]
lcd_type: uc1701
cs_pin: PA3
a0_pin: PA5
encoder_pins: ^!PC6, ^!PC4
click_pin: ^!PC2
kill_pin: !PG0
menu_reverse_navigation: true
