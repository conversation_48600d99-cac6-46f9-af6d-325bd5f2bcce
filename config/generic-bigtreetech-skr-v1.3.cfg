# This file contains common pin mappings for the BIGTREETECH SKR V1.3
# board. To use this config, the firmware should be compiled for the
# LPC1768.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: P2.2
dir_pin: !P2.6
enable_pin: !P2.1
microsteps: 16
rotation_distance: 40
endstop_pin: P1.29  # P1.28 for X-max
position_endstop: 0
position_max: 320
homing_speed: 50

[stepper_y]
step_pin: P0.19
dir_pin: !P0.20
enable_pin: !P2.8
microsteps: 16
rotation_distance: 40
endstop_pin: P1.27  # P1.26 for Y-max
position_endstop: 0
position_max: 300
homing_speed: 50

[stepper_z]
step_pin: P0.22
dir_pin: P2.11
enable_pin: !P0.21
microsteps: 16
rotation_distance: 8
endstop_pin: P1.25  # P1.24 for Z-max
position_endstop: 0.5
position_max: 400

[extruder]
step_pin: P2.13
dir_pin: !P0.11
enable_pin: !P2.12
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: P2.7
sensor_type: EPCOS 100K B57560G104F
sensor_pin: P0.24
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 260

#[extruder1]
#step_pin: P0.1
#dir_pin: P0.0
#enable_pin: !P0.10
#heater_pin: P2.4
#sensor_pin: P0.25
#...

[heater_bed]
heater_pin: P2.5
sensor_type: ATC Semitec 104GT-2
sensor_pin: P0.23
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: P2.3

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 200
max_accel: 2000
max_z_velocity: 25
max_z_accel: 100


########################################
# TMC2208 configuration
########################################

# For TMC2208 UART
#   1) Remove all of the jumpers below the stepper drivers
#   2) Place jumpers on the red pin headers labeled XUART (XUART, YUART etc.)

#[tmc2208 stepper_x]
#uart_pin: P1.17
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 stepper_y]
#uart_pin: P1.15
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 stepper_z]
#uart_pin: P1.10
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc2208 extruder]
#uart_pin: P1.8
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 extruder1]
#uart_pin: P1.1
#run_current: 0.800
#stealthchop_threshold: 999999


########################################
# TMC2130 configuration
########################################

# For TMC SPI
#   1) Place jumpers on all the red pin headers under the stepper drivers
#   2) Remove jumpers from the red pin headers labeled XUART (XUART, YUART etc.)

#[tmc2130 stepper_x]
#cs_pin: P1.17
#spi_software_miso_pin: P0.5
#spi_software_mosi_pin: P4.28
#spi_software_sclk_pin: P0.4
##diag1_pin: P1.29
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_y]
#cs_pin: P1.15
#spi_software_miso_pin: P0.5
#spi_software_mosi_pin: P4.28
#spi_software_sclk_pin: P0.4
##diag1_pin: P1.27
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_z]
#cs_pin: P1.10
#spi_software_miso_pin: P0.5
#spi_software_mosi_pin: P4.28
#spi_software_sclk_pin: P0.4
##diag1_pin: P1.25
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc2130 extruder]
#cs_pin: P1.8
#spi_software_miso_pin: P0.5
#spi_software_mosi_pin: P4.28
#spi_software_sclk_pin: P0.4
##diag1_pin: P1.28
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder1]
#cs_pin: P1.1
#spi_software_miso_pin: P0.5
#spi_software_mosi_pin: P4.28
#spi_software_sclk_pin: P0.4
##diag1_pin: P1.26
#run_current: 0.800
#stealthchop_threshold: 999999


########################################
# EXP1 / EXP2 (display) pins
########################################

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=P1.30, EXP1_3=P1.18, EXP1_5=P1.20, EXP1_7=P1.22, EXP1_9=<GND>,
    EXP1_2=P0.28, EXP1_4=P1.19, EXP1_6=P1.21, EXP1_8=P1.23, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=P0.17, EXP2_3=P3.26, EXP2_5=P3.25, EXP2_7=P1.31, EXP2_9=<GND>,
    EXP2_2=P0.15, EXP2_4=P0.16, EXP2_6=P0.18, EXP2_8=<RST>, EXP2_10=<NC>
    # Pins EXP2_1, EXP2_6, EXP2_2 are also MISO, MOSI, SCK of bus "ssp0"

# See the sample-lcd.cfg file for definitions of common LCD displays.
