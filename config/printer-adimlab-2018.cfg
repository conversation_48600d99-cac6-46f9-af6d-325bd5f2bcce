# This file contains pin mappings for the ADIMLab 3d printer 2018.
# To use this config, the firmware should be compiled for the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PA3
dir_pin: !PA1
enable_pin: !PA5
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PA0
position_min: -5
position_endstop: -5
position_max: 310
homing_speed: 30.0

[stepper_y]
step_pin: PC5
dir_pin: !PC4
enable_pin: !PC6
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PA4
position_endstop: 0
position_max: 310
homing_speed: 30.0

[stepper_z]
step_pin: PC2
dir_pin: PC1
enable_pin: !PC3
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PA7
position_endstop: 0.0
position_max: 400
homing_speed: 5.0

[extruder]
step_pin: PL7
dir_pin: PL6
enable_pin: !PC0
microsteps: 16
rotation_distance: 34.557
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PE4
sensor_type: ATC Semitec 104GT-2
sensor_pin: PK0
control: pid
pid_Kp: 15.717
pid_Ki: 0.569
pid_Kd: 108.451
min_temp: 0
max_temp: 245

[heater_bed]
heater_pin: PG5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK2
control: pid
pid_Kp: 74.883
pid_Ki: 1.809
pid_Kd: 775.038
min_temp: 0
max_temp: 110

[verify_heater heater_bed]
# adjust for personal bed setup, this prevents stock heated bed from issuing
# false positive heating errors due to slow temperature increase
# 1 deg per 2 minutes.
heating_gain: 1
check_gain_time: 120

[mcu]
serial: /dev/ttyUSB0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 10
max_z_accel: 60

[output_pin stepper_xy_current]
pin: PL5
pwm: True
scale: 2.0
cycle_time: .000030
hardware_pwm: True
value: 1.3

[output_pin stepper_z_current]
pin: PL4
pwm: True
scale: 2.0
cycle_time: .000030
hardware_pwm: True
value: 1.3

[output_pin stepper_e_current]
pin: PL3
pwm: True
scale: 2.0
cycle_time: .000030
hardware_pwm: True
value: 1.25

[display]
lcd_type: st7920
cs_pin: PD1
sclk_pin: PJ1
sid_pin: PJ0
encoder_pins: ^PG0, ^PG1
click_pin: ^!PD2

# The filament runout sensor (on pin PA2) is not currently supported
# in Klipper.

[output_pin case_light]
pin: PH4
value: 1
