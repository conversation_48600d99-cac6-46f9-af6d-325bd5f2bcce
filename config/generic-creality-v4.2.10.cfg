# This file contains pin mappings for the Creality "v4.2.10" board. To
# use this config, during "make menuconfig" select the STM32F103 with
# a "28KiB bootloader" and serial (on USART1 PA10/PA9) communication.

# If you prefer a direct serial connection, in "make menuconfig"
# select "Enable extra low-level configuration options" and select
# serial (on USART3 PB11/PB10), which is broken out on the 10 pin IDC
# cable used for the LCD module as follows:
# 3: Tx, 4: Rx, 9: GND, 10: VCC

# Flash this firmware by copying "out/klipper.bin" to a SD card and
# turning on the printer with the card inserted. The firmware
# filename must end in ".bin" and must not match the last filename
# that was flashed.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC2
dir_pin: !PB9
enable_pin: !PC3
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PA3
position_endstop: 0
position_max: 220
homing_speed: 50

[stepper_y]
step_pin: PB8
dir_pin: !PB7
enable_pin: !PC3
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PA7
position_endstop: 0
position_max: 240

[stepper_z]
step_pin: PB6
dir_pin: !PB5
enable_pin: !PC3
microsteps: 16
rotation_distance: 2.75
endstop_pin: ^!PA5
position_endstop: 0.0
position_max: 200

[extruder]
step_pin: PB4
dir_pin: !PB3
enable_pin: !PC3
microsteps: 16
rotation_distance: 23.0
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA0
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC5
control: pid
pid_Kp: 21.527
pid_Ki: 1.063
pid_Kd: 108.982
min_temp: 0
max_temp: 250

# K-FAN1
[fan]
pin: PA2

[heater_bed]
heater_pin: PA1
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC4
control: pid
pid_Kp: 54.027
pid_Ki: 0.770
pid_Kd: 948.182
min_temp: 0
max_temp: 130

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 200
max_accel: 1500
max_z_velocity: 20
max_z_accel: 500

# [filament_switch_sensor spool]
# pause_on_runout: True
# switch_pin: ^!PA6

# K-FAN2
# [fan_generic k_fan2]
# pin: PC0

# K-FAN3
# [fan_generic k_fan3]
# pin: PC1

# [temperature_sensor mcu_temp]
# sensor_type: temperature_mcu
# min_temp: 0
# max_temp: 100


# Neopixel LED support
# [neopixel led_neopixel]
# pin: PC14

# BL-touch
# [bltouch]
# control_pin: PB0
# sensor_pin: PB1

# TMC Uart Mod Pins:
# https://github.com/adelyser/Marlin-CR30/wiki
# [tmc2208 stepper_x]
# uart_pin: PB0
#
# [tmc2208 stepper_y]
# uart_pin: PB1
#
# [tmc2208 stepper_z]
# uart_pin: PA13
#
# [tmc2208 extruder]
# uart_pin: PA14

# [display]
# lcd_type: st7920
# cs_pin: PB12
# sclk_pin: PB13
# sid_pin: PB15
# encoder_pins: ^PB14, ^PB10
# click_pin: ^!PB2

# [output_pin beeper]
# pin: PC6
