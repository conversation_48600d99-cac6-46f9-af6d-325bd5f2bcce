# This file contains common pin mappings for the BIGTREETECH SKR
# MINI. To use this config, the firmware should be compiled for the
# STM32F103 with a "28KiB bootloader" and USB communication.

# The "make flash" command does not work on the SKR mini. Instead,
# after running "make", copy the generated "out/klipper.bin" file to a
# file named "firmware.bin" on an SD card and then restart the SKR
# mini with that SD card.

# See docs/Config_Reference.md for a description of parameters.

# Note: This board has a design flaw in its thermistor circuits that
# cause inaccurate temperatures (most noticeable at low temperatures).

[stepper_x]
step_pin: PC6
dir_pin: PC7
enable_pin: !PB15
microsteps: 16
rotation_distance: 40
endstop_pin: PC2 # X+ is PA2
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PB13
dir_pin: PB14
enable_pin: !PB12
microsteps: 16
rotation_distance: 40
endstop_pin: PC1 # Y+ is PA1
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PB10
dir_pin: PB11
enable_pin: !PB2
microsteps: 16
rotation_distance: 8
endstop_pin: PC0 # Z+ is PC3
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PC5
dir_pin: PB0
enable_pin: !PC4
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA8
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA0
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

#[heater_bed]
#heater_pin: PC9
#sensor_type: ATC Semitec 104GT-2
#sensor_pin: PB1
#control: watermark
#min_temp: 0
#max_temp: 130

[fan]
pin: PC8

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PC10, EXP1_3=PB6,  EXP1_5=PC13, EXP1_7=PC15, EXP1_9=<GND>,
    EXP1_2=PC11, EXP1_4=PC12, EXP1_6=PB7,  EXP1_8=PC14, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PB4, EXP2_3=PD2,  EXP2_5=PB8, EXP2_7=PB9,   EXP2_9=<GND>,
    EXP2_2=PB3, EXP2_4=PA15, EXP2_6=PB5, EXP2_8=<RST>, EXP2_10=<NC>

# See the sample-lcd.cfg file for definitions of common LCD displays.
