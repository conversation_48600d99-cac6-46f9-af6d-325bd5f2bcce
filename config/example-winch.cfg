# This file is an example config file for cable winch style printers.
# One may copy and edit this file to configure a new cable winch
# printer.

# CABLE WINCH SUPPORT IS EXPERIMENTAL - PROCEED WITH CAUTION!

# Homing is not implemented on cable winch kinematics. In order to
# home the printer, manually send movement commands until the toolhead
# is at 0, 0, 0 and then issue a G28 command.

# See docs/Config_Reference.md for a description of parameters.

[stepper_a]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
anchor_x: 0
anchor_y: -2000
anchor_z: -100

[stepper_b]
step_pin: PF6
dir_pin: PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
anchor_x: 2000
anchor_y: 1000
anchor_z: -100

[stepper_c]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 40
anchor_x: -2000
anchor_y: 1000
anchor_z: -100

[stepper_d]
step_pin: PC1
dir_pin: PC3
enable_pin: !PC7
microsteps: 16
rotation_distance: 40
anchor_x: 0
anchor_y: 0
anchor_z: 3000

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4
sensor_type: ATC Semitec 104GT-2
sensor_pin: PK5
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: watermark
min_temp: 0
max_temp: 130

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: winch
max_velocity: 300
max_accel: 3000
