# This file contains common pin mappings for the BigTreeTech SKR 3.
# This board can ship with one of two chips, STM32H743 or STM32H723.
# To use this config, during "make menuconfig" enable "low-level
# options", "STM32H743" or "STM32H723", "128KiB bootloader",
# and "25MHz clock".

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PD4
dir_pin: PD3
enable_pin: !PD6
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC1
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PA15
dir_pin: !PA8
enable_pin: !PD1
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC3
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PE2
dir_pin: PE3
enable_pin: !PE0
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC0
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PD15
dir_pin: PD14
enable_pin: !PC7
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB3
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA2
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

#[extruder1]
#step_pin: PD11
#dir_pin: PD10
#enable_pin: !PD13
#heater_pin: PB4
#sensor_pin: PA3
#...

[heater_bed]
heater_pin: PD7
sensor_type: Generic 3950
sensor_pin: PA1
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PB7

#[heater_fan fan1]
#pin: PB6

#[heater_fan fan2]
#pin: PB5

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

########################################
# EXP1 / EXP2 (display) pins
########################################

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PC5, EXP1_3=PB1, EXP1_5=PE9,  EXP1_7=PE11, EXP1_9=<GND>,
    EXP1_2=PB0, EXP1_4=PE8, EXP1_6=PE10, EXP1_8=PE12, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PA6, EXP2_3=PE7, EXP2_5=PB2, EXP2_7=PC4,   EXP2_9=<GND>,
    EXP2_2=PA5, EXP2_4=PA4, EXP2_6=PA7, EXP2_8=<RST>, EXP2_10=<NC>

# See the sample-lcd.cfg file for definitions of common LCD displays.

########################################
# TMC2209 configuration
########################################

#[tmc2209 stepper_x]
#uart_pin: PD5
#run_current: 0.800
#diag_pin:

#[tmc2209 stepper_y]
#uart_pin: PD0
#run_current: 0.800
#diag_pin:

#[tmc2209 stepper_z]
#uart_pin: PE1
#run_current: 0.800
#diag_pin:

#[tmc2209 extruder]
#uart_pin: PC6
#run_current: 0.600
#diag_pin:

#[tmc2209 extruder1]
#uart_pin: PD12
#run_current: 0.600
#diag_pin:

########################################
# TMC2130 configuration
########################################

#[tmc2130 stepper_x]
#cs_pin: PD5
#spi_software_miso_pin: PE15
#spi_software_mosi_pin: PE13
#spi_software_sclk_pin: PE14
#run_current: 0.800
#stealthchop_threshold: 999999
#diag1_pin: PC1

#[tmc2130 stepper_y]
#cs_pin: PD0
#spi_software_miso_pin: PE15
#spi_software_mosi_pin: PE13
#spi_software_sclk_pin: PE14
#run_current: 0.800
#stealthchop_threshold: 999999
#diag1_pin: PC3

#[tmc2130 stepper_z]
#cs_pin: PE1
#spi_software_miso_pin: PE15
#spi_software_mosi_pin: PE13
#spi_software_sclk_pin: PE14
#run_current: 0.650
#stealthchop_threshold: 999999
#diag1_pin: PC0

#[tmc2130 extruder]
#cs_pin: PC6
#spi_software_miso_pin: PE15
#spi_software_mosi_pin: PE13
#spi_software_sclk_pin: PE14
#run_current: 0.800
#stealthchop_threshold: 999999
#diag1_pin: PC2

#[tmc2130 extruder1]
#cs_pin: PD12
#spi_software_miso_pin: PE15
#spi_software_mosi_pin: PE13
#spi_software_sclk_pin: PE14
#run_current: 0.800
#stealthchop_threshold: 999999
#diag1_pin: PA0
