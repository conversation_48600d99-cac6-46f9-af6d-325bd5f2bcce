# Configuration for DIY MMU2S based on SKR-MINI
# <PERSON> <EMAIL>
# More info : https://www.thingiverse.com/thing:3910546
#
#
# config inspired from https://github.com/mwr666/klipper/blob/sunbeam2.0c_multi_mcu_mmu2/config/printer-protodev-corexy-multi-mcu-mmu2.cfg


########################################
#
# Configuration for a SKR MINI board with 3 TMC2208 UART
# and 2 1.7A stepper motor at 1/16
# current for a 24V power
#
########################################
[mcu mmboard]
serial: /dev/serial/by-id/usb-Klipper_stm32f103xe_33FFD8054E48363008771743-if00

[respond]
default_type: command

# E0 : MMU2S 5mm road with the 5 gears
[manual_stepper gear_stepper]
step_pin: mmboard:PC5
dir_pin: !mmboard:PB0
enable_pin: !mmboard:PC4
microsteps: 16
# 140 : mk8 gear
#rotation_distance: 22.857
# 165 : fystec gear for mmu2s
rotation_distance: 19.394
velocity: 20
accel: 10
endstop_pin: ^mmboard:PC2 # PINDA X+

[tmc2208 manual_stepper gear_stepper]
uart_pin: mmboard:PC14
run_current: 1.000
sense_resistor: 0.110

# Y : MMU2S idler with the 5 ball bearings
[manual_stepper idler_stepper]
step_pin: mmboard:PB13
dir_pin: mmboard:PB14
enable_pin: !mmboard:PB12
microsteps: 16
rotation_distance: 128
velocity: 100
accel: 80

[tmc2209 manual_stepper idler_stepper]
uart_pin: mmboard:PB7
run_current: 0.800
sense_resistor: 0.110
stealthchop_threshold: 999999

# X : MMU2S color selector
[manual_stepper selector_stepper]
step_pin: mmboard:PC6
dir_pin: mmboard:PC7
enable_pin: !mmboard:PB15
# 80 step/mm
microsteps: 16
rotation_distance: 8
velocity: 35
accel: 100
endstop_pin: !mmboard:PC0 # switch endstop on the left Z-

[tmc2209 manual_stepper selector_stepper]
uart_pin: mmboard:PC12
run_current: 1.000
sense_resistor: 0.110
stealthchop_threshold: 999999

#IR
[filament_switch_sensor ir_sensor]
pause_on_runout: False
switch_pin: !P1.28 # P1.28 for X-max

################################
#
#  PARAMETER TO ADAPT TO YOUR SETUP
#
# variable_timeout_pause         : timeout of the MMU2S pause when user intervention is needed
# variable_disable_heater        : timeout of the heater during pause (temperature is saved and restored before resuming)
# variable_bowden_load_length1   : length of the bowden between the selector and the entry of the extruder (quick MOVE)
# variable_bowden_load_length2   : length of the bowden between the entry of the extruder and the extruder gear (slow MOVE)
# variable_bowden_unload_length  : length between the the extruder gear and the PINDA
# variable_pinda_load_length     : length between the MMU2S and the PINDA during loading
# variable_pinda_unload_length   : length to retract the filament into the MMU2S from the PINDA during unloading
# variable_colorselector         : the 5 positions of the color selector
# variable_idler                 : the 5 positions of the idler bearing ball
# variable_idler_home_position   : the homing position of the idler
# variable_pause_x               : x position when MMU2S need intervention and the printer is paused
# variable_pause_y               : y position when MMU2S need intervention and the printer is paused
# variable_pause_z               : z lift when MMU2S need intervention and the printer is paused
# variable_min_temp_extruder     : minimal required heater temperature to load/unload filament from the extruder gear to the nozzle
# variable_extruder_eject_temp   : heater temperature used to eject filament during home if the filament is already loaded
# variable_enable_5in1           : pass from MMU2S standard (0) to MMU2S-5in1 mode with splitter
#
################################
[gcode_macro VAR_MMU2S]
variable_timeout_pause: 36000
variable_disable_heater: 600
variable_bowden_load_length1: 770
variable_bowden_load_length2: 790
variable_bowden_unload_length: 830
variable_pinda_load_length: 120
variable_pinda_unload_length: 15
variable_colorselector = [71,57,43,29,16]
variable_idler = [5,20,35,50,65]
variable_idler_home_position: 85
variable_pause_x: 0
variable_pause_y: 300
variable_pause_z: 10
variable_min_temp_extruder: 180
variable_extruder_eject_temp: 200
variable_enable_5in1: 0
gcode:

# MMU2S SPLITTER
# variable_bowden_load_length1: 0
# variable_bowden_load_length2: 102
# variable_bowden_unload_length: 120
# MMU2S STANDARD
# variable_bowden_load_length1: 770
# variable_bowden_load_length2: 790
# variable_bowden_unload_length: 830

###############################
###############################
# END OF PARAMETER TO ADAPT TO YOUR SETUP
###############################
###############################


###############################
#
# MMU2S endstop status : IR extruder, PINDA, selector switch
# 0 : open, 1 : TRIGGERED
#
###############################

# Display the last queried status
[gcode_macro ENDSTOPS_STATUS]
gcode:
    M118 Enstop status :
    M118 Extruder : {printer["filament_switch_sensor ir_sensor"].filament_detected}
    M118 PINDA    : {printer.query_endstops.last_query["manual_stepper gear_stepper"]}
    M118 Selector : {printer.query_endstops.last_query["manual_stepper selector_stepper"]}

# Query and display the status
[gcode_macro infos]
gcode:
    QUERY_ENDSTOPS
    ENDSTOPS_STATUS

###############################
#
# PAUSE MACROS
# PAUSE_MMU is called when an human intervention is needed
# use MMU_UNLOCK to park the idler and start the manual intervention
# and use RESUME when the invention is ended to resume the current print
#
###############################

[pause_resume]
#recover_velocity: 50.

# park the idler, stop the delayed stop of the heater
[gcode_macro MMU_UNLOCK]
gcode:
   M118 Resume print
   SET_GCODE_VARIABLE MACRO=PAUSE_MMU VARIABLE=is_paused VALUE=0
   UPDATE_DELAYED_GCODE ID=disable_heater DURATION=0
   HOME_IDLER
   M109 S{printer["gcode_macro PAUSE_MMU"].extruder_temp}

[delayed_gcode disable_heater]
gcode:
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int != 0 %}
    M118 Disable extruder heater
    M104 S0
    {% endif %}

# Pause the MMU, park the extruder at the parking position
# Save the current state and start the delayed stop of the heated
# modify the timeout of the printer acorrdly to timeout_pause
[gcode_macro PAUSE_MMU]
variable_is_paused: 0
variable_extruder_temp: 0
gcode:
    SET_GCODE_VARIABLE MACRO=PAUSE_MMU VARIABLE=extruder_temp VALUE={printer.extruder.temperature}
    SET_GCODE_VARIABLE MACRO=PAUSE_MMU VARIABLE=is_paused VALUE=1
    SAVE_GCODE_STATE NAME=PAUSE_MMU_state
    SET_IDLE_TIMEOUT TIMEOUT={printer["gcode_macro VAR_MMU2S"].timeout_pause}
    UPDATE_DELAYED_GCODE ID=disable_heater DURATION={printer["gcode_macro VAR_MMU2S"].disable_heater}
    M118 START PAUSE
    bip
    bip
    bip
    PAUSE
    G91
    G1 Z{printer["gcode_macro VAR_MMU2S"].pause_z}
    G90
    G1 X{printer["gcode_macro VAR_MMU2S"].pause_x} Y{printer["gcode_macro VAR_MMU2S"].pause_y} F3000
    bip
    bip
    bip
    M118 END PAUSE
    RESTORE_GCODE_STATE NAME=PAUSE_MMU_state

############################################
#
# T0, T1, ..., T4 : Change extruder MACRO
# if th new extruder is different from the current extruder :
#     eject the filament if needed
#     load the new one
#
############################################

[gcode_macro T0]
gcode:
    {% if printer["gcode_macro SELECT_TOOL"].color_selected|int != 0 %}
    M117 Change Tool T0
    UT
    LT VALUE=0
    {% endif %}

[gcode_macro T1]
gcode:
    {% if printer["gcode_macro SELECT_TOOL"].color_selected|int != 1 %}
    M117 Change Tool T1
    UT
    LT VALUE=1
    {% endif %}

[gcode_macro T2]
gcode:
    {% if printer["gcode_macro SELECT_TOOL"].color_selected|int != 2 %}
    M117 Change Tool T2
    UT
    LT VALUE=2
    {% endif %}

[gcode_macro T3]
gcode:
    {% if printer["gcode_macro SELECT_TOOL"].color_selected|int != 3 %}
    M117 Change Tool T3
    UT
    LT VALUE=3
    {% endif %}

[gcode_macro T4]
gcode:
    {% if printer["gcode_macro SELECT_TOOL"].color_selected|int != 4 %}
    M117 Change Tool T4
    UT
    LT VALUE=4
    {% endif %}

############################################
#
# Unloading/Loading Macros
#
############################################

# Load filament from MMU2S to nozzle
[gcode_macro LT]
gcode:
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    M118 LT {params.VALUE|int} ...
        SELECT_TOOL VALUE={params.VALUE|int}
        LOAD_FILAMENT_TO_EXTRUDER
        LOAD_FILAMENT_IN_EXTRUDER
    {% endif %}

# Unload filament from nozzle to MMU2S
[gcode_macro UT]
gcode:
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    {% if printer["gcode_macro SELECT_TOOL"].color_selected|int != -1 %}
    M118 UT {printer["gcode_macro SELECT_TOOL"].color_selected|int} ...
    UNLOAD_FILAMENT_IN_EXTRUDER
    SELECT_TOOL VALUE={printer["gcode_macro SELECT_TOOL"].color_selected|int}
    UNLOAD_FILAMENT_FROM_EXTRUDER
    {% endif %}
    {% endif %}

############################################
#
# Select/Unselect a tool
# move the idler and the color selector (if needed) to the requested tool (0-4)
#
############################################

# Select a tool. move the idler and then move the color selector (if needed)
[gcode_macro SELECT_TOOL]
variable_tool_selected: -1
variable_color_selected: -1
gcode:
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    {% if printer["gcode_macro HOME_MMU"].home != -1 %}
        M118 Select Tool {params.VALUE} ...
        MANUAL_STEPPER STEPPER=idler_stepper MOVE={printer["gcode_macro VAR_MMU2S"].idler[params.VALUE|int]}
        {% if printer["gcode_macro VAR_MMU2S"].enable_5in1 == 0 %}
        MANUAL_STEPPER STEPPER=selector_stepper MOVE={printer["gcode_macro VAR_MMU2S"].colorselector[params.VALUE|int]}
        {% endif %}
        SET_GCODE_VARIABLE MACRO=SELECT_TOOL VARIABLE=tool_selected VALUE={params.VALUE}
        SET_GCODE_VARIABLE MACRO=SELECT_TOOL VARIABLE=color_selected VALUE={params.VALUE}
        M118 Tool {params.VALUE} Enabled
    {% else %}
    M118 Could not select tool, MMU is not homed
    {% endif %}
    {% endif %}

# Unselect a tool, only park the idler
[gcode_macro UNSELECT_TOOL]
gcode:
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    {% if printer["gcode_macro HOME_MMU"].home != -1 %}
        MANUAL_STEPPER STEPPER=idler_stepper MOVE={printer["gcode_macro VAR_MMU2S"].idler_home_position}
        SET_GCODE_VARIABLE MACRO=SELECT_TOOL VARIABLE=tool_selected VALUE=-1
    {% else %}
        M118 Could not unselect tool, MMU is not homed
    {% endif %}
    {% endif %}


############################################
#
# Loading/Unloading part FROM/TO EXTRUDER TO/FROM NOZZLE
# During loading, if the IR sensor does not detect the filament, it tries 3 times to reinsert it
#
############################################

# Try to reinsert the filament into the extruder
# Called when the IR sensor does not detect the filament
# the MMU2S push the filament of 10mm
# and the extruder gear try to insert it into the nozzle
[gcode_macro RETRY_LOAD_FILAMENT_IN_EXTRUDER]
gcode:
    {% if printer["filament_switch_sensor ir_sensor"].filament_detected == False %}
    M118 Retry loading ....
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    {% if printer.extruder.temperature > printer["gcode_macro VAR_MMU2S"].min_temp_extruder %}
        M118 Loading Filament...
        G91
        SELECT_TOOL VALUE={printer["gcode_macro SELECT_TOOL"].color_selected|int}
        MANUAL_STEPPER STEPPER=gear_stepper SET_POSITION=0
        MANUAL_STEPPER STEPPER=gear_stepper MOVE=10 SPEED=30
        MANUAL_STEPPER STEPPER=gear_stepper SET_POSITION=0
        G1 E10 F600
        UNSELECT_TOOL
        G1 E10 F1800
        G1 E22 F1393
        G1 E10 F614
        G92 E0
        G90
    {% endif %}
    {% endif %}
    {% endif %}

# Load the filament into the extruder
# the MMU2S push the filament of 20mm
# and the extruder gear try to insert it into the nozzle
# if the filament is not detected by the IR, call RETRY_LOAD_FILAMENT_IN_EXTRUDER 3 times
#
# Call PAUSE_MMU if the filament is not detected by the IR sensor
[gcode_macro LOAD_FILAMENT_IN_EXTRUDER]
gcode:
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    {% if printer.extruder.temperature > printer["gcode_macro VAR_MMU2S"].min_temp_extruder %}
        M118 Loading Filament...
        G91
        MANUAL_STEPPER STEPPER=gear_stepper SET_POSITION=0
        MANUAL_STEPPER STEPPER=gear_stepper MOVE=20 SPEED=30
        MANUAL_STEPPER STEPPER=gear_stepper SET_POSITION=0
        G1 E10 F600
        UNSELECT_TOOL
        G1 E10 F1800
        G1 E22 F1393
        G1 E10 F614
        G92 E0
        G90
        RETRY_LOAD_FILAMENT_IN_EXTRUDER
        RETRY_LOAD_FILAMENT_IN_EXTRUDER
        RETRY_LOAD_FILAMENT_IN_EXTRUDER
        IS_FILAMENT_IN_EXTRUDER
        M118 Load Complete
    {% else %}
    M118 Extruder too cold
    PAUSE_MMU
    {% endif %}
    {% endif %}

# Retry unload, try correct misalignment of bondtech gear
[gcode_macro RETRY_UNLOAD_FILAMENT_IN_EXTRUDER]
gcode:
    {% if printer["filament_switch_sensor ir_sensor"].filament_detected == True %}
    M118 Retry unloading ....
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    {% if printer.extruder.temperature > printer["gcode_macro VAR_MMU2S"].min_temp_extruder %}
        M118 Unloading Filament...
        G91
        G92 E0
        G1 E10 F500
        G1 E-20 F500
        G1 E-60 F3000
        G92 E0
        G90
    {% endif %}
    {% endif %}
    {% endif %}

# Unload the filament from the nozzle (without RAMMING !!!)
# Retract the filament from the nozzle to the out of the extruder gear
#
# Call PAUSE_MMU if the IR sensor detects the filament after the ejection
[gcode_macro UNLOAD_FILAMENT_IN_EXTRUDER]
gcode:
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    {% if printer.extruder.temperature > printer["gcode_macro VAR_MMU2S"].min_temp_extruder %}
    {% if printer["gcode_macro SELECT_TOOL"].tool_selected|int == -1 %}
        M118 Unloading Filament...
        G91
        G1 E-20 F500
        G1 E-30 F3000
        G90
        G92 E0
        G4 P1000
        RETRY_UNLOAD_FILAMENT_IN_EXTRUDER
        RETRY_UNLOAD_FILAMENT_IN_EXTRUDER
        RETRY_UNLOAD_FILAMENT_IN_EXTRUDER
        IS_FILAMENT_STUCK_IN_EXTRUDER
        M118 Filament removed
    {% else %}
        M118 Tool selected, UNSELECT it
        PAUSE_MMU
    {% endif %}
    {% else %}
        M118 Extruder too cold
        PAUSE_MMU
    {% endif %}
    {% endif %}

# Ramming process for standard PLA, code extracted from slic3r gcode
[gcode_macro RAMMING_SLICER]
gcode:
    G91
    G92 E0
    G1 E0.6873 F165
    G1 E0.7007 F168
    G1 E0.7376 F177
    G1 E0.7879 F189
    G1 E0.8214 F197
    G1 E0.8483 F204
    G1 E0.9019 F216
    G1 E0.9757 F234
    G1 E1.0260 F246
    G1 E1.0427 F250
    G1 E-15.000 F6000.0
    G1 E-24.5000 F5400.0
    G1 E-7.0000 F2700.0
    G1 E-3.5000 F1620.0
    G1 E20.000 F180.0
    G1 E-20.000 F160.0
    G1 E20.000 F140.0
    G1 E-20.000 F120.0
    G1 E-50.0000 F2000
    G92 E0

# Unload from extruder with ramming
[gcode_macro UNLOAD_FILAMENT_IN_EXTRUDER_WITH_RAMMING]
gcode:
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    {% if printer.extruder.temperature > printer["gcode_macro VAR_MMU2S"].min_temp_extruder %}
    {% if printer["gcode_macro SELECT_TOOL"].tool_selected|int == -1 %}
        M118 Ramming and Unloading Filament...
        G91
        RAMMING_SLICER
        UNLOAD_FILAMENT_IN_EXTRUDER
        M118 Filament rammed and removed
    {% else %}
        M118 Tool selected, UNSELECT it
        PAUSE_MMU
    {% endif %}
    {% else %}
        M118 Extruder too cold
        PAUSE_MMU
    {% endif %}
    {% endif %}

############################################
#
# Loading/Unloading MACROS from MMU2S to the enter of the extruder gear
#
############################################

# Load filament until the PINDA detect it and push it 10mm more to be sure is well detected
#
# PAUSE_MMU is called if the PINDA does not detect the filament
[gcode_macro LOAD_FILAMENT_TO_PINDA]
gcode:
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    {% if printer["gcode_macro SELECT_TOOL"].tool_selected|int != -1 %}
        M118 Loading filament to PINDA ...
        MANUAL_STEPPER STEPPER=gear_stepper SET_POSITION=0
        MANUAL_STEPPER STEPPER=gear_stepper MOVE={printer["gcode_macro VAR_MMU2S"].pinda_load_length} STOP_ON_ENDSTOP=2
        MANUAL_STEPPER STEPPER=gear_stepper SET_POSITION=0
        MANUAL_STEPPER STEPPER=gear_stepper MOVE=10
        IS_FILAMENT_IN_PINDA
        M118 Loading done to PINDA
    {% else %}
        M118 Cannot load to PINDA, tool not selected !!
    {% endif %}
    {% endif %}

# Load from the PINDA to the extruder gear
[gcode_macro LOAD_FILAMENT_FROM_PINDA_TO_EXTRUDER]
gcode:
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    {% if printer["gcode_macro SELECT_TOOL"].tool_selected|int != -1 %}
        M118 Loading filament from PINDA to extruder ...
        MANUAL_STEPPER STEPPER=gear_stepper SET_POSITION=0
        MANUAL_STEPPER STEPPER=gear_stepper MOVE={printer["gcode_macro VAR_MMU2S"].bowden_load_length1} SPEED=120 ACCEL=80
        MANUAL_STEPPER STEPPER=gear_stepper MOVE={printer["gcode_macro VAR_MMU2S"].bowden_load_length2} SPEED=60 ACCEL=80
        MANUAL_STEPPER STEPPER=gear_stepper SET_POSITION=0
        M118 Loading done from PINDA to extruder
    {% else %}
        M118 Cannot load to extruder, tool not selected !!
    {% endif %}
    {% endif %}

# Load from MMU2S to extruder gear by calling LOAD_FILAMENT_TO_PINDA and next LOAD_FILAMENT_FROM_PINDA_TO_EXTRUDER
#
# PAUSE_MMU is called if the PINDA does not detect the filament
[gcode_macro LOAD_FILAMENT_TO_EXTRUDER]
gcode:
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    {% if printer["gcode_macro SELECT_TOOL"].tool_selected|int != -1 %}
        M118 Loading filament from MMU to extruder ...
        {% if printer["gcode_macro VAR_MMU2S"].enable_5in1 == 0 %}
        LOAD_FILAMENT_TO_PINDA
        {% endif %}
        LOAD_FILAMENT_FROM_PINDA_TO_EXTRUDER
        M118 Loading done from MMU to extruder
    {% else %}
        M118 Cannot load to extruder, tool not selected !!
    {% endif %}
    {% endif %}

# Unload filament until the PINDA detect it and push it -10mm more to be sure is well not detected
#
# PAUSE_MMU is called if the PINDA does detect the filament
[gcode_macro UNLOAD_FILAMENT_FROM_PINDA]
gcode:
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    {% if printer["gcode_macro SELECT_TOOL"].tool_selected|int != -1 %}
        M118 Unloading filament from PINDA ...
        MANUAL_STEPPER STEPPER=gear_stepper SET_POSITION=0
        MANUAL_STEPPER STEPPER=gear_stepper MOVE=-{printer["gcode_macro VAR_MMU2S"].pinda_unload_length}
        MANUAL_STEPPER STEPPER=gear_stepper SET_POSITION=0
        MANUAL_STEPPER STEPPER=gear_stepper MOVE=-10
        MANUAL_STEPPER STEPPER=gear_stepper SET_POSITION=0
        IS_FILAMENT_STUCK_IN_PINDA
        SET_GCODE_VARIABLE MACRO=SELECT_TOOL VARIABLE=color_selected VALUE=-1
        M118 Unloading done from PINDA
    {% else %}
        M118 Cannot unload from PINDA, tool not selected !!
    {% endif %}
    {% endif %}

# Unload from extruder gear to the PINDA
[gcode_macro UNLOAD_FILAMENT_FROM_EXTRUDER_TO_PINDA]
gcode:
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    {% if printer["gcode_macro SELECT_TOOL"].tool_selected|int != -1 %}
        M118 Unloading filament from extruder to PINDA ...
        MANUAL_STEPPER STEPPER=gear_stepper SET_POSITION=0
        {% if printer["gcode_macro VAR_MMU2S"].enable_5in1 == 0 %}
        MANUAL_STEPPER STEPPER=gear_stepper MOVE=-{printer["gcode_macro VAR_MMU2S"].bowden_unload_length} SPEED=120 ACCEL=80 STOP_ON_ENDSTOP=-2
        IS_FILAMENT_STUCK_IN_PINDA
        {% else %}
        MANUAL_STEPPER STEPPER=gear_stepper MOVE=-{printer["gcode_macro VAR_MMU2S"].bowden_unload_length} SPEED=120 ACCEL=80
        {% endif %}
        M118 Unloading done from PINDA to extruder
    {% else %}
        M118 Cannot unload from extruder to PINDA, tool not selected !!
    {% endif %}
    {% endif %}

# Unload from the extruder gear to the MMU2S by calling UNLOAD_FILAMENT_FROM_EXTRUDER_TO_PINDA and next UNLOAD_FILAMENT_FROM_PINDA
[gcode_macro UNLOAD_FILAMENT_FROM_EXTRUDER]
gcode:
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    {% if printer["gcode_macro SELECT_TOOL"].tool_selected|int != -1 %}
        M118 Unloading filament from extruder to MMU ...
        UNLOAD_FILAMENT_FROM_EXTRUDER_TO_PINDA
        {% if printer["gcode_macro VAR_MMU2S"].enable_5in1 == 0 %}
        UNLOAD_FILAMENT_FROM_PINDA
        {% endif %}
        M118 Unloading done from extruder to MMU
    {% else %}
        M118 Cannot unload from extruder to MMU, tool not selected !!
    {% endif %}
    {% endif %}

############################################
#
# Endstop check MACROS
# Verify the state of the PINDA or the IR sensor
#
############################################

# Call PAUSE_MMU if the filament is not detected by the IR sensor
[gcode_macro IS_FILAMENT_IN_EXTRUDER]
gcode:
    {% if printer["filament_switch_sensor ir_sensor"].filament_detected == True %}
        M118 Filament in extruder
    {% else %}
        M118 Filament not in extruder
        PAUSE_MMU
    {% endif %}

# Call PAUSE_MMU if the filament is not detected by the PINDA
[gcode_macro IS_FILAMENT_IN_PINDA]
gcode:
    QUERY_ENDSTOPS
    IS_IN_PINDA

[gcode_macro IS_IN_PINDA]
gcode:
    {% if printer.query_endstops.last_query["manual_stepper gear_stepper"] == 1 %}
        M118 Filament in PINDA
    {% else %}
        M118 Filament not in PINDA
        PAUSE_MMU
    {% endif %}


# Call PAUSE_MMU if the filament is detected by the IR sensor
[gcode_macro IS_FILAMENT_STUCK_IN_EXTRUDER]
gcode:
    {% if printer["filament_switch_sensor ir_sensor"].filament_detected == True %}
        M118 Filament stuck in extruder
        PAUSE_MMU
    {% else %}
        M118 Filament not in extruder
    {% endif %}

# Call PAUSE_MMU if the filament is detected by the PINDA
[gcode_macro IS_FILAMENT_STUCK_IN_PINDA]
gcode:
     QUERY_ENDSTOPS
     IS_STUCK_IN_PINDA

[gcode_macro IS_STUCK_IN_PINDA]
gcode:
    {% if printer.query_endstops.last_query["manual_stepper gear_stepper"] == 1 %}
        M118 Filament stuck in PINDA
        PAUSE_MMU
    {% else %}
        M118 Filament not in PINDA
    {% endif %}

############################################
#
# M702 macro called by the end-gcode to eject the filament at the end of the print
#
############################################

# Eject the filament with ramming from the extruder nozzle to the MMU2S
[gcode_macro EJECT_RAMMING]
gcode:
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    {% if printer["gcode_macro SELECT_TOOL"].color_selected|int != -1 %}
    M118 UT {printer["gcode_macro SELECT_TOOL"].color_selected|int} ...
    UNLOAD_FILAMENT_IN_EXTRUDER_WITH_RAMMING
    SELECT_TOOL VALUE={printer["gcode_macro SELECT_TOOL"].color_selected|int}
    UNLOAD_FILAMENT_FROM_EXTRUDER
    {% endif %}
    {% endif %}

# M702 first part
# unload filament if inserted into the IR sensor
[gcode_macro M702]
gcode:
    UT
    QUERY_ENDSTOPS
    END_M702

# M702 second part
# Unselect the current tool
[gcode_macro END_M702]
gcode:
    {% if printer["gcode_macro VAR_MMU2S"].enable_5in1 == 0 %}
    {% if printer.query_endstops.last_query["manual_stepper gear_stepper"] != 1 %}
        UNSELECT_TOOL
        M118 M702 ok ...
    {% else %}
        M118 M702 Error !!!
    {% endif %}
    {% else %}
    UNSELECT_TOOL
    SET_GCODE_VARIABLE MACRO=SELECT_TOOL VARIABLE=color_selected VALUE=-1
    M118 M702 ok ...
    {% endif %}

############################################
#
# MACROS called during homing to try to eject the filament if loaded
#
############################################

# Preheat the heater if needed and unload the filament with ramming
# eject from nozlle to extruder gear out
[gcode_macro EJECT_FROM_EXTRUDER]
gcode:
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    {% if printer["filament_switch_sensor ir_sensor"].filament_detected == True %}
        M118 Filament in extruder, trying to eject it ..
        M118 Preheat Nozzle
        M109 S{printer["gcode_macro VAR_MMU2S"].extruder_eject_temp}
        UNLOAD_FILAMENT_IN_EXTRUDER_WITH_RAMMING
        M104 S0
    {% else %}
        M118 Filament not in extruder
    {% endif %}
    {% endif %}

# Eject from extruder gear to MMU2S
[gcode_macro EJECT_BEFORE_HOME]
gcode:
    M118 Eject Filament if loaded ...
    {% if printer["filament_switch_sensor ir_sensor"].filament_detected == True %}
        EJECT_FROM_EXTRUDER
        IS_FILAMENT_STUCK_IN_EXTRUDER
    {% endif %}
    {% if printer["gcode_macro VAR_MMU2S"].enable_5in1 == 0 %}
    {% if printer.query_endstops.last_query["manual_stepper gear_stepper"] == 1 %}
        UNLOAD_FILAMENT_FROM_EXTRUDER
        IS_FILAMENT_STUCK_IN_PINDA
        M118 Filament ejected !
    {% else %}
        M118 Filament already ejected !
    {% endif %}
    {% else %}
        M118 Filament already ejected !
    {% endif %}

############################################
#
# Homing MACROS
# HOME_MMU must be called before using the MMU2S
#
############################################

# Home the MMU
# eject filament if loaded with EJECT_BEFORE_HOME
# next home the mmu with HOME_MMU_ONLY
[gcode_macro HOME_MMU]
variable_home: -1
gcode:
    SET_FILAMENT_SENSOR SENSOR=ir_sensor ENABLE=0
    SET_GCODE_VARIABLE MACRO=HOME_MMU VARIABLE=home VALUE=1
    M118 Homing MMU ...
    QUERY_ENDSTOPS
    EJECT_BEFORE_HOME
    HOME_MMU_ONLY

# Home the idler
[gcode_macro HOME_IDLER]
gcode:
    M118 Homing idler
    MANUAL_STEPPER STEPPER=idler_stepper SET_POSITION=0
    MANUAL_STEPPER STEPPER=idler_stepper MOVE=7
    MANUAL_STEPPER STEPPER=idler_stepper MOVE=-95
    MANUAL_STEPPER STEPPER=idler_stepper SET_POSITION=2
    MANUAL_STEPPER STEPPER=idler_stepper MOVE={printer["gcode_macro VAR_MMU2S"].idler_home_position}

# Home the MMU :
# 1) home the idler
# 2) home the color selector (if needed)
# 3) try to load filament 0 to PINDA and then unload it. Used to verify the MMU2S gear
#
# if all is ok, the MMU2S is ready to be used
[gcode_macro HOME_MMU_ONLY]
gcode:
    {% if printer["gcode_macro PAUSE_MMU"].is_paused|int == 0 %}
    HOME_IDLER
    {% if printer["gcode_macro VAR_MMU2S"].enable_5in1 == 0 %}
    M118 Homing selector
    MANUAL_STEPPER STEPPER=selector_stepper SET_POSITION=0
    MANUAL_STEPPER STEPPER=selector_stepper MOVE=-76 STOP_ON_ENDSTOP=1
    MANUAL_STEPPER STEPPER=selector_stepper SET_POSITION=0
    {% endif %}
    MANUAL_STEPPER STEPPER=idler_stepper MOVE=0
    SET_GCODE_VARIABLE MACRO=SELECT_TOOL VARIABLE=tool_selected VALUE=-1
    SET_GCODE_VARIABLE MACRO=SELECT_TOOL VARIABLE=color_selected VALUE=-1
    M118 Test load filament 0
    SELECT_TOOL VALUE=0
    {% if printer["gcode_macro VAR_MMU2S"].enable_5in1 == 0 %}
    LOAD_FILAMENT_TO_PINDA
    G4 P1000
    UNLOAD_FILAMENT_FROM_PINDA
    {% else %}
    SET_GCODE_VARIABLE MACRO=SELECT_TOOL VARIABLE=color_selected VALUE=-1
    {% endif %}
    UNSELECT_TOOL
    SET_GCODE_VARIABLE MACRO=HOME_MMU VARIABLE=home VALUE=1
    M118 Homing MMU ended ...
    {% else %}
    M118 Homing MMU failed, MMU is paused, unlock it ...
    {% endif %}

###############################################
