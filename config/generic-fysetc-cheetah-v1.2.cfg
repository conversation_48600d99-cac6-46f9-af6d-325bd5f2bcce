# This file contains common pin mappings for the Fysetc Cheetah v1.2b
# board. To use this config, the firmware should be compiled for the
# STM32F103 with "No bootloader" and serial (on USART1 PA10/PA9)
# communication.

# The "make flash" command does not work on the Cheetah. Instead,
# after running "make", run the following command to flash the board:
#  stm32flash -w out/klipper.bin -v -i rts,-dtr,dtr /dev/ttyUSB0

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PB8
dir_pin: !PB9
enable_pin: !PA8
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA1
position_endstop: 0
position_max: 200
homing_speed: 50

[tmc2208 stepper_x]
uart_pin: PA12
tx_pin: PA11
run_current: 0.800
stealthchop_threshold: 999999

[stepper_y]
step_pin: PB2
dir_pin: !PB3
enable_pin: !PB1
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB4
position_endstop: 0
position_max: 200
homing_speed: 50

[tmc2208 stepper_y]
uart_pin: PB7
tx_pin: PB6
run_current: 0.800
stealthchop_threshold: 999999

[stepper_z]
step_pin: PC0
dir_pin: PC1
enable_pin: !PC2
microsteps: 16
rotation_distance: 8
endstop_pin: ^PA15
position_endstop: 0
position_max: 200

[tmc2208 stepper_z]
uart_pin: PB11
tx_pin: PB10
run_current: 0.800
stealthchop_threshold: 999999

[extruder]
step_pin: PC15
dir_pin: !PC14
enable_pin: !PC13
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC6
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC4
control: pid
pid_kp: 21.527
pid_ki: 1.063
pid_kd: 108.982
min_temp: 0
max_temp: 250

[tmc2208 extruder]
uart_pin: PA3
tx_pin: PA2
run_current: 1.0
stealthchop_threshold: 999999

[heater_bed]
heater_pin: PC7
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC5
control: pid
pid_kp: 54.027
pid_ki: 0.770
pid_kd: 948.182
min_temp: 0
max_temp: 130

[fan]
pin: PC8

[heater_fan heatbreak_cooling_fan]
pin: PB0

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB2.0-Serial-if00-port0
restart_method: cheetah

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PC9,  EXP1_3=PC11, EXP1_5=PC10, EXP1_7=PB12, EXP1_9=<GND>,
    EXP1_2=PC12, EXP1_4=PB14, EXP1_6=PB13, EXP1_8=PB15, EXP1_10=<5V>
    # Pins EXP1_4, EXP1_8, EXP1_6 are also MISO, MOSI, SCK of bus "spi2"

# See the sample-lcd.cfg file for definitions of common LCD displays.
