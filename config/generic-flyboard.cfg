# This file contains common pin mappings for the 9 stepper motor
# "flymaker flyboard FLYF407ZG" board from creative3dprinter.com. To
# use this config, the firmware should be compiled for the STM32F407
# with a "32KiB bootloader".

# The "make flash" command does not work on the FLYBOARD. Instead,
# after running "make", copy the generated "out/klipper.bin" file to a
# file named "firmware.bin" on an SD card and then restart the FLYBOARD
# with that SD card.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PB9
dir_pin: PE0
enable_pin: !PE1
microsteps: 16
rotation_distance: 40
endstop_pin: PC3
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PB8
dir_pin: PG11
enable_pin: !PG12
microsteps: 16
rotation_distance: 40
endstop_pin: PF2
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PA8
dir_pin: PD6
enable_pin: !PD7
microsteps: 16
rotation_distance: 8
endstop_pin: PF0
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PC7
dir_pin: PD3
enable_pin: !PD4
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PF7 # Heat0
sensor_pin: PA0 # T1 Header
sensor_type: EPCOS 100K B57560G104F
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 350

#[extruder1]
#step_pin: PC6
#dir_pin: PA15
#enable_pin: !PD0
#heater_pin: PF6 # Heat1
#sensor_pin: PC1 # T2
#...

#[extruder2]
#step_pin: PD15
#dir_pin: PG7
#enable_pin: !PG8
#heater_pin: PE6 # Heat2
#sensor_pin: PC0 # T3
#...

#[extruder3]
#step_pin: PD14
#dir_pin: PG4
#enable_pin: !PG5
#heater_pin: PE5 # Heat3
#sensor_pin: PF10 # T4
#...

#[extruder4]
#step_pin: PD13
#dir_pin: PD11
#enable_pin: !PG2
#heater_pin: PE4 # Heat4
#sensor_pin: PF5 # T5
#...

#[extruder4]
#step_pin: PD12
#dir_pin: PD8
#enable_pin: !PD9
#heater_pin: PE3 # Heat5
#sensor_pin: PF4 # T6
#...

[heater_bed]
heater_pin: PE2
sensor_pin: PF3 # T0
sensor_type: ATC Semitec 104GT-2
control: watermark
min_temp: 0
max_temp: 200

[fan]
pin: PF8

[heater_fan fan1]
pin: PF9

#[heater_fan fan2]
#pin: PA2

#[heater_fan fan3]
#pin: PA1

#[heater_fan fan4]
#pin: PE13

#[heater_fan fan5]
#pin: PB11

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: corexy
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100


########################################
# TMC2208 configuration
########################################

#[tmc2208 stepper_x]
#uart_pin: PG13
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 stepper_y]
#uart_pin: PG10
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 stepper_z]
#uart_pin: PD5
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc2208 extruder]
#uart_pin: PD1
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 extruder1]
#uart_pin: PA14
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 extruder2]
#uart_pin: PG6
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 extruder3]
#uart_pin: PG3
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 extruder4]
#uart_pin: PD10
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 extruder5]
#uart_pin: PB12
#run_current: 0.800
#stealthchop_threshold: 999999


########################################
# TMC2130 configuration
########################################

#[tmc2130 stepper_x]
#cs_pin: PG13
##diag1_pin: PC3
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_y]
#cs_pin: PG10
##diag1_pin: PF2
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_z]
#cs_pin: PBD5
##diag1_pin: PF0
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc2130 extruder]
#cs_pin: PD1
##diag1_pin: PE15
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder1]
#cs_pin: PA14
##diag1_pin: PE10
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder2]
#cs_pin: PG6
##diag1_pin: PC15
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder3]
#cs_pin: PG3
##diag1_pin: PC15
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder4]
#cs_pin: PD10
##diag1_pin: PC15
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder5]
#cs_pin: PB12
##diag1_pin: PC15
#run_current: 0.800
#stealthchop_threshold: 999999


########################################
# EXP1 / EXP2 (display) pins
########################################

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PB10, EXP1_3=PE14, EXP1_5=PE10, EXP1_7=PE8, EXP1_9=<GND>,
    EXP1_2=PE15, EXP1_4=PE12, EXP1_6=PE9,  EXP1_8=PE7, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PB14, EXP2_3=PC5,  EXP2_5=PC4,  EXP2_7=PB2,   EXP2_9=<GND>,
    EXP2_2=PB13, EXP2_4=PF11, EXP2_6=PB15, EXP2_8=<RST>, EXP2_10=<GND>
    # Pins EXP2_1, EXP2_6, EXP2_2 are also MISO, MOSI, SCK of bus "spi2"

# See the sample-lcd.cfg file for definitions of common LCD displays.
