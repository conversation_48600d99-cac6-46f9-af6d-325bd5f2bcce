# This file contains common pin mappings for the BIGTREETECH SKR E3
# DIP. To use this config, the firmware should be compiled for the
# STM32F103 with a "28KiB bootloader" and USB communication. Also,
# select "Enable extra low-level configuration options" and configure
# "GPIO pins to set at micro-controller startup" to "!PC13".

# The "make flash" command does not work on the SKR E3 DIP. Instead,
# after running "make", copy the generated "out/klipper.bin" file to a
# file named "firmware.bin" on an SD card and then restart the SKR E3
# DIP with that SD card.

# See docs/Config_Reference.md for a description of parameters.

# Note: This board has a design flaw in its thermistor circuits that
# cause inaccurate temperatures (most noticeable at low temperatures).

[stepper_x]
step_pin: PC6
dir_pin: !PB15
enable_pin: !PC7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC1
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_y]
step_pin: PB13
dir_pin: !PB12
enable_pin: !PB14
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC0
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_z]
step_pin: PB10
dir_pin: PB2
enable_pin: !PB11
microsteps: 16
rotation_distance: 8
endstop_pin: ^PC15
position_endstop: 0.0
position_max: 250

[extruder]
step_pin: PB0
dir_pin: !PC5
enable_pin: !PB1
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC8
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA0
control: pid
pid_Kp: 21.527
pid_Ki: 1.063
pid_Kd: 108.982
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PC9
sensor_type: ATC Semitec 104GT-2
sensor_pin: PC3
control: pid
pid_Kp: 54.027
pid_Ki: 0.770
pid_Kd: 948.182
min_temp: 0
max_temp: 130

[fan]
pin: PA8

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[static_digital_output usb_pullup_enable]
pins: !PC13


########################################
# TMC2208 configuration
########################################

#[tmc2208 stepper_x]
#uart_pin: PC10
#run_current: 0.580
#stealthchop_threshold: 999999

#[tmc2208 stepper_y]
#uart_pin: PC11
#run_current: 0.580
#stealthchop_threshold: 999999

#[tmc2208 stepper_z]
#uart_pin: PC12
#run_current: 0.580
#stealthchop_threshold: 999999

#[tmc2208 extruder]
#uart_pin: PD2
#run_current: 0.650
#stealthchop_threshold: 999999


########################################
# TMC2130 configuration
########################################

#[tmc2130 stepper_x]
#cs_pin: PC10
#spi_bus: spi3
#run_current: 0.580
#stealthchop_threshold: 999999

#[tmc2130 stepper_y]
#cs_pin: PC11
#spi_bus: spi3
#run_current: 0.580
#stealthchop_threshold: 999999

#[tmc2130 stepper_z]
#cs_pin: PC12
#spi_bus: spi3
#run_current: 0.580
#stealthchop_threshold: 999999

#[tmc2130 extruder]
#cs_pin: PD2
#spi_bus: spi3
#run_current: 0.650
#stealthchop_threshold: 999999


########################################
# EXP1 (display) pins
########################################

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PA15, EXP1_3=PA9,   EXP1_5=PA10, EXP1_7=PB8, EXP1_9=<GND>,
    EXP1_2=PB6,  EXP1_4=<RST>, EXP1_6=PB9,  EXP1_8=PB7, EXP1_10=<5V>

# See the sample-lcd.cfg file for definitions of common LCD displays.
