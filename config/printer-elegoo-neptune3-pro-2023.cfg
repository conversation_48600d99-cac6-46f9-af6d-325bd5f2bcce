# This file contains pin mappings for the stock Elegoo Neptune 3 Pro (ZNP Robin Nano_DW V2.2)
# To use this config, during "make menuconfig" select the  STM32F401 with a
# "32KiB bootloader" and serial (on USART1 PA10/PA9) communication.

# Note that the "make flash" command does not work with ZNP Robin boards.
# After running "make", rename the out/klipper.bin file to out/ZNP_ROBIN_NANO.bin
# Copy the file out/ZNP_ROBIN_NANO.bin to an SD card formatted to FAT32
# and then restart the printer with the SD card inserted.

# See docs/Config_Reference.md for a description of parameters.

# Core

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

# Steppers

[stepper_x]
step_pin: PC12
dir_pin: PB3
enable_pin: !PD2
microsteps: 16
rotation_distance: 40
endstop_pin: PA13
position_min: -5
position_endstop: -5
position_max: 235
homing_speed: 50

[stepper_y]
step_pin: PC11
dir_pin: PA15
enable_pin: !PC10
microsteps: 16
rotation_distance: 40
endstop_pin: PB8
position_endstop: 0
position_max: 234
homing_speed: 50

[stepper_z]
step_pin: PC7
dir_pin: !PC9
enable_pin: !PC8
microsteps: 16
rotation_distance: 8
position_min: -0.8
endstop_pin: probe:z_virtual_endstop
position_max: 283
homing_speed: 10

[probe]
pin: PA8
# NOTE: Set this to a value based on your printer and bed.
z_offset: 0
x_offset: -28.5
y_offset: 22

[extruder]
step_pin: PB10
dir_pin: PB1
enable_pin: !PC6
microsteps: 16
rotation_distance: 8.42
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA6
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC1
control: pid
# NOTE: These settings are for PETG, thus 240C at 30% fan.
pid_Kp: 26.27
pid_Ki: 1.607
pid_Kd: 107.380
min_temp: 0
max_temp: 260
max_extrude_only_distance: 100

[heater_bed]
heater_pin: PA5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC0
control: pid
# NOTE: These settings are for PETG, thus 80C bed temperature.
pid_Kp: 70.173
pid_Ki: 1.418
pid_Kd: 868.388
min_temp: 0
max_temp: 100

# Coooling

[fan]
pin: PA7

[heater_fan hotend_fan]
pin: PB0

# Rest

# This is put on the FAN3 pin.
[led top_LEDs]
white_pin: PB9
cycle_time: 0.005

[filament_switch_sensor filament_runout_sensor]
switch_pin: PB4

[safe_z_home]
home_xy_position: 143.5, 93

[bed_mesh]
mesh_min: 10, 25
mesh_max: 205, 220
probe_count: 6, 6
algorithm: bicubic
speed: 100
