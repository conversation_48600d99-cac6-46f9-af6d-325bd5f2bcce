# This file contains common pin mappings for the TH3D EZBoard Lite v1.2.
# To use this config, the firmware should be compiled for the
# LPC1769 with USB communication.

# The "make flash" command does not work on this board. Instead,
# after running "make", copy the generated "out/klipper.bin" file to a
# file named "firmware.bin" on an SD card and then restart the board
# with that SD card.

# See docs/Config_Reference.md for a description of parameters.

[mcu]
serial: /dev/serial/by-id/usb-Klipper_lpc1768_00000000000000000000000000000000-if00

[printer]
kinematics: cartesian
max_velocity: 500
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[stepper_x]
step_pin: P2.0
dir_pin: P1.16
enable_pin: !P1.17
microsteps: 16
rotation_distance: 40
endstop_pin: ^P1.24
position_endstop: 0
position_max: 350

[tmc2208 stepper_x]
uart_pin: P0.5
tx_pin: P0.4
run_current: 0.600
stealthchop_threshold: 999999

[stepper_y]
step_pin: P2.1
dir_pin: P1.10
enable_pin: !P1.9
microsteps: 16
rotation_distance: 40
endstop_pin: ^P1.25
position_endstop: 0
position_max: 350

[tmc2208 stepper_y]
uart_pin: P0.11
tx_pin: P0.10
run_current: 0.600
stealthchop_threshold: 999999

[stepper_z]
step_pin: P2.2
dir_pin: P1.15
enable_pin: !P1.14
microsteps: 16
rotation_distance: 8
endstop_pin: ^P1.26
position_endstop: 0.5
position_max: 400

[tmc2208 stepper_z]
uart_pin: P0.20
tx_pin: P0.19
run_current: 0.700
stealthchop_threshold: 999999

[extruder]
step_pin: P2.3
dir_pin: P1.4
enable_pin: !P1.8
microsteps: 16
rotation_distance: 34.406
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: P2.7
sensor_type: EPCOS 100K B57560G104F
sensor_pin: P0.23
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 210

[tmc2208 extruder]
uart_pin: P0.21
tx_pin: P0.22
run_current: 0.800
stealthchop_threshold: 999999

[heater_bed]
heater_pin: P2.5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: P0.24
control: pid
pid_Kp: 54.027
pid_Ki: 0.770
pid_Kd: 948.182
min_temp: 0
max_temp: 130

[fan]
pin: P2.6

#[bltouch]
#sensor_pin: P1.26
#control_pin: P2.4

#[filament_switch_sensor my_sensor]
#switch_pin: P1.27

########################################
# EXP1 / EXP2 (display) pins
########################################

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=P1.31, EXP1_3=P3.26, EXP1_5=P3.25, EXP1_7=P0.16, EXP1_9=<GND>,
    EXP1_2=P1.30, EXP1_4=<RST>, EXP1_6=P0.15, EXP1_8=P0.18, EXP1_10=<5V>

# See the sample-lcd.cfg file for definitions of common LCD displays.
