# This file contains common pin mappings for Sovol Sv01 printer. To
# use this config, the firmware should be compiled for the AVR
# atmega2560.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PE5
position_endstop: 0
position_max: 300
homing_speed: 50

[stepper_y]
step_pin: PF6
dir_pin: PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: ^PJ1
position_endstop: 0
position_max: 255
homing_speed: 50

[stepper_z]
step_pin: PL3
dir_pin: !PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 8
endstop_pin: ^PD3
position_endstop: 0
position_max: 300

[extruder]
step_pin: PA4
dir_pin: !PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 7.680
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5
control: pid
pid_Kp: 31.147
pid_Ki: 2.076
pid_Kd: 116.803
min_temp: 0
max_temp: 265

[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: pid
pid_Kp: 72.174
pid_Ki: 1.816
pid_Kd: 717.224
min_temp: 0
max_temp: 110

[filament_switch_sensor my_sensor]
switch_pin: PE4
pause_on_runout: True

[fan]
pin: PH6

[mcu]
serial: /dev/ttyUSB0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[display]
lcd_type: st7920
cs_pin: PH1
sclk_pin: PA1
sid_pin: PH0
encoder_pins: ^PC4, ^PC6
click_pin: ^!PC2
