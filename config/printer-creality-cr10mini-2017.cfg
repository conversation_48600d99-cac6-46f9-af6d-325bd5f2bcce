# This file contains common pin mappings for the 2017 Creality CR-10
# mini. To use this config, the firmware should be compiled for the
# AVR atmega1284p.

# Note, a number of Melzi boards are shipped with a bootloader that
# requires the following command to flash the board:
#  avrdude -p atmega1284p -c arduino -b 57600 -P /dev/ttyUSB0 -U out/klipper.elf.hex
# If the above command does not work and "make flash" does not work
# then one may need to flash a bootloader to the board - see the
# Klipper docs/Bootloaders.md file for more information.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PD7
dir_pin: !PC5
enable_pin: !PD6
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC2
position_endstop: 0
position_max: 300
homing_speed: 50

[stepper_y]
step_pin: PC6
dir_pin: !PC7
enable_pin: !PD6
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC3
position_endstop: 0
position_max: 220
homing_speed: 50

[stepper_z]
step_pin: PB3
dir_pin: PB2
enable_pin: !PA5
microsteps: 16
rotation_distance: 8
endstop_pin: ^PC4
position_endstop: 0.0
position_max: 300

[extruder]
step_pin: PB1
dir_pin: !PB0
enable_pin: !PD6
microsteps: 16
rotation_distance: 33.683
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PD5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA7
control: pid
pid_Kp: 22.57
pid_Ki: 1.72
pid_Kd: 73.96
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PD4
sensor_type: ATC Semitec 104GT-2
sensor_pin: PA6
control: pid
pid_Kp: 426.68
pid_Ki: 78.92
pid_Kd: 576.71
min_temp: 0
max_temp: 130

[fan]
pin: PB4

[mcu]
serial: /dev/ttyUSB0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[display]
lcd_type: st7920
cs_pin: PA3
sclk_pin: PA1
sid_pin: PC1
encoder_pins: ^PD2, ^PD3
click_pin: ^!PC0
