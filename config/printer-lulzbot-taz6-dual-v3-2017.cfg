#This file contains pin mappings for the Lulzbot TAZ 6 circa 2017 using RAMBo and Dual v3 toolhead.
#To use this config, the firmware should be compiled for the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

#-------------------------------------------------------------------------------------------------
#-------------------------------------------------------------------------------------------------
#-------------------------------------------------------------------------------------------------
# LULZBOT TAZ6 (RAMBo) with Dual v3 Extruder Master Config
#-------------------------------------------------------------------------------------------------
#-------------------------------------------------------------------------------------------------
#-------------------------------------------------------------------------------------------------
#Notes:
# - This config includes START and END gcode blocks that already factor in the Marlin X and Y
#   offsets for the Dual v3 toolhead using the true 0.01mm/step (100step/mm) linear movement value
#   instead of the 0.00995mm/step (100.5step/mm) value that is stock on the TAZ6 to compensate for
#   material shrinkage. Material shrinkage compensation should be done in the slicer based on the
#   material being used.
#
# - The START and END gcode macros use parameters that should be set using the supplied START and
#   END gcode blocks. These blocks have gcode variables that have been tested with Cura
#   Ultimaker Edition (UE) v4.3 and opareate based on how many extruders are active.
#
# - Pressure Advance feature has been disabled and should be tuned if enabled.
#
# - All PID values pulled from Lulzbot Marlin 1.1.9.34, however, the PID calibration procedure
#   should be done to tune these values to your specific hardware.
#
#-------------------------------------------------------------------------------------------------
# LULZBOT TAZ6 Dual v3 Required Parameters
#-------------------------------------------------------------------------------------------------
[stepper_x]
step_pin: PC0
dir_pin: PL1
enable_pin: !PA7
microsteps: 16
rotation_distance: 32
endstop_pin: ^PB6
position_endstop: -20
position_min: -20
position_max: 295
homing_speed: 50
second_homing_speed: 5

[stepper_y]
step_pin: PC1
dir_pin: !PL0
enable_pin: !PA6
microsteps: 16
rotation_distance: 32
endstop_pin: ^PA1
position_endstop: 306
position_min: -17
position_max: 306
homing_speed: 50
second_homing_speed: 5

[stepper_z]
step_pin: PC2
dir_pin: PL2
enable_pin: !PA5
microsteps: 16
rotation_distance: 2
endstop_pin: ^!PB4
position_endstop: 5.0
position_min: -5.8
position_max: 270
homing_speed: 10
second_homing_speed: 1

[extruder]
# This is Extruder0 on the dual v3 (all -1 index in schematic)
# The Dual v3 uses the same temp sensor as the single extruder
# The Dual v3 uses 2x SOMEstruders with modified PID values
step_pin: PC4
dir_pin: !PL7
enable_pin: !PA3
microsteps: 16
rotation_distance: 4.211
nozzle_diameter: 0.500
filament_diameter: 2.850
heater_pin: PH4
sensor_type: ATC Semitec 104GT-2
sensor_pin: PF1
control: pid
pid_Kp: 47.45
pid_Ki: 4.83
pid_Kd: 116.63
min_temp: 0
max_temp: 300
min_extrude_temp: 120

[extruder1]
# This is Extruder1 on the dual v3 (all -0 index in schematic)
# The Dual v3 uses the same temp sensor as the single extruder
# The Dual v3 uses 2x SOMEstruders with modified PID values
step_pin: PC3
dir_pin: PL6
enable_pin: !PA4
microsteps: 16
rotation_distance: 4.211
nozzle_diameter: 0.500
filament_diameter: 2.850
heater_pin: PH6
sensor_type: ATC Semitec 104GT-2
sensor_pin: PF0
control: pid
pid_Kp: 47.45
pid_Ki: 4.83
pid_Kd: 116.63
min_temp: 0
max_temp: 300
min_extrude_temp: 120

[heater_bed]
#The Heater Bed uses Honeywell 100K 135-104LAG-J01 temp sensor and PID control
heater_pin: PE5
sensor_type: Honeywell 100K 135-104LAG-J01
sensor_pin: PF2
control: pid
pid_Kp: 162.0
pid_Ki: 17.0
pid_Kd: 378.0
min_temp: 0
max_temp: 130

[fan]
#On Dual v3 heat break fan is connected to PH3 (part cooling fan on single extruder)
pin: PH3

[heater_fan heatbreak_cooling_fan]
#On Dual v3 part fans are connected to PH5 (heat break fan on single extruder)
pin: PH5

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 2
max_z_accel: 10

[ad5206 stepper_digipot]
enable_pin: PD7
scale: 2.08
# Channel 1 is E0, 2 is E1, 3 is unused, 4 is Z, 5 is X, 6 is Y
channel_1: 1.34
channel_2: 1.34
channel_4: 1.1
channel_5: 1.1
channel_6: 1.1

[static_digital_output stepper_config]
# Enable 16 micro-steps on steppers X, Y, Z, E0, E1
pins:
    PG1, PG0,
    PK7, PG2,
    PK6, PK5,
    PK3, PK4,
    PK1, PK2

[static_digital_output yellow_led]
pins: !PB7

[display]
lcd_type: st7920
cs_pin: PG4
sclk_pin: PJ2
sid_pin: PG3
encoder_pins: ^PJ6,^PJ5
click_pin: ^!PE2
menu_timeout:5

[probe]
#Define the nozzle probe feature
pin: ^!PA0
#---> z_offset may vary from machine to machine. Stock is 1.377. <---
z_offset:1.350
speed: 1.0
samples: 2
samples_tolerance: 0.100

[bed_tilt]
#Enable bed tilt measurements using the probe we defined above
#Probe points using X0 Y0 offsets @ 0.01mm/step
points: -3, -6
        282, -6
        282, 300
        -3, 300
speed: 75
horizontal_move_z: 5

[safe_z_home]
#Needed to lift the Z to clear homing switch on bed
# ---> WARNING! - Z_MAX limit switch not monitored in Klipper! <---
# ---> This could potentially crash the toolhead if already at the top of Z travel! <---
home_xy_position: -19, 265
speed: 50.0
z_hop: 15.0
move_to_previous: False

[gcode_macro G29]
#Preform the ABL by running G29 in the START gcode script
gcode:
    BED_TILT_CALIBRATE

#-------------------------------------------------------------------------------------------------
# Macros to Support TAZ6 START and END Blocks
#-------------------------------------------------------------------------------------------------
#Cura UE 4.3 (and perhaps older) has extruder enable controls that define the number of active
#extruders. These blocks use that information to control only the active extruders.
#NOTE: T0 is the default extruder, T1 is optional.
#START block for use with dual or single extrusion prints
[gcode_macro START_PRINT]
gcode:
    {% set EXTRUDERS_ENABLED_COUNT = 1 %}
    {% set MATERIAL_STANDBY_TEMPERATURE_0 = 175 %}
    {% set MATERIAL_STANDBY_TEMPERATURE_1 = 175 %}
    {% set MATERIAL_BED_TEMPERATURE_LAYER_0 = 65 %}
    {% set MATERIAL_PRINT_TEMPERATURE_LAYER_0_0 = 210 %}
    {% set MATERIAL_PRINT_TEMPERATURE_LAYER_0_1 = 210 %}
    {% set MATERIAL_SOFTEN_TEMP_OFFSET = 35 %}
    {% set MATERIAL_WIPE_TEMP_OFFSET = 35 %}
    {% set MATERIAL_PROBE_TEMP_OFFSET = 35 %}
    ;This profile is designed specifically for LulzBot TAZ6 3D Printer with the Yellowfin Dual running Klipper
    M73 P0                       ; clear GLCD progress bar
    M107                         ; disable fans
    G90                          ; absolute positioning
    M104 S{MATERIAL_STANDBY_TEMPERATURE_0|int - MATERIAL_SOFTEN_TEMP_OFFSET|int} T0     ; soften filament
    {% if EXTRUDERS_ENABLED_COUNT|int == 2 %}
        M104 S{MATERIAL_STANDBY_TEMPERATURE_1|int - MATERIAL_SOFTEN_TEMP_OFFSET|int} T1 ; soften filament
    {% endif %}
    M140 S{MATERIAL_BED_TEMPERATURE_LAYER_0}  ; get bed heating up
    G28 X Y                      ; home X and Y
    G1 X-17 F3000                ; clear X endstop
    M117 Heating...              ; LCD status message
    M106 S64                     ; Fan On lightly
    M109 S{MATERIAL_STANDBY_TEMPERATURE_0|int - MATERIAL_SOFTEN_TEMP_OFFSET|int} T0     ; wait for temp
    {% if EXTRUDERS_ENABLED_COUNT|int == 2 %}
        M109 S{MATERIAL_STANDBY_TEMPERATURE_1|int - MATERIAL_SOFTEN_TEMP_OFFSET|int} T1 ; wait for temp
    {% endif %}
    M107                         ; Fan Off
    T0                           ; return to first extruder
    G28 Z                        ; home Z
    T0                           ; select this extruder first
    M82                          ; set extruder to absolute mode
    G92 E0                       ; set extruder to zero
    G1  E-15 F100                ; suck up 15mm of filament
    {% if EXTRUDERS_ENABLED_COUNT|int == 2 %}
        T1                           ; switch extruders
        M82                          ; set extruder to absolute mode
        G92 E0                       ; set extruder to zero
        G1  E-15 F100                ; suck up 15mm of filament
    {% endif %}
    M104 S{MATERIAL_STANDBY_TEMPERATURE_0|int - MATERIAL_WIPE_TEMP_OFFSET|int} T0     ; set to wipe temp
    {% if EXTRUDERS_ENABLED_COUNT|int == 2 %}
        M104 S{MATERIAL_STANDBY_TEMPERATURE_1|int - MATERIAL_WIPE_TEMP_OFFSET|int} T1 ; set to wipe temp
    {% endif %}
    M106                         ; Turn on fans to speed cooling
    G1 X-18 Y107 F3000           ; move above wiper pad
    M117 Cooling...              ; LCD status message
    M109 S{MATERIAL_STANDBY_TEMPERATURE_0|int - MATERIAL_WIPE_TEMP_OFFSET|int} T0     ; wait for T0 to reach temp
    {% if EXTRUDERS_ENABLED_COUNT|int == 2 %}
        M109 S{MATERIAL_STANDBY_TEMPERATURE_1|int - MATERIAL_WIPE_TEMP_OFFSET|int} T1 ; wait for T1 to reach temp
    {% endif %}
    M107                         ; Turn off fan
    M117 Wiping...              ; LCD status message
    T0                           ; switch extruders
    G1 Z1                        ; push nozzle into wiper
    G1 X -18 Y107 F1000                  ; slow wipe
    G1 X -18 Y102 F1000                  ; slow wipe
    G1 X -18 Y97 F1000                   ; slow wipe
    G1 X -17 Y102 F1000                  ; slow wipe
    G1 X -18 Y92 F1000                   ; slow wipe
    G1 X -17 Y107 F1000                  ; slow wipe
    G1 X -18 Y87 F2000                   ; fast wipe
    G1 X -17 Y77 F2000                   ; fast wipe
    G1 X -18 Y82 F2000                   ; fast wipe
    G1 X -17 Y72 F2000                   ; fast wipe
    G1 X -18 Y67 F2000                   ; fast wipe
    G1 X -17 Y62 F2000                   ; fast wipe
    G1 X -18 Y52 F2000                   ; fast wipe
    G1 X -17 Y57 F2000                   ; fast wipe
    G1 X -18 Y47 F2000                   ; fast wipe
    G1 X -17 Y52 F2000                   ; fast wipe
    G1 X -18 Y82 F2000                   ; fast wipe
    G1 X -17 Y42 Z2 F2000                ; fast wipe
    G1 X -18 Y47 F2000                   ; fast wipe
    G1 X -17 Y37 F2000                   ; fast wipe
    G1 X -18 Y42 F2000                   ; fast wipe
    G1 X -17 Y37 Z1.5 F1000              ; slow wipe
    G1 X -18 Y35 F1000                   ; slow wipe
    G1 X -17 Z5                         ; raise extruder
    M109 S{MATERIAL_STANDBY_TEMPERATURE_0|int - MATERIAL_PROBE_TEMP_OFFSET|int} ; heat to probe temp
    M117 Leveling Bed...              ; LCD status message
    M204 S100                    ; set accel for probing
    G29                          ; probe sequence (for auto-leveling)
    G1 Z10                       ; clear Z home switch
    M204 S500                    ; set accel back to normal
    M117 Heating...              ; LCD status message
    M106 S64                     ; Fan On lightly
    M104 S{MATERIAL_PRINT_TEMPERATURE_LAYER_0_0} T0 ; set extruder temp
    {% if EXTRUDERS_ENABLED_COUNT|int == 2 %}
        M104 S{MATERIAL_PRINT_TEMPERATURE_LAYER_0_1} T1; set extruder temp
    {% endif %}
    G1 X100 Y-16 Z0.5 F3000      ; move to open space
    M400                         ; clear buffer
    M109 S{MATERIAL_PRINT_TEMPERATURE_LAYER_0_0} T0 ; set extruder temp and wait
    {% if EXTRUDERS_ENABLED_COUNT|int == 2 %}
        M109 S{MATERIAL_PRINT_TEMPERATURE_LAYER_0_1} T1; set extruder temp and wait
    {% endif %}
    M107                         ; Fan Off
    M117 Purging...              ; LCD status message
    T0                           ; select this extruder first
    G92 E-15                     ; set extruder negative amount to purge
    G1  E0 F100                  ; undo retraction
    G92 E-15                     ; set extruder negative amount to purge
    G1  E0 F100                  ; purge 15mm of filament
    {% if EXTRUDERS_ENABLED_COUNT|int == 2 %}
        T1                           ; switch to second extruder
        G92 E-15                     ; set extruder negative amount to purge
        G1  E0 F100                  ; undo retraction
        G92 E-15                     ; set extruder negative amount to purge
        G1  E0 F50                   ; purge 15mm of filament
        G92 E0                       ; zero out T1
        G1  E-15 F250                ; retract 15mm from T1 - extrude a 500mm skirt/brim to undo in initial layer
        M400                         ; clear buffer
        M106                         ; Max Fan Speed
        M109 S{MATERIAL_STANDBY_TEMPERATURE_1} T1 ; wait for T1 to cool to prevent ooze
        M107                         ; Fan Off
    {% endif %}
    G1 Z0.5                      ; clear bed (barely)
    G1 X100 Y0 F5000             ; move above bed to shear off filament
    T0                           ; switch to first extruder
    M190 S{MATERIAL_BED_TEMPERATURE_LAYER_0}; get bed temping up during first layer
    G1 Z2 E0 F75
    M400                         ; clear buffer
    M117 TAZ Printing...         ; LCD status message

#END block for use with dual or single extrusion prints
[gcode_macro END_PRINT]
gcode:
    {% set EXTRUDERS_ENABLED_COUNT = 1 %}
    {% set MATERIAL_PART_REMOVAL_TEMPERATURE = 45 %}
    {% set MATERIAL_KEEP_PART_REMOVAL_TEMPERATURE_T = 0 %}
    M400
    M104 S0 T0                     ; T0 hotend off
    {% if EXTRUDERS_ENABLED_COUNT|int == 2 %}
        M104 S0 T1                     ; T1 hotend off
    {% endif %}
    M107                           ; fans off
    G91                            ; relative positioning
    T0
    G1 E-1 F300                    ; retract the filament a bit before lifting the nozzle, to release some of the pressure
    G1 Z20 E-5 X-20 Y-20 F3000     ; move Z up a bit and retract filament even more
    G1 E6                          ; re-prime extruder 1
    {% if EXTRUDERS_ENABLED_COUNT|int == 2 %}
        T1
        G1 E-5 F300                    ; retract the filament a bit before lifting the nozzle, to release some of the pressure
        G1 E5                          ; re-prime extruder 2
        T0
    {% endif %}
    M117 Cooling ...               ; progress indicator message
    G90                            ; absolute positioning
    G1 Y0 F3000                    ; move to cooling position
    M190 S{MATERIAL_PART_REMOVAL_TEMPERATURE} ; set bed to cool off
    G1 Y280 F3000                  ; present finished print
    M140 S{MATERIAL_KEEP_PART_REMOVAL_TEMPERATURE_T}; keep temperature or cool downs
    M84                            ; steppers off
    G90                            ; absolute positioning
    M117 Print Complete            ; progress indicator message
