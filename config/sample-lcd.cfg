# This file provides example configuration for common "RepRap" style
# LCD displays that use EXP1/EXP2 plugs.

# To configure a display from this file, make sure the main
# printer.cfg file has a [board_pins] config section defining pin
# aliases for the EXP1/EXP2 plugs, find the appropriate LCD type in
# this file, and then copy-and-paste that section into printer.cfg.

# See docs/Config_Reference.md for a description of parameters.


######################################################################
# "RepRapDiscount 128x64 Full Graphic Smart Controller" type displays
######################################################################

[display]
lcd_type: st7920
cs_pin: EXP1_4
sclk_pin: EXP1_5
sid_pin: EXP1_3
encoder_pins: ^EXP2_3, ^EXP2_5
click_pin: ^!EXP1_2
#kill_pin: ^!EXP2_8

[output_pin beeper]
pin: EXP1_1


######################################################################
# "RepRapDiscount 2004 Smart Controller" type displays
######################################################################

[display]
lcd_type: hd44780
rs_pin: EXP1_4
e_pin: EXP1_3
d4_pin: EXP1_5
d5_pin: EXP1_6
d6_pin: EXP1_7
d7_pin: EXP1_8
encoder_pins: ^EXP2_3, ^EXP2_5
click_pin: ^!EXP1_2
#kill_pin: ^!EXP2_8

[output_pin beeper]
pin: EXP1_1


######################################################################
# 128x64 Full Graphic Creality CR10 / ENDER 3 stockdisplay
######################################################################

# This section is used for a Creality "12864" display with a single
# ribbon cable between the display's EXP3 plug and the
# micro-controller board's EXP1 connector.

[display]
lcd_type: st7920
cs_pin: EXP1_7
sclk_pin: EXP1_6
sid_pin: EXP1_8
encoder_pins: ^EXP1_5, ^EXP1_3
click_pin: ^!EXP1_2

[output_pin beeper]
pin: EXP1_1


######################################################################
# MKS Mini 12864 LCD
######################################################################

# Make sure that the EXP1 and EXP2 are rotated correctly on the
# display board. The cutouts on the connectors should be towards the
# center of the PCB. See:
#   https://reprap.org/wiki/MKS_MINI_12864#Physical_Interface
# If they are wrong, the connector housing can be pried off carefully
# with a small screwdriver and relocated the correct way.

[display]
lcd_type: uc1701
cs_pin: EXP1_6
a0_pin: EXP1_7
contrast: 40
encoder_pins: ^EXP2_3, ^EXP2_5
click_pin: ^!EXP1_2
## Some micro-controller boards may require an spi bus to be specified:
#spi_bus: spi
## Alternatively, some micro-controller boards may work with software spi:
#spi_software_miso_pin: EXP2_1
#spi_software_mosi_pin: EXP2_6
#spi_software_sclk_pin: EXP2_2

[output_pin beeper]
pin: EXP1_1


########################################################################################
# Anet 128x64 LCD with alternative wiring (ANET_FULL_GRAPHICS_LCD_ALT_WIRING in Marlin)
########################################################################################

[display]
lcd_type: st7920
cs_pin: EXP1_8
sclk_pin: EXP1_4
sid_pin: EXP1_6
encoder_pins: ^!EXP1_7, ^!EXP1_5
click_pin: ^!EXP1_3

[output_pin beeper]
pin: EXP1_1

######################################################################
# Fysetc Mini 12864Panel v2.1 (with neopixel backlight leds)
######################################################################

[display]
lcd_type: uc1701
cs_pin: EXP1_3
a0_pin: EXP1_4
rst_pin: EXP1_5
contrast: 63
encoder_pins: ^EXP2_5, ^EXP2_3
click_pin: ^!EXP1_2
## Some micro-controller boards may require an spi bus to be specified:
#spi_bus: spi
## Alternatively, some micro-controller boards may work with software spi:
#spi_software_miso_pin: EXP2_1
#spi_software_mosi_pin: EXP2_6
#spi_software_sclk_pin: EXP2_2

[output_pin beeper]
pin: EXP1_1

[neopixel fysetc_mini12864]
pin: EXP1_6
chain_count: 3
color_order: RGB
initial_RED: 0.4
initial_GREEN: 0.4
initial_BLUE: 0.4


######################################################################
# Plug pin locations
######################################################################

# Some micro-controller boards and displays use inconsistent labeling
# for the EXP1 and EXP2 headers. The following diagram shows the
# correct location of pin 1 along with ground and power pins on the
# EXP1 and EXP2 plugs:
#
#          EXP1:                        EXP2:
#   +-----------------+          +-----------------+
#   |  o  o  o  o  5V |          |  o  o  o  o  o  |
#   |  1  o  o  o GND |          |  1  o  o  o GND |
#   +------     ------+          +------     ------+
#
# Some boards may have the cutout in the wrong location. If so, it may
# be possible to carefully pry the plastic header off of the pins with
# a small screwdriver, then correct the orientation and reapply the
# plastic header.
