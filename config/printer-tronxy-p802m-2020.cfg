# This file contains common pin mappings for Tronxy P802M printer. To
# use this config, the firmware should be compiled for the AVR
# atmega1284p. The Tronxy P802MA is similar, but will need
# auto leveling configuration.

# Note that the "make flash" command does not work with Tronxy boards -
# the boards are typically flashed with this command:
# avrdude -p atmega1284p -c arduino -b 57600 -P /dev/ttyUSB0 -U out/klipper.elf.hex

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PD7
dir_pin: PC5
enable_pin: !PD6
microsteps: 16
rotation_distance: 32
endstop_pin: ^!PC2
position_endstop: -8
position_max: 220
position_min: -45
homing_speed: 50

[stepper_y]
step_pin: PC6
dir_pin: PC7
enable_pin: !PD6
microsteps: 16
rotation_distance: 32
endstop_pin: ^!PC3
position_endstop: 0
position_min: -5
position_max: 220
homing_speed: 50

[stepper_z]
step_pin: PB3
dir_pin: !PB2
enable_pin: !PA5
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PC4
position_endstop: 0
position_max: 230
homing_speed: 20

[extruder]
step_pin: PB1
dir_pin: PB0
enable_pin: !PD6
microsteps: 16
rotation_distance: 33.600
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PD5
sensor_type: ATC Semitec 104GT-2
sensor_pin: PA7
control: pid
pid_kp: 23.060
pid_ki: 1.025
pid_kd: 129.711
min_temp: 0
max_temp: 250
max_extrude_only_distance: 400

[heater_bed]
heater_pin: PD4
sensor_type: ATC Semitec 104GT-2
sensor_pin: PA6
control: pid
pid_kp: 72.174
pid_ki: 1.677
pid_kd: 776.767
min_temp: 0
max_temp: 110

[fan]
pin: PB4

[mcu]
serial: /dev/serial/by-id/usb-FTDI_FT232R_USB_UART_A96PLN7R-if00-port0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 1000
max_z_velocity: 20
max_z_accel: 100

[display]
lcd_type: hd44780
rs_pin: PA3
e_pin: PA2
d4_pin: PD2
d5_pin: PD3
d6_pin: PC0
d7_pin: PC1
up_pin: PA1
analog_range_up_pin: 9000, 13000
down_pin: PA1
analog_range_down_pin: 800, 1300
click_pin: PA1
analog_range_click_pin: 2000, 2500
back_pin: PA1
analog_range_back_pin: 4500, 5000
#kill_pin: PA1
#analog_range_kill_pin: 400, 600
