# This file contains an example configuration to use a PWM-controlled tool
# such as a laser or spindle.
# See docs/Using_PWM_Tools.md for a more detailed description.

[pwm_tool TOOL]
pin: !ar9       # use your fan's pin number
hardware_pwm: True
cycle_time: 0.001
shutdown_value: 0
maximum_mcu_duration: 5
# Default: 0 (disabled)
# Amount of time in which the host has to acknowledge
# a non-shutdown output value.
# Suggested value is around 5 seconds.
# Use a value that does not burn up your stock.
# Please note that during homing, your tool
# needs to be in default speed.

[gcode_macro M3]
gcode:
    {% set S = params.S|default(0.0)|float %}
    SET_PIN PIN=TOOL VALUE={S / 255.0}

[gcode_macro M4]
gcode:
    {% set S = params.S|default(0.0)|float %}
    SET_PIN PIN=TOOL VALUE={S / 255.0}

[gcode_macro M5]
gcode:
    SET_PIN PIN=TOOL VALUE=0


# Optional: LCD Menu Control

[menu __main __control __toolonoff]
type: input
enable: {'pwm_tool TOOL' in printer}
name: Fan: {'ON ' if menu.input else 'OFF'}
input: {printer['pwm_tool TOOL'].value}
input_min: 0
input_max: 1
input_step: 1
gcode:
    M3 S{255 if menu.input else 0}

[menu __main __control __toolspeed]
type: input
enable: {'pwm_tool TOOL' in printer}
name: Tool speed: {'%3d' % (menu.input*100)}%
input: {printer['pwm_tool TOOL'].value}
input_min: 0
input_max: 1
input_step: 0.01
gcode:
    M3 S{'%d' % (menu.input*255)}
