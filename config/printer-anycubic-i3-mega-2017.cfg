# This file contains pin mappings for the Anycubic i3 Mega with
# Ultrabase from 2017. (This config may work on an Anycubic i3 Mega v1
# prior to the Ultrabase if you comment out the definition of the
# endstop_pin in the stepper_z1 section.) To use this config, the
# firmware should be compiled for the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF0
dir_pin: !PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PE5
position_min: -5
position_endstop: -5
position_max: 210
homing_speed: 30.0

[stepper_y]
step_pin: PF6
dir_pin: PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PL7
position_endstop: 0
position_max: 210
homing_speed: 30.0

[stepper_z]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PD3
position_endstop: 0.0
position_max: 205
homing_speed: 5.0

[stepper_z1]
step_pin: PC1
dir_pin: PC3
enable_pin: !PC7
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PL6

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 34.557
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4
sensor_type: ATC Semitec 104GT-2
sensor_pin: PK5
control: pid
pid_Kp: 15.717
pid_Ki: 0.569
pid_Kd: 108.451
min_temp: 0
max_temp: 245

[heater_fan extruder_fan]
pin: PL5

[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: pid
pid_Kp: 74.883
pid_Ki: 1.809
pid_Kd: 775.038
min_temp: 0
max_temp: 110

[fan]
pin: PH6

[mcu]
serial: /dev/ttyUSB0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 10
max_z_accel: 60

[heater_fan stepstick_fan]
pin: PH4
