# This file contains common pin mappings for the Azteeg X5 Mini v3. To use
# this config, the firmware should be compiled for the LPC1769.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: P2.1
dir_pin: P0.11
enable_pin: !P0.10
microsteps: 16
rotation_distance: 40
endstop_pin: ^P1.24
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: P2.2
dir_pin: P0.20
enable_pin: !P0.19
microsteps: 16
rotation_distance: 40
endstop_pin: ^P1.26
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: P2.3
dir_pin: P0.22
enable_pin: !P0.21
microsteps: 16
rotation_distance: 8
endstop_pin: ^P1.28
position_endstop: 0
position_max: 200
homing_speed: 50

[extruder]
step_pin: P2.0
dir_pin: P0.5
enable_pin: !P0.4
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: P2.5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: P0.24
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: P2.7
sensor_type: EPCOS 100K B57560G104F
sensor_pin: P0.23
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: P0.26

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[mcp4451 stepper_digipot1]
i2c_address: 44
# Scale the config so that wiper values can be specified in amps.
scale: 2
# wiper 0 is X (aka alpha), 1 is Y, 2 is Z, 3 is E0
wiper_0: 1.0
wiper_1: 1.0
wiper_2: 1.0
wiper_3: 1.0

# Mini Viki2 LCD - this board does not work with Reprap LCDs
#[display]
#lcd_type: uc1701
#cs_pin: P0.16
#a0_pin: P2.6
#encoder_pins: ^!P3.25, ^P3.26
#click_pin: ^!P2.11
