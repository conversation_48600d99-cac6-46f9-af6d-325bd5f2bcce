# Creality Ender 5 S1 (HW version: CR4NS200141C13)
#
# printer_size: 220x220x280
# To use this config, during "make menuconfig" select the STM32F401
# with a "64KiB bootloader" and serial (on USART1 PA10/PA9)
# communication.
#
# Flash this firmware by creating a directory named "STM32F4_UPDATE"
# on an SD card, copying the "out/klipper.bin" to it and then turn
# on the printer with the card inserted. The firmware filename must
# end in ".bin" and must not match the last filename that was flashed.
#
# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC2
dir_pin: !PB9
enable_pin: !PC3
rotation_distance: 40
microsteps: 16
endstop_pin: !PA5
position_endstop: 220
position_max: 222
homing_speed: 80

[stepper_y]
step_pin: PB8
dir_pin: !PB7
enable_pin: !PC3
rotation_distance: 40
microsteps: 16
endstop_pin: !PA6
position_endstop: 220
position_max: 220
homing_speed: 80

[stepper_z]
step_pin: PB6
dir_pin: PB5
enable_pin: !PC3
rotation_distance: 8
microsteps: 16
endstop_pin: probe:z_virtual_endstop
position_max: 280
homing_speed: 20
second_homing_speed: 1
homing_retract_dist: 2.0

[extruder]
step_pin: PB4
dir_pin: PB3
enable_pin: !PC3
rotation_distance: 7.5
microsteps: 16
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA1
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC5
control: pid  # tuned for stock hardware with 210 degree Celsius target
pid_kp: 20.749
pid_ki: 1.064
pid_kd: 101.153
min_temp: 0
max_temp: 305

[heater_bed]
heater_pin: PA7
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC4
control: pid  # tuned for stock hardware with 60 degree Celsius target
pid_kp: 66.566
pid_ki: 0.958
pid_kd: 1155.761
min_temp: 0
max_temp: 110

# Part cooling fan
[fan]
pin: PA0
kick_start_time: 0.5

# Hotend fan
# set fan running when extruder temperature is over 60
[heater_fan heatbreak_fan]
pin: PC0
heater:extruder
heater_temp: 60
fan_speed: 0.8

[filament_switch_sensor filament_sensor]
pause_on_runout: true
switch_pin: ^!PC15

# Stock CR Touch bed sensor
[bltouch]
sensor_pin: ^PC14
control_pin: PC13
x_offset: -13
y_offset: 27
z_offset: 2.0
speed: 10
stow_on_each_sample: true    # Occasional bed crashes when false
samples: 4
sample_retract_dist: 2
samples_result: average
probe_with_touch_mode: true

[bed_mesh]
speed: 150
mesh_min: 3,28         # need to handle head distance with bl_touch
mesh_max: 205,218
mesh_pps: 3
probe_count: 4,4
fade_start: 1
fade_end: 10
fade_target: 0

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[safe_z_home]
home_xy_position: 123,83
speed: 200
z_hop: 10
z_hop_speed: 10

# Many Ender 5 S1 printers appear to suffer from a slight twist
# in the X axis.  This twist can be measured, and compensated for
# using the AXIS_TWIST_COMPENSATION_CALIBRATE G-Code command.  See
# https://www.klipper3d.org/Axis_Twist_Compensation.html for more
# information.  This section provides the setup for this optional
# calibration step.
[axis_twist_compensation]
calibrate_start_x: 3
calibrate_end_x: 207
calibrate_y: 110

# Probe locations for assisted bed screw adjustment.
[screws_tilt_adjust]
screw1: 38,6
screw1_name: Front Left Screw
screw2: 215,6
screw2_name: Front Right Screw
screw3: 215,175
screw3_name: Rear Right Screw
screw4: 38,175
screw4_name: Rear Left Screw
horizontal_move_z: 5
speed: 100
screw_thread: CW-M4

[bed_screws]
screw1: 25,25
screw1_name: Front Left Screw
screw2: 195,25
screw2_name: Front Right Screw
screw3: 195,195
screw3_name: Rear Right Screw
screw4: 25,195
screw4_name: Rear Left Screw

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 5000
max_z_velocity: 5
max_z_accel: 100
square_corner_velocity: 5.0
