# This file contains a configuration for the Anycubic Kobra Plus printer.
#
# The Kobra Plus mainboard must be modified to correct conflicting UART
# addresses. As delivered, the X stepper and E0 stepper use UART address 0.
# To correct, move resistor R65 to R66. This moves the X stepper to address 3.
#
# After making this modification, any future firmwares will need to use the new
# address for the X stepper. To revert to the stock firmware, either undo the
# modification, or recompile the stock firmware using the correct addresses for
# X_SLAVE_ADDRESS and E0_SLAVE_ADDRESS.
#
# See docs/Config_Reference.md for a description of parameters.
#
# To build the firmware, use the following configuration:
#   - Micro-controller: Huada Semiconductor HC32F460
#   - Communication interface: Serial (PA3 & PA2) - Anycube
#   - Clock Speed: 200 MHz
#
# Installation:
#  1. Rename the klipper bin to `firmware.bin` and copy it to an SD Card.
#  2. Power off the Printer, insert the SD Card and power it on.
#  3. The printer should beep several times and the LCD will be stuck on the
#     Splash screen.

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 1800
max_z_velocity: 40
max_z_accel: 100

[stepper_x]
step_pin: PA5
dir_pin: PA4
enable_pin: !PC3
microsteps: 16
rotation_distance: 40
endstop_pin: !PA6
position_min: -4
position_endstop: -4
position_max: 304
homing_speed: 100

[tmc2209 stepper_x]
uart_pin: PA15
tx_pin: PA9
sense_resistor: 0.100
run_current: 0.9
uart_address: 3
stealthchop_threshold: 999999

[stepper_y]
step_pin: PC4
dir_pin: PA7
enable_pin: !PC3
microsteps: 16
rotation_distance: 32
endstop_pin: !PC5
position_min: -6
position_endstop: -6
position_max: 300
homing_speed: 100

[tmc2209 stepper_y]
uart_pin: PA15
tx_pin: PA9
sense_resistor: 0.100
run_current: 0.9
uart_address: 1
stealthchop_threshold: 999999

[stepper_z]
step_pin: PC7
dir_pin: !PC6
enable_pin: !PC3
microsteps: 16
rotation_distance: 8
endstop_pin: PA8
position_endstop: 0
position_min: -15
position_max: 350
homing_speed: 10

[tmc2209 stepper_z]
uart_pin: PA15
tx_pin: PA9
sense_resistor: 0.100
run_current: 0.9
uart_address: 2
stealthchop_threshold: 999999

[stepper_z1]
step_pin: PB1
dir_pin: !PB0
enable_pin: !PC3
microsteps: 16
rotation_distance: 8

[extruder]
max_extrude_only_distance: 200
max_extrude_only_velocity: 60
max_extrude_only_accel: 3000
step_pin: PC14
dir_pin: !PC15
enable_pin: !PC3
microsteps: 16
rotation_distance: 7.71
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA1
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC1
min_extrude_temp: 170
control: pid
pid_kp: 22.20
pid_ki: 1.08
pid_kd: 119.0
min_temp: 0
max_temp: 275

[tmc2208 extruder]
uart_pin: PA15
tx_pin: PA9
sense_resistor: 0.100
run_current: 0.8
uart_address: 0
stealthchop_threshold: 999999

[heater_bed]
heater_pin: PA0
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC0
control: pid
pid_kp: 97.1
pid_ki: 1.41
pid_kd: 1675.16
min_temp: 0
max_temp: 120

[filament_switch_sensor runout]
pause_on_runout: True
switch_pin: !PC13

[controller_fan controller_fan]
pin: PA14
heater: heater_bed

[heater_fan hotend_fan]
pin: PA13

[fan]
pin: PB9

[probe]
pin: !PB6
speed: 2.0
lift_speed: 4.0
samples: 2
sample_retract_dist: 5
samples_tolerance_retries: 3
z_offset: 0.2
activate_gcode: probe_reset

[output_pin probe_reset_pin]
pin: PB7
value: 1

[safe_z_home]
home_xy_position: 0, 0
speed: 100
z_hop: 10
z_hop_speed: 15
move_to_previous: False

[bed_mesh]
speed: 100
mesh_min: 10, 10
mesh_max: 290, 290
algorithm: bicubic
probe_count: 5, 5

[gcode_macro probe_reset]
gcode:
  SET_PIN PIN=probe_reset_pin VALUE=0
  G4 P300
  SET_PIN PIN=probe_reset_pin VALUE=1
  G4 P100

[output_pin LED]
pin: PB8

[output_pin beeper]
pin: PB5
