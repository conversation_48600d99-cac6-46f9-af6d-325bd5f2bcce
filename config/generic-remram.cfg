# This file contains common pin mappings for remram boards. To use this
# config, the firmware should be compiled for the ST stm32f765.

# See docs/Config_Reference.md for a description of parameters.

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[stepper_x]
step_pin: PA15
dir_pin: PC10
enable_pin: !PC12
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PB12  # X_MIN
position_endstop: 0
position_max: 200
homing_speed: 10

[tmc2130 stepper_x]
cs_pin: PB1
spi_bus: spi1
run_current: 0.600
stealthchop_threshold: 999999
diag1_pin: ^!PC11

[stepper_y]
step_pin: PB3
dir_pin: !PD6
enable_pin: !PD4
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PB14  # Y_MIN
position_endstop: 0
position_max: 200
homing_speed: 10

[tmc2130 stepper_y]
cs_pin: PB0
spi_bus: spi1
run_current: 0.600
stealthchop_threshold: 999999
diag1_pin: ^!PD5

[stepper_z]
step_pin: PB10
dir_pin: PE13
enable_pin: !PE15
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PD8  # Z_MIN
position_endstop: 0.5
position_max: 200
homing_speed: 10

[tmc2130 stepper_z]
cs_pin: PC5
spi_bus: spi1
run_current: 0.600
stealthchop_threshold: 999999
diag1_pin: ^!PE14

[extruder]
step_pin: PB11
dir_pin: PE10
enable_pin: !PE11
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA3  # HEATER
sensor_type: Generic 3950
sensor_pin: PC1  # TEMP2
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[tmc2130 extruder]
cs_pin: PC4
spi_bus: spi1
run_current: 0.600
stealthchop_threshold: 999999
diag1_pin: ^!PE12

[heater_fan hotend_fan]
pin: PA2  # FAN2

[heater_bed]
heater_pin: PA1  # BED OUT
sensor_type: Generic 3950
sensor_pin: PC0  # TEMP1
control: watermark
min_temp: 0
max_temp: 130

[fan]  # Print cooling fan.
pin: PA0  # FAN1

# [temperature_sensor temp3]
# sensor_type: Generic 3950
# sensor_pin: PC2  # TEMP3
# min_temp: 0
# max_temp: 300
# gcode_id: temp3

[static_digital_output yellow_led]
pins: !PD0  # STATUS LED
