# This file contains common pin mappings for MKS Robin Nano (v1.2.004)
# boards. To use this config, the firmware should be compiled for the
# STM32F103. When running "make menuconfig", enable "extra low-level
# configuration setup", select the 28KiB bootloader, and serial (on
# USART3 PB11/PB10) communication.

# Note that the "make flash" command does not work with MKS Robin
# boards. After running "make", run the following command:
#   ./scripts/update_mks_robin.py out/klipper.bin out/Robin_nano.bin
# Copy the file out/Robin_nano.bin to an SD card and then restart the
# printer with that SD card.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PE3
dir_pin: !PE2
enable_pin: !PE4
microsteps: 16
rotation_distance: 40
endstop_pin: !PA15
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PE0
dir_pin: !PB9
enable_pin: !PE1
microsteps: 16
rotation_distance: 40
endstop_pin: !PA12
position_endstop: 230
position_max: 230
homing_speed: 50

[stepper_z]
step_pin: PB5
dir_pin: PB4
enable_pin: !PB8
microsteps: 16
rotation_distance: 8
endstop_pin: !PA11
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PD6
dir_pin: !PD3
enable_pin: !PB3
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC3
sensor_type: ATC Semitec 104GT-2
sensor_pin: PC1
control: pid
pid_Kp: 14.669
pid_Ki: 0.572
pid_Kd: 94.068
min_temp: 0
max_temp: 250

#[extruder1]
#step_pin: PA6
#dir_pin: !PA1
#enable_pin: !PA3
#heater_pin: PB0
#sensor_pin: PC2
#...

[heater_bed]
heater_pin: PA0
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC0
control: pid
pid_Kp: 325.10
pid_Ki: 63.35
pid_Kd: 417.10
min_temp: 0
max_temp: 130

[fan]
pin: PB1

[mcu]
serial: /dev/ttyUSB0
restart_method: command

[printer]
kinematics: corexy
max_velocity: 250
max_accel: 4500
max_z_velocity: 25
max_z_accel: 100
