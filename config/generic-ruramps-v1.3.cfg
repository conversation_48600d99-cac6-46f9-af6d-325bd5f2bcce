# This file contains common pin mappings for RuRamps (v1.3) boards.  To
# use this config, the firmware should be compiled for the Arduino
# Due.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC5
dir_pin: PC4
enable_pin: !PA7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC7
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PD10
dir_pin: PC3
enable_pin: !PA7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC9
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PD9
dir_pin: PB25
enable_pin: !PA7
microsteps: 16
rotation_distance: 8
endstop_pin: ^PA20
position_endstop: 0
position_max: 200
homing_speed: 50

[extruder]
step_pin: PD6
dir_pin: PD3
enable_pin: !PC1
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB27
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA16
control: pid
pid_kp: 15.572
pid_ki: 0.446
pid_kd: 136.064
min_temp: 0
max_temp: 270

#[extruder1]
#step_pin: PB26
#dir_pin: PA15
#enable_pin: !PD1

[heater_bed]
heater_pin: PC23
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA6
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PB18

#[heater_fan extruder_cooler_fan]
#pin: PB17

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100
