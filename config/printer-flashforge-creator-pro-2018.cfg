# This file contains common pin mappings for the FlashForge-Creator-Pro
# To use this config, the firmware should be compiled for
# the Atmel atmega2560.

# Use the following command to flash the board:
#  avrdude -c stk500v2 -p m2560 -P /dev/serial/by-id/usb-MakerBot_Industries_The_Replicator_557373136313514061A2-if00 -b 57600 -D -U out/klipper.elf.hex

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF1
dir_pin: !PF0
enable_pin: !PF2
microsteps: 16
rotation_distance: 34
endstop_pin: ^!PL1
position_endstop: 116
position_max: 116
position_min: -116
homing_speed: 50

[stepper_y]
step_pin: PF5
dir_pin: !PF4
enable_pin: !PF6
microsteps: 16
rotation_distance: 34
endstop_pin: ^!PL3
position_endstop: 80
position_max: 80
position_min: -80
homing_speed: 50

[stepper_z]
step_pin: PK1
dir_pin: !PK0
enable_pin: !PK2
microsteps: 16
rotation_distance: 8
endstop_pin: !PL6
position_endstop: -0.25
position_max: 181
position_min: -0.25

[extruder]
step_pin: PA3
dir_pin: !PA2
enable_pin: !PA4
microsteps: 16
rotation_distance: 33
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PH3
sensor_type: MAX6675
sensor_pin: PE3
spi_software_miso_pin: PE5
spi_software_sclk_pin: PE2
spi_software_mosi_pin: PA1 #dummy mosi pin
control: pid
pid_kp: 27.341
pid_ki: 1.293
pid_kd: 144.566
min_temp: 0
max_temp: 260

[extruder1]
step_pin: PA7
dir_pin: PA6
enable_pin: !PG2
microsteps: 16
rotation_distance: 33
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB5
sensor_type: MAX6675
sensor_pin: PE4
spi_software_miso_pin: PE5
spi_software_sclk_pin: PE2
spi_software_mosi_pin: PA1 #dummy mosi pin
control: pid
pid_kp: 27.341
pid_ki: 1.293
pid_kd: 144.566
min_temp: 0
max_temp: 260

[gcode_macro T0]
gcode:
    SET_GCODE_OFFSET X=-34
    ACTIVATE_EXTRUDER EXTRUDER=extruder

[gcode_macro T1]
gcode:
    SET_GCODE_OFFSET X=0
    ACTIVATE_EXTRUDER EXTRUDER=extruder1


[heater_fan extruder_fan]
pin: PH4

[heater_fan extruder1_fan]
pin: PB6

[fan]
pin: PL5

[heater_bed]
heater_pin: PL4
sensor_type: ATC Semitec 104GT-2
sensor_pin: PK7
control: pid
pid_kp: 72.914
pid_ki: 2.060
pid_kd: 645.290
min_temp: 0
max_temp: 130

[mcu]
serial: /dev/serial/by-id/usb-MakerBot_Industries_The_Replicator_557373136313514061A2-if00
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 1000
max_z_velocity: 5
max_z_accel: 100

[mcp4018 x_axis_pot]
i2c_software_scl_pin: PJ5
i2c_software_sda_pin: PF3
wiper: 118
scale: 127

[mcp4018 y_axis_pot]
i2c_software_scl_pin: PJ5
i2c_software_sda_pin: PF7
wiper: 118
scale: 127

[mcp4018 z_axis_pot]
i2c_software_scl_pin: PJ5
i2c_software_sda_pin: PK3
wiper: 40
scale: 127

[mcp4018 a_axis_pot]
i2c_software_scl_pin: PJ5
i2c_software_sda_pin: PA5
wiper: 118
scale: 127

[mcp4018 b_axis_pot]
i2c_software_scl_pin: PJ5
i2c_software_sda_pin: PJ6
wiper: 118
scale: 127

[display]
lcd_type: hd44780_spi
spi_software_mosi_pin: PC3
spi_software_sclk_pin: PC2
#miso not used, dummy pin.
spi_software_miso_pin: PJ1
latch_pin: PC4

click_pin: ^PJ0
back_pin: ^PJ2
up_pin: ^PJ4
down_pin: ^PJ3

[pca9533 led_strip]
i2c_bus: twi
i2c_address: 98
initial_RED: 1
initial_GREEN: 1
initial_BLUE: 1
