# This file contains common pin mappings for the 2019 Monoprice
# Select Mini v2. To use this config, the firmware should be compiled for the
# STM32F070 microcontroller with an 8MHz crystal and USB for communication.
#
# Use the following settings in make menuconfig:
#
#  * Microcontroller architecture: STM32
#  * Processor: STM32F070
#  * Clockspeed: 8 Mhz
#  * USB communication (instead of serial)
#
#  IMPORTANT: A bootloader offset of 8KiB will preserver the stock bootloader
#  and allows easy flashing via SDCard without additional hardware.
#
#  Also make sure to use the following string in the low-level configuration
#  options to set a couple of GPIOs to high when the MCU boots:
#
#  PA8, PB11, PB1
#
#  This will deactivate the steppers until klippy takes over.
#
# Note 1: You can flash klipper by copying out/klipper.bin to the MPSMv2's
# SDCard, inserting the SDCard while the printer is off and switching it on.
# Returning to stock firmware without a programmer is possible, as long as you
# do not overwrite the bootloader.
#
# Note 2: Stepper directions are not consistent in MSPMv2 printers. Check the
# directions of your device with M503 in the stock firmware and adjust the
# dir_pins below accordingly.
#
# Note 3: <PERSON>lipper currently does not support the LCD-UI of this
# printer, which is connected via serial interface and controlled by an
# esp8266 using a custom protocol. But it is possible to use the original
# rotary encoder & button with an extra display. An example of how to attach
# a SSD1306 OLED display is commented out at the bottom.
#
# Note 4: Monoprice has made small changes in the series of Select Mini
# printers without updating the printers version. Please consider adding your
# boards reversion to the list of compatible motion controllers below or
# submitting a new config template if significant changes are required.
#
# Tested montion controller reversions:
# * MPSM motion M2-v3.0-170721
#
# See ../docs/Config_Reference.md file for a description of all parameters.


[stepper_x]
homing_speed: 15
step_pin: PB14
dir_pin: !PB15 #modify stepper direction if necessary
enable_pin: !PA8
# rotation_distance varies in the printer model. Check the correct
# step-rate of your Select Mini in the original firmware (with M503)
# and calculate the appropriate value for rotation_distance. This has
# to be done for all axes.
# This config contains values for later MSPMv2 printers.
microsteps: 16
rotation_distance: 34.510 # 17 teeth on pulley; MXL belt (2.03 pitch)
endstop_pin: ^!PB4
position_endstop: 0
position_max: 120
position_min: -1

[stepper_y]
homing_speed: 15
step_pin: PB12
dir_pin: PB13 #modify stepper direction if necessary
enable_pin: !PA8
microsteps: 16
rotation_distance: 34.510 # check comment in [stepper_x] section
endstop_pin: ^!PA15
position_endstop: 0
position_max: 120
position_min: -1

[stepper_z]
homing_speed: 10
step_pin: PB10
dir_pin: PB2 #modify stepper direction if necessary
enable_pin: !PB11
microsteps: 16
full_steps_per_rotation: 48
rotation_distance: 0.7 # M4 rod.  check comment in [stepper_x] section
endstop_pin: ^!PB5
position_endstop: 0.5
position_max: 120

[extruder]
# extruder stepper
step_pin: PB0
dir_pin: !PC13 #modify stepper direction if necessary
enable_pin: !PB1
microsteps: 16
rotation_distance: 32.990 # 97 steps/mm. check comment in [stepper_x] section
nozzle_diameter: 0.400
filament_diameter: 1.750
# heater
heater_pin: PB6
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA0
# pid @ Vt 180°C on stock hardware
control: pid
pid_kp: 21.00
pid_ki: 0.75
pid_kd: 125.00
# temperatures
min_temp: 0
max_temp: 260
max_extrude_only_distance: 425 #for (un-)loading

[heater_bed]
heater_pin: PB7
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA1
# pid @ Vt 50°C on stock hardware
control: pid
pid_kp: 70.00
pid_ki: 1.50
pid_kd: 812.00
min_temp: 0
max_temp: 100

# Print cooling fan
[heater_fan fan]
pin: PB8

# chassis fan
[controller_fan chassis_fan]
pin: PB3
off_below: 0.25

[mcu]
serial: /dev/ttyACM0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 150
max_accel: 800
max_z_velocity: 1.5
max_z_accel: 20

# Positions for BED_SCREWS_ADJUST levelling with bed screws still accessible
[bed_screws]
screw1: 22, 10
screw2: 22, 90
screw3: 98, 90
screw4: 98, 10

######################################################################
# Optional: i2c ssd1306 OLED-display controlled by raspi host_mcu
######################################################################

#[mcu host]
#serial: /tmp/klipper_host_mcu
#
#[display]
#lcd_type: ssd1306
#i2c_mcu: host
#i2c_bus: i2c.1
#encoder_pins: ^host:gpiochip0/gpio7, ^host:gpiochip0/gpio8
#click_pin: !host:gpiochip0/gpio4

######################################################################
# Optional: Touch sensor via unpopulated header
######################################################################

# Later motion controllers have a unused socket that can be used to
# attach a probe or other additional hardware.
# You will need a JST PH 4-pin connector. Pin order (from STM32 to
# SDCard-slot) is: GND-PA2-PA3-3.3V
#
# Older BLtouch sensors and knock offs require 5V supply voltage.
# Since the motion controller does not supply 5V you will need to add
# an appropriate power source. easiest is probably soldering a 5V step
# down converter to the 12V supply pins. When running the touch sensor
# on 5V you will also need reduce the voltage on the sensor_pin with a
# voltage divider or level shifter. The control_pin can be attached to
# a 5V touch sensor directly.

#[bltouch]
#control_pin: PA2
#sensor_pin: PA3
#z_offset: 1
