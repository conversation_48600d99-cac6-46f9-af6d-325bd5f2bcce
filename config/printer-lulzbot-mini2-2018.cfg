#This file contains pin mappings for the stock Lulzbot Mini 2 which uses
#EinsyRetro mainboard and SingleExtruder(0.5mm) hotend.
#To use this config, the firmware should be compiled for the AVR atmega2560.

# Pin numbers checked against Lulzbot fork of Marlin pins_EINSYRETRO.h
# https://gitlab.com/lulzbot3d/marlin/-/blob/master/Marlin/src/pins/rambo/
#pins_EINSY_RETRO.h
# validated against https://github.com/ultimachine/EinsyRetro/blob/1.0b/board/
#Project%20Outputs%20for%20EinsyRetro/Schematic%20Prints_EinsyRetro_1.0b.PDF

# See docs/Config_Reference.md for a description of parameters.

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 2000
max_z_velocity: 40
max_z_accel: 100

[stepper_x]
step_pin: PC0
dir_pin: !PL0
enable_pin: !PA7
rotation_distance: 32
microsteps: 16
endstop_pin: tmc2130_stepper_x:virtual_endstop
position_endstop: -7
position_min: -7
position_max: 168
homing_retract_dist: 0
homing_speed: 30

[stepper_y]
step_pin: PC1
dir_pin: !PL1
enable_pin: !PA6
rotation_distance: 32
microsteps: 16
endstop_pin: tmc2130_stepper_y:virtual_endstop
position_endstop: -5
position_min: -5
position_max: 192
homing_retract_dist: 0
homing_speed: 30

[stepper_z]
step_pin: PC2
dir_pin: !PL2
enable_pin: !PA5
rotation_distance: 32
microsteps: 16
endstop_pin: PH4
position_endstop: 183
position_max: 185
position_min: -2

[extruder]
step_pin: PC3
dir_pin: PL6
enable_pin: !PA4
microsteps: 16
rotation_distance: 7.465
nozzle_diameter: 0.5
filament_diameter: 2.85
heater_pin: PE5
sensor_type: ATC Semitec 104GT-2
sensor_pin: PF0
min_temp: 0
max_temp: 280
control: pid
pid_kp: 24.121
pid_ki: 1.079
pid_kd: 134.779

[heater_bed]
heater_pin: PG5
sensor_type: Honeywell 100K 135-104LAG-J01
sensor_pin: PF2
min_temp: 0
max_temp: 130
control: pid
pid_kp: 71.304
pid_ki: 1.662
pid_kd: 764.734

[fan]
pin: PH5

[heater_fan nozzle_cooling_fan]
pin: PH3

[probe]
pin: ^!PB4
z_offset: 1.377
#z offset will need to be adjusted if you use the magnetic bed upgrade as the
#washer thickness will apply a different offset between the nozzle and true
#bed level.
samples: 3
sample_retract_dist: 1.0
samples_tolerance: 0.200

[bed_tilt]
points: -5, 22
        -5, 190
        160, 190
        160, 22
speed: 30
horizontal_move_z: 5

[tmc2130 stepper_x]
cs_pin: PG0
run_current: 0.975
diag0_pin: ^!PK2
driver_SGT: 4
sense_resistor: 0.120

[tmc2130 stepper_y]
cs_pin: PG2
run_current: 0.975
diag0_pin: ^!PK7
driver_SGT: 4
sense_resistor: 0.120

[tmc2130 stepper_z]
cs_pin: PK5
run_current: 0.960
diag0_pin: ^!PK6
stealthchop_threshold: 999999
sense_resistor: 0.120

[tmc2130 extruder]
cs_pin: PK4
run_current: 0.960
diag0_pin: ^!PK3
stealthchop_threshold: 999999
sense_resistor: 0.120

[static_digital_output sd_card]
pins: PB0

[safe_z_home]
home_xy_position:  75, 75
speed: 30.0
z_hop: 5

[display]
lcd_type: st7920
cs_pin: PD5
sclk_pin: PD2
sid_pin: PD3
menu_timeout: 5
encoder_pins: ^PJ1,^PJ2
encoder_steps_per_detent: 2
click_pin: ^!PH6
