# This file contains common pin mappings for Duet2 Eth/Wifi boards
# that have the Duex expansion board. To use this config, the firmware
# should be compiled for the SAM4E8E.

# See docs/Config_Reference.md for a description of parameters.

## Drivers
# Here are the pins for the 10 stepper drivers supported by a Duet2 board
# | Drive |  DIR pin |  STEP pin |  ENDSTOP pin |  SPI EN pin |
# |-------|----------|-----------|--------------|-------------|
# | X     |  PD11    |  PD6      |  PC14        |  PD14       |
# | Y     |  PD12    |  PD7      |  PA2         |  PC9        |
# | Z     |  PD13    |  PD8      |  PD29        |  PC10       |
# | E0    |  PA1     |  PD5      |  PD10        |  PC17       |
# | E1    |  PD9     |  PD4      |  PC16        |  PC25       |
# | E2    |  PD28    |  PD2      |  PE0*        |  PD23       |
# | E3    |  PD22    |  PD1      |  PE1*        |  PD24       |
# | E4    |  PD16    |  PD0      |  PE2*        |  PD25       |
# | E5    |  PD17    |  PD3      |  PE3*        |  PD26       |
# | E6    |  PC0     |  PD27     |  PA17*       |  PB14       |
# Pins marked with asterisks (*) are only assigned to these functions
# if no duex is connected. If a duex is connected, these endstops are
# remapped to the SX1509 on the Duex (unfortunately they can't be used
# as endstops in klipper, however one may use them as digital outs or
# PWM outs). The SPI EN pins are required for the TMC2660 drivers (use
# the SPI EN pin as 'cs_pin' in the respective config block). The
# **enable pin for all steppers** is TMC_EN = !PC6.
#
## Fans
# | FAN  |          PIN          |
# |------|-----------------------|
# | FAN0 |  PC23                 |
# | FAN1 |  PC26                 |
# | FAN2 |  PA0                  |
# | FAN3 |  sx1509_duex:PIN_12*  |
# | FAN4 |  sx1509_duex:PIN_7*   |
# | FAN5 |  sx1509_duex:PIN_6*   |
# | FAN6 |  sx1509_duex:PIN_5*   |
# | FAN7 |  sx1509_duex:PIN_4*   |
# | FAN8 |  sx1509_duex:PIN_15*  |
# Pins marked with (*) assume the following sx1509 config section:
#[sx1509 duex]
#i2c_address: 62
#
## Heaters and Thermistors
# | Extruder Drive |  HEAT pin |  TEMP pin |
# |----------------|-----------|-----------|
# | BED            |  PA19     |  PC13     |
# | E0             |  PA20     |  PC15     |
# | E1             |  PA16     |  PC12     |
# | E2             |  PC3      |  PC29     |
# | E3             |  PC5      |  PC30     |
# | E4             |  PC8      |  PC31     |
# | E5             |  PC11     |  PC27     |
# | E6             |  PA15     |  PA18     |
#
## Misc pins
# |    Name     |   Pin   |
# |-------------|---------|
# | ZProbe_IN   |  PC1    |
# | PS_ON       |  PD15   |
# | LED_ONBOARD |  PC2    |
# | SPI0_CS0    |  PC24   |
# | SPI0_CS1    |  PB2    |
# | SPI0_CS2    |  PC18   |
# | SPI0_CS3    |  PC19   |
# | SPI0_CS4    |  PC20   |
# | SPI0_CS5    |  PA24   |
# | SPI0_CS6    |  PE1*   |
# | SPI0_CS7    |  PE2*   |
# | SPI0_CS8    |  PE3*   |
# | SX1509_IRQ  |  PA17*  |
# | SG_TST      |  PE0*   |
# | ENC_SW      |  PA7    |
# | ENC_A       |  PA8    |
# | ENC_B       |  PC7    |
# | LCD_DB7     |  PD18   |
# | LCD_DB6     |  PD19   |
# | LCD_DB5     |  PD20   |
# | LCD_DB4     |  PD21   |
# | LCD_RS      |  PC28   |
# | LCD_E       |  PA25   |
# Pins marked with one asterisk (*) replace E2_STOP-E6_STOP if a duex is present
# For the remaining pins check the schematics provided here: https://github.com/T3P3/Duet

[stepper_x]
step_pin: PD6
dir_pin: PD11
enable_pin: !PC6
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC14
position_endstop: 0
position_max: 250

[tmc2660 stepper_x]
cs_pin: PD14 # X_SPI_EN Required for communication
spi_bus: usart1 # All TMC2660 drivers are connected to USART1
run_current: 1.000
sense_resistor: 0.051
idle_current_percent: 20

[stepper_y]
step_pin: PD7
dir_pin: !PD12
enable_pin: !PC6
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA2
position_endstop: 0
position_max: 210

[tmc2660 stepper_y]
cs_pin: PC9
spi_bus: usart1
run_current: 1.000
sense_resistor: 0.051
idle_current_percent: 20

[stepper_z]
step_pin: PD8
dir_pin: PD13
enable_pin: !PC6
microsteps: 16
rotation_distance: 8
endstop_pin: ^PD29
position_endstop: 0.5
position_max: 200

[tmc2660 stepper_z]
cs_pin: PC10
spi_bus: usart1
run_current: 1.000
sense_resistor: 0.051

#On drive E4
[stepper_z1]
step_pin: PD0
dir_pin: PD16
enable_pin: !PC6
microsteps: 16
rotation_distance: 8

[tmc2660 stepper_z1]
cs_pin: PD25
spi_bus: usart1
run_current: 1.000
sense_resistor: 0.051

#On drive E5
[stepper_z2]
step_pin: PD3
dir_pin: !PD17
enable_pin: !PC6
microsteps: 16
rotation_distance: 8

[tmc2660 stepper_z2]
cs_pin: PD26
spi_bus: usart1
run_current: 1.000
sense_resistor: 0.051

#On drive E6
[stepper_z3]
step_pin: PD27
dir_pin: !PC0
enable_pin: !PC6
microsteps: 16
rotation_distance: 8

[tmc2660 stepper_z3]
cs_pin: PB14
spi_bus: usart1
run_current: 1.000
sense_resistor: 0.051

#On drive E0
[extruder]
step_pin: PD5
dir_pin: PA1
enable_pin: !PC6
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: !PA20
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC15
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[tmc2660 extruder]
cs_pin: PC17
spi_bus: usart1
run_current: 1.000
sense_resistor: 0.051

#On drive E1
[extruder1]
step_pin: PD4
dir_pin: PD9
enable_pin: !PC6
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: !PA16
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC12
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[tmc2660 extruder1]
cs_pin: PC25
spi_bus: usart1
run_current: 1.000
sense_resistor: 0.051

# On drive E2
[extruder2]
step_pin: PD2
dir_pin: !PD28
enable_pin: !PC6
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: !PC3
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC29
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[tmc2660 extruder2]
cs_pin: PD23
spi_bus: usart1
run_current: 1.000
sense_resistor: 0.051

# On drive E3
[extruder3]
step_pin: PD1
dir_pin: !PD22
enable_pin: !PC6
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: !PC5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC30
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[tmc2660 extruder3]
cs_pin: PD24
spi_bus: usart1
run_current: 1.000
sense_resistor: 0.051

[heater_bed]
heater_pin: !PA19
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC13
control: watermark
min_temp: 0
max_temp: 130

# Fan0
[fan]
pin: PC23

# Fan1 controlled by extruder
[heater_fan heatbreak_cooling_fan]
pin: PC26
heater: extruder
heater_temp: 45
fan_speed: 1.0

# Fan2, controlled by E5_TEMP
[temperature_fan chamber_fan]
pin: PA0
max_power: 1
shutdown_speed: 1
cycle_time: 0.01
min_temp: 40
max_temp: 120
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC27
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[sx1509 duex]
i2c_address: 62 # Address is fixed on duex boards

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[static_digital_output onboard_led]
pins: !PC2

[output_pin FAN3]
pin: sx1509_duex:PIN_12
pwm: True
hardware_pwm: True # Only hardware PWM fans are supported

[output_pin FAN4]
pin: sx1509_duex:PIN_7
pwm: True
hardware_pwm: True

[output_pin FAN5]
pin: sx1509_duex:PIN_6
pwm: True
hardware_pwm: True

[output_pin FAN6]
pin: sx1509_duex:PIN_5
pwm: True
hardware_pwm: True

[output_pin FAN7]
pin: sx1509_duex:PIN_4
pwm: True
hardware_pwm: True

[output_pin FAN8]
pin: sx1509_duex:PIN_15
pwm: True
hardware_pwm: True

[output_pin GPIO1] # General purpose pin broken out on the duex
pin: sx1509_duex:PIN_11
pwm: False
value: 1
