# This file contains common pin mappings for the BIGTREETECH Manta M5P
# To use this config, the firmware should be compiled for the
# STM32G0B1 with a "8KiB bootloader" "8 MHz crystal"
# and "USB (on PA11/PA12)" or "CAN bus (on PD0/PD1)".

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC8
dir_pin: !PC9
enable_pin: !PA15
microsteps: 16
rotation_distance: 40
endstop_pin: ^PD3
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PA10
dir_pin: !PA14
enable_pin: !PA13
microsteps: 16
rotation_distance: 40
endstop_pin: ^PD2
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PC6
dir_pin: PC7
enable_pin: !PA9
microsteps: 16
rotation_distance: 8
endstop_pin: ^PC3
position_endstop: 0.0
position_max: 200

[extruder]
step_pin: PB12
dir_pin: !PB11
enable_pin: !PA8
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA1
control: pid
pid_Kp: 21.527
pid_Ki: 1.063
pid_Kd: 108.982
min_temp: 0
max_temp: 250

#sensor_type:MAX31865
#sensor_pin: PA4
#spi_bus: spi1
#rtd_nominal_r: 100
#rtd_reference_r: 430
#rtd_num_of_wires: 2

#[filament_switch_sensor material_0]
#switch_pin: PC2

#[extruder1]
#step_pin: PB0
#dir_pin: PB1
#enable_pin: !PC4
#heater_pin: PA7
#sensor_pin: PA2
#...

[heater_bed]
heater_pin: PA5
sensor_type: Generic 3950
sensor_pin: PA0
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PA4

#[heater_fan fan1]
#pin: PA3

#[heater_fan SoC_fan]
#pin: CB1:gpio79
#pin: RPI:gpio26

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

########################################
# TMC2209 configuration
########################################

#[tmc2209 stepper_x]
#uart_pin: PD9
#run_current: 0.800
#diag_pin: PD3

#[tmc2209 stepper_y]
#uart_pin: PD8
#run_current: 0.800
#diag_pin: PD2

#[tmc2209 stepper_z]
#uart_pin: PB10
#run_current: 0.800
#diag_pin: PC3

#[tmc2209 extruder]
#uart_pin: PB2
#run_current: 0.600
#diag_pin: PC2

#[tmc2209 extruder1]
#uart_pin: PA6
#run_current: 0.600
#diag_pin:

########################################
# TMC2130 configuration
########################################

#[tmc2130 stepper_x]
#cs_pin: PD9
#spi_bus: spi2
#run_current: 0.800
#stealthchop_threshold: 999999
#diag1_pin: PD3

#[tmc2130 stepper_y]
#cs_pin: PD8
#spi_bus: spi2
#run_current: 0.800
#stealthchop_threshold: 999999
#diag1_pin: PD2

#[tmc2130 stepper_z]
#cs_pin: PB10
#spi_bus: spi2
#run_current: 0.650
#stealthchop_threshold: 999999
#diag1_pin: PC3

#[tmc2130 extruder]
#cs_pin: PB2
#spi_bus: spi2
#run_current: 0.800
#stealthchop_threshold: 999999
#diag1_pin: PC2

#[tmc2130 extruder1]
#cs_pin: PA6
#spi_bus: spi2
#run_current: 0.800
#stealthchop_threshold: 999999
#diag1_pin:

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PD5, EXP1_3=PB3, EXP1_5=PB5, EXP1_7=PB7, EXP1_9=<GND>,
    EXP1_2=PD4,  EXP1_4=PD6, EXP1_6=PB4, EXP1_8=PB6, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PB14, EXP2_3=PB8, EXP2_5=PC10, EXP2_7=PC12,  EXP2_9=<GND>,
    EXP2_2=PB13, EXP2_4=PB9, EXP2_6=PB15, EXP2_8=<RST>, EXP2_10=<NC>

# See the sample-lcd.cfg file for definitions of common LCD displays.

#[bltouch]
#sensor_pin: PC13
#control_pin: PC15

# Proximity switch
#[probe]
#pin: PC15

#[neopixel my_neopixel1]
#pin: PC11

#[neopixel my_neopixel2]
#pin: PC14

#[adxl345]
#cs_pin: PC0
#spi_bus: spi2
