# This file contains common pin mappings for the BIGTREETECH Manta E3EZ
# To use this config, the firmware should be compiled for the
# STM32G0B1 with a "8KiB bootloader" "8 MHz crystal"
# and "USB (on PA11/PA12)" or "CAN bus (on PB12/PB13)".

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PA14
dir_pin: !PA10
enable_pin: !PA13
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC4
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_y]
step_pin: PC8
dir_pin: !PA15
enable_pin: !PC14
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB0
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_z]
step_pin: PD2
dir_pin: PD4
enable_pin: !PD3
microsteps: 16
rotation_distance: 8
endstop_pin: ^PC6
position_endstop: 0
position_max: 270

[extruder]
step_pin: PD5
dir_pin: !PD6
enable_pin: !PB3
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB11 #HE0
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA4 #TH0
control: pid
pid_Kp: 21.527
pid_Ki: 1.063
pid_Kd: 108.982
min_temp: 0
max_temp: 250

#[filament_switch_sensor material_0]
#switch_pin: PC5

#[extruder1]
#step_pin: PB7
#dir_pin: PB6
#enable_pin: !PB4
#heater_pin: PB10 # HE1
#sensor_pin: PA5 # T1

#[filament_switch_sensor material_1]
#switch_pin: PB1

[heater_bed]
heater_pin: PB2 #HB
sensor_type: EPCOS 100K B57560G104F #Generic 3950
sensor_pin: PA3 #TB
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PA8

#[heater_fan fan1]
#pin: PB15

#[heater_fan fan2]
#pin: PB14

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

########################################
# TMC2209 configuration
########################################

#[tmc2209 stepper_x]
#uart_pin: PB8
##diag_pin: PC4
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2209 stepper_y]
#uart_pin: PC9
##diag_pin: PB0
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2209 stepper_z]
#uart_pin: PD0
##diag_pin: PC6
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc2209 extruder]
#uart_pin: PD1
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2209 extruder1]
#uart_pin: PB5
#run_current: 0.800
#stealthchop_threshold: 999999

########################################
# TMC2130 configuration
########################################

#[tmc2130 stepper_x]
#cs_pin: PB8
#spi_software_miso_pin: PC11
#spi_software_mosi_pin: PC12
#spi_software_sclk_pin: PC10
##diag1_pin: PF3
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_y]
#cs_pin: PC9
#spi_software_miso_pin: PC11
#spi_software_mosi_pin: PC12
#spi_software_sclk_pin: PC10
##diag1_pin: PF4
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_z]
#cs_pin: PD0
#spi_software_miso_pin: PC11
#spi_software_mosi_pin: PC12
#spi_software_sclk_pin: PC10
##diag1_pin: PF5
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc2130 extruder]
#cs_pin: PD1
#spi_software_miso_pin: PC11
#spi_software_mosi_pin: PC12
#spi_software_sclk_pin: PC10
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder1]
#cs_pin: PB5
#spi_software_miso_pin: PC11
#spi_software_mosi_pin: PC12
#spi_software_sclk_pin: PC10
#run_current: 0.800
#stealthchop_threshold: 999999

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PC1, EXP1_3=PC3, EXP1_5=PC0, EXP1_7=PA2, EXP1_9=<GND>,
    EXP1_2=PC2,  EXP1_4=<RST>, EXP1_6=PA0, EXP1_8=PA1, EXP1_10=<5V>

#[bltouch]
#sensor_pin: PA6
#control_pin: PA7

#[output_pin PS_ON]
#pin: PA9

#[output_pin pb9_pin]
#pin: PB9

#[neopixel my_neopixel]
#pin: PC7

#[adxl345]
#cs_pin: PC15
#spi_software_miso_pin: PC11
#spi_software_mosi_pin: PC12
#spi_software_sclk_pin: PC10
