# This file contains common pin mappings for the Biqu B1 SE Plus.
# To use this config, the firmware should be compiled for the
# STM32F407 with a "32KiB bootloader".

# In newer versions of this board shipped in late 2021 the STM32F429
# is used, if this is the case compile for this with a "32KiB bootloader"
# You will need to check the chip on your board to identify which you have.
#
# The "make flash" command does not work on the SKR 2. Instead,
# after running "make", copy the generated "out/klipper.bin" file to a
# file named "firmware.bin" on an SD card and then restart the SKR 2
# with that SD card.

# See docs/Config_Reference.md for a description of parameters.

[mcu]
serial: /dev/serial/by-id/usb-Klipper_stm32f407xx_1D0039000F47393438343535-if00

########################################
# Stepper X Pins and TMC2208 configuration
########################################
[stepper_x]
step_pin: PE2
dir_pin: !PE1
enable_pin: !PE3
microsteps: 16
rotation_distance: 40
endstop_pin: !PC1
position_endstop: 0
position_max: 310
homing_speed: 50

[tmc2208 stepper_x]
uart_pin: PE0
run_current: 0.800
stealthchop_threshold: 999999

########################################
# Stepper Y Pins and TMC2208 configuration
########################################
[stepper_y]
step_pin: PD5
dir_pin: PD4
enable_pin: !PD6
microsteps: 16
rotation_distance: 40
endstop_pin: !PC3
position_endstop: 0
position_max: 310
homing_speed: 50

[tmc2208 stepper_y]
uart_pin: PD3
run_current: 0.800
stealthchop_threshold: 999999

########################################
# Stepper Z Pins and TMC2208 configuration
########################################
[stepper_z]
step_pin: PA15
dir_pin: PA8
enable_pin: !PD1
microsteps: 16
rotation_distance: 8
endstop_pin: probe:z_virtual_endstop
homing_speed: 10
second_homing_speed: 1
position_min: -2
position_max: 340

[tmc2208 stepper_z]
uart_pin: PD0
run_current: 0.800
stealthchop_threshold: 999999

########################################
# Extruder Pins and TMC2208 configuration
########################################
[extruder]
step_pin: PD15
dir_pin: !PD14
enable_pin: !PC7
microsteps: 16
rotation_distance: 34.2152 # Calibrar - ver https://www.klipper3d.org/Rotation_Distance.html
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB3
sensor_type: Generic 3950
sensor_pin: PA2 #thermistor pin
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[tmc2208 extruder]
uart_pin: PC6
run_current: 0.800
stealthchop_threshold: 999999

########################################
# Heater Bed Pins
########################################
[heater_bed]
heater_pin: PD7
sensor_type: Generic 3950
sensor_pin: PA1
control: pid
pid_Kp: 54
pid_Ki: 0.77
pid_Kd: 900
min_temp: 0
max_temp: 110

########################################
# Printer Configuration
########################################
[printer]
kinematics: cartesian
max_velocity: 200
max_accel: 1000
max_z_velocity: 5
max_z_accel: 100

########################################
# Probe configuration
########################################
[probe]
pin: ^!PE4
z_offset: 0.0
x_offset: 0.0
y_offset: 0.0
speed: 10.0
samples: 2
samples_result: average
sample_retract_dist: 2.0
samples_tolerance: 0.2

[safe_z_home]
home_xy_position: 155,155
speed: 100
z_hop: 5
z_hop_speed: 5

[output_pin probe_enable]
pin: PE5
value: 1

########################################
# Bed Mesh configuration
########################################
[bed_mesh]
speed: 2000
horizontal_move_z: 3
mesh_min: 20, 20
mesh_max: 290, 290
probe_count: 7, 7
mesh_pps: 2,2
algorithm: bicubic
bicubic_tension: 0.2

########################################
# Fan Nozzle configuration
########################################
[fan]
pin: PB7

[heater_fan Cooling_fan]
pin: PB6
max_power: 1.0
kick_start_time: 0.100
heater: heater_bed

[heater_fan Board_fan]
pin: PB5
max_power: 1.0
kick_start_time: 0.100
heater: extruder

########################################
# Filament Sensor configuration
########################################
[filament_switch_sensor Sensor_Filamento]
switch_pin: !PC2
pause_on_runout: true #pause handled by macro

########################################
# Motor Power Pin
########################################
[output_pin motor_power]
pin: PC13
value: 1
