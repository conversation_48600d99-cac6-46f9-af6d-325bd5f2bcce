# This file is an example config file for polar style printers. One
# may copy and edit this file to configure a new polar printer.

# POLAR KINEMATICS ARE A WORK IN PROGRESS. Moves around the 0, 0
# position are known to not work properly.

# See docs/Config_Reference.md for a description of parameters.

[stepper_bed]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
gear_ratio: 80:16

[stepper_arm]
step_pin: PF6
dir_pin: PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: ^PJ1
position_endstop: 300
position_max: 300
homing_speed: 50

[stepper_z]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 8
endstop_pin: ^PD3
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4
sensor_type: ATC Semitec 104GT-2
sensor_pin: PK5
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PH6

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: polar
max_velocity: 300
max_accel: 3000
max_z_velocity: 25
max_z_accel: 30
