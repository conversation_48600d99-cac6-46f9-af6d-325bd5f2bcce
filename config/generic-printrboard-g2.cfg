# This file contains common pin mappings for Printrboard G2 boards.
# To use this config, the firmware should be compiled for the SAM3x8c.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PB15
dir_pin: !PA16
enable_pin: !PB16
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA11
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PA29
dir_pin: !PB1
enable_pin: !PB0
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB26
position_endstop: 150
position_max: 150
homing_speed: 50

[stepper_z]
step_pin: PA21
dir_pin: PA26
enable_pin: !PA25
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PA10
position_endstop: 0
position_min: -2
position_max: 200

[output_pin motor_x_pwm]
pin: PB17
pwm: True
hardware_pwm: True
scale: 2.25
cycle_time: .000004
value: 0.8

[output_pin motor_y_pwm]
pin: PB19
pwm: True
hardware_pwm: True
scale: 2.25
cycle_time: .000004
value: 0.8

[output_pin motor_z_pwm]
pin: PB18
pwm: True
hardware_pwm: True
scale: 2.25
cycle_time: .000004
value: 0.8

[output_pin motor_e_pwm]
pin: PA2
pwm: True
hardware_pwm: True
scale: 2.25
cycle_time: .000004
value: 0.5

[output_pin heater_enable]
pin: PA7
pwm: True
cycle_time: 0.050
value: 0.1

[thermistor G2]
temperature1: 20
resistance1: 140000
temperature2: 195
resistance2: 593
temperature3: 255
resistance3: 189

[extruder]
step_pin: PB14
dir_pin: PB23
enable_pin: !PB22
microsteps: 16
rotation_distance: 25.600
nozzle_diameter: 0.300
filament_diameter: 1.750
heater_pin: PA5
sensor_pin: PA23
sensor_type: G2
inline_resistor: 4700
control: pid
pid_kp: 29.852
pid_ki: 2.843
pid_kd: 78
min_temp: 0
max_temp: 290

[fan]
pin: PB27

[heater_fan heatbreak_cooling_fan]
pin: PA6

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 400
max_accel: 2500
max_z_velocity: 15
max_z_accel: 300

[static_digital_output step_config]
pins: PA19, PB20, PA27, PB10
