# This file contains pin mappings for the MTW Create (circa 2015). To
# use this config, the firmware should be compiled for the AVR
# atmega2560.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC0
dir_pin: !PL1
enable_pin: !PA7
microsteps: 16
rotation_distance: 32
endstop_pin: ^PB6
#endstop_pin: ^PA2
position_endstop: 0
position_max: 250
homing_speed: 50

[stepper_y]
step_pin: PC1
dir_pin: PL0
enable_pin: !PA6
microsteps: 16
rotation_distance: 32
endstop_pin: ^PB5
#endstop_pin: ^PA1
position_endstop: 0
position_max: 320
homing_speed: 50

[stepper_z]
step_pin: PC2
dir_pin: PL2
enable_pin: !PA5
microsteps: 16
rotation_distance: 4
endstop_pin: probe:z_virtual_endstop
#endstop_pin: ^PC7
#position_endstop: 0.5
position_max: 225

[extruder]
step_pin: PC3
dir_pin: PL6
enable_pin: !PA4
microsteps: 16
rotation_distance: 32.640
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PH6
sensor_type: ATC Semitec 104GT-2
sensor_pin: PF0
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 275

#Uncomment below for dual extruder printers
#[extruder1]
#step_pin: PC4
#dir_pin: PL7
#enable_pin: !PA3
#heater_pin: PH4
#sensor_pin: PF1
#microsteps: 16
#rotation_distance: 32.640
#nozzle_diameter: 0.400
#filament_diameter: 1.750
#sensor_type: ATC Semitec 104GT-2
#sensor_pin: PF0
#control: pid
#pid_Kp: 22.2
#pid_Ki: 1.08
#pid_Kd: 114
#min_temp: 0
#max_temp: 275

[heater_bed]
heater_pin: PE5
sensor_type: Generic 3950
sensor_pin: PF2
control: watermark
min_temp: 0
max_temp: 130

[bltouch]
sensor_pin: PB4
control_pin: PL5
x_offset: -17
y_offset: 37
z_offset: 2.0

[safe_z_home]
home_xy_position: 125, 150
z_hop: 10
z_hop_speed: 5

[bed_mesh]
mesh_min: 5, 5
mesh_max: 225, 225

[fan]
pin: PH5

[heater_fan heatbreak_cooling_fan]
pin: PH3

[mcu]
serial: /dev/serial/by-id/usb-UltiMachine__ultimachine.com__RAMBo_854333433343516051F1-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[ad5206 stepper_digipot]
enable_pin: PD7
scale: 2.08
# Channel 1 is E0, 2 is E1, 3 is unused, 4 is Z, 5 is X, 6 is Y
channel_1: 1.34
channel_2: 1.0
channel_4: 1.1
channel_5: 1.1
channel_6: 1.1

# Enable 16 micro-steps on steppers X, Y, Z, E0, E1
[static_digital_output stepper_config]
pins:
    PG1, PG0,
    PK7, PG2,
    PK6, PK5,
    PK3, PK4,
    PK1, PK2

[static_digital_output yellow_led]
pins: !PB7

# Common EXP1 / EXP2 (display) pins
[board_pins]
aliases:
    # Common EXP1/EXP2 headers found on RAMBo v1.4
    EXP1_1=PE6, EXP1_3=PG3, EXP1_5=PJ2, EXP1_7=PJ7, EXP1_9=<GND>,
    EXP1_2=PE2, EXP1_4=PG4, EXP1_6=PJ3, EXP1_8=PJ4, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PB3, EXP2_3=PJ5, EXP2_5=PJ6, EXP2_7=PD4, EXP2_9=<GND>,
    EXP2_2=PB1, EXP2_4=PB0, EXP2_6=PB2, EXP2_8=PE7, EXP2_10=PH2
    # Pins EXP2_1, EXP2_6, EXP2_2 are also MISO, MOSI, SCK of bus "spi"

# See the sample-lcd.cfg file for definitions of common LCD displays.
