# This file contains common pin mappings for MKS Monster8
# boards. To use this config, the firmware should be compiled for the
# stm32f407. When running "make menuconfig", select the 48KiB
# bootloader, and enable "USB for communication".

# The "make flash" command does not work on the MKS Monster8. Instead,
# after running "make", copy the generated "out/klipper.bin" file to a
# file named "mks_monster8.bin" on an SD card or Udisk and then restart the
# MKS Monster8 with that SD card or Udisk.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC14
dir_pin: PC13
enable_pin: !PC15
microsteps: 16
rotation_distance: 40
endstop_pin: !PA14  # PA13 for X-max; endstop have'!' is NO
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PE5
dir_pin: !PE4
enable_pin: !PC15
microsteps: 16
rotation_distance: 40
endstop_pin: !PA15  # PC5 for Y-max; endstop have'!' is NO
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PE1
dir_pin: PE0
enable_pin: !PE2
microsteps: 16
rotation_distance: 8
endstop_pin: !PB13  # PB12 for Z-max; endstop have'!' is NO
position_endstop: 0
position_max: 220

[extruder]
step_pin: PB5
dir_pin: !PB4
enable_pin: !PB6
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB1
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC1
control: pid
pid_Kp: 22
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 260

#[extruder1]
#step_pin: PD6
#dir_pin: !PD5
#enable_pin: !PD7
#heater_pin: PB0
#sensor_pin: PC2

#[extruder2]
#step_pin: PD2
#dir_pin: !PD1
#enable_pin: !PD3
#heater_pin: PA3
#sensor_pin: PC3

#[extruder3]
#step_pin: PC7
#dir_pin: PC6
#enable_pin: !PC8

#[extruder4]
#step_pin: PD13
#dir_pin: !PD12
#enable_pin: !PD14

[heater_bed]
heater_pin: PB10
sensor_type: NTC 100K MGB18-104F39050L32
sensor_pin: PC0
max_power: 1.0
control: pid
pid_kp: 71.039
pid_ki: 2.223
pid_kd: 567.421
min_temp: 0
max_temp: 200

#fan for printed model FAN0
[fan]
pin: PA2

#fan for hotend FAN1
#[heater_fan my_nozzle_fan]
[heater_fan fan1]
pin: PA1
shutdown_speed: 1

#fan for control board FAN2
#[heater_fan my_control_fan]
[heater_fan fan2]
pin: PA0
shutdown_speed: 1

[mcu]
serial: /dev/serial/by-id/usb-Klipper_stm32f407xx_4D0045001850314335393520-if00

[printer]
kinematics: cartesian
max_velocity: 10000
max_accel: 20000
max_z_velocity: 100
max_z_accel: 1000

#####################################################################
# LED Control
#####################################################################

#[output_pin caselight ](Use PA9)
##  Chamber Lighting - In 5V-RGB Position
#pin: PA9
#pwm: true
#shutdown_value: 0
#value:100
#cycle_time: 0.01

########################################
# TMC UART configuration
########################################

#[tmc2208 stepper_x]
#uart_pin: PE6
#run_current: 0.8
#hold_current: 0.5
#stealthchop_threshold: 999999

#[tmc2208 stepper_y]
#uart_pin: PE3
#run_current: 0.8
#hold_current: 0.5
#stealthchop_threshold: 999999

#[tmc2208 stepper_z]
#uart_pin: PB7
#run_current: 0.8
#hold_current: 0.5
#stealthchop_threshold: 999999

#[tmc2208 extruder]
#uart_pin: PB3
#run_current: 0.8
#hold_current: 0.5
#sense_resistor: 0.110
#stealthchop_threshold: 999999

#[tmc2208 extruder1]
#uart_pin: PD4
#run_current: 0.8
#hold_current: 0.5
#stealthchop_threshold: 999999

#[tmc2208 extruder2]
#uart_pin: PD0
#run_current: 0.8
#hold_current: 0.5
#stealthchop_threshold: 999999

#[tmc2208 extruder3]
#uart_pin: PD15
#run_current: 0.8
#hold_current: 0.5
#stealthchop_threshold: 999999

#[tmc2208 extruder4]
#uart_pin: PD11
#run_current: 0.8
#hold_current: 0.5
#stealthchop_threshold: 999999

########################################
# TMC SPI configuration
########################################

#[tmc2130 stepper_x]
#spi_bus: spi4
#cs_pin: PE6
#diag1_pin: PA14
#run_current: 0.800
#hold_current: 0.500
#stealthchop_threshold: 999999

#[tmc2130 stepper_y]
#spi_bus: spi4
#cs_pin: PE3
#diag1_pin: PA15
#run_current: 0.800
#hold_current: 0.500
#stealthchop_threshold: 999999

#[tmc2130 stepper_z]
#spi_bus: spi4
#cs_pin: PB7
#diag1_pin: PB13
#run_current: 0.800
#hold_current: 0.500
#stealthchop_threshold: 999999

#[tmc2130 extruder]
#spi_bus: spi4
#cs_pin: PB3
#diag1_pin: PA13
#run_current: 0.800
#hold_current: 0.500
#stealthchop_threshold: 999999

#[tmc2130 extruder1]
#spi_bus: spi4
#cs_pin: PD4
#diag1_pin: PC5
#run_current: 0.800
#hold_current: 0.500
#stealthchop_threshold: 999999

#[tmc2130 extruder2]
#spi_bus: spi4
#cs_pin: PD0
#diag1_pin: PB12
#run_current: 0.800
#hold_current: 0.500
#stealthchop_threshold: 999999

#[tmc2130 extruder3]
#spi_bus: spi4
#cs_pin: PD15
#run_current: 0.800
#hold_current: 0.500
#stealthchop_threshold: 999999

#[tmc2130 extruder4]
#spi_bus: spi4
#cs_pin: PD11
#run_current: 0.800
#hold_current: 0.500
#stealthchop_threshold: 999999

########################################
# EXP1 / EXP2 (display) pins
########################################

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PB2,  EXP1_3=PE11, EXP1_5=PD9, EXP1_7=PE15, EXP1_9=<GND>,
    EXP1_2=PE10, EXP1_4=PD10, EXP1_6=PD8, EXP1_8=PE7,  EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PA6, EXP2_3=PE9, EXP2_5=PE8, EXP2_7=PB11,  EXP2_9=<GND>,
    EXP2_2=PA5, EXP2_4=PA4, EXP2_6=PA7, EXP2_8=<RST>, EXP2_10=<3.3v>
    # Pins EXP2_1, EXP2_6, EXP2_2 are also MISO, MOSI, SCK of bus "ssp1"
