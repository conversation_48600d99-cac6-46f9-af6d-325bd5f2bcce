# This file contains pin mappings for the stock 2021 Creality
# Ender2 Pro. To use this config, during "make menuconfig" select
# the STM32F103 with a "28KiB bootloader" and serial
# (on USART1 PA10/PA9) communication.

# If you prefer a direct serial connection, in "make menuconfig"
# select "Enable extra low-level configuration options" and select
# serial (on USART2 PA3/PA2), which is broken out on the 10 pin IDC
# cable used for the LCD module as follows:
# 3: Tx, 4: Rx, 9: GND, 10: VCC

# Flash this firmware by copying "out/klipper.bin" to a SD card and
# turning on the printer with the card inserted. The firmware
# filename must end in ".bin" and must not match the last filename
# that was flashed.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC2
dir_pin: PB9
enable_pin: !PC3
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA5
position_min: -20
position_endstop: -20
position_max: 165
homing_speed: 50

[stepper_y]
step_pin: PB8
dir_pin: PB7
enable_pin: !PC3
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA6
position_min: -5
position_endstop: -5
position_max: 165
homing_speed: 50

[stepper_z]
step_pin: PB6
dir_pin: !PB5
enable_pin: !PC3
microsteps: 16
rotation_distance: 8
endstop_pin: ^PA7
position_endstop: 0.0
position_max: 180

[extruder]
max_extrude_only_distance: 100.0
step_pin: PB4
dir_pin: PB3
enable_pin: !PC3
microsteps: 16
rotation_distance: 27.53480577
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA1
sensor_pin: PC5
sensor_type: EPCOS 100K B57560G104F
control: pid
pid_Kp: 29.634
pid_Ki: 2.102
pid_Kd: 104.459
min_temp: 0
max_temp: 260

[heater_bed]
heater_pin: PB10
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC4
control: pid
pid_Kp: 72.921
pid_Ki: 1.594
pid_Kd: 834.031
min_temp: 0
max_temp: 80

[fan]
pin: PA0

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[display]
lcd_type: st7920
cs_pin: PB12
sclk_pin: PB13
sid_pin: PB15
encoder_pins: ^PB14, ^PA2
click_pin: ^!PB2

[output_pin beeper]
pin: PC6
