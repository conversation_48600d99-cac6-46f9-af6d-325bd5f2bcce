# This file contains common pin mappings for the Mellow Fly-Gemini-v1.
# To use this config, the firmware should be compiled for the
# STM32F405 with a "32KiB bootloader".

# The "make flash" command does not work on the Fly-Gemini-v1. Instead,
# after running "make", copy the generated "out/klipper.bin" file to a
# file named "firmware.bin" or "klipper.bin" on an SD card and then restart the Fly-Gemini-v1
# with that SD card.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC13
dir_pin: PC1
enable_pin: !PB2
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA3
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PC14
dir_pin: !PC4
enable_pin: !PB6
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB1
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PC15
dir_pin: PC5
enable_pin: !PB5
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB10
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PC3
dir_pin: PC7
enable_pin: !PB4
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA0
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC0
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PA2
sensor_type: Generic 3950
sensor_pin: PC2
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PC6

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100


########################################
# TMC2209 configuration
########################################

#[tmc2209 stepper_x]
#uart_pin: PB11
#run_current: 0.800
#diag_pin: PA3
#stealthchop_threshold: 999999

#[tmc2209 stepper_y]
#uart_pin: PB9
#run_current: 0.800
#diag_pin: PB1
#stealthchop_threshold: 999999

#[tmc2209 stepper_z]
#uart_pin: PB8
#run_current: 0.800
#diag_pin: PB10
#stealthchop_threshold: 999999

#[tmc2209 extruder]
#uart_pin: PB7
#run_current: 0.600
#diag_pin:
#stealthchop_threshold: 999999

########################################
# TMC5160 configuration
########################################

#[tmc5160 stepper_x]
#cs_pin: PB11
#spi_bus: spi1
#run_current: 0.800
#diag1_pin: PA3
#stealthchop_threshold: 999999

#[tmc5160 stepper_y]
#cs_pin: PB9
#spi_bus: spi1
#run_current: 0.800
#diag1_pin: PB1
#stealthchop_threshold: 999999

#[tmc5160 stepper_z]
#cs_pin: PB8
#spi_bus: spi1
#run_current: 0.800
#diag1_pin: PB10
#stealthchop_threshold: 999999

#[tmc5160 extruder]
#cs_pin: PB7
#spi_bus: spi1
#run_current: 0.800
#diag1_pin:
#stealthchop_threshold: 999999

########################################
# EXP1 / EXP2 (display) pins
########################################

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=<NC>, EXP1_3=PA13,  EXP1_5=PA9,   EXP1_7=<NC>,  EXP1_9=<GND>,
    EXP1_2=PA4,  EXP1_4=PA10,  EXP1_6=PA8,   EXP1_8=<NC>,  EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PB14, EXP2_3=PA15,  EXP2_5=PA14,  EXP2_7=PB3,   EXP2_9=<GND>,
    EXP2_2=PB13, EXP2_4=PB12,  EXP2_6=PB15,  EXP2_8=<RST>, EXP2_10=<NC>,

# See the sample-lcd.cfg file for definitions of common LCD displays.
