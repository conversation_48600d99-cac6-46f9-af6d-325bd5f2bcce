# This file contains example pin mappings for testing with the
# "simulavr" program. To use this config, compile the firmware for an
# AVR atmega644p, enable "low-level configuration options", and enable
# "simulavr software emulation". Further details are in
# docs/Debugging.md.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
# Pins: PA5, PA4, PA1
step_pin: PA5
dir_pin: PA4
enable_pin: PA1
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB0
position_min: -0.25
position_endstop: 0
position_max: 200

[stepper_y]
# Pins: PA3, PA2
step_pin: PA3
dir_pin: PA2
enable_pin: PA1
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB1
position_min: -0.25
position_endstop: 0
position_max: 200

[stepper_z]
# Pins: PC7, PC6
step_pin: PC7
dir_pin: PC6
enable_pin: PA1
microsteps: 16
rotation_distance: 8
endstop_pin: ^PB2
position_min: 0.1
position_endstop: 0.5
position_max: 200

[extruder]
# Pins: PC3, PC2
step_pin: PC3
dir_pin: PC2
enable_pin: PA1
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.500
filament_diameter: 3.500
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA7
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
min_extrude_temp: 0
max_temp: 210

[heater_bed]
heater_pin: PB3
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA0
control: watermark
min_temp: 0
max_temp: 110

[fan]
pin: PD6

[mcu]
serial: /tmp/pseudoserial

[printer]
kinematics: cartesian
max_velocity: 500
max_accel: 3000
max_z_velocity: 250
max_z_accel: 30
