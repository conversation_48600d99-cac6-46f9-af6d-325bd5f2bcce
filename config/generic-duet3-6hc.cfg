# This file contains common pin mappings for the Duet3 6HC. To use
# this config, the firmware should be compiled for the SAME70Q20B.

# To flash the board, erase the existing firmware by jumpering the erase jumper.
# Boot the board, wait for reset to complete, remove the jumper, and then reboot the board,
# as described in <PERSON><PERSON>'s documentation:
#    https://docs.duet3d.com/en/User_manual/RepRapFirmware/Updating_firmware#all-other-duet-boards
# Then run: make flash FLASH_DEVICE=/dev/ttyACM0

# See docs/Config_Reference.md for a description of parameters.


# Pins for reference, v1.02 board:
# Driver Step Pins - 0:PC18 1:PC16 2:PC28 3:PC1  4:PC4 5:PC9
# Driver Dir Pins  - 0:PB5  1:PD10 2:PA4  3:PA22 4:PC3 5:PD14
# Driver Enable - !PA9
# Driver CS - PD17
# Thermistor Pins - TEMP0:PC15 TEMP1:PC29 TEMP2:PC30 TEMP3:PC31
# Pullup Resistor - 2200
# Vssa Sense:PC13 | Vref Sense:PC0
# Current Sense resistor for drivers - 0.05ohm
# SPI lines:{PC25} -> SPIMosi:PC27 SPIMiso:PC26 SPISCLK:PC24
# Vin Monitor:PA20
# CAN Pins - TX0:PB2 RX0:PB3 TX1:PD12 RX1:PC12
# Heaters - Out0:PA7 Out1:PA24 Out2:PA16 Out3:PA11
# Fan outputs - Out4:PA15 Out5:PC5 Out6:PA8 Out7:PC11 Out8:PC8 Out9:PA12 | Out9 is shared with VFD_Out
# Tach Pins for Fans - Out4.Tach:PC7 Out5.Tach:PD23 Out6.Tach:PA1
# GPIO_out - IO0:PD26 IO1:PD16 IO2:PD27 IO3:PA3 IO4:PE0  IO5:PD21 IO6:PA0  IO7:PD23 IO8:PE1
# GPIO_in -  IO0:PD25 IO1:PD15 IO2:PD28 IO3:PE5 IO4:PD30 IO5:PA19 IO6:PA18 IO7:PA17 IO8:PE3
# Driver Diag - 0:PD29 1:PC17 2:PD13 3:PC2 4:PD31 5:PC10

[stepper_x]
#driver 0
step_pin: PC18
dir_pin: PB5
enable_pin: !PA9
microsteps: 128
rotation_distance: 40
endstop_pin: PD25 #IO0
position_endstop: 0
position_max: 450

[tmc5160 stepper_x]
cs_pin: PD17
spi_bus: usart1
chain_position: 1
chain_length: 6
interpolate: False
run_current: 1
sense_resistor: 0.05

[stepper_y]
#driver 1
step_pin: PC16
dir_pin: PD10
enable_pin: !PA9
microsteps: 128
rotation_distance: 40
endstop_pin: PD15 #IO1
position_endstop: 0
position_max: 450

[tmc5160 stepper_y]
cs_pin: PD17
chain_position: 2
chain_length: 6
interpolate: False
run_current: 1.0
sense_resistor: 0.05

[stepper_z]
#driver2
step_pin: PC28
dir_pin: PA4
enable_pin: !PA9
microsteps: 64
rotation_distance: 8
endstop_pin: PD28 #IO2
position_endstop: 0
position_min: 0
position_max: 400

[tmc5160 stepper_z]
cs_pin: PD17
chain_position: 3
chain_length: 6
interpolate: False
run_current: 1.0
sense_resistor: 0.05

[adc_scaled vref_scaled]
vref_pin: PC0
vssa_pin: PC13

[extruder]
#driver3
step_pin: PC1
dir_pin: PA22
enable_pin: !PA9
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA24 #Out1
sensor_type: ATC Semitec 104GT-2
pullup_resistor: 2200
sensor_pin: vref_scaled:PC29 #Temp1
control: pid
pid_Kp: 30.089
pid_Ki: 2.229
pid_Kd: 101.550
min_temp: 0
max_temp: 285

[tmc5160 extruder]
cs_pin: PD17
chain_position: 4
chain_length: 6
interpolate: False
run_current: .6
sense_resistor: 0.05

[heater_fan heatbreak_fan]
pin: PC8 #Out8
heater: extruder
heater_temp: 50.0

[heater_bed]
heater_pin: PA7 #Out0
sensor_type: Generic 3950
sensor_pin: vref_scaled:PC15 #Temp0
control: pid
pullup_resistor: 2200
pid_Kp: 61.049
pid_Ki: 2.339
pid_Kd: 398.344
min_temp: 0
max_temp: 130

[heater_fan heatbreak_fan]
pin: PA15 #Out4

[fan]
pin: PC5 #Out5

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 350
max_accel: 3000
