# This file contains common pin mappings for the Duet3 1LC. To use
# this config, the firmware should be compiled for the SAMC21G18 with:
# Bootloader offset of "No Bootloader"
# Clock Reference of "25 Mhz crystal" if the board version is v1.1 or later
# Clock Reference of "12 Mhz crystal" if the board version is v1.0 or earlier
# Communication interface of "CAN bus (on PA25/PA24)"

# To flash the board use a debugger, or use a raspberry pi and follow
# the instructions at docs/Bootloaders.md for the SAMC21. You may
# supply power to the 1LC by connecting the 3.3v rail on the Pi to the
# 5v input of the SWD header on the 1LC.

# See docs/Config_Reference.md for a description of parameters.


# Pins for reference, v1.3 board:
# Driver Step Pin - PA27
# Driver Dir Pin  - PA28
# Driver Enable - !PB2
# Thermistor Pins - TEMP0:PB9 TEMP1:PA2
# Pullup Resistor - 2200
# Vssa Sense:PA6 | Vref Sense:PA7
# Current Sense resistor for drivers - 0.091ohm
# CAN Pins - CAN0_TX:PA24 CAN0_RX:PA25
# Heaters - OUT_0:PA11
# Fan outputs - OUT_1:PA10 OUT_2:PB11
# Tach Pins for Fans - OUT_1_TACHO:PA13 OUT_2_TACHO:PB10
# GPIO_out - IO0:PA12
# GPIO_in -  IO0:PA9 IO1:PA21 IO2:PA18
# Driver Diag - 0:PB3

[adc_scaled toolboard_vref_scaled]
vref_pin: toolboard:PA7
vssa_pin: toolboard:PA6

[extruder]
step_pin: toolboard:PA27
dir_pin: toolboard:PA28
enable_pin: !toolboard:PB2
rotation_distance: 23.1336867485061
gear_ratio: 50:10
microsteps: 64
full_steps_per_rotation: 200
nozzle_diameter: 0.400
filament_diameter: 1.75
heater_pin: toolboard:PA11
sensor_type: PT1000
sensor_pin: toolboard_vref_scaled:PB9
pullup_resistor: 2200
min_temp: 0
max_temp: 280
max_power: 1.0
control: pid
pwm_cycle_time: 0.01666
pid_Kp: 26.454
pid_Ki: 1.357
pid_Kd: 128.955

[tmc2209 extruder]
uart_pin: toolboard:PA20
tx_pin: toolboard:PA22
interpolate: False
run_current: 0.35
sense_resistor: 0.091

[fan]
pin: toolboard:PA10
tachometer_pin: toolboard:PA13

[heater_fan hotend_fan]
pin: toolboard:PB11
tachometer_pin: toolboard:PB10
heater: extruder
heater_temp: 50.0

[probe]
pin: toolboard:PA9
z_offset: 20

[samd_sercom sercom_i2c]
sercom: sercom1
tx_pin: toolboard:PA16
clk_pin: toolboard:PA17

[lis3dh]
i2c_mcu: toolboard
i2c_bus: sercom1

[mcu toolboard]
canbus_uuid: 4b194673554e
