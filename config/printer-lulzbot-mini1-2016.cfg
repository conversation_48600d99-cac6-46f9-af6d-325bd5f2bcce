#This file contains pin mappings for the Lulzbot Mini 1 using RAMBo Mini and SingleExtruder
#To use this config, the firmware should be compiled for the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

#-------------------------------------------------------------------------------------------------
# LULZBOT Mini 1 SingleExtruder (RAMBoMini)
# defines are copied from Marlin pins_MINIRAMBO.h
# pin conversion was found in fastio_1280.h
#-------------------------------------------------------------------------------------------------
[stepper_x]
#define X_STEP_PIN         37
step_pin: PC0
#define X_DIR_PIN          48
dir_pin: PL1
#define X_ENABLE_PIN       29
enable_pin: !PA7
# 1/100
microsteps: 16
rotation_distance: 32
#define X_MIN_PIN          12
endstop_pin: ^!PB6
position_endstop: -3
position_min: -3
position_max: 159
homing_speed: 50
homing_retract_dist: 2.0
second_homing_speed: 5

[stepper_y]
#define Y_STEP_PIN         36
step_pin: PC1
#define Y_DIR_PIN          49
dir_pin: !PL0
#define Y_ENABLE_PIN       28
enable_pin: !PA6
# 1/100
microsteps: 16
rotation_distance: 32
#define Y_MIN_PIN          11
endstop_pin: ^!PB5
position_endstop: -7
position_min: -7
position_max: 173
homing_speed: 50
homing_retract_dist: 2.0
second_homing_speed: 5

[stepper_z]
#define Z_STEP_PIN         35
step_pin: PC2
#define Z_DIR_PIN          47
dir_pin: PL2
#define Z_ENABLE_PIN       27
enable_pin: !PA5
# 1/1600
microsteps: 16
rotation_distance: 2
#define Z_MAX_PIN          23
endstop_pin: ^!PA1
# I have replaced the original nozzle with
# a hardened steel one that make the extruder
# 1-1.5mm longer, so this may be a little too low
position_endstop: 161.5
position_max: 161.5
position_min: -2.0
homing_speed: 7
homing_retract_dist: 2.0
second_homing_speed: 2

[extruder]
#define E0_STEP_PIN        34
step_pin: PC3
#define E0_DIR_PIN         43
dir_pin: !PL6
#define E0_ENABLE_PIN      26
enable_pin: !PA4
# 1/833
microsteps: 16
rotation_distance: 3.842
nozzle_diameter: 0.400
filament_diameter: 2.850
#define HEATER_0_PIN        3
heater_pin: PE5
#The Extruder uses 100K thermistor - ATC Semitec 104GT-2 (#5) and PID control
sensor_type: ATC Semitec 104GT-2
# I can't explain this from Marlin
sensor_pin: PF0
control: pid
pid_Kp: 28.79
pid_Ki: 1.91
pid_Kd: 108.51
min_temp: 0
max_temp: 280
min_extrude_temp: 160
max_extrude_only_velocity: 500
max_extrude_only_accel: 2000

[heater_bed]
#define HEATER_BED_PIN      4
heater_pin: PG5
# The Heater Bed uses Honeywell 100K 135-104LAG-J01 temp sensor (#7) and PID control
sensor_type: Honeywell 100K 135-104LAG-J01
# I can't explain this from Marlin
sensor_pin: PF2
control: pid
pid_Kp: 294.0
pid_Ki: 65.0
pid_Kd: 382.0
min_temp: 0
max_temp: 130

[fan]
#define FAN_PIN             8
pin: PH5

[heater_fan heatbreak_cooling_fan]
#define FAN1_PIN            6
pin: PH3

[output_pin stepper_xy_current]
#define MOTOR_CURRENT_PWM_XY_PIN 46
pin: PL3
pwm: True
scale: 2.0
cycle_time: .000030
hardware_pwm: True
value: 1.300

[output_pin stepper_z_current]
pin: PL4
pwm: True
scale: 2.0
cycle_time: .000030
hardware_pwm: True
value: 1.630

[output_pin stepper_e_current]
pin: PL5
pwm: True
scale: 2.0
cycle_time: .000030
hardware_pwm: True
value: 1.250

[static_digital_output stepper_config]
# Microstepping pins
#define X_MS1_PIN          40
#define X_MS2_PIN          41
#define Y_MS1_PIN          69
#define Y_MS2_PIN          39
#define Z_MS1_PIN          68
#define Z_MS2_PIN          67
#define E0_MS1_PIN         65
#define E0_MS2_PIN         66
pins:
    PG1, PG0,
    PK7, PG2,
    PK6, PK5,
    PK3, PK4

[static_digital_output yellow_led]
#define LED_PIN            13
pins: !PB7

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 2000
max_z_velocity: 7
max_z_accel: 500

[homing_override]
gcode:
  G91
  G28 Z
  G0 Z-5
  G28 X Y
  G0 X3 Y7
  G90

[probe]
#define Z_MIN_PROBE_PIN  10
pin: ^!PB4
# z_offset equals washer thickness ~= 1.377
z_offset: 1.377
speed: 5.0
samples: 2
sample_retract_dist: 1.0
samples_result: average
samples_tolerance: 0.200
samples_tolerance_retries: 2

[bed_tilt]
# Enable bed tilt measurements using the probe we defined above
# Probe points using X0 Y0 offsets @ 0.01mm/step
points: -2, -6
        156, -6
        156, 158
        -2, 158
speed: 75
horizontal_move_z: 2

[firmware_retraction]
retract_length: 2
retract_speed: 200
unretract_extra_length: 0
unretract_speed: 200

[gcode_macro G29]
# Preform the ABL by running G29 in the start GCODE script
gcode:
    BED_TILT_CALIBRATE
