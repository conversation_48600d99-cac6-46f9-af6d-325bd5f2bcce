# This file contains common pin mappings for Ultimachine Archim2
# boards. To use this config, the firmware should be compiled for the
# SAM3x8e.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC6
dir_pin: PC5
enable_pin: !PC9
microsteps: 16
rotation_distance: 40
endstop_pin: ^PD4
position_endstop: 0
position_max: 200
homing_speed: 50

[tmc2130 stepper_x]
cs_pin: PC7
run_current: .5
sense_resistor: 0.120
diag1_pin: !PA4
spi_software_sclk_pin: PD2
spi_software_mosi_pin: PD3
spi_software_miso_pin: PD1

[stepper_y]
step_pin: PC12
dir_pin: PC11
enable_pin: !PC14
microsteps: 16
rotation_distance: 40
endstop_pin: ^PD6
position_endstop: 0
position_max: 400
homing_speed: 50

[tmc2130 stepper_y]
cs_pin: PC13
run_current: .5
sense_resistor: 0.120
diag1_pin: !PC15
spi_software_sclk_pin: PD2
spi_software_mosi_pin: PD3
spi_software_miso_pin: PD1

[stepper_z]
step_pin: PC17
dir_pin: PC16
enable_pin: !PC19
microsteps: 16
rotation_distance: 8
endstop_pin: ^PA7
position_endstop: 0
position_max: 400
homing_speed: 50

[tmc2130 stepper_z]
cs_pin: PC18
run_current: .5
sense_resistor: 0.120
diag1_pin: PC4
spi_software_sclk_pin: PD2
spi_software_mosi_pin: PD3
spi_software_miso_pin: PD1

[extruder]
step_pin: PB10
dir_pin: PC10
enable_pin: !PB22
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC24
sensor_type: ATC Semitec 104GT-2
sensor_pin: PB19
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[tmc2130 extruder]
cs_pin: PC20
run_current: .5
sense_resistor: 0.120
diag1_pin: !PB23
spi_software_sclk_pin: PD2
spi_software_mosi_pin: PD3
spi_software_miso_pin: PD1

#[extruder1]
#step_pin: PB26
#dir_pin: PB24
#enable_pin: !PA11
#microsteps: 16
#rotation_distance: 33.500
#nozzle_diameter: 0.400
#filament_diameter: 1.750
#heater_pin: PC23
#sensor_type: ATC Semitec 104GT-2
#sensor_pin: PB18
#control: pid
#pid_Kp: 22.2
#pid_Ki: 1.08
#pid_Kd: 114
#min_temp: 0
#max_temp: 250

#[tmc2130 extruder1]
#cs_pin: PA10
#run_current: .5
#sense_resistor: 0.120
#diag1_pin: PD0
#spi_software_sclk_pin: PD2
#spi_software_mosi_pin: PD3
#spi_software_miso_pin: PD1

[heater_bed]
heater_pin: PC21
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PB20
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PC26

[heater_fan heatbreak_cooling_fan]
pin: PC25

[mcu]
serial: /dev/serial/by-id/usb-Klipper_sam3x8e_nnn

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100


########################################
# EXP1 / EXP2 (display) pins
########################################

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PA14, EXP1_3=PA15, EXP1_5=PA0,  EXP1_7=PA1, EXP1_9=<GND>,
    EXP1_2=PA13, EXP1_4=PA12, EXP1_6=PA16, EXP1_8=PC2, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PA25, EXP2_3=PB27, EXP2_5=PA3,  EXP2_7=PB25,  EXP2_9=<GND>,
    EXP2_2=PA27, EXP2_4=PA29, EXP2_6=PA26, EXP2_8=<RST>, EXP2_10=<3.3V>
    # Pins EXP2_1, EXP2_6, EXP2_2 are also MISO, MOSI, SCK of bus "spi0"

# See the sample-lcd.cfg file for definitions of common LCD displays.
