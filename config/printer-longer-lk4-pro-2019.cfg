# This file contains common pin mappings for Longer LK4 Pro.
# To use this config, the firmware should be compiled for the AVR
# atmega2560 (though other AVR chips are also possible).

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF0
dir_pin: !PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PE5
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_y]
step_pin: PF6
dir_pin: PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PJ1
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_z]
step_pin: PL3
dir_pin: !PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PC2
# Uncomment for BLTouch
# endstop_pin: probe:z_virtual_endstop
position_endstop: 0.5
position_max: 250

# Uncomment this section for BLTouch

# [homing_override]
# axes: z
# set_position_z: 0.0
# gcode:
#     G90
#     G0 Z5 F600
#     G28 X0 Y0
#     G0 X143 Y137 F3600
#     G28 Z0
#     G0 Z5 F600

# [bltouch]
# According this follow mapping :
# https://arduiblog.com/2020/06/22/installation-dun-bltouch-sur-lalfawise-u30-pro/
# & see "Branchement" paragraph & picture
# sensor_pin: ^PC2
# control_pin: PH4
# If you use this fang : https://www.thingiverse.com/thing:3603067
# you can use this follow values for x & y offset
# x_offset: -28
# y_offset: -26
# and test this z_offset
# z_offset: 1.56

# [bed_mesh]
# mesh_min: 16, 16
# mesh_max: 201, 200
# probe_count: 4, 3

[extruder]
step_pin: PA4
dir_pin: !PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 34.5576
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[filament_switch_sensor filament_sensor]
switch_pin: ^!PE4

[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PH6

[mcu]
serial: /dev/ttyUSB0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

# Klipper doesn't able to working display at this time
