# This file contains common pin mappings for Einsy Rambo boards. To use
# this config, the firmware should be compiled for the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

# Note: The Einsy boards sold by Prusa have defective firmware on the
# usb-to-serial chip that make the boards unusable with Klipper
# (boards sold by Ultimaker do not have this issue). See
# https://github.com/PrusaOwners/mk3-32u2-firmware for a fixed
# usb-to-serial firmware.

[stepper_x]
step_pin: PC0
dir_pin: PL0
enable_pin: !PA7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB6
#endstop_pin: tmc2130_stepper_x:virtual_endstop
position_endstop: 0
position_max: 250

[tmc2130 stepper_x]
cs_pin: PG0
run_current: .5
sense_resistor: 0.220
diag1_pin: !PK2

[stepper_y]
step_pin: PC1
dir_pin: !PL1
enable_pin: !PA6
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB5
#endstop_pin: tmc2130_stepper_y:virtual_endstop
position_endstop: 0
position_max: 210

[tmc2130 stepper_y]
cs_pin: PG2
run_current: .5
sense_resistor: 0.220
diag1_pin: !PK7

[stepper_z]
step_pin: PC2
dir_pin: PL2
enable_pin: !PA5
microsteps: 16
rotation_distance: 8
endstop_pin: ^PB4
#endstop_pin: tmc2130_stepper_z:virtual_endstop
position_endstop: 0.5
position_max: 200

[tmc2130 stepper_z]
cs_pin: PK5
run_current: .5
sense_resistor: 0.220
diag1_pin: !PK6

[extruder]
step_pin: PC3
dir_pin: PL6
enable_pin: !PA4
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PE5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF0
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[tmc2130 extruder]
cs_pin: PK4
run_current: .5
sense_resistor: 0.220
diag1_pin: !PK3

[heater_bed]
heater_pin: PG5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF2
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PH5

#[heater_fan heatbreak_cooling_fan]
#pin: PH3

[temperature_sensor board_sensor]
sensor_pin: PF6
sensor_type: TDK NTCG104LH104JT1
min_temp: 0
max_temp: 50

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[static_digital_output yellow_led]
pins: !PB7
