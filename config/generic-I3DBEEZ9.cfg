# This file contains common pin mappings for the I3DBEEZ9 V1.0.
# To use this config, the firmware should be compiled for the
# STM32F407 with a "32KiB bootloader".

# The "make flash" command does not work on the I3DBEEZ9. Instead,
# after running "make", copy the generated "out/klipper.bin" file to a
# file named "firmware.bin" on an SD card and then restart the I3DBEEZ9
# with that SD card.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PE9
dir_pin: PF1
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: PB10
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PE11
dir_pin: PE1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: PE12
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PE13
dir_pin: PC2
enable_pin: !PC0
microsteps: 16
rotation_distance: 8
endstop_pin: PG8
position_endstop: 0
position_max: 200

[extruder]
step_pin: PE14
dir_pin: PA0
enable_pin: !PC3
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB1 # Heat0
sensor_pin:  PF4 # T1 Header
sensor_type: EPCOS 100K B57560G104F
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

#[extruder1]
#step_pin: PD15
#dir_pin: PE7
#enable_pin: !PA3
#heater_pin: PD14 # Heat1
#sensor_pin: PF5 # T2
#...

#[extruder2]
#step_pin: PD13
#dir_pin: PG9
#enable_pin: !PF0
#heater_pin: PB0 # Heat2
#sensor_pin: PF6 # T3
#...

#[stepper_z1]
#step_pin: PE4
#dir_pin: PE3
#enable_pin: !PC13
#microsteps: 16
#rotation_distance: 8
#endstop_pin: PD0
#position_endstop: 0.5
#position_max: 200

[heater_bed]
heater_pin: PD12
sensor_pin: PF3 # T0
sensor_type: ATC Semitec 104GT-2
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PC8

[heater_fan fan1]
pin: PE5

#[heater_fan fan2]
#pin: PE6

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100


########################################
# TMC2208 configuration
########################################

#[tmc2208 stepper_x]
#uart_pin: PA15
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 stepper_y]
#uart_pin: PB8
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 stepper_z]
#uart_pin: PB9
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc2208 extruder]
#uart_pin: PB3
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 extruder1]
#uart_pin: PG15
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 extruder2]
#uart_pin: PG12
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 stepper_z1]
#uart_pin: PE2
#run_current: 0.650
#stealthchop_threshold: 999999

########################################
# TMC2130 configuration
########################################

#[tmc2130 stepper_x]
#cs_pin: PA15
#spi_bus: spi3a
##diag1_pin: PB10
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_y]
#cs_pin: PB8
#spi_bus: spi3a
##diag1_pin: PE12
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_z]
#cs_pin: PB9
#spi_bus: spi3a
##diag1_pin: PG8
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc2130 extruder]
#cs_pin: PB3
#spi_bus: spi3a
##diag1_pin: PE15
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder1]
#cs_pin: PG15
#spi_bus: spi3a
##diag1_pin: PE10
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder2]
#cs_pin: PG12
#spi_bus: spi3a
##diag1_pin: PG5
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_z1]
#cs_pin: PE2
#spi_bus: spi3a
##diag1_pin: PD0
#run_current: 0.650
#stealthchop_threshold: 999999


########################################
# EXP1 / EXP2 (display) pins
########################################

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PG4, EXP1_3=PD11, EXP1_5=PG2, EXP1_7=PG6, EXP1_9=<GND>,
    EXP1_2=PA8, EXP1_4=PD10, EXP1_6=PG3, EXP1_8=PG7, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PB14, EXP2_3=PG10, EXP2_5=PF11, EXP2_7=PF12,  EXP2_9=<GND>,
    EXP2_2=PB13, EXP2_4=PB12, EXP2_6=PB15, EXP2_8=<RST>, EXP2_10=PF13
    # Pins EXP2_1, EXP2_6, EXP2_2 are also MISO, MOSI, SCK of bus "spi2"

# See the sample-lcd.cfg file for definitions of common LCD displays.
