# This file contains common pin mappings for MKS RUMBA32 boards.  To use
# this config, the firmware should be compiled for the STMicroelectronics STM32,
# Processor model STM32F446, Clock Reference 12 MHz crystal.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PA0
dir_pin: PC15
enable_pin: !PC11
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB12
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PE5
dir_pin: !PE6
enable_pin: !PE3
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB15
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PE1
dir_pin: PE2
enable_pin: !PB7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PD9
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PB5
dir_pin: PB6
enable_pin: !PC12
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC6
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC4
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

#[extruder1]
#step_pin: PD6
#dir_pin: PD7
#enable_pin: !PD4
#heater_pin: PC7
#sensor_pin: PC3
#...

#[extruder2]
#step_pin: PD2
#dir_pin: PD3
#enable_pin: !PD0
#heater_pin: PC8
#sensor_pin: PC2
#...

[heater_bed]
heater_pin: PA1
sensor_type: Generic 3950
sensor_pin: PC0
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PC9

#[heater_fan fan1]
#pin: PA8

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100


########################################
# EXP1 / EXP2 (display) pins
########################################

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PE8, EXP1_3=PE9, EXP1_5=PE12, EXP1_7=PE14, EXP1_9=<GND>,
    EXP1_2=PE7, EXP1_4=PE10, EXP1_6=PE13, EXP1_8=PE15, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PA6, EXP2_3=PB2, EXP2_5=PB1, EXP2_7=PB0,  EXP2_9=<GND>,
    EXP2_2=PA5, EXP2_4=PA2, EXP2_6=PA7, EXP2_8=<RST>, EXP2_10=PC5

# See the sample-lcd.cfg file for definitions of common LCD displays.


########################################
# TMC2209 configuration
########################################

#[tmc2209 stepper_x]
#uart_pin: PC14
##tx_pin: PA3
#run_current: 0.800
#diag_pin:

#[tmc2209 stepper_y]
#uart_pin: PE4
##tx_pin: PA4
#run_current: 0.800
#diag_pin:

#[tmc2209 stepper_z]
#uart_pin: PE0
##tx_pin: PD13
#run_current: 0.800
#diag_pin:

#[tmc2209 extruder]
#uart_pin: PC13
##tx_pin: PD14
#run_current: 0.600
#diag_pin:
