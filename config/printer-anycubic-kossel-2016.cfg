# This file contains a configuration for the Anycubic Kossel delta
# printer from 2016.

# The Anycubic delta printers use the TriGorilla board which is an
# AVR ATmega2560 Arduino + RAMPS compatible board.
# To use this config, the firmware should be compiled for the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

[stepper_a]
step_pin: PF0
dir_pin: !PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PE4
homing_speed: 60
# The next parameter needs to be adjusted for
# your printer. You may want to start with 280
# and measure the distance from nozzle to bed.
# This value then needs to be added.
position_endstop: 273.0
arm_length: 229.4

[stepper_b]
step_pin: PF6
dir_pin: !PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: ^PJ0

[stepper_c]
step_pin: PL3
dir_pin: !PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 40
endstop_pin: ^PD2

[extruder]
step_pin: PA4
dir_pin: !PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 35.165
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5
control: pid
pid_Kp: 25.349
pid_Ki: 1.216
pid_Kd: 132.130
min_extrude_temp: 150
min_temp: 0
max_temp: 275

#[heater_bed]
#heater_pin: PH5
#sensor_type: EPCOS 100K B57560G104F
#sensor_pin: PK6
#control: watermark
#min_temp: 0
#max_temp: 130

[fan]
pin: PH6
kick_start_time: 0.200

[heater_fan extruder_cooler_fan]
pin: PL5

[mcu]
serial: /dev/serial/by-id/usb-Silicon_Labs_CP2102_USB_to_UART_Bridge_Controller_0001-if00-port0

[printer]
kinematics: delta
max_velocity: 500
max_accel: 3000
max_z_velocity: 200
delta_radius: 99.8
# if you want to DELTA_CALIBRATE you may need that
#minimum_z_position: -5

[idle_timeout]
timeout: 360

#[delta_calibrate]
#radius: 80
#manual_probe:
#   If true, then DELTA_CALIBRATE will perform manual probing. If
#   false, then a PROBE command will be run at each probe
#   point. Manual probing is accomplished by manually jogging the Z
#   position of the print head at each probe point and then issuing a
#   NEXT extended g-code command to record the position at that
#   point. The default is false if a [probe] config section is present
#   and true otherwise.

# "RepRapDiscount 2004 Smart Controller" type displays
[display]
lcd_type: hd44780
rs_pin: PH1
e_pin: PH0
d4_pin: PA1
d5_pin: PA3
d6_pin: PA5
d7_pin: PA7
encoder_pins: ^PC6, ^PC4
click_pin: ^!PC2
kill_pin: ^!PG0
