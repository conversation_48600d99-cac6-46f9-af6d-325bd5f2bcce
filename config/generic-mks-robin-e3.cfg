# This file contains common pin mappings for MKS Robin E3 boards. To
# use this config, the firmware should be compiled for the STM32F103.
# When running "make menuconfig", enable "extra low-level
# configuration setup", select the 20KiB bootloader, and serial (on
# USART1 PA10/PA9) communication.

# Note that the "make flash" command does not work with MKS Robin
# boards. After running "make", run the following command:
#   ./scripts/update_mks_robin.py out/klipper.bin out/Robin_e3.bin
# Copy the file out/Robin_e3.bin to an SD card and then restart the
# printer with that SD card.

# MKS Robin E3 has onboard TMC2209. This config can also be used for
# MKS Robin E3D if equipped with TMC2209 stepper drivers.

# Please note pin name change for stepper Z for v1.1 of the boards.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC0
dir_pin: PB2
enable_pin: !PC13
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA12
position_endstop: 0
position_max: 165
homing_speed: 50

[stepper_y]
step_pin: PC2
dir_pin: PB9
enable_pin: !PB12
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA11
position_endstop: 0
position_max: 165
homing_speed: 50

[stepper_z]
step_pin: PB7 # PC14 if using board v1.1
dir_pin: !PB6 # !PC15 if using board v1.1
enable_pin: !PB8
microsteps: 16
rotation_distance: 8
endstop_pin: ^PC6
position_endstop: 0
position_max: 200

[extruder]
step_pin: PB4
dir_pin: PB3
enable_pin: !PB5
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC9
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA0
control: pid
pid_Kp: 21.527
pid_Ki: 1.063
pid_Kd: 108.982
min_temp: 0
max_temp: 250

[tmc2209 stepper_x]
uart_pin: PC7
run_current: 0.800
stealthchop_threshold: 999999

[tmc2209 stepper_y]
uart_pin: PD2
run_current: 0.800
stealthchop_threshold: 999999

[tmc2209 stepper_z]
uart_pin: PC12
run_current: 0.650
stealthchop_threshold: 999999

[tmc2209 extruder]
uart_pin: PC11
run_current: 0.800
stealthchop_threshold: 999999

[fan]
pin: PA8

#[heater_bed]
#heater_pin: PC8
#sensor_pin: PA1
#...

#[bltouch]
#sensor_pin: ^PC6
#control_pin: PA3
#...

#[filament_switch_sensor my_sensor]
#switch_pin: PB10
#...

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB2.0-Serial-if00-port0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 1500
max_z_velocity: 5
max_z_accel: 100

# EXP1 / EXP2 (display) pins
[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PC3, EXP1_3=PA4, EXP1_5=PA6, EXP1_7=PC4, EXP1_9=<GND>,
    EXP1_2=PC1, EXP1_4=PA5, EXP1_6=PA7, EXP1_8=PC5, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PB14, EXP2_3=PB11, EXP2_5=PB0,  EXP2_7=PC10,  EXP2_9=<GND>,
    EXP2_2=PB13, EXP2_4=PA15, EXP2_6=PB15, EXP2_8=<RST>, EXP2_10=<NC>,
    # CR_EXP3 header - additional header dedicated to Creality Ender 3/5 stock screens
    CR_EXP3_10=<5V>, CR_EXP3_8=PA4, CR_EXP3_6=PA6, CR_EXP3_4=<RST>, CR_EXP3_2=PC3,
    CR_EXP3_9=<GND>, CR_EXP3_7=PA5, CR_EXP3_5=PB0, CR_EXP3_3=PB11,  CR_EXP3_1=PC1

# If you're using this board to replace stock Creality board, connect display to EXP3
# and use this display config
#[display]
#lcd_type: st7920
#sid_pin: CR_EXP3_8
#cs_pin: CR_EXP3_7
#sclk_pin: CR_EXP3_6
#encoder_pins: ^CR_EXP3_5, ^CR_EXP3_3
#click_pin: ^!CR_EXP3_2
#
#[output_pin beeper]
## pin: EXP1_9
#pin: CR_EXP3_1

# See the sample-lcd.cfg file for definitions of common LCD displays.
