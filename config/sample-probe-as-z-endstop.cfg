# This file provides example config file settings for use on a printer
# that uses a Z probe instead of a traditional Z endstop switch. This
# file is just a "snippet" of config sections - it must be added to a
# config file containing the configuration of the rest of the printer.

# Be sure to review and update this config with the appropriate pins
# and coordinates for your printer.

# See docs/Config_Reference.md for a description of parameters.

# Define a probe
[probe]
pin: ar30
z_offset: 2.345

# Example settings to add to stepper_z section
[stepper_z]
endstop_pin: probe:z_virtual_endstop
position_min: -2 # The Z carriage may need to travel below the Z=0
                 # homing point if the bed has a significant tilt.

# The safe_z_home section modifies the default G28 behavior
[safe_z_home]
home_xy_position: 100, 100
speed: 50
z_hop: 15
z_hop_speed: 5

# Example bed_tilt config section
[bed_tilt]
points:
    100, 100
    10, 10
    10, 100
    10, 190
    100, 10
    100, 190
    190, 10
    190, 100
    190, 190

# Example bed_mesh config section
[bed_mesh]
mesh_min: 20, 20
mesh_max: 200, 200
probe_count: 4, 4
