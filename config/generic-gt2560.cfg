# This file contains common pin mappings for the Geeetech GT2560
# board. GT2560 board uses a firmware compiled for the AVR
# atmega2560.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PA3
dir_pin: PA1
enable_pin: !PA5
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA0
position_endstop: 0
position_max: 200
homing_speed: 30

[stepper_y]
step_pin: PC6
dir_pin: PC4
enable_pin: !PA7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA4
position_endstop: 0
position_max: 200
homing_speed: 30

[stepper_z]
step_pin: PC0
dir_pin: !PG2
enable_pin: !PC2
microsteps: 16
rotation_distance: 8
endstop_pin: ^PC7
position_endstop: 0
position_max: 200
position_min: 0.0

[extruder]
step_pin: PL6
dir_pin: PL4
enable_pin: !PG0
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.4
filament_diameter: 1.750
heater_pin: PE4
sensor_type: ATC Semitec 104GT-2
sensor_pin: PK0
min_temp: 0
max_temp: 250
control: pid
pid_kp: 29.800
pid_ki: 1.774
pid_kd: 125.159

[heater_bed]
heater_pin: PG5
sensor_type: ATC Semitec 104GT-2
sensor_pin: PK2
min_temp: 0
max_temp: 120
control: pid
pid_kp: 63.041
pid_ki: 2.898
pid_kd: 342.787

[fan]
pin: PH4

[mcu]
serial: /dev/ttyUSB0

[printer]
kinematics: cartesian
max_velocity: 200
max_accel: 1500
max_z_velocity: 20
max_z_accel: 500

[display]
lcd_type: hd44780
rs_pin: PD1
e_pin: PH0
d4_pin: PH1
d5_pin: PD0
d6_pin: PE3
d7_pin: PH3
encoder_pins: ^PL7, ^PG1
click_pin: ^!PD2
