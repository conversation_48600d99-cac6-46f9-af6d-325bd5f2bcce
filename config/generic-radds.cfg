# This file contains common pin mappings for RADDS (v1.5) boards.  To
# use this config, the firmware should be compiled for the Arduino
# Due.

# See docs/Config_Reference.md for a description of parameters.

# Temp sensor pins: PA16..PA6
# Mosfet Pins: PC23 (Heatbed), PC22, PC21, PD7, PD8, PB27

[stepper_x]
step_pin: PA15
dir_pin: PA14
enable_pin: PD1
microsteps: 16
rotation_distance: 40
endstop_pin: ^PD3
#endstop_pin: ^PC2
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PA12
dir_pin: !PA13
enable_pin: PB26
microsteps: 16
rotation_distance: 40
endstop_pin: ^PD9
#endstop_pin: ^PC4
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PB25
dir_pin: PC28
enable_pin: PD5
microsteps: 16
rotation_distance: 8
endstop_pin: ^PD10
#endstop_pin: ^PC6
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PA2
dir_pin: PA3
enable_pin: PB17
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB27
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA16
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

#[extruder1]
#step_pin:           PB19
#dir_pin:            PB18
#enable_pin:         PB20

#[extruder2]
#step_pin:           PC12
#dir_pin:            PB14
#enable_pin:         PC14

[heater_bed]
heater_pin: PC23
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA24
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PC21

#[heater_fan heatbreak_cooling_fan]
#pin: PC22

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

# "RepRapDiscount 2004 Smart Controller" type displays
#[display]
#lcd_type: hd44780
#rs_pin: PA19
#e_pin: PA20
#d4_pin: PC19
#d5_pin: PC18
#d6_pin: PC17
#d7_pin: PC16
#encoder_pins: ^PB21, ^PC13
#click_pin: ^!PC15

# "RepRapDiscount 128x64 Full Graphic Smart Controller" type displays
#[display]
#lcd_type: st7920
#cs_pin: PA19
#sclk_pin: PC19
#sid_pin: PA20
