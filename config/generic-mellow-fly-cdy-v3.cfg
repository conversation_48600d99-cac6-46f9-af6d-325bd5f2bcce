# This file contains common pin mappings for the Mellow Fly-CDY-v3.
# To use this config, the firmware should be compiled for the
# STM32F407 with a "32KiB bootloader".

# The "make flash" command does not work on the Fly-CDY-v3. Instead,
# after running "make", copy the generated "out/klipper.bin" file to a
# file named "firmware.bin" on an SD card and then restart the Fly-CDY-v3
# with that SD card.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PE5
dir_pin: PC0
enable_pin: !PC1
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC7        # X-MAX PC6
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PE4
dir_pin: !PC13
enable_pin: !PC14
microsteps: 16
rotation_distance: 40
endstop_pin: ^PD11       # Y-MAX PD10
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PE3
dir_pin: PB7
enable_pin: !PB8
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB10       # Z-MAX PB11
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PE2
dir_pin: PD6
enable_pin: !PD7
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PD12
sensor_pin: PA3
sensor_type: EPCOS 100K B57560G104F
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

#[extruder1]
#step_pin: PE1
#dir_pin: !PD3
#enable_pin: !PD4
#heater_pin: PD13
#sensor_pin: PC4

#[extruder2]
#step_pin: PE0
#dir_pin: !PA15
#enable_pin: !PD0
#heater_pin: PD14
#sensor_pin: PC5

[heater_bed]
heater_pin: PB0
sensor_type: Generic 3950
sensor_pin: PB1
control: watermark
min_temp: 0
max_temp: 130

#FAN0
[fan]
pin: PA0

#FAN1
#[heater_fan fan1]
#pin: PA1

#FAN2
#[heater_fan fan2]
#pin: PA2


[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100


########################################
# TMC2209 configuration
########################################

#[tmc2209 stepper_x]
#uart_pin: PC15
#run_current: 0.800
#diag_pin: PC7
#stealthchop_threshold: 999999

#[tmc2209 stepper_y]
#uart_pin: PA8
#run_current: 0.800
#diag_pin: PC6
#stealthchop_threshold: 999999

#[tmc2209 stepper_z]
#uart_pin: PB6
#run_current: 0.800
#diag_pin: PD11
#stealthchop_threshold: 999999

#[tmc2209 extruder]
#uart_pin: PD5
#run_current: 0.600
#diag_pin: PD10
#stealthchop_threshold: 999999

#[tmc2209 extruder1]
#uart_pin: PD1
#run_current: 0.600
#diag_pin: PB10
#stealthchop_threshold: 999999

#[tmc2209 extruder2]
#uart_pin: PE9
#run_current: 0.600
#diag_pin: PB11
#stealthchop_threshold: 999999

########################################
# TMC5160 configuration
########################################

#[tmc5160 stepper_x]
#cs_pin: PC15
#spi_bus: spi3
#run_current: 0.800
#diag1_pin: PC7
#stealthchop_threshold: 999999

#[tmc5160 stepper_y]
#cs_pin: PA8
#spi_bus: spi3
#run_current: 0.800
#diag1_pin: PC6
#stealthchop_threshold: 999999

#[tmc5160 stepper_z]
#cs_pin: PB6
#spi_bus: spi3
#run_current: 0.800
#diag1_pin: PD11
#stealthchop_threshold: 999999

#[tmc5160 extruder]
#cs_pin: PD5
#spi_bus: spi3
#run_current: 0.800
#diag1_pin: PD10
#stealthchop_threshold: 999999

#[tmc5160 extruder1]
#cs_pin: PD1
#spi_bus: spi3
#run_current: 0.800
#diag1_pin: PB10
#stealthchop_threshold: 999999

#[tmc5160 extruder2]
#cs_pin: PE9
#spi_bus: spi3
#run_current: 0.800
#diag1_pin: PB11
#stealthchop_threshold: 999999

########################################
# Accelerometer (ADXL345) pins
########################################

## SCK=PA5, MISO=PA6, MOSI=PA7
#[adxl345]
#cs_pin: PE7
#spi_bus: spi1

########################################
# EXP1 / EXP2 (display) pins
########################################

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=<NC>, EXP1_3=PB2,  EXP1_5=PE15, EXP1_7=PA13,  EXP1_9=<GND>,
    EXP1_2=PA9,  EXP1_4=PA10, EXP1_6=PE14, EXP1_8=PA14,  EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PA6,  EXP2_3=PD8,  EXP2_5=PD9,  EXP2_7=PE13,  EXP2_9=<GND>,
    EXP2_2=PA5,  EXP2_4=PA4,  EXP2_6=PA7,  EXP2_8=<RST>, EXP2_10=<NC>,

# See the sample-lcd.cfg file for definitions of common LCD displays.
