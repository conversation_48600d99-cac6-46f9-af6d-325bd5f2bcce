# This file contains common pin mappings for the TH3D EZBoard v2.
# To use this config, check "Enable extra low-level configuration options"
# and compile the firmware for the STM32F405 with 12mhz Crystal,
# 48KiB Bootloader, and USB communication.

# After the firmware is compiled, execute the following command
# arm-none-eabi-objcopy -O srec out/klipper.elf out/firmware.bin

# The "make flash" command does not work on this board. Instead,
# after running "make", copy the generated "out/firmware.bin" file to
# an SD card and then restart the board with that SD card.

# See docs/Config_Reference.md for a description of parameters.

[mcu]
serial: /dev/serial/by-id/usb-Klipper_stm32f407xx_0000000000000000-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[stepper_x]
step_pin: PB3
dir_pin: PD2
enable_pin: !PB5
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PC1
position_endstop: 0
position_max: 235
homing_speed: 50

[tmc2209 stepper_x]
uart_pin: PC11
tx_pin: PC10
run_current: 0.600
uart_address: 0

[stepper_y]
step_pin: PB8
dir_pin: PC13
enable_pin: !PC12
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PC2
position_endstop: 0
position_max: 235
homing_speed: 50

[tmc2209 stepper_y]
uart_pin: PC11
tx_pin: PC10
run_current: 0.600
uart_address: 1

[stepper_z]
step_pin: PA3
dir_pin: PB1
enable_pin: !PC14
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PC3
position_endstop: 0.5
position_max: 250

[tmc2209 stepper_z]
uart_pin: PC11
tx_pin: PC10
run_current: 0.700
uart_address: 2

[extruder]
step_pin: PA15
dir_pin: PB11
enable_pin: !PB2
microsteps: 16
rotation_distance: 34.406
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC8
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA1
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[tmc2209 extruder]
uart_pin: PC11
tx_pin: PC10
run_current: 0.800
stealthchop_threshold: 999999
uart_address: 3

[heater_bed]
heater_pin: PC9
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA0
control: pid
pid_Kp: 54.027
pid_Ki: 0.770
pid_Kd: 948.182
min_temp: 0
max_temp: 130

[fan]
pin: PC6

#[bltouch]
#sensor_pin: ^PC3
#control_pin: PA2

#[filament_switch_sensor my_sensor]
#switch_pin: PC0

########################################
# EXP1 / EXP2 (display) pins
########################################

[board_pins]
aliases:
# EXP1 header
    EXP1_1=PA14, EXP1_3=PC4, EXP1_5=PC5, EXP1_7=PB12, EXP1_9=<GND>,
    EXP1_2=PB0, EXP1_4=<RST>, EXP1_6=PB13, EXP1_8=PB15, EXP1_10=<5V>

# See the sample-lcd.cfg file for definitions of common LCD displays.
