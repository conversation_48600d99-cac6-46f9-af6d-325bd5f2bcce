# This file contains common pin mappings for Anet E10 printer from
# 2018.  To use this config, the firmware should be compiled for the
# AVR atmega1284p.

# Note that the "make flash" command does not work with Anet boards -
# the boards are typically flashed with this command:
#  avrdude -p atmega1284p -c arduino -b 57600 -P /dev/ttyUSB0 -U out/klipper.elf.hex

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PD7
dir_pin: PC5
enable_pin: !PD6
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PC2
position_endstop: -3
position_max: 220
position_min: -3
homing_speed: 50

[stepper_y]
step_pin: PC6
dir_pin: !PC7
enable_pin: !PD6
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PC3
position_endstop: -22
position_min: -22
position_max: 270
homing_speed: 50

[stepper_z]
step_pin: PB3
dir_pin: !PB2
enable_pin: !PA5
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PC4
position_endstop: 0.5
position_max: 300
homing_speed: 20

[extruder]
step_pin: PB1
dir_pin: !PB0
enable_pin: !PD6
microsteps: 16
rotation_distance: 32.000
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PD5
sensor_type: ATC Semitec 104GT-2
sensor_pin: PA7
control: pid
pid_Kp: 27.0
pid_Ki: 1.3
pid_Kd: 136.09
min_temp: 10
max_temp: 250

[heater_bed]
heater_pin: PD4
sensor_type: ATC Semitec 104GT-2
sensor_pin: PA6
control: pid
pid_Kp: 72.8
pid_Ki: 1.2
pid_Kd: 1100
min_temp: 10
max_temp: 130

[fan]
pin: PB4

[mcu]
serial: /dev/ttyUSB0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 1000
max_z_velocity: 20
max_z_accel: 1000

[display]
lcd_type: st7920
cs_pin: PA4
sclk_pin: PA1
sid_pin:PA3
