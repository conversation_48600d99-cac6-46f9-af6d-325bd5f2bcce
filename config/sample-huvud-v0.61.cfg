# This file contains common pin mappings for the Huvud V0.61 by <PERSON>us.
# https://github.com/bondus/Klipperhuvudboard
# To use this config, copy the contents into your main config file.

# The huvud is not capable of running a printer on it's own. It
# needs to be paired with another board that will control other
# aspects of the printer.

# The firmware should be compiled for the STM32F103 with a "2KiB
# bootloader" and a "8MHz crystal" clock reference.
# Select CAN bus (on PB8/PB9) or USB under communication interface.
# Flash by running make flash FLASH_DEVICE=1209:beba

# See docs/Config_Reference.md for a description of parameters.

[mcu huvud]
canbus_uuid: ac20f0bbda05
# Identify your canbus_uuid by running:
# ~/klippy-env/bin/python ~/klipper/scripts/canbus_query.py can0

[extruder]
step_pin: huvud:PB3
dir_pin: huvud:PB4
enable_pin: !huvud:PB5
rotation_distance: 22.52453125
nozzle_diameter: 0.400
filament_diameter: 1.75
heater_pin: huvud:PA6
sensor_type: NTC 100K MGB18-104F39050L32
sensor_pin: huvud:PA0
pullup_resistor: 2200
min_temp: 0
max_temp: 300
control: pid
pid_kp: 26.213
pid_ki: 1.304
pid_kd: 131.721

[tmc2209 extruder]
uart_pin: huvud:PA10
tx_pin: huvud:PA9
run_current: 0.35

[probe]
pin: huvud:PB12
z_offset: 0

[fan]
pin: huvud:PA8

[heater_fan extruder_fan]
pin: huvud:PA7

[adxl345]
cs_pin: PB1

[led huvud_led]
blue_pin: huvud:PC13
