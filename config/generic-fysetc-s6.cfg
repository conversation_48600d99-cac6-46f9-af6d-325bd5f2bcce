# This file contains common pin mappings for the Fysetc S6 board.  To use this
# config, the firmware should be compiled for the STM32F446 with a "64KiB
# bootloader".  When calling "menuconfig", enable "extra low-level configuration
# setup" and select the "12MHz crystal" as clock reference.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PE11
dir_pin: PE10
enable_pin: !PE12
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB14  # PA1 for X-max
position_endstop: 0
position_max: 200

[stepper_y]
step_pin: PD8
dir_pin: PB12
enable_pin: !PD9
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB13  # PA2 for Y-max
position_endstop: 0
position_max: 200

[stepper_z]
step_pin: PD14
dir_pin: PD13
enable_pin: !PD15
microsteps: 16
rotation_distance: 8
endstop_pin: ^PA0  # PA3 for Z-max (and servo)
position_endstop: 0
position_max: 400

[extruder]
step_pin: PD5
dir_pin: !PD6
enable_pin: !PD4
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB3
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC0
control: pid
pid_Kp: 22
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 260

#[extruder1]
#step_pin: PE6
#dir_pin: !PC13
#enable_pin: !PE5
#heater_pin: PB4
#sensor_pin: PC1

#[extruder2]
#step_pin: PE2
#dir_pin: !PE4
#enable_pin: !PE3
#heater_pin: PB15
#sensor_pin: PC2

[heater_bed]
heater_pin: PC8
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC3
control: watermark
min_temp: 0
max_temp: 130

#fan for printed model FAN0
[fan]
pin: PB0

#fan for hotend FAN1
#[heater_fan heatbreak_cooling_fan]
#pin: PB1
#shutdown_speed: 1

#fan for control board FAN2
#[heater_fan my_control_fan]
#pin: PB2

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

########################################
# TMC UART configuration
########################################

# For TMC UART
#   1) Remove all jumpers below the stepper drivers.
#   2) Place a jumper on the "PDN-EN" two-pin header.

# For TMC Sensorless homing / DIAG1
#   1) Place a jumper on the two pin header near the endstop.

#[tmc2208 stepper_x]
#uart_pin: PE8
#tx_pin: PE9
#run_current: 0.8
#stealthchop_threshold: 999999

#[tmc2208 stepper_y]
#uart_pin: PE13
#tx_pin: PE14
#run_current: 0.8
#stealthchop_threshold: 999999

#[tmc2208 stepper_z]
#uart_pin: PD12
#tx_pin: PD11
#run_current: 0.8
#stealthchop_threshold: 999999

#[tmc2208 extruder]
#uart_pin: PA15
#tx_pin: PD3
#run_current: 0.8
#stealthchop_threshold: 999999

#[tmc2208 extruder1]
#uart_pin: PC5
#tx_pin: PC4
#run_current: 0.8
#stealthchop_threshold: 999999

#[tmc2208 extruder2]
#uart_pin: PE0
#tx_pin: PE1
#run_current: 0.8
#stealthchop_threshold: 999999

########################################
# TMC SPI configuration
########################################

# For TMC SPI
#   1) Remove four jumpers below the stepper drivers, connecting the outermost and middle pins of each row.

# For TMC Sensorless homing / DIAG1
#   1) Place a jumper on the two pin header near the endstop.

# SPI pins:
# SCK   PA5
# MISO  PA6
# MOSI  PA7

#[tmc2130 stepper_x]
#spi_bus: spi1
#cs_pin: PE7
#diag1_pin: PB14
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_y]
#spi_bus: spi1
#cs_pin: PE15
#diag1_pin: PB13
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_z]
#spi_bus: spi1
#cs_pin: PD10
#diag1_pin: PA0
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder]
#spi_bus: spi1
#cs_pin: PD7
#diag1_pin: PA3
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder1]
#spi_bus: spi1
#cs_pin: PC14
#diag1_pin: PA2
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder2]
#spi_bus: spi1
#cs_pin: PC15
#diag1_pin: PA1
#run_current: 0.800
#stealthchop_threshold: 999999

########################################
# EXP1 / EXP2 (display) pins
########################################

# These must be turned 180° when compared to the default RAMPS layout.
# The aliases below are 180° turned from what Fysetc considers pin 1,
# but visually correspond to the plugs on the board.

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PC9, EXP1_2=PA8,
    EXP1_3=PC11, EXP1_4=PD2,
    EXP1_5=PC10, EXP1_6=PC12,    # Slot in the socket on this side
    EXP1_7=PD0, EXP1_8=PD1,
    EXP1_9=<GND>, EXP1_10=<5V>,

    # EXP2 header
    EXP2_1=PA6, EXP2_2=PA5,
    EXP2_3=PC6, EXP2_4=PA4,
    EXP2_5=PC7, EXP2_6=PA7,      # Slot in the socket on this side
    EXP2_7=PB10, EXP2_8=<RST>,
    EXP2_9=<GND>, EXP2_10=<5V>

# See the sample-lcd.cfg file for definitions of common LCD displays.

########################################
# RGB header
########################################

#[output_pin blue]
#pin: PB7

#[output_pin red]
#pin: PB6

#[output_pin green]
#pin: PB5

########################################
# Servo
########################################

#[servo my_servo1]
#pin: PA3  # shared with ZMAX

########################################
# AUX-3 / SPI header
########################################

# <CD>, <MOSI>, SS, <RESET>
# <5V>  , MISO  , SCK, <GND>
