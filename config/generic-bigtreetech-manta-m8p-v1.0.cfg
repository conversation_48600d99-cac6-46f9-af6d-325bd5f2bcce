# This file contains common pin mappings for the BIGTREETECH Manta M8P
# To use this config, the firmware should be compiled for the
# STM32G0B1 with a "8KiB bootloader" and USB communication.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PE2
dir_pin: PB4
enable_pin: !PC11
microsteps: 16
rotation_distance: 40
endstop_pin: ^PF3
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_y]
step_pin: PF12
dir_pin: PF11
enable_pin: !PB3
microsteps: 16
rotation_distance: 40
endstop_pin: ^PF4
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_z]
step_pin: PD7
dir_pin: !PD6
enable_pin: !PF10
microsteps: 16
rotation_distance: 8
endstop_pin: ^PF5
position_endstop: 0
position_max: 270

# Motor4
# The M8P only has 4 heater outputs which leaves an extra stepper
# This can be used for a second Z stepper, dual_carriage, extruder co-stepper,
# or other accessory such as an MMU
#[stepper_]
#step_pin: PD3
#dir_pin: PD2
#enable_pin: !PD5
#endstop_pin: PC0
#...

[extruder]
step_pin: PC9
dir_pin: PC8
enable_pin: !PD1
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.4
filament_diameter: 1.75
heater_pin: PE3 # HE0
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA1 # T0
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

#[filament_switch_sensor material_0]
#switch_pin: PC1

# Motor6
#[extruder1]
#step_pin: PA10
#dir_pin: PD15
#enable_pin: !PA15
#heater_pin: PB5 # HE1
#sensor_pin: PA2 # T1
#...

#[filament_switch_sensor material_1]
#switch_pin: PC2

# Motor7
#[extruder2]
#step_pin: PD12
#dir_pin: PD11
#enable_pin: !PD14
#heater_pin: PB6 # HE2
#sensor_pin: PA3 # T2
#...

# Motor8
#[extruder3]
#step_pin: PD10
#dir_pin: PD8
#enable_pin: !PD9
#heater_pin: PE1 # HE3
#sensor_pin: PA4 # T3
#...

[heater_bed]
heater_pin: PB7
sensor_type: Generic 3950
sensor_pin: PA0 # TB
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PE6

#[heater_fan fan1]
#pin: PE0

#[heater_fan fan2]
#pin: PC12

#[heater_fan fan3]
#pin: PE5

#[heater_fan fan4]
#pin: PE4
#tachometer_pin: PC13

#[heater_fan fan5]
#pin: PB8
#tachometer_pin: PC14

#[heater_fan fan6]
#pin: PB9
#tachometer_pin: PC15

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

########################################
# TMC2209 configuration
########################################

#[tmc2209 stepper_x]
#uart_pin: PC10
##diag_pin: PF3
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2209 stepper_y]
#uart_pin: PF13
##diag_pin: PF4
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2209 stepper_z]
#uart_pin: PF9
##diag_pin: PF5
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc2209 stepper_]
#uart_pin: PD4
##diag_pin: PC0
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc2209 extruder]
#uart_pin: PD0
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2209 extruder1]
#uart_pin: PF8
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2209 extruder2]
#uart_pin: PD13
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2209 extruder3]
#uart_pin: PC7
#run_current: 0.800
#stealthchop_threshold: 999999

########################################
# TMC2130 configuration
########################################

#[tmc2130 stepper_x]
#cs_pin: PC10
#spi_bus: spi1
##diag1_pin: PF3
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_y]
#cs_pin: PF13
#spi_bus: spi1
##diag1_pin: PF4
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_z]
#cs_pin: PF9
#spi_bus: spi1
##diag1_pin: PF5
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc2130 stepper_]
#cs_pin: PD4
#spi_bus: spi1
##diag1_pin: PC0
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder]
#cs_pin: PD0
#spi_bus: spi1
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder1]
#cs_pin: PF8
#spi_bus: spi1
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder2]
#cs_pin: PD13
#spi_bus: spi1
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder3]
#cs_pin: PC7
#spi_bus: spi1
#run_current: 0.800
#stealthchop_threshold: 999999

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PE9, EXP1_2=PE10,
    EXP1_3=PE11, EXP1_4=PE12,
    EXP1_5=PE13, EXP1_6=PE14,    # Slot in the socket on this side
    EXP1_7=PE15, EXP1_8=PB10,
    EXP1_9=<GND>, EXP1_10=<5V>,

    # EXP2 header
    EXP2_1=PB14, EXP2_2=PB13,
    EXP2_3=PF7, EXP2_4=PB12,
    EXP2_5=PE7, EXP2_6=PB11,      # Slot in the socket on this side
    EXP2_7=PE8, EXP2_8=<RST>,
    EXP2_9=<GND>, EXP2_10=<NC>

# See the sample-lcd.cfg file for definitions of common LCD displays.

#[bltouch]
#sensor_pin: PB2
#control_pin: PB1

# Proximity switch
#[probe]
#pin: PF6

#[output_pin ps_on_pin]
#pin: PC3

#[neopixel my_neopixel_1]
#pin: PC6

#[neopixel my_neopixel_2]
#pin: PA9

#[hall_filament_width_sensor]
#adc1: PC5
#adc2: PB0

#[adxl345]
#cs_pin: PB15
#spi_bus: spi1
