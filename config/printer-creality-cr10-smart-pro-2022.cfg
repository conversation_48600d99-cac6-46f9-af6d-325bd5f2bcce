# This file contains pin mappings for the Creality CR-10 Smart Pro
# with a CR-FDM-v2.5.S1 board.
#
# To use this config, during "make menuconfig" select the STM32F103
# with a "64KiB bootloader" and serial (on USART1 PA10/PA9)
# communication. Enable PA0 GPIO pin on startup.
#
# Flash this firmware on the MCU by copying "out/klipper.bin" to an SD
# card and turning the printer on by pressing and holding the power
# button with the card inserted for 10-20 seconds. The firmware
# filename must end in ".bin" and must not match the last filename
# that was flashed.
#
# The machine itself includes a small router that can run a Klipper
# frontend. You don't need to buy a single-board computer.
#
# See docs/Config_Reference.md for a description of parameters.


[stepper_x]
step_pin: PB8
dir_pin: !PB7
enable_pin: !PC3
rotation_distance: 40
microsteps: 16
endstop_pin: PC4
position_min: -5
position_endstop: -5
position_max: 305
homing_speed: 50

[stepper_y]
step_pin: PB6
dir_pin: PB5
enable_pin: !PC3
rotation_distance: 40
microsteps: 16
endstop_pin: PC5
position_min: -2
position_endstop: -2
position_max: 302
homing_speed: 50

[stepper_z]
step_pin: PB4
dir_pin: !PB3
enable_pin: !PC3
rotation_distance: 8
microsteps: 16
endstop_pin: probe:z_virtual_endstop
position_min: -1.5
position_max: 400
homing_speed: 4
second_homing_speed: 1
homing_retract_dist: 2.0

[extruder]
step_pin: PC2
dir_pin: !PB9
enable_pin: !PC3
rotation_distance: 7.640
microsteps: 16
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB14
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PB1
control: pid
pid_Kp: 22.865
pid_Ki: 1.292
pid_Kd: 101.178
min_temp: 0
max_temp: 250

[filament_switch_sensor runout_sensor]
pause_on_runout: false
runout_gcode: PAUSE
insert_gcode: RESUME
switch_pin: !PA15

[heater_bed]
heater_pin: PB13
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PB0
control: pid
pid_Kp: 72.49
pid_Ki: 0.844
pid_Kd: 1542.189
min_temp: 0
max_temp: 120

[heater_fan hotend_fan]
pin: PC13
heater: extruder
heater_temp: 50.0

[fan]
pin: PB15
kick_start_time: 0.5

[mcu]
serial: /dev/ttyPrinter
restart_method: command

[temperature_sensor Board_MCU]
sensor_type: temperature_mcu
min_temp: 0
max_temp: 100

[bltouch]
sensor_pin: ^PC15
control_pin: PC14
x_offset: -32.5
y_offset: -40.6
z_offset: 2.60 # initial safe value, get correct value by PROBE_CALIBRATE

[safe_z_home]
home_xy_position: 150,150
speed: 50
z_hop: 3
z_hop_speed: 5

[screws_tilt_adjust]
screw1: 60, 80
screw1_name: front left screw
screw2: 300, 80
screw2_name: front right screw
# The rear screws are actually mechanically not reachable for the
# probe, but that is ok, adjustments will still converge.
screw3: 300, 300
screw3_name: rear right screw
screw4: 60, 300
screw4_name: rear left screw
horizontal_move_z: 10.
speed: 50.
screw_thread: CW-M3

# Main light bar
[output_pin lights]
pin: PA7
value: 1

# Do not use PB12. PB12 resets the Wifi board.
#[output_pin factory_reset]
#pin: PB12
#value: 0

# Turns off the printer
[output_pin power]
pin: PA0
value: 1
shutdown_value: 1

# Conservative default values that mimic the behaviour of the
# stock firmware for easy results. It can go faster.
[printer]
kinematics: cartesian
max_velocity: 500
max_accel: 2000
square_corner_velocity: 5.0
max_z_velocity: 10
max_z_accel: 100
