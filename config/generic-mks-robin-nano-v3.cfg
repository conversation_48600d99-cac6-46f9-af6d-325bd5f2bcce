# This file contains common pin mappings for MKS Robin Nano V3
# boards. To use this config, the firmware should be compiled for the
# stm32f407. When running "make menuconfig", select the 48KiB
# bootloader, and enable "USB for communication".

# The "make flash" command does not work on the MKS Robin. Instead,
# after running "make", copy the generated "out/klipper.bin" file to a
# file named "Robin_nano_v3.bin" on an SD card and then restart the
# MKS Robin with that SD card.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PE3
dir_pin: !PE2
enable_pin: !PE4
microsteps: 16
rotation_distance: 40
endstop_pin: PA15
position_endstop: 0
position_max: 300
homing_speed: 50

[stepper_y]
step_pin: PE0
dir_pin: !PB9
enable_pin: !PE1
microsteps: 16
rotation_distance: 40
endstop_pin: !PD2
position_endstop: 0
position_max: 300
homing_speed: 50

[stepper_z]
step_pin: PB5
dir_pin: PB4
enable_pin: !PB8
microsteps: 16
rotation_distance: 8
endstop_pin: !PC8
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PD6
dir_pin: !PD3
enable_pin: !PB3
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PE5
sensor_type: ATC Semitec 104GT-2
sensor_pin: PC1
control: pid
pid_Kp: 14.669
pid_Ki: 0.572
pid_Kd: 94.068
min_temp: 0
max_temp: 250

#[extruder1]
#step_pin: PD15
#dir_pin: !PA1
#enable_pin: !PA3
#heater_pin: PB0
#sensor_pin: PA2
#...

[heater_bed]
heater_pin: PA0
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC0
control: pid
pid_Kp: 325.10
pid_Ki: 63.35
pid_Kd: 417.10
min_temp: 0
max_temp: 130

[fan]
pin: PC14   # fan1
#pin: PB1 # fan2

[mcu]
serial: /dev/serial/by-id/usb-Klipper_stm32f407xx_000000000000000000000000-if00

[printer]
kinematics: cartesian
max_velocity: 250
max_accel: 4500
max_z_velocity: 25
max_z_accel: 100

########################################
# EXP1 / EXP2 (display) pins
########################################

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PC5,  EXP1_3=PD13, EXP1_5=PE14, EXP1_7=PD11, EXP1_9=<GND>,
    EXP1_2=PE13, EXP1_4=PC6,  EXP1_6=PE15, EXP1_8=PD10, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PA6, EXP2_3=PE8, EXP2_5=PE11, EXP2_7=PE12,  EXP2_9=<GND>,
    EXP2_2=PA5, EXP2_4=PE10, EXP2_6=PA7, EXP2_8=<RST>, EXP2_10=<3.3v>
    # Pins EXP2_1, EXP2_6, EXP2_2 are also MISO, MOSI, SCK of bus "ssp1"

# See the sample-lcd.cfg file for definitions of common LCD displays.
