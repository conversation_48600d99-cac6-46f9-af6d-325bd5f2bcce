# This file contains common pin mappings for the Duet2 Maestro. To use
# this config, the firmware should be compiled for the sam4s8c.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC20
dir_pin: PC18
enable_pin: !PA1
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA24
position_endstop: 0
position_max: 200
homing_speed: 50

[tmc2208 stepper_x]
uart_pin: PA9
tx_pin: PA10
select_pins: !PC14, !PC16, !PC17
sense_resistor: 0.075
run_current: 0.800
stealthchop_threshold: 999999

[stepper_y]
step_pin: PC2
dir_pin: PA8
enable_pin: !PA1
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB6
position_endstop: 0
position_max: 200
homing_speed: 50

[tmc2208 stepper_y]
uart_pin: PA9
tx_pin: PA10
select_pins: PC14, !PC16, !PC17
sense_resistor: 0.075
run_current: 0.800
stealthchop_threshold: 999999

[stepper_z]
step_pin: PC28
dir_pin: PB4
enable_pin: !PA1
microsteps: 16
rotation_distance: 8
endstop_pin: ^PC10
position_endstop: 0.5
position_max: 200

[tmc2208 stepper_z]
uart_pin: PA9
tx_pin: PA10
select_pins: !PC14, PC16, !PC17
sense_resistor: 0.075
run_current: 0.800
stealthchop_threshold: 999999

# Support analog sensor adjustments using VREF/VSSA pins
[adc_scaled vref_scaled]
vref_pin: PA17
vssa_pin: PA19

[extruder]
step_pin: PC4
dir_pin: PB7
enable_pin: !PA1
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: !PC1
sensor_type: EPCOS 100K B57560G104F
pullup_resistor: 2200
sensor_pin: vref_scaled:PB0
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[tmc2208 extruder]
uart_pin: PA9
tx_pin: PA10
select_pins: PC14, PC16, !PC17
sense_resistor: 0.075
run_current: 0.800
stealthchop_threshold: 999999

#[extruder1]
#step_pin: PC5
#dir_pin: PC6
#enable_pin: !PA1
#heater_pin: !PA16
#sensor_pin: PC30
#pullup_resistor: 2200
#...
#[tmc2208 extruder1]
#select_pins: !PC14, !PC16, PC17
#sense_resistor: 0.075
#...

# External steppers
# e2: step_pin=PC31 dir_pin=PA18 enable_pin=PC27 select_pins=PC14,!PC16,PC17
# e3: step_pin=PC21 dir_pin=PC24 enable_pin=PC25 select_pins=!PC14,PC16,PC17
# e0_stop: endstop_pin=PA25
# e1_stop: endstop_pin=PC7
# c_temp: sensor_pin=PB1

[heater_bed]
heater_pin: !PC0
sensor_type: EPCOS 100K B57560G104F
pullup_resistor: 2200
sensor_pin: vref_scaled:PA20
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PC23  # FAN0

#[heater_fan heatbreak_cooling_fan]
#pin: PC22  # FAN1

#[heater_fan board_cooling_fan]
#pin: PC29  # FAN2

#[bltouch]
#sensor_pin: PC15  # Z_PROBE_IN
#control_pin: PC26  # Z_PROBE_MOD
#...

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[static_digital_output led]
pins: !PC26  # Z_PROBE_MOD / SERVO pin

# EXP1 / EXP2 (display) pins
[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PA15, EXP1_3=PA6, EXP1_5=PA2,  EXP1_7=<NC>, EXP1_9=<GND>,
    EXP1_2=PA7,  EXP1_4=PC9, EXP1_6=<NC>, EXP1_8=<NC>, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PA5, EXP2_3=PC3,  EXP2_5=PB5, EXP2_7=<NC>,  EXP2_9=<GND>,
    EXP2_2=PA2, EXP2_4=PB13, EXP2_6=PA6, EXP2_8=<RST>, EXP2_10=<NC>
    # Pins EXP2_1, EXP2_6, EXP2_2 are also MISO, MOSI, SCK of bus "usart0"

# See the sample-lcd.cfg file for definitions of common LCD displays.
