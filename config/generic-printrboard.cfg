# This file contains common pin mappings for Printrboard boards (rev B
# through D). To use this config the firmware should be compiled for
# the AVR at90usb1286.

# Note that the "make flash" command will not work for Printrboard!
# To flash:
# - Connect the "BOOT" jumper.
# - Press the reset button (on board) to enter into DFU mode.
# - Connect via USB and run:
# avrdude -c flip1 -p usb1286 -U flash:w:out/klipper.elf.hex

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PA0
dir_pin: !PA1
enable_pin: !PE7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PE3
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PA2
dir_pin: PA3
enable_pin: !PE6
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB0
# Printrboard RevF uses a different Y endstop pin.
#endstop_pin: ^PB4
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PA4
dir_pin: !PA5
enable_pin: !PC7
microsteps: 16
rotation_distance: 8
endstop_pin: ^PE4
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PA6
dir_pin: PA7
enable_pin: !PC3
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF1
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PC4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF0
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PC6

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

# Use the following on a Printrboard RevF to control stepper current.
#[mcp4728 stepper_current_dac]
#scale: 2.327
#channel_a: 1.2 # Extruder
#channel_b: 1.2 # stepper_z
#channel_c: 1.0 # stepper_y
#channel_d: 1.0 # stepper_x
