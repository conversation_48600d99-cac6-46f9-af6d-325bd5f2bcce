# Klipper firmware config file for Anycubic 4Max Pro 2.0. To use this config,
# the firmware should be compiled for the AVR atmega2560.

# You can build and flash firmware with commands in klipper directory:
# $ make clean
# $ make menuconfig
# $ make
# $ make flash FLASH_DEVICE={your serial port}

# For more precision, don't forget calibrate PIDs individually for your printer.

# For Anycubic 4Max Pro (not 2.0) owners:
#       Be careful when using this config! This config tested only on Anycubic
#       4Max Pro 2.0! At first, you should
#       set homing_speed on 5, and run homing and click on the endstops with
#       your fingers. It is necessary to make sure that all the motors are
#       spinning in the right direction, all the temperature sensors show the
#       correct values, and all the endstops are triggered correctly.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
rotation_distance: 32
microsteps: 16
endstop_pin: ^!PE5
position_min: -8
position_endstop: -8
position_max: 270
homing_speed: 50.0
homing_retract_dist: 5.0
second_homing_speed: 5.0

[stepper_y]
step_pin: PF6
dir_pin: !PF7
enable_pin: !PF2
rotation_distance: 40
microsteps: 16
endstop_pin: ^!PD2
position_max: 215
position_min: -1
position_endstop: -1
homing_speed: 50.0
homing_retract_dist: 5.0
second_homing_speed: 5.0

[stepper_z]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
rotation_distance: 4
microsteps: 16
endstop_pin: ^!PD3
position_endstop: 0.0
position_max: 195
homing_speed: 5.0
homing_retract_dist: 3.0
second_homing_speed: 0.8
homing_speed: 6.0

[extruder]
step_pin: PA4
dir_pin: !PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 7.488
nozzle_diameter: 0.400
filament_diameter: 1.750
instantaneous_corner_velocity: 1.5
max_extrude_only_distance: 500
heater_pin: PB4
sensor_type: ATC Semitec 104GT-2
sensor_pin: PK5
smooth_time: 2
control: pid
pid_kp: 29.735      # calibrate these PID values for your printer individually!
pid_ki: 1.304       # calibrate these PID values for your printer individually!
pid_kd: 169.491     # calibrate these PID values for your printer individually!
min_temp: 0
max_temp: 280

[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: pid
pid_kp: 71.423       # calibrate these PID values for your printer individually!
pid_ki: 0.679        # calibrate these PID values for your printer individually!
pid_kd: 1877.522     # calibrate these PID values for your printer individually!
min_temp: 0
max_temp: 125

[fan]
pin: PH6
kick_start_time: 1.0

[heater_fan heatbreak_fan]
pin: PL5
kick_start_time: 1.0
max_power: 1.0
heater_temp: 40.0
heater: extruder

[controller_fan system_fan]
pin: PH4
hardware_pwm: True
kick_start_time: 1.0
max_power: 1.0
idle_timeout: 300
idle_speed: 0.5
shutdown_speed: 1.0
heater: heater_bed,extruder

[output_pin light]
value: 1
pin: PL4

[mcu]
serial: /dev/serial/by-id/usb-Silicon_Labs_CP2102_USB_to_UART_Bridge_Controller_0001-if00-port0
restart_method: arduino

[printer]
kinematics: cartesian
max_velocity: 150
max_accel: 3000
max_z_velocity: 7
max_z_accel: 50
square_corner_velocity: 5

[bed_screws]
screw1: 5, 5
screw2: 265, 210
screw3: 5, 210
screw4: 265, 5

[filament_switch_sensor filament_sensor]
switch_pin: ^!PC4

[output_pin buzz]
pin: PC6
pwm: True

[output_pin AUTO_POWEROFF]
pin: PD0
pwm: True
cycle_time: 0.02
value: 1


# This macro (M300) uses internal integrated beeper
# Just use it in your G-code for making sounds. Example: M300 S1000 P500
[gcode_macro M300]
gcode:
    {% set S = params.S|default(800)|float %}
    {% set P = params.P|default(100)|int %}
    SET_PIN PIN=buzz VALUE=0.5 CYCLE_TIME={ 1.0 / S | float }
    G4 P{P}
    SET_PIN PIN=buzz VALUE=0

# This macro (M81) uses internal integrated PSU control-relay.
# Just use M81 in your end_gcode if you want to poweroff your printer after print.
# Note: as in original Marlin firmware, before powerdown, printer will be cool hotend
# until temperature will be below 45°С / 113°F.

[gcode_macro M81]
gcode:
    {% set required_extruder_temp = params.T|default(45)|int %}
    {% if printer.extruder.temperature > required_extruder_temp|default(45)|int %}
            M300
            M300
            M300
            M117 COOLING DOWN BEFORE POWER OFF
            M109 S{required_extruder_temp}
            SET_PIN PIN=AUTO_POWEROFF VALUE=0.5
            G4 P60
            SET_PIN PIN=AUTO_POWEROFF VALUE=1
    {% else %}
            M300
            M117 POWER OFF SOON
            G4 P10000
            SET_PIN PIN=AUTO_POWEROFF VALUE=0.5
            G4 P60
            SET_PIN PIN=AUTO_POWEROFF VALUE=1
    {% endif %}
