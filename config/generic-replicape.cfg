# This file contains an example configuration for the Replicape rev B3
# board. To use this config, one must compile and install the
# micro-controller code for the "Beaglebone PRU", and then compile and
# install the micro-controller code a second time for a "Linux
# process".

# NOTE: Klipper does not alter the input/output state of the
# Beaglebone pins and it does not control their pull-up resistors.
# Typically the correct settings are automatically applied when the
# Beaglebone detects the Replicape board, but if changes are needed
# they must be specified in a "device tree overlay" or via the
# config-pin program.

# See docs/Config_Reference.md for a description of parameters.

[mcu]
serial: /dev/rpmsg_pru30

[mcu host]
serial: /tmp/klipper_host_mcu

[replicape]
revision: B3
host_mcu: host
stepper_x_microstep_mode: spread16
stepper_x_current: 0.5
stepper_y_microstep_mode: spread16
stepper_y_current: 0.5
stepper_z_microstep_mode: spread16
stepper_z_current: 0.5
stepper_e_microstep_mode: 16
stepper_e_current: 0.5

[stepper_x]
step_pin: gpio0_27
dir_pin: gpio1_29
enable_pin: replicape:stepper_x_enable
microsteps: 16
rotation_distance: 40
endstop_pin: ^gpio3_21
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: gpio1_12
dir_pin: gpio0_22
enable_pin: replicape:stepper_y_enable
microsteps: 16
rotation_distance: 40
endstop_pin: ^gpio1_17
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: gpio0_23
dir_pin: gpio0_26
enable_pin: replicape:stepper_z_enable
microsteps: 16
rotation_distance: 8
endstop_pin: ^gpio0_31
position_endstop: 0
position_max: 200

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 25
max_z_accel: 30

[extruder]
step_pin: gpio1_28
dir_pin: gpio1_15
enable_pin: replicape:stepper_e_enable
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: replicape:power_e
sensor_type: EPCOS 100K B57560G104F
sensor_pin: host:analog4
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: replicape:power_hotbed
sensor_type: EPCOS 100K B57560G104F
sensor_pin: host:analog6
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: replicape:power_fan0

# The alternative servo pins channels on the endstops x2 and y2 can be used
# via the special relicape pins servo0 (gpio1_18) and servo1 (gpio1_19).
#[servo servo_x2]
#pin: replicape:servo0
#   PWM output pin controlling the servo. This parameter must be
#   provided.
#...

# Providing an example of a switch filament sensor using the Linux MCU for replicape, instead of the PRU which does not have enough memory:
#[filament_switch_sensor switch_sensor]
#switch_pin: HOST_X2_STOP

# providing board pin aliases for PRU firmware
[board_pins]
aliases:
   # step/dir pins
   X_DIR=gpio1_29, X_STEP=gpio0_27, Y_DIR=gpio0_22, Y_STEP=gpio1_12, Z_DIR=gpio0_26, Z_STEP=gpio0_23,
   E_DIR=gpio1_15, E_STEP=gpio1_28, H_DIR=gpio1_14, H_STEP=gpio1_13,
   # stepper fault pins
   FAULT_X=gpio2_4, FAULT_Y=gpio2_5, FAULT_Z=gpio0_15, FAULT_E=gpio2_1, FAULT_H=gpio2_3,
   # endstops
   STOP_X1=gpio3_21, STOP_X2=gpio0_30, STOP_Y1=gpio1_17, STOP_Y2=gpio3_17, STOP_Z1=gpio0_31, STOP_Z2=gpio0_4,
   # enable steppers (all on one pin)
   STEPPER_ENABLE=gpio0_20,
   # servos
   SERVO_0=gpio1_18, SERVO_1=gpio1_19,

[board_pins host]
mcu: host
aliases:
   # Host aliases for Linux MCU
   HOST_X2_STOP=gpio30, HOST_Y2_STOP=gpio113, HOST_Z2_STOP=gpio4,
   # Thermistors
   THERM_E=analog4, THERM_H=analog5, THERM_BED=analog6,
   # D1W pin
   DALLAS=gpio2
