# This file contains pin mappings for the BQ Prusa i3 Hephestos from 2014
# (https://www.reprap.org/wiki/Prusa_i3_Hephestos)
# It was sold in kit form, and uses a RAMPS board with HD44780 display without
# heated bed or any modern amenities.

# To use this config, the firmware should be compiled for the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

[display]
lcd_type: hd44780
rs_pin: PH1
e_pin: PH0
d4_pin: PA1
d5_pin: PA3
d6_pin: PA5
d7_pin: PA7
encoder_pins: ^PC4, ^PC6
click_pin:  ^!PC2
kill_pin: ^!PG0

[stepper_x]
step_pin: PF0
dir_pin: !PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PE5
position_endstop: 0
position_max: 215
homing_speed: 50

[stepper_y]
step_pin: PF6
dir_pin: PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PJ1
position_endstop: 0
position_max: 210
homing_speed: 50

[stepper_z]
step_pin: PL3
dir_pin: !PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 0.8
endstop_pin: ^!PD3
position_endstop: 0
position_max: 200
homing_speed: 3

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
microsteps: 16
# measured extruding 100mm of filament with stock Hephestos extruder
rotation_distance: 31.825
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5
min_temp: 0
max_temp: 250
control: pid
pid_kp: 19.462
pid_ki: 0.713
pid_kd: 132.830


# 5 points for manual bed leveling that still leave room for accessing the stock screws
[bed_screws]
screw1: 40, 40
screw2: 180, 40
screw3: 180, 160
screw4: 40, 160
screw5: 110, 100

[fan]
pin: PH6

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
# Must limit Z velocity, since RAMPS does not have enough timer resolution
max_z_velocity: 3
max_z_accel: 100

[mcu]
serial: /dev/ttyUSB0

# Common EXP1 / EXP2 (display) pins
[board_pins]
aliases:
    # Common EXP1 header found on many "all-in-one" ramps clones
    EXP1_1=PC0, EXP1_3=PH0, EXP1_5=PA1, EXP1_7=PA5, EXP1_9=<GND>,
    EXP1_2=PC2, EXP1_4=PH1, EXP1_6=PA3, EXP1_8=PA7, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PB3, EXP2_3=PC6, EXP2_5=PC4, EXP2_7=PL0, EXP2_9=<GND>,
    EXP2_2=PB1, EXP2_4=PB0, EXP2_6=PB2, EXP2_8=PG0, EXP2_10=<RST>
    # Pins EXP2_1, EXP2_6, EXP2_2 are also MISO, MOSI, SCK of bus "spi"
    # Note, some boards wire: EXP2_8=<RST>, EXP2_10=PG0
