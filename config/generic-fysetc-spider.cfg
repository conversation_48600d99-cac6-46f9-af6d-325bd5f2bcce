# This file contains common pin mappings for the Fysetc Spider board.
# To use this config, the firmware should be compiled for the STM32F446.
# When calling "menuconfig", enable "extra low-level configuration setup"
# and select the "12MHz crystal" as clock reference.
# For flashing, write the compiled klipper.bin to memory location 0x08000000

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PE11
dir_pin: PE10
enable_pin: !PE9
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB14  # PA1 for X-max
position_endstop: 0
position_max: 200

[stepper_y]
step_pin: PD8
dir_pin: PB12
enable_pin: !PD9
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB13  # PA2 for Y-max
position_endstop: 0
position_max: 200

[stepper_z]
step_pin: PD14
dir_pin: PD13
enable_pin: !PD15
microsteps: 16
rotation_distance: 8
endstop_pin: ^PA0  # PA3 for Z-max
position_endstop: 0
position_max: 400

[extruder]
step_pin: PD5
dir_pin: !PD6
enable_pin: !PD4
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB15
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC0
control: pid
pid_Kp: 22
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 260

#[extruder1]
#step_pin: PE6
#dir_pin: !PC13
#enable_pin: !PE5
#heater_pin: PC8
#sensor_pin: PC1

#[extruder2]
#step_pin: PE2
#dir_pin: !PE4
#enable_pin: !PE3
#heater_pin: PB3
#sensor_pin: PC2

#[extruder3]
#step_pin: PD12
#dir_pin: PC4
#enable_pin: !PE8

#[extruder4]
#step_pin: PE1
#dir_pin: !PE0
#enable_pin: !PC5

[heater_bed]
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC3
control: watermark
min_temp: 0
max_temp: 130

#fan for printed model FAN0
[fan]
pin: PB0

#fan for hotend FAN1
#[heater_fan heatbreak_cooling_fan]
#pin: PB1
#shutdown_speed: 1

#fan for control board FAN2
#[heater_fan my_control_fan]
#pin: PB2

#####################################################################
# LED Control
#####################################################################

#[output_pin caselight ]
##  Chamber Lighting - In 5V-RGB Position
#pin: PD3
#pwm: true
#shutdown_value: 0
#value:100
#cycle_time: 0.01

[mcu]
## Obtain definition by "ls -l /dev/serial/by-id/" then unplug to verify
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

########################################
# TMC UART configuration
########################################

#[tmc2208 stepper_x]
#uart_pin: PE7
#run_current: 0.8
#stealthchop_threshold: 999999

#[tmc2208 stepper_y]
#uart_pin: PE15
#run_current: 0.8
#stealthchop_threshold: 999999

#[tmc2208 stepper_z]
#uart_pin: PD10
#run_current: 0.8
#stealthchop_threshold: 999999

#[tmc2208 extruder]
#uart_pin: PD7
#run_current: 0.8
#sense_resistor: 0.110
#stealthchop_threshold: 999999

#[tmc2208 extruder1]
#uart_pin: PC14
#run_current: 0.8
#stealthchop_threshold: 999999

#[tmc2208 extruder2]
#uart_pin: PC15
#run_current: 0.8
#stealthchop_threshold: 999999

#[tmc2208 extruder3]
#uart_pin: PA15
#run_current: 0.8
#stealthchop_threshold: 999999

#[tmc2208 extruder4]
#uart_pin: PD11
#run_current: 0.8
#stealthchop_threshold: 999999

########################################
# TMC SPI configuration
########################################

#[tmc2130 stepper_x]
#spi_bus: spi4
#cs_pin: PE7
#diag1_pin: PB14
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_y]
#spi_bus: spi4
#cs_pin: PE15
#diag1_pin: PB13
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_z]
#spi_bus: spi4
#cs_pin: PD10
#diag1_pin: PA0
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder]
#spi_bus: spi4
#cs_pin: PD7
#diag1_pin: PA3
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder1]
#spi_bus: spi4
#cs_pin: PC14
#diag1_pin: PA2
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder2]
#spi_bus: spi4
#cs_pin: PC15
#diag1_pin: PA1
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder3]
#spi_bus: spi4
#cs_pin: PA15
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 extruder4]
#spi_bus: spi4
#cs_pin: PD11
#run_current: 0.800
#stealthchop_threshold: 999999

########################################
# EXP1 / EXP2 (display) pins
########################################

[board_pins]
aliases:
    # EXP2 header
    EXP1_10=<5V>, EXP1_9=<GND>,
    EXP1_8=PD1,   EXP1_7=PD0,
    EXP1_6=PC12,  EXP1_5=PC10,     # Slot in the socket on the other side
    EXP1_4=PD2,   EXP1_3=PC11,
    EXP1_2=PA8,   EXP1_1=PC9,

    # EXP1 header
    EXP2_10=<5V>, EXP2_9=<GND>,
    EXP2_8=<RST>, EXP2_7=PB10,
    EXP2_6=PA7,   EXP2_5=PC7,       # Slot in the socket on the other side
    EXP2_4=PA4,   EXP2_3=PC6,
    EXP2_2=PA5,   EXP2_1=PA6

# See the sample-lcd.cfg file for definitions of common LCD displays.
