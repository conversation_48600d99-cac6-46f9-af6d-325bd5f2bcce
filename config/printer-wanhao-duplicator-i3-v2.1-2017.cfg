# This file contains pin mappings and other appropriate default
# parameters for a Wanhao Duplicator i3 v2.1 and its clones (Monoprice
# Maker Select, Cocoon Create, etc.).
#
# This will probably work on older revisions (v1.0, v2.0) of the printer
# but is untested on those versions.

# Note, a number of Melzi boards are shipped with a bootloader that
# requires the following command to flash the board:
#  avrdude -p atmega1284p -c arduino -b 57600 -P /dev/ttyUSB0 -U out/klipper.elf.hex
# If the above command does not work and "make flash" does not work
# then one may need to flash a bootloader to the board - see the
# Klipper docs/Bootloaders.md file for more information.

# See docs/Config_Reference.md for a description of parameters.

# For best results with klipper and the Wanhao Duplicator i3, follow these
# guidelines:
#
# - Locate the USB serial port for your printer in /dev/serial/by-id/ format.
#   See https://www.klipper3d.org/FAQ.html#wheres-my-serial-port
#   It will be something like:
#   /dev/serial/by-id/usb-FTDI_FT232R_USB_UART_ABCD1234-if00-port0
#
# - Configure klipper to compile firmware for the AVR atmega1284p
#
# - At this point, "make flash FLASH_DEVICE=..." should successfully
#   flash your printer board. Use the /dev/serial/by-id/ format for
#   FLASH_DEVICE to ensure consistent results.
#   See https://www.klipper3d.org/FAQ.html#the-make-flash-command-doesnt-work
#   if you have problems.
#
# - Copy this sample file you are currently reading to ~/printer.cfg,
#   and customize the following parameters:
#   * [extruder] > rotation_distance
#
#     This is the inverse of "E steps" (extruder steps per mm) from the stock
#     Wanhao Repetier-based firmware.
#     (See https://3dprinterwiki.info/extruder-steps/ )
#
#   * [extruder] > PID parameters (pid_Kp, pid_Ki, pid_Kd)
#   * [heater_bed] > PID parameters (pid_Kp, pid_Ki, pid_Kd)
#
#     PID values from stock Wanhao firmware (Repetier) do not
#     translate directly to klipper. You will need to run klipper's
#     PID autotune function for the extruder and bed. After getting the
#     klipper firmware up and running, run the PID_CALIBRATE procedures
#     by sending these commands via octoprint terminal (one per autotune):
#
#        extruder:   PID_CALIBRATE HEATER=extruder TARGET=<temp>
#        heated bed: PID_CALIBRATE HEATER=heater_bed TARGET=<temp>
#
#     After the autotune process completes, PID parameter results
#     can be found in the Octoprint terminal tab (if you're quick)
#     or in /tmp/klippy.log.
#
#     Enter the PID parameters into the appropriate sections of ~/printer.cfg .
#
#   * [extruder] > max_temp
#   * [heater_bed] > max_temp
#
#     The max temps included in this printer config are limited to 230 for extruder
#     and 70 for heated bed. If your printer has been modified to handle higher temps
#     (like an upgraded hot end or a separate MOSFET for your heated bed), you may
#     want to increase these values.
#
#     Note: Some Melzi boards were shipped with 10K pullup resistors
#     instead of 4.7K. If the temperatures on your printer seem way
#     off before running the PID tune, you may need to add
#     "pullup_resistor: 10000" to both the extruder and the heater_bed
#     config sections.
#
#   * [mcu] > serial
#
#     Enter the USB serial port of the printer in /dev/serial/by-id/ format
#     for best results.
#
# - Power cycle the Wanhao Duplicator i3
#
# - Issue the command "RESTART" via the Octoprint terminal tab (similar to
#   how you would send a manual gcode command, but send the word RESTART).
#   This tells klipper to reload its config file and do an internal reset.
#   You should then see a status screen appear on the printer's LCD.
#
# - Be sure to follow these instructions before attempting any prints:
#   https://www.klipper3d.org/Config_checks.html

[stepper_x]
step_pin: PD7
dir_pin: PC5
enable_pin: !PD6
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PC2
position_endstop: 0
position_max: 200
homing_speed: 40

[stepper_y]
step_pin: PC6
dir_pin: PC7
enable_pin: !PD6
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PC3
position_endstop: 0
position_max: 200
homing_speed: 40

[stepper_z]
step_pin: PB3
dir_pin: !PB2
enable_pin: !PA5
microsteps: 16
rotation_distance: 8
endstop_pin: ^!PC4
position_endstop: 0.5
position_max: 180
homing_speed: 2

[extruder]
step_pin: PB1
dir_pin: !PB0
enable_pin: !PD6
microsteps: 16
rotation_distance: 29.888
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PD5
sensor_type: Generic 3950
sensor_pin: PA7
control: pid
pid_Kp: 18.214030
pid_Ki: 0.616380
pid_Kd: 134.556146
min_temp: 0
max_temp: 230

[heater_bed]
heater_pin: PD4
sensor_type: Generic 3950
sensor_pin: PA6
control: pid
pid_Kp: 71.321
pid_Ki: 1.989
pid_Kd: 639.210
min_temp: 0
max_temp: 70

[fan]
pin: PB4

[mcu]
serial: /dev/ttyUSB0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 200
max_accel: 1000
max_z_velocity: 2
max_z_accel: 100

[display]
lcd_type: st7920
cs_pin: PC1
sclk_pin: PD3
sid_pin: PC0
encoder_pins: ^PA2, ^PA1
click_pin: ^!PA3
kill_pin: ^!PD2
