# This file contains common configurations and pin mappings for the Prusa Buddy
# board. The LCD is not currently supported by <PERSON><PERSON><PERSON>, so the touchscreen will
# permanently display the bootloader screen after the Klipper firmware is flashed;
# use Fluidd, Mainsail, or OctoPrint etc. to control the printer.

# To use this config, the firmware should be compiled for the STM32F407. When
# running "make menuconfig", enable "extra low-level configuration setup",
# select the "128KiB + 512 byte offset" bootloader, and USB communication.
# Connect the printer to your Raspberry Pi using the printer's micro-USB port.
# If you prefer to remove Prusa's stock bootloader entirely, select the
# "No bootloader" option.

# When flashing for the first time, you will need to break the "appendix"
# on the Buddy board, then put the device into DFU mode by moving the jumper
# on the 3-pin header (older boards) or shorting the 2-pin header (newer boards)
# and resetting, and finally use "make flash" to install Klipper. Once Klipper is
# installed, you no longer need the jumper - just use "make flash" which will
# automatically put the device into DFU mode.

# Note that if you were previously running Prusa firmware, you must fully
# power cycle the board after flashing. Otherwise, <PERSON><PERSON><PERSON> will be unable to
# communicate with the TMC2209s due to the abrupt change in the baud rate,
# and will show this error: "Unable to read tmc uart register IFCNT".

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PD1
dir_pin: PD0
enable_pin: !PD3
microsteps: 16
rotation_distance: 32
endstop_pin: tmc2209_stepper_x:virtual_endstop
position_endstop: 200
position_min: 0
position_max: 200
homing_speed: 50
homing_retract_dist: 0

[stepper_y]
step_pin: PD13
dir_pin: PD12
enable_pin: !PD14
microsteps: 16
rotation_distance: 32
endstop_pin: tmc2209_stepper_y:virtual_endstop
position_endstop: 0
position_min: 0
position_max: 200
homing_speed: 50
homing_retract_dist: 0

[stepper_z]
step_pin: PD4
dir_pin: !PD15
enable_pin: !PD2
microsteps: 16
rotation_distance: 4
endstop_pin: probe:z_virtual_endstop
position_min: 0
position_max: 200

[extruder]
step_pin: PD9
dir_pin: !PD8
enable_pin: !PD10
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB1
sensor_type: ATC Semitec 104GT-2
sensor_pin: PC0
control: pid
pid_Kp: 7
pid_Ki: 0.5
pid_Kd: 45
min_temp: 10
max_temp: 305

[tmc2209 stepper_x]
uart_pin: PD5
uart_address: 1
diag_pin: ^PE2
driver_SGTHRS: 130
run_current: 0.35
sense_resistor: 0.22
stealthchop_threshold: 999999

[tmc2209 stepper_y]
uart_pin: PD5
uart_address: 3
diag_pin: ^PE1
driver_SGTHRS: 130
run_current: 0.35
sense_resistor: 0.22
stealthchop_threshold: 999999

[tmc2209 stepper_z]
uart_pin: PD5
uart_address: 0
diag_pin: ^PE3
driver_SGTHRS: 100
run_current: 0.35
sense_resistor: 0.22
stealthchop_threshold: 999999

[tmc2209 extruder]
uart_pin: PD5
uart_address: 2
diag_pin: ^PA15
driver_SGTHRS: 100
run_current: 0.4
sense_resistor: 0.22

[heater_bed]
heater_pin: PB0
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA4
control: pid
pid_Kp: 120
pid_Ki: 1.5
pid_Kd: 600
min_temp: 10
max_temp: 110

# Hotend fan.
[heater_fan hotend_fan]
pin: PE9
tachometer_pin: PE14

# Part cooling fan.
[fan]
pin: PE11
tachometer_pin: PE10

# The SuperPINDA has built-in temperature compensation and no thermistor output,
# so no compensation table is needed. The PINDA thermistor is otherwise on pin PA6.
[probe]
pin: PA8
x_offset: -29
y_offset: -3
z_offset: 0
speed: 6.0

[filament_switch_sensor filament_sensor]
switch_pin: ^PB4
pause_on_runout: True

[mcu]
serial: /dev/serial/by-id/usb-Klipper_stm32f407xx_3100380013504E4E53353420-if00
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 180
max_accel: 1250
max_z_velocity: 12
max_z_accel: 400
