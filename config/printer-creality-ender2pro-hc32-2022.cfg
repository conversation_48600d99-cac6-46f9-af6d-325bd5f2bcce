# This file contains pin mappings for the Creality Ender2 Pro
#  with the HC32F460. The board is CR-FDM-v2.5.S4.170
# To use this config, during "make menuconfig" select HC32F460

# Flash this firmware by copying "out/klipper.bin" to a SD card
# as /user/firmware.bin
# Turn on the printer with the card inserted.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC1
dir_pin: PC0
enable_pin: !PC2
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA5
position_min: -20
position_endstop: -20
position_max: 165
homing_speed: 50

[stepper_y]
step_pin: PB9
dir_pin: PB8
enable_pin: !PH2
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA6
position_min: -5
position_endstop: -5
position_max: 165
homing_speed: 50

[stepper_z]
step_pin: PB6
dir_pin: !PB5
enable_pin: !PB7
microsteps: 16
rotation_distance: 8
endstop_pin: ^PB0
position_endstop: 0.0
position_max: 180

[extruder]
max_extrude_only_distance: 100.0
step_pin: PB3
dir_pin: PA15
enable_pin: !PB4
microsteps: 16
rotation_distance: 27.53480577
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PA1
sensor_pin: PC5
sensor_type: Generic 3950
control: pid
pid_Kp: 29.634
pid_Ki: 2.102
pid_Kd: 104.459
min_temp: 0
max_temp: 260

[heater_bed]
heater_pin: PB10
sensor_type: Generic 3950
sensor_pin: PC4
control: pid
pid_Kp: 72.921
pid_Ki: 1.594
pid_Kd: 834.031
min_temp: 0
max_temp: 80

[fan]
pin: PA0

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[display]
lcd_type: st7920
cs_pin: PB15
sclk_pin: PB14
sid_pin: PB12
encoder_pins: ^PB13,^PA2
click_pin: ^!PC7
