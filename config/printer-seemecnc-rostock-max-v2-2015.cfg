# This file contains the pin mappings for the SeeMeCNC Rostock Max
# (version 2) delta printer from 2015. To use this config, the
# firmware should be compiled for the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

[stepper_a]
step_pin: PC0
dir_pin: !PL1
enable_pin: !PA7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA2
homing_speed: 50
position_endstop: 380
arm_length: 290.800

[stepper_b]
step_pin: PC1
dir_pin: PL0
enable_pin: !PA6
microsteps: 16
rotation_distance: 40
endstop_pin: ^PA1

[stepper_c]
step_pin: PC2
dir_pin: !PL2
enable_pin: !PA5
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC7

[extruder]
step_pin: PC3
dir_pin: !PL6
enable_pin: !PA4
microsteps: 16
rotation_distance: 34.538
nozzle_diameter: 0.500
filament_diameter: 1.750
heater_pin: PH6
sensor_type: ATC Semitec 104GT-2
sensor_pin: PF0
control: pid
pid_Kp: 20.9700
pid_Ki: 1.3400
pid_Kd: 80.5600
min_temp: 0
max_temp: 300

[heater_bed]
heater_pin: PE5
sensor_type: ATC Semitec 104GT-2
sensor_pin: PF2
control: pid
pid_Kp: 46.510
pid_Ki: 1.040
pid_Kd: 500.000
min_temp: 0
max_temp: 300

[fan]
pin: PH5

[heater_fan heatbreak_cooling_fan]
pin: PH4
heater: extruder

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: delta
max_velocity: 300
max_accel: 3000
max_z_velocity: 150
delta_radius: 174.75

[ad5206 stepper_digipot]
enable_pin: PD7
scale: 2.08
channel_1: 1.34
channel_2: 1.0
channel_4: 1.1
channel_5: 1.1
channel_6: 1.1

[static_digital_output stepper_config]
pins:
    PG1, PG0,
    PK7, PG2,
    PK6, PK5,
    PK3, PK4,
    PK1, PK2

[static_digital_output yellow_led]
pins: !PB7

[display]
lcd_type: hd44780
rs_pin: EXP1_4
e_pin: EXP1_3
d4_pin: EXP1_5
d5_pin: EXP1_6
d6_pin: EXP1_7
d7_pin: EXP1_8
encoder_pins: ^EXP2_3, ^EXP2_5
click_pin: ^!EXP1_2
#kill_pin: ^!EXP2_8

[board_pins]
aliases:
    # Common EXP1/EXP2 headers found on RAMBo v1.4
    EXP1_1=PE6, EXP1_3=PG3, EXP1_5=PJ2, EXP1_7=PJ7, EXP1_9=<GND>,
    EXP1_2=PE2, EXP1_4=PG4, EXP1_6=PJ3, EXP1_8=PJ4, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PB3, EXP2_3=PJ5, EXP2_5=PJ6, EXP2_7=PD4, EXP2_9=<GND>,
    EXP2_2=PB1, EXP2_4=PB0, EXP2_6=PB2, EXP2_8=PE7, EXP2_10=PH2
    # Pins EXP2_1, EXP2_6, EXP2_2 are also MISO, MOSI, SCK of bus "spi"
