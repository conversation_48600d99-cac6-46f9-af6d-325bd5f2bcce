# This file contains pin mappings for the Creality Sermoon V1
# with CR-FDM-v2.4.S1.200 motherboard.

# To use this config, during "make menuconfig" select the STM32F401
# with a "64KiB bootloader" and serial (on USART1 PA10/PA9)
# communication.

# If you prefer a direct serial connection, in "make menuconfig"
# select "Enable extra low-level configuration options" and select
# Serial (on USART2 PA3/PA2), which is broken out on the 10 pin IDC
# cable used for the LCD module as follows:
# 3: Tx, 4: Rx, 9: GND, 10: VCC

# Flash this firmware by copying "out/klipper.bin" to a SD card and
# turning on the printer with the card inserted. The firmware
# filename must changed to "firmware.bin"

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PA7
dir_pin: !PA4
enable_pin: !PB8
microsteps: 16
rotation_distance: 40
endstop_pin: PC4
position_endstop: 175
position_max: 175
position_min: 0
homing_speed: 50

[stepper_y]
step_pin: PB0
dir_pin: PB10
enable_pin: !PB8
microsteps: 16
rotation_distance: 40
endstop_pin:  PB13
position_endstop: 0
position_max: 175
position_min: 0
homing_speed: 50

[stepper_z]
step_pin: PB7
dir_pin: PB6
enable_pin: !PB8
microsteps: 16
rotation_distance: 8
endstop_pin:  PB3
position_endstop: 165
position_max: 168
position_min: -3

[extruder]
step_pin: PB1
dir_pin: PB12
enable_pin: !PB8
microsteps: 16
gear_ratio: 42:12
rotation_distance: 26.359
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC1
control: pid
pid_Kp: 30.090
pid_Ki: 1.875
pid_Kd: 120.735
min_temp: 0
max_temp: 290

[heater_bed]
heater_pin: PB9
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC0
control: pid
pid_Kp: 75.694
pid_Ki: 1.160
pid_Kd: 1234.759
min_temp: 0
max_temp: 90

[fan]
pin: PA5

[fan_generic side_fan]
pin: PC15

# [controller_fan controller_fan]
# In order to access the controller fan, the controller fan needs to be plugged
# in another location. See https://github.com/Klipper3d/klipper/pull/5621
# for more information.
# pin: PB4

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 2000
max_z_velocity: 5
max_z_accel: 100

[pause_resume]
recover_velocity: 25
