# This file contains common pin mappings for the Fysetc Cheetah V2.0
# To use this config, the firmware should be compiled for the
# STM32F401 with a "32KiB bootloader".

# Rename "klipper.bin" to "firmware.bin", copy to Sdcard and insert in motherboard

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC0
dir_pin: PC1
enable_pin: !PA8
rotation_distance: 40
microsteps: 64
endstop_pin: ^PB4
position_endstop: 0
position_max: 200
homing_speed: 50

[tmc2209 stepper_x]
uart_pin: PA3
tx_pin: PA2
uart_address: 0
run_current: 0.800
interpolate: false
stealthchop_threshold: 0

[stepper_y]
step_pin: PC14
dir_pin: !PC13
enable_pin: !PC15
rotation_distance: 40
microsteps: 64
endstop_pin: ^PC8
position_endstop: 0
position_max: 200
homing_speed: 50

[tmc2209 stepper_y]
uart_pin: PA3
tx_pin: PA2
uart_address: 2
run_current: 0.800
interpolate: false
stealthchop_threshold: 0

[stepper_z]
step_pin: PB9
dir_pin: PB8
enable_pin: !PC2
rotation_distance: 8
microsteps: 64
endstop_pin: ^PB1
position_endstop: 0
position_max: 200

[tmc2209 stepper_z]
uart_pin: PA3
tx_pin: PA2
uart_address: 1
run_current: 0.800
interpolate: false
stealthchop_threshold: 0

[extruder]
step_pin: PB2
dir_pin: !PA15
enable_pin: !PD2
rotation_distance: 33.500
microsteps: 16
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC6
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC4
control: pid
pid_kp: 21.527
pid_ki: 1.063
pid_kd: 108.982
min_temp: 0
max_temp: 250

[tmc2209 extruder]
uart_pin: PA3
tx_pin: PA2
uart_address: 3
run_current: 0.800
interpolate: false
stealthchop_threshold: 0

[heater_bed]
heater_pin: PC7
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC5
control: pid
pid_kp: 54.027
pid_ki: 0.770
pid_kd: 948.182
min_temp: 0
max_temp: 120

[fan]
pin: PA1

[heater_fan my_hotend_fan]
pin: PA13
heater: extruder
heater_temp: 50.0

[controller_fan my_controller_fan]
pin: PA14
heater: extruder, heater_bed
stepper: stepper_x, stepper_y, stepper_z, extruder

[printer]
kinematics: cartesian
max_velocity: 500
max_accel: 5000
max_z_velocity: 5
max_z_accel: 1000

[mcu]
serial: INSERTSERIALIDHERE

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=<5V>,  EXP1_3=<RST>, EXP1_5=PA7,  EXP1_7=PA4,  EXP1_9=PA5,
    EXP1_2=<GND>, EXP1_4=PC3,   EXP1_6=PC11, EXP1_8=PC10, EXP1_10=PA6,

    # EXP2 header
    EXP2_1=<5V>,  EXP2_3=PB7, EXP2_5=PB14, EXP2_7=PB12, EXP2_9=PC12,
    EXP2_2=<GND>, EXP2_4=PB6, EXP2_6=PB13, EXP2_8=PB15, EXP2_10=PC9,

    # EXP3 header
    EXP3_1=PC9,  EXP3_3=PC10, EXP3_5=PC11, EXP3_7=PB12, EXP3_9=<GND>,
    EXP3_2=PC12, EXP3_4=PB14, EXP3_6=PB13, EXP3_8=PB15, EXP3_10=<5V>
    # Pins EXP1_4, EXP1_8, EXP1_6 are also MISO, MOSI, SCK of bus "spi2"
