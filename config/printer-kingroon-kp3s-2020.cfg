# This file contains common pin mappings for the Kingroon KP3S printer,
# which uses a modified MKS Robin board.
# To use this config, the firmware should be compiled for the
# STM32F103. When running "make menuconfig", enable "extra low-level
# configuration setup", select the 28KiB bootloader, and serial (on
# USART3 PB11/PB10) communication. Also, select "Enable extra low-level
# configuration options" and configure "GPIO pins to set at
# micro-controller startup" to "!PC6,!PD13" to disable the LCD as it is not
# compatible with klipper

# Note that the "make flash" command does not work with MKS Robin
# boards. After running "make", run the following command:
#   ./scripts/update_mks_robin.py out/klipper.bin out/Robin_nano.bin
# Copy the file out/Robin_nano.bin to an SD card and then restart the
# printer with that SD card.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PE3
dir_pin: !PE2
enable_pin: !PE4
microsteps: 32
rotation_distance: 40
endstop_pin: !PA15
position_endstop: 0
position_max: 180
homing_speed: 50

[stepper_y]
step_pin: PE0
dir_pin: !PB9
enable_pin: !PE1
microsteps: 32
rotation_distance: 40
endstop_pin: !PA12
position_endstop: 0
position_max: 180
homing_speed: 50

[stepper_z]
step_pin: PB5
dir_pin: PB4
enable_pin: !PB8
microsteps: 32
rotation_distance: 8
endstop_pin: !PA11
position_endstop: 0.5
position_max: 180

[safe_z_home]
home_xy_position: 90,90
z_hop: 10

[thermistor Kingroon_B3950]
temperature1: 25.0
resistance1: 103180.0
temperature2: 150.0
resistance2: 1366.2
temperature3: 250.0
resistance3: 168.6

[extruder]
step_pin: PD6
dir_pin: !PD3
enable_pin: !PB3
microsteps: 32
gear_ratio: 3:1
rotation_distance: 23.244
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC3
sensor_type: Kingroon_B3950
sensor_pin: PC1
control: pid
pid_kp: 27.057
pid_ki: 1.171
pid_kd: 156.254
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PA0
sensor_type: Kingroon_B3950
sensor_pin: PC0
control: pid
pid_kp: 61.779
pid_ki: 1.572
pid_kd: 606.980
min_temp: 0
max_temp: 130

[fan]
pin: PB1

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 250
max_accel: 2000
max_z_velocity: 25
max_z_accel: 100

[static_digital_output display_reset]
pins: !PC6, !PD13
