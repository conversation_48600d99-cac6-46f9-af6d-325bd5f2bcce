# This file contains common pin mappings for Mini-RAMBo boards. To use
# this config, the firmware should be compiled for the AVR atmega2560.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC0
dir_pin: PL1
enable_pin: !PA7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB6
#endstop_pin: ^PC7
position_endstop: 0
position_max: 250

[stepper_y]
step_pin: PC1
dir_pin: !PL0
enable_pin: !PA6
microsteps: 16
rotation_distance: 40
endstop_pin: ^PB5
#endstop_pin: ^PA2
position_endstop: 0
position_max: 210

[stepper_z]
step_pin: PC2
dir_pin: PL2
enable_pin: !PA5
microsteps: 16
rotation_distance: 8
endstop_pin: ^PB4
#endstop_pin: ^PA1
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PC3
dir_pin: PL6
enable_pin: !PA4
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PE5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF0
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PG5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PF2
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PH5

#[heater_fan heatbreak_cooling_fan]
#pin: PH3

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[output_pin stepper_xy_current]
pin: PL3
pwm: True
scale: 2.0
cycle_time: .000030
hardware_pwm: True
value: 1.3

[output_pin stepper_z_current]
pin: PL4
pwm: True
scale: 2.0
cycle_time: .000030
hardware_pwm: True
value: 1.3

[output_pin stepper_e_current]
pin: PL5
pwm: True
scale: 2.0
cycle_time: .000030
hardware_pwm: True
value: 1.25

[static_digital_output stepper_config]
pins:
    PG1, PG0,
    PK7, PG2,
    PK6, PK5,
    PK3, PK4

[static_digital_output yellow_led]
pins: !PB7
