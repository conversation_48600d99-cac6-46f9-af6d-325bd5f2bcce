# This file is an example config file for cartesian style printers.
# One may copy and edit this file to configure a new printer with
# a generic cartesian kinematics.

# DO NOT COPY THIS FILE WITHOUT CAREFULLY READING AND UPDATING IT
# FIRST. Incorrectly configured parameters may cause damage.

# See docs/Config_Reference.md for a description of parameters.

[carriage x]
position_endstop: 0
position_max: 300
homing_speed: 50
endstop_pin: ^PE5

[carriage y]
position_endstop: 0
position_max: 200
homing_speed: 50
endstop_pin: ^PJ1

[extra_carriage y1]
primary_carriage: y
endstop_pin: ^PB6

[carriage z]
position_endstop: 0.5
position_max: 100
endstop_pin: ^PD3

[dual_carriage u]
primary_carriage: x
position_endstop: 300
position_max: 300
homing_speed: 50
endstop_pin: ^PE4

[stepper my_stepper_x]
carriages: x+y
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 40

[stepper my_stepper_u]
carriages: u-y1
step_pin: PH1
dir_pin: PH0
enable_pin: !PA1
microsteps: 16
rotation_distance: 40

[stepper my_stepper_y0]
carriages: y
step_pin: PF6
dir_pin: !PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 40

[stepper my_stepper_y1]
carriages: y1
step_pin: PE3
dir_pin: !PH6
enable_pin: !PG5
microsteps: 16
rotation_distance: 40

[stepper my_stepper_z0]
carriages: z
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 8

[stepper my_stepper_z1]
carriages: z
step_pin: PG1
dir_pin: PG0
enable_pin: !PH3
microsteps: 16
rotation_distance: 8

[extruder]
step_pin: PA4
dir_pin: PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 33.5
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[extruder1]
step_pin: PC1
dir_pin: PC3
enable_pin: !PC7
microsteps: 16
rotation_distance: 33.5
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK7
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
control: watermark
min_temp: 0
max_temp: 110

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: generic_cartesian
max_velocity: 500
max_accel: 3000
max_z_velocity: 20
max_z_accel: 100
