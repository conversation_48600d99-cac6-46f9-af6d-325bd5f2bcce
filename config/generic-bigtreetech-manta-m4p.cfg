# This file contains common pin mappings for the BIGTREETECH Manta M4P
# To use this config, the firmware should be compiled for the
# STM32G0B1 with a "8KiB bootloader" and USB communication.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PC6
dir_pin: PA14
enable_pin: !PC7
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC0
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_y]
step_pin: PB10
dir_pin: !PB2
enable_pin: !PB11
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC1
position_endstop: 0
position_max: 235
homing_speed: 50

[stepper_z]
step_pin: PB0
dir_pin: !PC5
enable_pin: !PB1
microsteps: 16
rotation_distance: 8
endstop_pin: ^PC2
position_endstop: 0
position_max: 270

[extruder]
step_pin: PB3
dir_pin: PB4
enable_pin: !PD5
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC8
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PA0
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

#[filament_switch_sensor filament_sensor]
#switch_pin: ^!PC15

[heater_bed]
heater_pin: PD8
sensor_type: Generic 3950
sensor_pin: PC4
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: PD2

#[heater_fan fan1]
#pin: PD3

#[heater_fan fan2]
#pin: PD4

#[heater_fan SoC_fan]
#pin: CB1:gpio79
#pin: RPI:gpio26

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 180
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

########################################
# EXP1 / EXP2 (display) pins
########################################

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PD6,  EXP1_3=PB9,  EXP1_5=PA15, EXP1_7=PA9,   EXP1_9=<GND>,
    EXP1_2=PB8,  EXP1_4=PC3,  EXP1_6=PA10, EXP1_8=PB5,   EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PB14, EXP2_3=PC11, EXP2_5=PC12, EXP2_7=PC13,  EXP2_9=<GND>,
    EXP2_2=PB13, EXP2_4=PA8,  EXP2_6=PB15, EXP2_8=<RST>, EXP2_10=<NC>

# See the sample-lcd.cfg file for definitions of common LCD displays.

########################################
# TMC2209 configuration
########################################

#[tmc2209 stepper_x]
#uart_pin: PB12
#run_current: 0.800
#diag_pin:

#[tmc2209 stepper_y]
#uart_pin: PC10
#run_current: 0.800
#diag_pin:

#[tmc2209 stepper_z]
#uart_pin: PC9
#run_current: 0.800
#diag_pin:

#[tmc2209 extruder]
#uart_pin: PA13
#run_current: 0.600
#diag_pin:

########################################
# TMC5160 configuration
########################################

#[[tmc2130 stepper_x]
#cs_pin: PB12
#spi_bus: spi1
#diag1_pin: PG6
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_y]
#cs_pin: PC10
#spi_bus: spi1
#diag1_pin: PG9
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2130 stepper_z]
#cs_pin: PC9
#spi_bus: spi1
#diag1_pin: PG10
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc2130 extruder]
#cs_pin: PA13
#spi_bus: spi1
#diag1_pin: PC15
#run_current: 0.650
#stealthchop_threshold: 999999

#[bltouch]
#sensor_pin: PC14
#control_pin: PA1

#[adxl345]
#cs_pin: PD9
#spi_bus: spi1
#axes_map: z,y,-x

#[neopixel rgb1]
#pin: PD0

#[neopixel rgb2]
#pin: PD1
