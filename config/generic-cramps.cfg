# This file contains an example configuration for a Beaglebone PRU
# micro-controller attached to a CRAMPS board.

# THIS FILE HAS NOT BEEN TESTED - PROCEED WITH CAUTION!

# NOTE: Klipper does not alter the input/output state of the
# Beaglebone pins and it does not control their pull-up resistors.  In
# order to set the pin state one must use a "device tree overlay" or
# use the config-pin program.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: gpio0_23
dir_pin: gpio1_12
enable_pin: !gpio1_18
microsteps: 16
rotation_distance: 40
endstop_pin: ^gpio2_3
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: gpio1_15
dir_pin: gpio0_26
enable_pin: !gpio1_18
microsteps: 16
rotation_distance: 40
endstop_pin: ^gpio2_4
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: gpio0_22
dir_pin: gpio2_1
enable_pin: !gpio1_18
microsteps: 16
rotation_distance: 8
endstop_pin: ^gpio0_31
position_endstop: 0
position_max: 200

[extruder]
step_pin: gpio1_19
dir_pin: gpio1_28
enable_pin: !gpio1_18
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: gpio1_16
sensor_type: EPCOS 100K B57560G104F
pullup_resistor: 2000
sensor_pin: host:analog5
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: gpio1_13
sensor_type: EPCOS 100K B57560G104F
pullup_resistor: 2000
sensor_pin: host:analog4
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: gpio0_20

[mcu]
serial: /dev/rpmsg_pru30

[mcu host]
serial: /tmp/klipper_host_mcu

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[output_pin machine_enable]
pin: gpio1_17
value: 1
shutdown_value: 0
