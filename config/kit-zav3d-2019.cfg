# ZAV3D MAX config. To use this config, the firmware should be
# compiled for the AVR atmega2560.

# This is a base printer.cfg file for the ZAV3D Max printer and
# matches the manual/build guide exactly for controllers used (MKS
# MINI-B V1.0) and pin layout for all connected components.
# Created by "Nurmukhamed Artykaly"

# This file is only an example - be sure to review and update it
# according to the specifics of your printer.

# See docs/Config_Reference.md for a description of parameters.

# AND PLEASE READ THROUGH THE KLIPPER DOCUMENTATION FIRST!
# https://www.klipper3d.org/Overview.html

# *** THINGS TO CHANGE/CHECK: ***
# Arduino paths                         [mcu] section
# Thermistor types                      [extruder] and [heater_bed] sections - See 'sensor types' list at end of file
# FSR switch (z endstop) location       [homing_override] section
# FSR switch (z endstop) offset for Z0  [stepper_z] section
# Probe points                          [quad_gantry_level] section
# Min & Max gantry corner positions     [quad_gantry_level] section
# PID tune                              [extruder] and [heater_bed] sections
# Fine tune E steps                     [extruder] section


[stepper_x]
step_pin: PF0
dir_pin: PF1
enable_pin: !PD7
microsteps: 16
rotation_distance: 32
endstop_pin: ^PE5
position_endstop: 0
position_max: 200
homing_speed: 50

# The stepper_y section is used to describe the Y axis as well as the
# stepper controlling the X-Y movement.
[stepper_y]
step_pin: PF6
dir_pin: PF7
enable_pin: !PF2
microsteps: 16
rotation_distance: 32
endstop_pin: ^PJ1
position_endstop: 0
position_max: 200
homing_speed: 50

## Configuration with Z Endstop, without Probe tool like BLTOUCH or others.
#[stepper_z]
#step_pin: PL3
#dir_pin: PL1
#enable_pin: !PK0
#microsteps: 16
#rotation_distance: 8
## I used Z_MAX_ENDSTOP
#endstop_pin: ^PD2
## More about z-calibration is here https://vk.com/topic-107680682_34101598
#position_endstop: 235
#position_max: 235
#homing_positive_dir: true

## Configuration for Bltouch probe tool.
## Read more about BLTOUCH here https://www.klipper3d.org/BLTouch.html
[stepper_z]
step_pin: PL3
dir_pin: PL1
enable_pin: !PK0
microsteps: 16
rotation_distance: 8
position_min: -3
position_max: 230
endstop_pin: probe:z_virtual_endstop

## Configuration with PID Calibration.
## Read more here https://www.klipper3d.org/Config_checks.html
[extruder]
step_pin: PA4
dir_pin: !PA6
enable_pin: !PA2
microsteps: 16
rotation_distance: 13.5744
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB4
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK5
min_temp: 0
max_temp: 250
control: pid
pid_kp: 26.596
pid_ki: 1.166
pid_kd: 151.598

## Configuration with PID Calibration.
## Read more here https://www.klipper3d.org/Config_checks.html
[heater_bed]
heater_pin: PH5
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PK6
min_temp: 0
max_temp: 130
control: pid
pid_kp: 73.517
pid_ki: 1.822
pid_kd: 741.600

[fan]
pin: PH6

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: corexy
max_velocity: 300
max_accel: 3000
max_z_velocity: 25
max_z_accel: 30

[bltouch]
sensor_pin: ^PD3
control_pin: PH4
x_offset: 39
y_offset: 11
z_offset: 0.9
pin_up_touch_mode_reports_triggered: false
samples: 2
sample_retract_dist: 3.0

[bed_mesh]
speed: 100
horizontal_move_z: 5
mesh_min: 69, 41
mesh_max: 189, 161
probe_count: 3, 3

[homing_override]
set_position_z: 6
axes: z
gcode:
        G90
        G1 Z10 F6000
        G28 X Y
        G1 X100 Y100 F6000
        G28 Z0
        G1 X100 Y100 Z10a

[gcode_macro G29]
gcode:
        G28
        G1 Z10 F600
        BED_MESH_CALIBRATE
