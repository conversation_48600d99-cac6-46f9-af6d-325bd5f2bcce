# This file contains common pin mappings for the 8 stepper motor "Mellow Super
# 8 HV Board" board. To use this config, the firmware should be compiled for
# the STM32F407 with a "32KiB bootloader".

# The "make flash" command does not work on the Super 8 HV. Instead, after
# running "make", copy the generated "out/klipper.bin" file to a file named
# "firmware.bin" on an SD card and then restart the Super 8 HV with that SD card.

# Serial options:
#  * PA11/PA12 for serial over USB
#  * PA9 /PA10 for serial UART
#  * PD8 /PD9  for serial UART (to ESP32 module)

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: PE2  # Drive0
dir_pin: PC5
enable_pin: !PF11
microsteps: 16
rotation_distance: 40
endstop_pin: PG12  # IO0
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: PE3  # Drive1
dir_pin: PF13
enable_pin: !PF14
microsteps: 16
rotation_distance: 40
endstop_pin: PG11  # IO1
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: PE4  # Drive2
dir_pin: PG0
enable_pin: !PG1
microsteps: 16
rotation_distance: 8
endstop_pin: PG10  # IO2
position_endstop: 0.5
position_max: 200

[extruder]
step_pin: PE14
dir_pin: PE8
enable_pin: !PE9
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB0 # Heat0
sensor_pin: PF4 # ADC_0
sensor_type: EPCOS 100K B57560G104F
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 350

#[extruder1]
#step_pin: PE15
#dir_pin: PE11
#enable_pin: !PF2
#heater_pin: PB1  # Heat1
#sensor_pin: PF5  # ADC_1
#...

#[extruder2]
#step_pin: PE1
#dir_pin: PF0
#enable_pin: !PC15
#heater_pin: PC7  # Heat2
#sensor_pin: PF9  # ADC_2
#...

#[extruder3]
#step_pin: PE0
#dir_pin: PG3
#enable_pin: !PG4
#heater_pin: PF7  # Heat3
#sensor_pin: PF10 # ADC_3
#...

#[extruder4]
#step_pin: PE6
#dir_pin: PG6
#enable_pin: !PG7
#heater_pin: PF6  # Heat4
#sensor_pin: PC0  # ADC_4
#...

[heater_bed]
heater_pin: PE5   # BED
sensor_pin: PC1   # ADC_5
sensor_type: ATC Semitec 104GT-2
control: watermark
min_temp: 0
max_temp: 200

# BLTouch port, has <GND>,<5V>,<CTRL>,<GND>,<PROBE>
#[bltouch]
#sensor_pin: ^PC3
#control_pin: PC6
#...

# Accelerometer port, has <5V>,<GND>,<MISO>,<MOSI>,<INT>,<CS>,<SCLK>
#[adxl345]
#spi_speed: 5000
#spi_software_miso_pin: PD0
#spi_software_mosi_pin: PD1
##interrupt: PD3
#cs_pin: PD4
#spi_software_sclk_pin: PD5
#axes_map: x,y,z
#rate: 3200

[fan]
pin: PA0  # Fan0

[heater_fan fan1]
pin: PA1

#[heater_fan fan2]
#pin: PA2

#[heater_fan fan3]
#pin: PA3

#[heater_fan fan4]
#pin: PA15

#[heater_fan fan5]
#pin: PB11

#[heater_fan fan6]
#pin: PB10

#[heater_fan fan7]
#pin: PD12

#[heater_fan fan8]
#pin: PD14

#[heater_fan fan9]
#pin: PD15

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: corexy
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

########################################
# Stand-alone configuration
########################################
#
# Jumpers A,B,C,D for stand-alone module is as follows:
#
#  +-+-+-+-+
#  |A|B|C|D|  B = MS3
#  |A|B|C|D|  C = MS2
#  |.|.|.|.|  D = MS1
#  +-+-+-+-+


########################################
# TMC2208 configuration
########################################
#
# Jumper A for the stepStick module in UART mode is as follows:
#
#  +-+-+-+-+
#  |.|.|.|.|
#  |.|A|.|.|
#  |.|A|.|.|
#  +-+-+-+-+

#[tmc2208 stepper_x]
#uart_pin: PC4
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 stepper_y]
#uart_pin: PF12
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 stepper_z]
#uart_pin: PF15
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc2208 extruder]
#uart_pin: PE7
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 extruder1]
#uart_pin: PE10
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 extruder2]
#uart_pin: PF1
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 extruder3]
#uart_pin: PG2
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc2208 extruder4]
#uart_pin: PG5
#run_current: 0.800
#stealthchop_threshold: 999999


########################################
# TMC5160 configuration
########################################
#
# Jumpers A,B,C,D for the stepStick module in SPI mode is as follows:
#
#  +-+-+-+-+
#  |.|.|.|.|
#  |A|B|C|D|
#  |A|B|C|D|
#  +-+-+-+-+

#[tmc5160 stepper_x]
#cs_pin: PC4
#spi_bus: spi3
##diag1_pin: PG12
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc5160 stepper_y]
#cs_pin: PF12
#spi_bus: spi3
##diag1_pin: PG11
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc5160 stepper_z]
#cs_pin: PF15
#spi_bus: spi3
##diag1_pin: PG10
#run_current: 0.650
#stealthchop_threshold: 999999

#[tmc5160 extruder]
#cs_pin: PE7
#spi_bus: spi3
##diag1_pin: PG9
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc5160 extruder1]
#cs_pin: PE10
#spi_bus: spi3
##diag1_pin: PD7
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc5160 extruder2]
#cs_pin: PF1
#spi_bus: spi3
##diag1_pin: PD6
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc5160 extruder3]
#cs_pin: PG2
#spi_bus: spi3
##diag1_pin: PA8
#run_current: 0.800
#stealthchop_threshold: 999999

#[tmc5160 extruder4]
#cs_pin: PG5
#spi_bus: spi3
##diag1_pin: PF3
#run_current: 0.800
#stealthchop_threshold: 999999


########################################
# EXP1 / EXP2 (display) pins
########################################

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PE12, EXP1_3=PB2, EXP1_5=PC14, EXP1_7=PG14, EXP1_9=<GND>,
    EXP1_2=PE13, EXP1_4=PG8, EXP1_6=PC13, EXP1_8=PG13, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PA6, EXP2_3=PB7, EXP2_5=PB6, EXP2_7=PG15,  EXP2_9=<GND>,
    EXP2_2=PA4, EXP2_4=PA4, EXP2_6=PA7, EXP2_8=<RST>, EXP2_10=<5V>

# See the sample-lcd.cfg file for definitions of common LCD displays.
