# This file contains common pin mappings for the BIGTREETECH SKR V1.1
# board. To use this config, the firmware should be compiled for the
# LPC1768.

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: P0.4
dir_pin: !P0.5
enable_pin: !P4.28
microsteps: 16
rotation_distance: 8
endstop_pin: P1.29
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_y]
step_pin: P2.1
dir_pin: P2.2
enable_pin: !P2.0
microsteps: 16
rotation_distance: 8
endstop_pin: P1.27
position_endstop: 0
position_max: 200
homing_speed: 50

[stepper_z]
step_pin: P0.20
dir_pin: P0.21
enable_pin: !P0.19
microsteps: 16
rotation_distance: 40
endstop_pin: !P1.25
position_endstop: 0.5
position_max: 200

#[stepper_z1]
#step_pin: P0.1
#dir_pin: P0.0
#enable_pin: !P0.10
#position_endstop: 0.5
#position_max: 200

[extruder]
step_pin: P0.11
dir_pin: P2.13
enable_pin: !P2.12
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: P2.7
sensor_type: EPCOS 100K B57560G104F
sensor_pin: P0.24
control: pid
pid_Kp: 22.2
pid_Ki: 1.08
pid_Kd: 114
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: P2.5
sensor_type: ATC Semitec 104GT-2
sensor_pin: P0.23
control: watermark
min_temp: 0
max_temp: 130

[fan]
pin: P2.3

[mcu]
serial: /dev/serial/by-id/usb-Klipper_Klipper_firmware_12345-if00

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 3000
max_z_velocity: 5
max_z_accel: 100

[board_pins]
aliases:
    # EXP1 header
    EXP1_1=P1.30, EXP1_3=P0.18, EXP1_5=P0.15, EXP1_7=<NC>, EXP1_9=<GND>,
    EXP1_2=P2.11, EXP1_4=P0.16, EXP1_6=<NC>,  EXP1_8=<NC>, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=P0.17, EXP2_3=P3.26, EXP2_5=P3.25, EXP2_7=P1.31, EXP2_9=<GND>,
    EXP2_2=P0.15, EXP2_4=P1.23, EXP2_6=P0.18, EXP2_8=<RST>, EXP2_10=<NC>
    # Pins EXP2_1, EXP2_6, EXP2_2 are also MISO, MOSI, SCK of bus "ssp0"

# See the sample-lcd.cfg file for definitions of common LCD displays.
