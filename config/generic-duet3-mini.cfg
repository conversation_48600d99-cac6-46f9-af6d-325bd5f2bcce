# This file contains common pin mappings for the Duet3 Mini 5+. To use
# this config, the firmware should be compiled for the SAME54P20 with
# a "25Mhz crystal", "16KiB bootloader", and USB communication.

# To flash the board, double tap the board's reset button to enter the
# bootloader and then run: make flash FLASH_DEVICE=/dev/ttyACM0

# See docs/Config_Reference.md for a description of parameters.


# Pins for reference:
# Driver Step Pins - 0:PC26, 1:PC25, 2:PC24, 3:PC19, 4:PC16, 5:PC30, 6:PC18
# Driver Dir pins - 0:PB3, 1:PB29, 2:PB28, 3:PD20, 4:PD21, 5:PB0, 6:PA27
# Driver Enable - !PC28
# Uart addresses - 0:0 1:1 2:2 3:3 4:!0 5:!1 6:!2 | "!" is for inverted select pin
# Thermistor Pins - T0:PC0, T1:PC1, T2:PC2
# Vssa Sense:PB4 | Vref Sense:PB5
# Current Sense resistor for drivers - .076ohm
# SPI lines:{PD11, PC7} -> Shared SerCom#7, SPIMosi:PC12, SPIMiso:PC15, SPISCLK:PC13
# Vin Monitor:PC3, uses 11:1 voltage divider
# LED's - Diag:PA31, Act:PA30
# 12864 LCD - LCDCSPIN:PC6, ENCA:PC11, ENCB:PD1, ENCSW:PB9, LCD A0:PA2, LCDBeep:PA9, LCD Neopixel Out:PB12 (shared with IO3.out)
# Neopixel Out - PA8
# Serial0 - TX:PB25, RX:PB24 (USB)
# Serial1 - TX:PB31, RX:PB30
# SBC SPISS pin:PA6, SBCTfrReady:PA3, SerComPins:{PA4, PA5, PA6, PA7}
# CAN Pins - TX:PB14 RX:PB15
# Heaters, Fan outputs - {Out0:PB17 Out1:PC10 Out2:PB13 Out3:PB11 Out4:PA11, Out5:PB2, Out6:PB1} | Out6 is shared with VFD_Out
# Tach Pins for Fans - {Out3.Tach:PB27 Out4.Tach:PB26}
# GPIO_out - {IO1:PB31 IO2:PD9 IO3:PB12 IO4:PD10} IO4 is shared with PSON
# GPIO_in - {IO1:PB30 IO2:PD8 IO3:PB7 IO4:PC5 IO5:PC4 IO6:PC31}
# Driver Diag - {D0:PA10, D1:PB8, D2:PA22, D3:PA23, D4:PC21, D5:PB10, D6:PA27}
# Mux Pin - PD0
# EXP headers only support 12864 LCD's

[stepper_x]
#driver0
step_pin: PC26
dir_pin: !PB3
enable_pin: !PC28
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC31
position_endstop: 0
position_max: 450

[tmc2209 stepper_x]
uart_pin: PA1
tx_pin: PA0
select_pins: PD0
uart_address: 0
run_current: 1
sense_resistor: 0.056

[stepper_y]
#driver1
step_pin: PC25
dir_pin: PB29
enable_pin: !PC28
microsteps: 16
rotation_distance: 40
endstop_pin: ^PC4
position_endstop: 0
position_max: 450

[tmc2209 stepper_y]
uart_pin: PA1
tx_pin: PA0
select_pins: PD0
uart_address: 1
run_current: 1
sense_resistor: 0.056

[stepper_z]
#driver2
step_pin: PC24
dir_pin: PB28
enable_pin: !PC28
microsteps: 16
rotation_distance: 8
endstop_pin: PC5
position_endstop: 0
position_max: 400

[tmc2209 stepper_z]
uart_pin: PA1
tx_pin: PA0
select_pins: PD0
uart_address: 2
run_current: 1
sense_resistor: 0.056

[adc_scaled vref_scaled]
vref_pin: PB5
vssa_pin: PB4

[extruder]
#driver3
step_pin: PC19
dir_pin: PD20
enable_pin: !PC28
microsteps: 16
rotation_distance: 33.500
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB13 # out2
sensor_type: ATC Semitec 104GT-2
pullup_resistor: 2200
sensor_pin: vref_scaled:PC1
control: pid
pid_Kp: 30.089
pid_Ki: 2.229
pid_Kd: 101.550
min_temp: 0
max_temp: 285

[tmc2209 extruder]
uart_pin: PA1
tx_pin: PA0
uart_address: 3
select_pins: PD0
run_current: 1
sense_resistor: 0.056

[heater_bed]
heater_pin: PB17 #out1
sensor_type: Generic 3950
sensor_pin: vref_scaled:PC0
control: pid
pullup_resistor: 2200
pid_Kp: 61.049
pid_Ki: 2.339
pid_Kd: 398.344
min_temp: 0
max_temp: 130

[heater_fan heatbreak_fan]
pin: PB11

[fan]
pin: PA11

[mcu]
serial: /dev/ttyACM0

[printer]
kinematics: cartesian
max_velocity: 350
max_accel: 3000

# EXP1 / EXP2 (display) pins
[board_pins]
aliases:
    # EXP1 header
    EXP1_1=PA9, EXP1_3=PC6, EXP1_5=<LCD_RST>, EXP1_7=<NC>, EXP1_9=<GND>,
    EXP1_2=PB9, EXP1_4=PA2, EXP1_6=PB12,      EXP1_8=<NC>, EXP1_10=<5V>,
    # EXP2 header
    EXP2_1=PC15,  EXP2_3=PD1,  EXP2_5=PC11, EXP2_7=PD12,        EXP2_9=<GND>,
    EXP2_2=PC13, EXP2_4=PC14, EXP2_6=PC12, EXP2_8=<RESET_EXT>, EXP2_10=<NC>
    # Pins EXP2_1, EXP2_6, EXP2_2 are also MISO, MOSI, SCK of spi bus "sercom7"

# See the sample-lcd.cfg file for definitions of common LCD displays.
