# This file contains a configuration for the Anycubic Kobra Go printer.
#
# See docs/Config_Reference.md for a description of parameters.
#
# To build the firmware, use the following configuration:
#   - Micro-controller: Huada Semiconductor HC32F460
#   - Communication interface: Serial (PA3 & PA2) - Anycubic
#
# Installation:
#  1. Rename the klipper bin to `firmware.bin` and copy it to an SD Card.
#  2. Power off the Printer, insert the SD Card and power it on.
#  3. The the LCD will be stuck on the Firmware-update screen.
#     Just Wait for 3-5 minutes to ensure the firmware is flashed.
#  4. After waiting, shutdown the printer and remove the SD Card.

[stepper_x]
step_pin: PA12
dir_pin: PA11
enable_pin: !PA15
microsteps: 16
rotation_distance: 40
endstop_pin: !PH2
position_endstop: -13
position_min:-13
position_max: 236
homing_speed: 50

[stepper_y]
step_pin: PA9
dir_pin: PA8
enable_pin: !PA15
microsteps: 16
rotation_distance: 40
endstop_pin: ^!PC13
position_endstop: -9
position_min:-9
position_max: 230
homing_speed: 50

[stepper_z]
step_pin: PC7
dir_pin: !PC6
enable_pin: !PA15
microsteps: 16
rotation_distance: 8
endstop_pin: ^PC14
position_endstop: 0
position_min: -10
position_max: 255
homing_speed: 5

[extruder]
step_pin: PB15
dir_pin: PB14
enable_pin: !PA15
microsteps: 16
rotation_distance: 31.07
max_extrude_only_velocity: 25
max_extrude_only_accel: 1000
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PB8
sensor_type: ATC Semitec 104GT-2
sensor_pin: PC3
min_extrude_temp: 170
min_temp: 0
max_temp: 250
control: pid
pid_kp: 19.56
pid_ki: 1.62
pid_kd: 200.00

[heater_bed]
heater_pin: PB9
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC1
min_temp: 0
max_temp: 120
control: pid
pid_kp: 97.1
pid_ki: 1.41
pid_kd: 1675.16

[bed_mesh]
speed: 200
horizontal_move_z: 2.5
mesh_min: 5, 5
mesh_max: 215, 215
probe_count: 5, 5

[probe]
pin: PA1
x_offset: -20.8
y_offset: 0
z_offset: 0
samples: 3
samples_result: average
samples_tolerance_retries: 3
sample_retract_dist: 0.5
speed: 2
lift_speed: 4

[safe_z_home]
home_xy_position: 0, 0
speed: 5
z_hop: 10
z_hop_speed: 15

[controller_fan controller_fan]
pin: PB12

[heater_fan extruder_fan]
pin: PB13

[fan]
pin: PB5
cycle_time: 0.00005 #20kHz

[output_pin enable_pin]
pin: PB6
value: 1
    #This pin enables the bed, hotend, extruder fan, part fan.

[mcu]
serial: /dev/serial/by-id/usb-1a86_USB_Serial-if00-port0
restart_method: command

[printer]
kinematics: cartesian
max_velocity: 300
max_accel: 500
max_z_velocity: 4
max_z_accel: 100
