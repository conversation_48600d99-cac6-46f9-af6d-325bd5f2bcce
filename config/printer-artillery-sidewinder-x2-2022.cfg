# This file contains pin mappings for the Artillery Sidewinder X2 (2022)
# with a Artillery_Ruby-v1.2 board. To use this config, during "make menuconfig"
# select the STM32F401 with "No bootloader" and USB (on PA11/PA12)
# communication.

# To flash this firmware, set the physical bridge between +3.3V and Boot0 PIN
# on Artillery_Ruby mainboard. Then run the command:
#   make flash FLASH_DEVICE=/dev/serial/by-id/usb-Klipper_stm32f401xc_*-if00

# See docs/Config_Reference.md for a description of parameters.

[stepper_x]
step_pin: !PB14
dir_pin: PB13
enable_pin: !PB15
microsteps: 16
rotation_distance: 40
endstop_pin: !PA2
position_endstop: 0
position_max: 300
homing_speed: 50
second_homing_speed: 10

[stepper_y]
step_pin: PB10
dir_pin: PB2
enable_pin: !PB12
microsteps: 16
rotation_distance: 40
endstop_pin: !PA1
position_endstop: 0
position_max: 300
homing_speed: 50
second_homing_speed: 10

[stepper_z]
step_pin: PB0
dir_pin: !PC5
enable_pin: !PB1
microsteps: 16
rotation_distance: 8
endstop_pin: probe:z_virtual_endstop
position_max: 410
homing_speed: 10
second_homing_speed: 2

[extruder]
step_pin: PA7
dir_pin: PA6
enable_pin: !PC4
microsteps: 16
rotation_distance: 20.925
gear_ratio: 66:22
nozzle_diameter: 0.400
filament_diameter: 1.750
heater_pin: PC9
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC0
control: pid
pid_kp: 20.860
pid_ki: 1.131
pid_kd: 96.218
min_temp: 0
max_temp: 250

[heater_bed]
heater_pin: PA8
sensor_type: EPCOS 100K B57560G104F
sensor_pin: PC1
control: pid
pid_kp: 42.365
pid_ki: 0.545
pid_kd: 822.940
min_temp: 0
max_temp: 130

[fan]
pin: PC8
off_below: 0.1

[heater_fan extruder]
pin: PC7
off_below: 0.2

[controller_fan case]
pin: PC6
off_below: 0.3
idle_speed: 0.0

[temperature_sensor mainboard]
sensor_type: temperature_mcu
min_temp: 10
max_temp: 60

[bltouch]
sensor_pin: PC2
control_pin: PC3
x_offset:27.25
y_offset:-12.8
z_offset: 1.0
samples: 3
samples_tolerance: 0.050
samples_tolerance_retries: 3

[safe_z_home]
home_xy_position: 150,150
z_hop: 15

[bed_mesh]
speed: 100
mesh_min: 27.25, 12.8
mesh_max: 272.75, 287.2
algorithm: bicubic
probe_count: 5,5
mesh_pps: 3
fade_end: 10

[bed_screws]
screw1: 50,50
screw1_name: front left
screw2: 250,50
screw2_name: front right
screw3: 250,250
screw3_name: back right
screw4: 50,250
screw4_name: back left
speed: 100.0

[screws_tilt_adjust]
screw1: 23,63
screw1_name: front left
screw2: 223,63
screw2_name: front right
screw3: 223,263
screw3_name: back right
screw4: 23,263
screw4_name: back left
speed: 100.0
screw_thread: CW-M5

[mcu]
serial: /dev/ttyACM0

[neopixel extruder]
pin: PB7
initial_RED: 1.0
initial_GREEN: 1.0
initial_BLUE: 1.0

[printer]
kinematics: cartesian
max_velocity: 250
max_accel: 1500
max_z_velocity: 50
max_z_accel: 400
square_corner_velocity: 5.0
